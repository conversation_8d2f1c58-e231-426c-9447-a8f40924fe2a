# AGENTS.md

## Build, Lint, and Test Commands

- Build all: `yarn nx run-many -t build`
- Build single: `yarn nx build <project>`
- Lint all: `yarn nx run-many -t lint`
- Lint single: `yarn nx lint <project>`
- Test all: `yarn nx run-many -t test`
- Test single: `yarn nx test <project>`
- Run a single test: `yarn nx test <project> --testNamePattern="<test name>"`
- E2E: `yarn nx e2e twenty-e2e-testing`

## Code Style Guidelines

- **Formatting**: Prettier (2-space, single quotes, trailing commas, 80-char width, semicolons)
- **Imports**: Order: external, internal (absolute), then relative. No unused imports. Prefer const over let.
- **Naming**: camelCase (vars/fns), PascalCase (types/classes), SCREAMING_SNAKE_CASE (consts), kebab-case (files/dirs). Suffix React prop types with 'Props'.
- **Types**: Strict TypeScript, no `any`, strict mode, use `type` over `interface` (except 3rd-party), string literal unions over enums, leverage inference, use type guards and generics.
- **Functions**: Small, focused, required params first, optional last. Export types used across files.
- **Error Handling**: Use proper error types/messages, log and rethrow, never swallow errors silently.
- **Comments**: Explain business logic/non-obvious code, use JSDoc for public APIs, TODOs for future work.

## React & State Management

- Functional components only, named exports, destructure props, event handlers over useEffect, small/focused components, extract logic to hooks, compose from smaller components, memoize as needed.
- Use atoms/selectors (Recoil) for state, multiple useState for unrelated state, useReducer for complex logic, props down/events up, normalize complex data, batch updates, avoid heavy computations in selectors.

## Testing

- AAA pattern, test behavior not implementation, descriptive names, query by user-visible elements, keep tests isolated, mock services, use test data factories, 70% unit/20% integration/10% E2E, async helpers for user interactions.

For full details, see `.cursor/rules/` (code-style, typescript-guidelines, testing-guidelines, react-general-guidelines, react-state-management).
