---
description: Complete setup and run workflow for Twenty CRM system
---

# Twenty CRM - Complete Setup and Run Workflow

This workflow provides step-by-step instructions to set up and run the entire Twenty CRM system from scratch.

## Prerequisites

1. **Node.js**: Version 22.12.0 or higher (check with `node --version`)
2. **Yarn**: Version 4.0.2 or higher (check with `yarn --version`)
3. **Docker**: For running databases and services
4. **Git**: For cloning the repository

## System Architecture Overview

Twenty CRM consists of multiple packages:
- `twenty-server`: NestJS backend API server
- `twenty-front`: React frontend application
- `twenty-emails`: Email templates and services
- `twenty-ui`: Shared UI components
- `twenty-website`: Marketing website
- `twenty-chrome-extension`: Browser extension
- `twenty-zapier`: Zapier integration
- `twenty-e2e-testing`: End-to-end tests

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
# Clone the repository
git clone https://github.com/twentyhq/twenty.git
cd twenty

# Install dependencies using Yarn
yarn install
```

### 2. Set Up Development Databases

Twenty requires PostgreSQL, Redis, and optionally <PERSON><PERSON><PERSON>ouse for analytics.

#### Option A: Using Docker (Recommended)

```bash
# Start PostgreSQL database
make postgres-on-docker

# Start Redis cache
make redis-on-docker

# Optional: Start ClickHouse for analytics
make clickhouse-on-docker

# Optional: Start Grafana for monitoring
make grafana-on-docker

# Optional: Start OpenTelemetry collector
make opentelemetry-collector-on-docker
```

#### Option B: Using Docker Compose

```bash
# Navigate to docker directory
cd packages/twenty-docker

# Copy environment file
cp .env.example .env

# Edit .env file with your configuration
# Set strong passwords and secrets

# Start all services
docker-compose up -d
```

### 3. Configure Environment Variables

Create environment configuration for the server:

```bash
# Navigate to server directory
cd packages/twenty-server

# Create .env file (if not using Docker)
cat > .env << EOF
# Database Configuration
PG_DATABASE_URL=postgres://postgres:postgres@localhost:5432/default
REDIS_URL=redis://localhost:6379

# Server Configuration
SERVER_URL=http://localhost:3000
FRONT_BASE_URL=http://localhost:3001

# Security
APP_SECRET=$(openssl rand -base64 32)
ACCESS_TOKEN_SECRET=$(openssl rand -base64 32)
REFRESH_TOKEN_SECRET=$(openssl rand -base64 32)
FILE_TOKEN_SECRET=$(openssl rand -base64 32)

# Storage
STORAGE_TYPE=local

# Optional: Email Configuration
# EMAIL_FROM_ADDRESS=<EMAIL>
# EMAIL_FROM_NAME="Twenty CRM"
# EMAIL_DRIVER=smtp
# EMAIL_SMTP_HOST=smtp.gmail.com
# EMAIL_SMTP_PORT=587
# EMAIL_SMTP_USER=<EMAIL>
# EMAIL_SMTP_PASSWORD=your-app-password

# Optional: Google OAuth
# AUTH_GOOGLE_ENABLED=true
# AUTH_GOOGLE_CLIENT_ID=your-google-client-id
# AUTH_GOOGLE_CLIENT_SECRET=your-google-client-secret
# AUTH_GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/redirect
EOF
```

### 4. Initialize Database

```bash
# Navigate back to root directory
cd ../..

# Initialize and migrate database
cd packages/twenty-server
yarn database:init:prod
```

### 5. Build the Applications

```bash
# Navigate back to root directory
cd ../..

# Build all packages
npx nx run-many -t build -p twenty-server twenty-front twenty-emails twenty-ui
```

## Running the System

### Development Mode (Recommended for Development)

```bash
# Start both server and frontend in development mode
yarn start

# This command runs:
# - twenty-server on http://localhost:3000
# - twenty-front on http://localhost:3001
# - Background worker processes
```

### Production Mode

```bash
# Build for production
npx nx run-many -t build -p twenty-server twenty-front

# Start server in production mode
cd packages/twenty-server
yarn start:prod

# In another terminal, start worker
yarn worker:prod

# Serve frontend (if needed separately)
cd ../twenty-front
npx serve dist
```

### Individual Services

```bash
# Start only the server
npx nx run twenty-server:start

# Start only the frontend
npx nx run twenty-front:start

# Start background worker
npx nx run twenty-server:worker

# Start email service
npx nx run twenty-emails:start
```

## Verification Steps

### 1. Check Database Connection

```bash
# Test PostgreSQL connection
docker exec twenty_pg psql -U postgres -d default -c "SELECT version();"

# Test Redis connection
docker exec twenty_redis redis-cli ping
```

### 2. Verify Services

- **Frontend**: Open http://localhost:3001
- **Backend API**: Open http://localhost:3000/graphql
- **Health Check**: http://localhost:3000/health
- **Grafana** (if enabled): http://localhost:4000 (admin/admin)

### 3. Test Basic Functionality

1. Create a new account at http://localhost:3001
2. Log in and verify dashboard loads
3. Create a test company or person record
4. Verify data persistence

## Troubleshooting

### Common Issues

1. **Port Conflicts**:
   ```bash
   # Check what's using ports
   lsof -i :3000
   lsof -i :3001
   lsof -i :5432
   lsof -i :6379
   ```

2. **Database Connection Issues**:
   ```bash
   # Restart PostgreSQL container
   docker restart twenty_pg
   
   # Check logs
   docker logs twenty_pg
   ```

3. **Build Errors**:
   ```bash
   # Clear Nx cache
   npx nx reset
   
   # Clean node_modules and reinstall
   rm -rf node_modules
   yarn install
   ```

4. **Permission Issues**:
   ```bash
   # Fix file permissions
   sudo chown -R $(whoami) .
   ```

### Logs and Debugging

```bash
# View server logs
cd packages/twenty-server
npx nx run twenty-server:start --verbose

# View frontend logs
cd packages/twenty-front
npx nx run twenty-front:start --verbose

# View Docker container logs
docker logs twenty_pg
docker logs twenty_redis
```

## Development Workflow

### Code Changes

1. **Frontend Changes**: Hot reload at http://localhost:3001
2. **Backend Changes**: Server restarts automatically
3. **Database Changes**: Run migrations manually

### Testing

```bash
# Run unit tests
npx nx run-many -t test

# Run E2E tests
npx nx run twenty-e2e-testing:e2e

# Run linting
npx nx run-many -t lint
```

### Database Management

```bash
# Create new migration
cd packages/twenty-server
npx typeorm migration:create src/database/typeorm/core/migrations/YourMigrationName

# Run migrations
yarn database:migrate:prod

# Reset database (CAUTION: This will delete all data)
docker exec twenty_pg psql -U postgres -d postgres -c "DROP DATABASE IF EXISTS default;"
docker exec twenty_pg psql -U postgres -d postgres -c "CREATE DATABASE default WITH OWNER postgres;"
yarn database:init:prod
```

## Stopping the System

```bash
# Stop development servers (Ctrl+C in terminals)

# Stop Docker containers
docker stop twenty_pg twenty_redis twenty_clickhouse twenty_grafana twenty_otlp_collector

# Or stop all containers
docker stop $(docker ps -q)

# Remove containers (optional)
docker rm twenty_pg twenty_redis twenty_clickhouse twenty_grafana twenty_otlp_collector
```

## Additional Resources

- **Documentation**: https://twenty.com/developers
- **Local Setup Guide**: https://twenty.com/developers/local-setup
- **Self-hosting Guide**: https://twenty.com/developers/section/self-hosting
- **Discord Community**: https://discord.gg/cx5n4Jzs57
- **GitHub Issues**: https://github.com/twentyhq/twenty/issues

## Quick Start Summary

For experienced developers, here's the minimal setup:

```bash
# Clone and install
git clone https://github.com/twentyhq/twenty.git && cd twenty && yarn install

# Start databases
make postgres-on-docker && make redis-on-docker

# Initialize database
cd packages/twenty-server && yarn database:init:prod && cd ../..

# Start development servers
yarn start

# Access at http://localhost:3001
```

The system should now be fully operational with the frontend at http://localhost:3001 and the backend API at http://localhost:3000.
