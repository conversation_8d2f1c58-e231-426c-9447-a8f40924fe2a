# Database Configuration
PG_DATABASE_URL=postgres://postgres:postgres@localhost:5432/default
REDIS_URL=redis://localhost:6379

# Server Configuration
SERVER_URL=http://localhost:3000
FRONT_BASE_URL=http://localhost:3001

# Security
APP_SECRET=$(openssl rand -base64 32)
ACCESS_TOKEN_SECRET=$(openssl rand -base64 32)
REFRESH_TOKEN_SECRET=$(openssl rand -base64 32)
FILE_TOKEN_SECRET=$(openssl rand -base64 32)

# Storage
STORAGE_TYPE=local
NODE_ENV=production
