import { NumberDataType } from 'src/engine/metadata-modules/field-metadata/interfaces/field-metadata-settings.interface';
import { FieldMetadataSeed } from 'src/engine/workspace-manager/dev-seeder/metadata/types/field-metadata-seed.type';
import { FieldMetadataType } from 'twenty-shared/types';

export const SCORE_TEMPLATE_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    type: FieldMetadataType.TEXT,
    label: 'Template Code',
    name: 'templateCode',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Description',
    name: 'description',
    settings: {
      displayedMaxRows: 3,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Max Score',
    name: 'maxScore',
    settings: {
      dataType: NumberDataType.INT,
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Passing Score',
    name: 'passingScore',
    settings: {
      dataType: NumberDataType.INT,
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.SELECT,
    label: 'Assessment Type',
    name: 'assessmentType',
    settings: {
      options: [
        { value: 'Exam', label: 'Exam', color: 'red' },
        { value: 'Quiz', label: 'Quiz', color: 'orange' },
        { value: 'Assignment', label: 'Assignment', color: 'blue' },
        { value: 'Project', label: 'Project', color: 'green' },
        { value: 'Presentation', label: 'Presentation', color: 'purple' },
      ],
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Grading Criteria',
    name: 'gradingCriteria',
    settings: {
      displayedMaxRows: 4,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.BOOLEAN,
    label: 'Is Active',
    name: 'isActive',
    settings: {},
  } as FieldMetadataSeed,
];