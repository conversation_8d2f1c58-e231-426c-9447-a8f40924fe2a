import { NumberDataType } from 'src/engine/metadata-modules/field-metadata/interfaces/field-metadata-settings.interface';
import { FieldMetadataSeed } from 'src/engine/workspace-manager/dev-seeder/metadata/types/field-metadata-seed.type';
import { FieldMetadataType } from 'twenty-shared/types';

export const CLASS_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    type: FieldMetadataType.TEXT,
    label: 'Class Code',
    name: 'classCode',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Description',
    name: 'description',
    settings: {
      displayedMaxRows: 3,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Capacity',
    name: 'capacity',
    settings: {
      dataType: NumberDataType.INT,
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Current Enrollment',
    name: 'currentEnrollment',
    settings: {
      dataType: NumberDataType.INT,
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'Start Date',
    name: 'startDate',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'End Date',
    name: 'endDate',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Schedule',
    name: 'schedule',
    settings: {
      displayedMaxRows: 2,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.SELECT,
    label: 'Status',
    name: 'status',
    settings: {
      options: [
        { value: 'Active', label: 'Active', color: 'green' },
        { value: 'Upcoming', label: 'Upcoming', color: 'blue' },
        { value: 'Completed', label: 'Completed', color: 'gray' },
        { value: 'Cancelled', label: 'Cancelled', color: 'red' },
      ],
    },
  } as FieldMetadataSeed,
];