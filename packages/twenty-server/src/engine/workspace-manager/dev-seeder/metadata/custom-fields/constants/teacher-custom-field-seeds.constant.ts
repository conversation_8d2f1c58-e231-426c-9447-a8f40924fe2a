import { FieldMetadataSeed } from 'src/engine/workspace-manager/dev-seeder/metadata/types/field-metadata-seed.type';
import { FieldMetadataType } from 'twenty-shared/types';

export const TEACHER_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    type: FieldMetadataType.TEXT,
    label: 'Employee ID',
    name: 'employeeId',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Email',
    name: 'email',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Phone',
    name: 'phone',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Department',
    name: 'department',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Specialization',
    name: 'specialization',
    settings: {
      displayedMaxRows: 2,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'Hire Date',
    name: 'hireDate',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.SELECT,
    label: 'Employment Status',
    name: 'employmentStatus',
    settings: {
      options: [
        { value: 'Full-time', label: 'Full-time', color: 'green' },
        { value: 'Part-time', label: 'Part-time', color: 'blue' },
        { value: 'Contract', label: 'Contract', color: 'orange' },
        { value: 'Inactive', label: 'Inactive', color: 'gray' },
      ],
    },
  } as FieldMetadataSeed,
];