import { FieldMetadataSeed } from 'src/engine/workspace-manager/dev-seeder/metadata/types/field-metadata-seed.type';
import { FieldMetadataType } from 'twenty-shared/types';

export const ORDER_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    type: FieldMetadataType.TEXT,
    label: 'Order Number',
    name: 'orderNumber',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'Order Date',
    name: 'orderDate',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.CURRENCY,
    label: 'Total Amount',
    name: 'totalAmount',
    settings: {
      currencyCode: 'USD',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.SELECT,
    label: 'Sale Type',
    name: 'saleType',
    settings: {
      options: [
        { value: 'Course Enrollment', label: 'Course Enrollment', color: 'blue' },
        { value: 'Program Registration', label: 'Program Registration', color: 'green' },
        { value: 'Workshop', label: 'Workshop', color: 'orange' },
        { value: 'Tutoring', label: 'Tutoring', color: 'purple' },
        { value: 'Materials', label: 'Materials', color: 'gray' },
      ],
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.SELECT,
    label: 'Order Status',
    name: 'orderStatus',
    settings: {
      options: [
        { value: 'Pending', label: 'Pending', color: 'yellow' },
        { value: 'Confirmed', label: 'Confirmed', color: 'blue' },
        { value: 'Processing', label: 'Processing', color: 'orange' },
        { value: 'Completed', label: 'Completed', color: 'green' },
        { value: 'Cancelled', label: 'Cancelled', color: 'red' },
      ],
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Notes',
    name: 'notes',
    settings: {
      displayedMaxRows: 2,
    },
  } as FieldMetadataSeed,
];