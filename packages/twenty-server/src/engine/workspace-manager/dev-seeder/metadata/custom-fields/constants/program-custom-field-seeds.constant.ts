import { FieldMetadataSeed } from 'src/engine/workspace-manager/dev-seeder/metadata/types/field-metadata-seed.type';
import { FieldMetadataType } from 'twenty-shared/types';

export const PROGRAM_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    type: FieldMetadataType.TEXT,
    label: 'Program Code',
    name: 'programCode',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Description',
    name: 'description',
    settings: {
      displayedMaxRows: 3,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Duration (months)',
    name: 'duration',
    settings: {
      dataType: 'INT',
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Department',
    name: 'department',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.SELECT,
    label: 'Level',
    name: 'level',
    settings: {
      options: [
        { value: 'Beginner', label: 'Beginner', color: 'green' },
        { value: 'Intermediate', label: 'Intermediate', color: 'blue' },
        { value: 'Advanced', label: 'Advanced', color: 'orange' },
        { value: 'Graduate', label: 'Graduate', color: 'purple' },
      ],
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.SELECT,
    label: 'Status',
    name: 'status',
    settings: {
      options: [
        { value: 'Active', label: 'Active', color: 'green' },
        { value: 'Inactive', label: 'Inactive', color: 'gray' },
        { value: 'Discontinued', label: 'Discontinued', color: 'red' },
      ],
    },
  } as FieldMetadataSeed,
];