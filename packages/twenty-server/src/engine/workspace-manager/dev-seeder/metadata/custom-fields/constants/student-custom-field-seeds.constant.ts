import { FieldMetadataSeed } from 'src/engine/workspace-manager/dev-seeder/metadata/types/field-metadata-seed.type';
import { FieldMetadataType } from 'twenty-shared/types';

export const STUDENT_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    type: FieldMetadataType.TEXT,
    label: 'Student ID',
    name: 'studentId',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Email',
    name: 'email',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Phone',
    name: 'phone',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'Date of Birth',
    name: 'dateOfBirth',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Address',
    name: 'address',
    settings: {
      displayedMaxRows: 2,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Emergency Contact',
    name: 'emergencyContact',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Emergency Phone',
    name: 'emergencyPhone',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.SELECT,
    label: 'Status',
    name: 'status',
    settings: {
      options: [
        { value: 'Active', label: 'Active', color: 'green' },
        { value: 'Inactive', label: 'Inactive', color: 'gray' },
        { value: 'Graduated', label: 'Graduated', color: 'blue' },
        { value: 'Withdrawn', label: 'Withdrawn', color: 'red' },
      ],
    },
  } as FieldMetadataSeed,
];