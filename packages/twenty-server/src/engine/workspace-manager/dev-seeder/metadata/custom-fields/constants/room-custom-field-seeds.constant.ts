import { NumberDataType } from 'src/engine/metadata-modules/field-metadata/interfaces/field-metadata-settings.interface';
import { FieldMetadataSeed } from 'src/engine/workspace-manager/dev-seeder/metadata/types/field-metadata-seed.type';
import { FieldMetadataType } from 'twenty-shared/types';

export const ROOM_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    type: FieldMetadataType.TEXT,
    label: 'Room Number',
    name: 'roomNumber',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Building',
    name: 'building',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Capacity',
    name: 'capacity',
    settings: {
      dataType: NumberDataType.INT,
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.SELECT,
    label: 'Room Type',
    name: 'roomType',
    settings: {
      options: [
        { value: 'Classroom', label: 'Classroom', color: 'blue' },
        { value: 'Laboratory', label: 'Laboratory', color: 'green' },
        { value: 'Auditorium', label: 'Auditorium', color: 'purple' },
        { value: 'Conference Room', label: 'Conference Room', color: 'orange' },
        { value: 'Study Room', label: 'Study Room', color: 'gray' },
      ],
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Equipment',
    name: 'equipment',
    settings: {
      displayedMaxRows: 2,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.BOOLEAN,
    label: 'Is Available',
    name: 'isAvailable',
    settings: {},
  } as FieldMetadataSeed,
];