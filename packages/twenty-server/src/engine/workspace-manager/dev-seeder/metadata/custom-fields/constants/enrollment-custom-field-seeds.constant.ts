import { NumberDataType } from 'src/engine/metadata-modules/field-metadata/interfaces/field-metadata-settings.interface';
import { FieldMetadataSeed } from 'src/engine/workspace-manager/dev-seeder/metadata/types/field-metadata-seed.type';
import { FieldMetadataType } from 'twenty-shared/types';

export const ENROLLMENT_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    type: FieldMetadataType.TEXT,
    label: 'Enrollment Code',
    name: 'enrollmentCode',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'Enrollment Date',
    name: 'enrollmentDate',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE,
    label: 'Start Date',
    name: 'startDate',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE,
    label: 'End Date',
    name: 'endDate',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.SELECT,
    label: 'Enrollment Status',
    name: 'enrollmentStatus',
    settings: {
      options: [
        { value: 'Active', label: 'Active', color: 'green' },
        { value: 'Pending', label: 'Pending', color: 'yellow' },
        { value: 'Completed', label: 'Completed', color: 'blue' },
        { value: 'Withdrawn', label: 'Withdrawn', color: 'red' },
        { value: 'Suspended', label: 'Suspended', color: 'orange' },
      ],
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Completion %',
    name: 'completionPercentage',
    settings: {
      dataType: NumberDataType.FLOAT,
      decimals: 1,
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Attended Sessions',
    name: 'attendedSessions',
    settings: {
      dataType: NumberDataType.INT,
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Total Sessions',
    name: 'totalSessions',
    settings: {
      dataType: NumberDataType.INT,
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.CURRENCY,
    label: 'Paid Amount',
    name: 'paidAmount',
    settings: {
      currencyCode: 'USD',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.CURRENCY,
    label: 'Outstanding Amount',
    name: 'outstandingAmount',
    settings: {
      currencyCode: 'USD',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.SELECT,
    label: 'Payment Method',
    name: 'paymentMethod',
    settings: {
      options: [
        { value: 'Credit Card', label: 'Credit Card', color: 'blue' },
        { value: 'Bank Transfer', label: 'Bank Transfer', color: 'green' },
        { value: 'Cash', label: 'Cash', color: 'gray' },
        { value: 'Check', label: 'Check', color: 'orange' },
        { value: 'Scholarship', label: 'Scholarship', color: 'purple' },
      ],
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.SELECT,
    label: 'Payment Status',
    name: 'paymentStatus',
    settings: {
      options: [
        { value: 'Paid', label: 'Paid', color: 'green' },
        { value: 'Partial', label: 'Partial', color: 'yellow' },
        { value: 'Pending', label: 'Pending', color: 'orange' },
        { value: 'Overdue', label: 'Overdue', color: 'red' },
      ],
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Progress Note',
    name: 'progressNote',
    settings: {
      displayedMaxRows: 3,
    },
  } as FieldMetadataSeed,
];