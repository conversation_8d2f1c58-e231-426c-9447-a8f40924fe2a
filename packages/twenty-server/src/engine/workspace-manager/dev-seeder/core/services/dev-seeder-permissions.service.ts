import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { WorkspaceActivationStatus } from 'twenty-shared/workspace';
import { Repository } from 'typeorm';

import { Workspace } from 'src/engine/core-modules/workspace/workspace.entity';
import { ObjectMetadataEntity } from 'src/engine/metadata-modules/object-metadata/object-metadata.entity';
import { ObjectPermissionService } from 'src/engine/metadata-modules/object-permission/object-permission.service';
import { RoleService } from 'src/engine/metadata-modules/role/role.service';
import { UserRoleService } from 'src/engine/metadata-modules/user-role/user-role.service';
import { USER_WORKSPACE_DATA_SEED_IDS } from 'src/engine/workspace-manager/dev-seeder/core/utils/seed-user-workspaces.util';
import {
  SEED_APPLE_WORKSPACE_ID,
  SEED_YCOMBINATOR_WORKSPACE_ID,
} from 'src/engine/workspace-manager/dev-seeder/core/utils/seed-workspaces.util';

@Injectable()
export class DevSeederPermissionsService {
  private readonly logger = new Logger(DevSeederPermissionsService.name);

  constructor(
    private readonly roleService: RoleService,
    private readonly userRoleService: UserRoleService,
    @InjectRepository(Workspace, 'core')
    private readonly workspaceRepository: Repository<Workspace>,
    private readonly objectPermissionService: ObjectPermissionService,
    @InjectRepository(ObjectMetadataEntity, 'core')
    private readonly objectMetadataRepository: Repository<ObjectMetadataEntity>,
  ) {}

  public async initPermissions(workspaceId: string) {
    this.logger.log(`Starting permissions initialization for workspace ${workspaceId}`);
    const adminRole = await this.roleService.createAdminRole({
      workspaceId,
    });
    this.logger.log(`Created admin role: ${adminRole.id}`);
    
    // Test: create initial object permissions for admin role immediately
    await this.createObjectPermissionsForAllRoles(workspaceId);

    let adminUserWorkspaceId: string | undefined;
    let memberUserWorkspaceIds: string[] = [];
    let limitedUserWorkspaceId: string | undefined;
    let guestUserWorkspaceId: string | undefined;

    if (workspaceId === SEED_APPLE_WORKSPACE_ID) {
      adminUserWorkspaceId = USER_WORKSPACE_DATA_SEED_IDS.JANE;
      limitedUserWorkspaceId = USER_WORKSPACE_DATA_SEED_IDS.TIM;
      memberUserWorkspaceIds = [USER_WORKSPACE_DATA_SEED_IDS.JONY];
      guestUserWorkspaceId = USER_WORKSPACE_DATA_SEED_IDS.PHIL;

      // Create guest role only in this workspace
      const guestRole = await this.roleService.createGuestRole({
        workspaceId,
      });

      await this.userRoleService.assignRoleToUserWorkspace({
        workspaceId,
        userWorkspaceId: guestUserWorkspaceId,
        roleId: guestRole.id,
      });

      const limitedRole =
        await this.createLimitedRoleForSeedWorkspace(workspaceId);

      await this.userRoleService.assignRoleToUserWorkspace({
        workspaceId,
        userWorkspaceId: limitedUserWorkspaceId,
        roleId: limitedRole.id,
      });
    } else if (workspaceId === SEED_YCOMBINATOR_WORKSPACE_ID) {
      adminUserWorkspaceId = USER_WORKSPACE_DATA_SEED_IDS.TIM_ACME;
      memberUserWorkspaceIds = [
        USER_WORKSPACE_DATA_SEED_IDS.JONY_ACME,
        USER_WORKSPACE_DATA_SEED_IDS.JANE_ACME,
        USER_WORKSPACE_DATA_SEED_IDS.PHIL_ACME,
      ];
    }

    if (adminUserWorkspaceId) {
      await this.userRoleService.assignRoleToUserWorkspace({
        workspaceId,
        userWorkspaceId: adminUserWorkspaceId,
        roleId: adminRole.id,
      });
    }

    const memberRole = await this.roleService.createMemberRole({
      workspaceId,
    });

    await this.workspaceRepository.update(workspaceId, {
      defaultRoleId: memberRole.id,
      activationStatus: WorkspaceActivationStatus.ACTIVE,
    });

    if (memberUserWorkspaceIds) {
      for (const memberUserWorkspaceId of memberUserWorkspaceIds) {
        await this.userRoleService.assignRoleToUserWorkspace({
          workspaceId,
          userWorkspaceId: memberUserWorkspaceId,
          roleId: memberRole.id,
        });
      }
    }

    // Create object permissions for all roles and objects
    await this.createObjectPermissionsForAllRoles(workspaceId);
  }

  private async createLimitedRoleForSeedWorkspace(workspaceId: string) {
    const customRole = await this.roleService.createRole({
      workspaceId,
      input: {
        label: 'Object-restricted',
        description:
          'All permissions except read on Rockets and update on Pets',
        icon: 'custom',
        canUpdateAllSettings: true,
        canReadAllObjectRecords: true,
        canUpdateAllObjectRecords: true,
        canSoftDeleteAllObjectRecords: true,
        canDestroyAllObjectRecords: true,
      },
    });

    const petObjectMetadata = await this.objectMetadataRepository.findOneOrFail(
      {
        where: {
          nameSingular: 'pet',
          workspaceId,
        },
      },
    );

    const rocketObjectMetadata =
      await this.objectMetadataRepository.findOneOrFail({
        where: {
          nameSingular: 'rocket',
          workspaceId,
        },
      });

    await this.objectPermissionService.upsertObjectPermissions({
      workspaceId,
      input: {
        roleId: customRole.id,
        objectPermissions: [
          {
            objectMetadataId: petObjectMetadata.id,
            canReadObjectRecords: true,
            canUpdateObjectRecords: false,
            canSoftDeleteObjectRecords: false,
            canDestroyObjectRecords: false,
          },
          {
            objectMetadataId: rocketObjectMetadata.id,
            canReadObjectRecords: false,
            canUpdateObjectRecords: false,
            canSoftDeleteObjectRecords: false,
            canDestroyObjectRecords: false,
          },
        ],
      },
    });

    return customRole;
  }

  private async createObjectPermissionsForAllRoles(workspaceId: string) {
    try {
      // Get all roles in the workspace
      const roles = await this.roleService.getWorkspaceRoles(workspaceId);
      
      // Get all object metadata for the workspace
      const objectMetadatas = await this.objectMetadataRepository.find({
        where: { workspaceId },
      });

      this.logger.log(`Found ${roles.length} roles and ${objectMetadatas.length} objects for workspace ${workspaceId}`);

      // Create object permissions for each role-object combination
      for (const role of roles) {
        // Skip creating permissions for limited role if it already has custom permissions
        if (role.label === 'Object-restricted') {
          continue;
        }

        const objectPermissions = objectMetadatas.map((objectMetadata) => ({
          objectMetadataId: objectMetadata.id,
          canReadObjectRecords: role.canReadAllObjectRecords,
          canUpdateObjectRecords: role.canUpdateAllObjectRecords,
          canSoftDeleteObjectRecords: role.canSoftDeleteAllObjectRecords,
          canDestroyObjectRecords: role.canDestroyAllObjectRecords,
        }));

        if (objectPermissions.length > 0) {
          this.logger.log(`Creating ${objectPermissions.length} object permissions for role ${role.label}`);
          await this.objectPermissionService.upsertObjectPermissions({
            workspaceId,
            input: {
              roleId: role.id,
              objectPermissions,
            },
          });
        }
      }
    } catch (error) {
      this.logger.error('Failed to create object permissions:', error);
      throw error;
    }
  }
}
