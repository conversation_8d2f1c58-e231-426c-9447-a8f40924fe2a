type ScoreTemplateDataSeed = {
  id: string;
  name: string;
  templateCode: string;
  description: string;
  maxScore: number;
  passingScore: number;
  assessmentType: string;
  gradingCriteria: string;
  isActive: boolean;
};

export const SCORE_TEMPLATE_DATA_SEED_COLUMNS: (keyof ScoreTemplateDataSeed)[] = [
  'id',
  'name',
  'templateCode',
  'description',
  'maxScore',
  'passingScore',
  'assessmentType',
  'gradingCriteria',
  'isActive',
];

export const SCORE_TEMPLATE_DATA_SEED_IDS = {
  ID_1: 'd0d0d0d0-0001-4000-8000-000000000001',
  ID_2: 'd0d0d0d0-0002-4000-8000-000000000002',
  ID_3: 'd0d0d0d0-0003-4000-8000-000000000003',
  ID_4: 'd0d0d0d0-0004-4000-8000-000000000004',
  ID_5: 'd0d0d0d0-0005-4000-8000-000000000005',
  ID_6: 'd0d0d0d0-0006-4000-8000-000000000006',
};

export const SCORE_TEMPLATE_DATA_SEEDS: ScoreTemplateDataSeed[] = [
  {
    id: SCORE_TEMPLATE_DATA_SEED_IDS.ID_1,
    name: 'Standard Exam Template',
    templateCode: 'EXAM-STD',
    description: 'Standard examination template for most courses',
    maxScore: 100,
    passingScore: 70,
    assessmentType: 'Exam',
    gradingCriteria: 'A: 90-100, B: 80-89, C: 70-79, D: 60-69, F: <60',
    isActive: true,
  },
  {
    id: SCORE_TEMPLATE_DATA_SEED_IDS.ID_2,
    name: 'Weekly Quiz Template',
    templateCode: 'QUIZ-WK',
    description: 'Template for weekly quizzes and short assessments',
    maxScore: 20,
    passingScore: 14,
    assessmentType: 'Quiz',
    gradingCriteria: 'Pass: 14+, Fail: <14. Focus on key concepts covered in lectures.',
    isActive: true,
  },
  {
    id: SCORE_TEMPLATE_DATA_SEED_IDS.ID_3,
    name: 'Programming Assignment',
    templateCode: 'PROG-ASSGN',
    description: 'Template for programming assignments and coding projects',
    maxScore: 50,
    passingScore: 35,
    assessmentType: 'Assignment',
    gradingCriteria: 'Code Quality: 20pts, Functionality: 20pts, Documentation: 10pts',
    isActive: true,
  },
  {
    id: SCORE_TEMPLATE_DATA_SEED_IDS.ID_4,
    name: 'Final Project Template',
    templateCode: 'PROJ-FINAL',
    description: 'Comprehensive final project evaluation template',
    maxScore: 200,
    passingScore: 140,
    assessmentType: 'Project',
    gradingCriteria: 'Research: 50pts, Implementation: 75pts, Presentation: 50pts, Report: 25pts',
    isActive: true,
  },
  {
    id: SCORE_TEMPLATE_DATA_SEED_IDS.ID_5,
    name: 'Oral Presentation Rubric',
    templateCode: 'PRES-ORAL',
    description: 'Evaluation template for oral presentations and speeches',
    maxScore: 30,
    passingScore: 21,
    assessmentType: 'Presentation',
    gradingCriteria: 'Content: 10pts, Delivery: 10pts, Visual Aids: 5pts, Q&A: 5pts',
    isActive: false,
  },
  {
    id: SCORE_TEMPLATE_DATA_SEED_IDS.ID_6,
    name: 'Lab Practical Exam',
    templateCode: 'LAB-PRAC',
    description: 'Hands-on laboratory practical examination template',
    maxScore: 75,
    passingScore: 53,
    assessmentType: 'Exam',
    gradingCriteria: 'Procedure: 30pts, Results: 25pts, Analysis: 15pts, Safety: 5pts',
    isActive: true,
  },
];