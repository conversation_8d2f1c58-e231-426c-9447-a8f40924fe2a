type ProgramDataSeed = {
  id: string;
  name: string;
  programCode: string;
  description: string;
  duration: number;
  department: string;
  level: string;
  status: string;
};

export const PROGRAM_DATA_SEED_COLUMNS: (keyof ProgramDataSeed)[] = [
  'id',
  'name',
  'programCode',
  'description',
  'duration',
  'department',
  'level',
  'status',
];

export const PROGRAM_DATA_SEED_IDS = {
  ID_1: '90909090-0001-4000-8000-000000000001',
  ID_2: '90909090-0002-4000-8000-000000000002',
  ID_3: '90909090-0003-4000-8000-000000000003',
  ID_4: '90909090-0004-4000-8000-000000000004',
  ID_5: '90909090-0005-4000-8000-000000000005',
  ID_6: '90909090-0006-4000-8000-000000000006',
};

export const PROGRAM_DATA_SEEDS: ProgramDataSeed[] = [
  {
    id: PROGRAM_DATA_SEED_IDS.ID_1,
    name: 'Computer Science Bachelor',
    programCode: 'BSCS',
    description: 'Four-year bachelor degree program in computer science covering programming, algorithms, and software engineering',
    duration: 48,
    department: 'Computer Science',
    level: 'Graduate',
    status: 'Active',
  },
  {
    id: PROGRAM_DATA_SEED_IDS.ID_2,
    name: 'Digital Art Certificate',
    programCode: 'DART-CERT',
    description: 'Six-month certificate program in digital art and design fundamentals',
    duration: 6,
    department: 'Art & Design',
    level: 'Beginner',
    status: 'Active',
  },
  {
    id: PROGRAM_DATA_SEED_IDS.ID_3,
    name: 'Mathematics Major',
    programCode: 'MATH-MAJ',
    description: 'Comprehensive mathematics program covering calculus, statistics, and advanced mathematical concepts',
    duration: 36,
    department: 'Mathematics',
    level: 'Intermediate',
    status: 'Active',
  },
  {
    id: PROGRAM_DATA_SEED_IDS.ID_4,
    name: 'English Literature Master',
    programCode: 'MA-ENG',
    description: 'Advanced graduate program in English literature and critical analysis',
    duration: 24,
    department: 'English Literature',
    level: 'Graduate',
    status: 'Active',
  },
  {
    id: PROGRAM_DATA_SEED_IDS.ID_5,
    name: 'Physics Research Track',
    programCode: 'PHYS-RES',
    description: 'Research-focused physics program with laboratory work and thesis requirement',
    duration: 30,
    department: 'Physics',
    level: 'Advanced',
    status: 'Inactive',
  },
  {
    id: PROGRAM_DATA_SEED_IDS.ID_6,
    name: 'Basic Computing Skills',
    programCode: 'COMP-BASIC',
    description: 'Introductory computer skills for non-technical students',
    duration: 3,
    department: 'Computer Science',
    level: 'Beginner',
    status: 'Discontinued',
  },
];