type EnrollmentDataSeed = {
  id: string;
  name: string;
  enrollmentCode: string;
  enrollmentDate: string;
  startDate: string;
  endDate: string;
  enrollmentStatus: string;
  completionPercentage: number;
  attendedSessions: number;
  totalSessions: number;
  paidAmount: number;
  outstandingAmount: number;
  paymentMethod: string;
  paymentStatus: string;
  progressNote: string;
};

export const ENROLLMENT_DATA_SEED_COLUMNS: (keyof EnrollmentDataSeed)[] = [
  'id',
  'name',
  'enrollmentCode',
  'enrollmentDate',
  'startDate',
  'endDate',
  'enrollmentStatus',
  'completionPercentage',
  'attendedSessions',
  'totalSessions',
  'paidAmount',
  'outstandingAmount',
  'paymentMethod',
  'paymentStatus',
  'progressNote',
];

export const ENROLLMENT_DATA_SEED_IDS = {
  ID_1: 'b0b0b0b0-0001-4000-8000-000000000001',
  ID_2: 'b0b0b0b0-0002-4000-8000-000000000002',
  ID_3: 'b0b0b0b0-0003-4000-8000-000000000003',
  ID_4: 'b0b0b0b0-0004-4000-8000-000000000004',
  ID_5: 'b0b0b0b0-0005-4000-8000-000000000005',
  ID_6: 'b0b0b0b0-0006-4000-8000-000000000006',
};

export const ENROLLMENT_DATA_SEEDS: EnrollmentDataSeed[] = [
  {
    id: ENROLLMENT_DATA_SEED_IDS.ID_1,
    name: 'Emily Johnson - MATH101',
    enrollmentCode: 'ENR-2024-001',
    enrollmentDate: '2024-08-20T10:00:00.000Z',
    startDate: '2024-08-26',
    endDate: '2024-12-15',
    enrollmentStatus: 'Active',
    completionPercentage: 65.5,
    attendedSessions: 18,
    totalSessions: 30,
    paidAmount: 1200.00,
    outstandingAmount: 0.00,
    paymentMethod: 'Credit Card',
    paymentStatus: 'Paid',
    progressNote: 'Excellent progress in calculus concepts. Strong performance on midterm exam.',
  },
  {
    id: ENROLLMENT_DATA_SEED_IDS.ID_2,
    name: 'Michael Chen - CS101',
    enrollmentCode: 'ENR-2024-002',
    enrollmentDate: '2024-08-18T14:30:00.000Z',
    startDate: '2024-08-26',
    endDate: '2024-12-15',
    enrollmentStatus: 'Active',
    completionPercentage: 72.0,
    attendedSessions: 20,
    totalSessions: 28,
    paidAmount: 800.00,
    outstandingAmount: 400.00,
    paymentMethod: 'Bank Transfer',
    paymentStatus: 'Partial',
    progressNote: 'Good understanding of programming fundamentals. Need to improve debugging skills.',
  },
  {
    id: ENROLLMENT_DATA_SEED_IDS.ID_3,
    name: 'Sarah Williams - ENG101',
    enrollmentCode: 'ENR-2024-003',
    enrollmentDate: '2024-08-22T09:15:00.000Z',
    startDate: '2024-08-26',
    endDate: '2024-12-15',
    enrollmentStatus: 'Active',
    completionPercentage: 58.3,
    attendedSessions: 16,
    totalSessions: 32,
    paidAmount: 0.00,
    outstandingAmount: 950.00,
    paymentMethod: 'Scholarship',
    paymentStatus: 'Pending',
    progressNote: 'Shows creativity in writing assignments. Attendance needs improvement.',
  },
  {
    id: ENROLLMENT_DATA_SEED_IDS.ID_4,
    name: 'James Rodriguez - DART-CERT',
    enrollmentCode: 'ENR-2024-004',
    enrollmentDate: '2024-06-01T11:00:00.000Z',
    startDate: '2024-06-01',
    endDate: '2024-11-30',
    enrollmentStatus: 'Completed',
    completionPercentage: 100.0,
    attendedSessions: 24,
    totalSessions: 24,
    paidAmount: 1500.00,
    outstandingAmount: 0.00,
    paymentMethod: 'Cash',
    paymentStatus: 'Paid',
    progressNote: 'Successfully completed digital art certificate. Portfolio shows strong artistic development.',
  },
  {
    id: ENROLLMENT_DATA_SEED_IDS.ID_5,
    name: 'Ashley Thompson - BSCS',
    enrollmentCode: 'ENR-2024-005',
    enrollmentDate: '2024-01-15T13:45:00.000Z',
    startDate: '2024-01-15',
    endDate: '2027-12-15',
    enrollmentStatus: 'Suspended',
    completionPercentage: 25.0,
    attendedSessions: 45,
    totalSessions: 180,
    paidAmount: 5000.00,
    outstandingAmount: 15000.00,
    paymentMethod: 'Check',
    paymentStatus: 'Overdue',
    progressNote: 'Enrollment suspended due to academic probation. Requires academic counseling.',
  },
  {
    id: ENROLLMENT_DATA_SEED_IDS.ID_6,
    name: 'Daniel Kim - PHYS201',
    enrollmentCode: 'ENR-2024-006',
    enrollmentDate: '2024-12-01T16:20:00.000Z',
    startDate: '2025-01-15',
    endDate: '2025-05-10',
    enrollmentStatus: 'Withdrawn',
    completionPercentage: 0.0,
    attendedSessions: 0,
    totalSessions: 30,
    paidAmount: 200.00,
    outstandingAmount: 0.00,
    paymentMethod: 'Credit Card',
    paymentStatus: 'Paid',
    progressNote: 'Student withdrew before semester start. Deposit refunded.',
  },
];