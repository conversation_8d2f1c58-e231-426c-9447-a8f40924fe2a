type StudentDataSeed = {
  id: string;
  name: string;
  studentId: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  address: string;
  emergencyContact: string;
  emergencyPhone: string;
  status: string;
};

export const STUDENT_DATA_SEED_COLUMNS: (keyof StudentDataSeed)[] = [
  'id',
  'name',
  'studentId',
  'email',
  'phone',
  'dateOfBirth',
  'address',
  'emergencyContact',
  'emergencyPhone',
  'status',
];

export const STUDENT_DATA_SEED_IDS = {
  ID_1: '60606060-0001-4000-8000-000000000001',
  ID_2: '60606060-0002-4000-8000-000000000002',
  ID_3: '60606060-0003-4000-8000-000000000003',
  ID_4: '60606060-0004-4000-8000-000000000004',
  ID_5: '60606060-0005-4000-8000-000000000005',
  ID_6: '60606060-0006-4000-8000-000000000006',
};

export const STUDENT_DATA_SEEDS: StudentDataSeed[] = [
  {
    id: STUDENT_DATA_SEED_IDS.ID_1,
    name: '<PERSON>',
    studentId: 'STU001',
    email: '<EMAIL>',
    phone: '******-0101',
    dateOfBirth: '2000-03-15T00:00:00.000Z',
    address: '123 Oak Street, Springfield, IL 62701',
    emergencyContact: 'Robert Johnson',
    emergencyPhone: '******-0102',
    status: 'Active',
  },
  {
    id: STUDENT_DATA_SEED_IDS.ID_2,
    name: 'Michael Chen',
    studentId: 'STU002',
    email: '<EMAIL>',
    phone: '******-0201',
    dateOfBirth: '1999-08-22T00:00:00.000Z',
    address: '456 Pine Avenue, Chicago, IL 60614',
    emergencyContact: 'Li Chen',
    emergencyPhone: '******-0202',
    status: 'Active',
  },
  {
    id: STUDENT_DATA_SEED_IDS.ID_3,
    name: 'Sarah Williams',
    studentId: 'STU003',
    email: '<EMAIL>',
    phone: '******-0301',
    dateOfBirth: '2001-01-10T00:00:00.000Z',
    address: '789 Maple Drive, Aurora, IL 60506',
    emergencyContact: 'David Williams',
    emergencyPhone: '******-0302',
    status: 'Active',
  },
  {
    id: STUDENT_DATA_SEED_IDS.ID_4,
    name: 'James Rodriguez',
    studentId: 'STU004',
    email: '<EMAIL>',
    phone: '******-0401',
    dateOfBirth: '2000-11-05T00:00:00.000Z',
    address: '321 Elm Street, Naperville, IL 60540',
    emergencyContact: 'Maria Rodriguez',
    emergencyPhone: '******-0402',
    status: 'Active',
  },
  {
    id: STUDENT_DATA_SEED_IDS.ID_5,
    name: 'Ashley Thompson',
    studentId: 'STU005',
    email: '<EMAIL>',
    phone: '******-0501',
    dateOfBirth: '1998-07-18T00:00:00.000Z',
    address: '654 Cedar Lane, Peoria, IL 61614',
    emergencyContact: 'Susan Thompson',
    emergencyPhone: '******-0502',
    status: 'Graduated',
  },
  {
    id: STUDENT_DATA_SEED_IDS.ID_6,
    name: 'Daniel Kim',
    studentId: 'STU006',
    email: '<EMAIL>',
    phone: '******-0601',
    dateOfBirth: '2001-12-03T00:00:00.000Z',
    address: '987 Birch Road, Rockford, IL 61107',
    emergencyContact: 'Grace Kim',
    emergencyPhone: '******-0602',
    status: 'Withdrawn',
  },
];