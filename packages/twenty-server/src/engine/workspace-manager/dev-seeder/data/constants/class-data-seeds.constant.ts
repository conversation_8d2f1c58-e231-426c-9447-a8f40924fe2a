type ClassDataSeed = {
  id: string;
  name: string;
  classCode: string;
  description: string;
  capacity: number;
  currentEnrollment: number;
  startDate: string;
  endDate: string;
  schedule: string;
  status: string;
};

export const CLASS_DATA_SEED_COLUMNS: (keyof ClassDataSeed)[] = [
  'id',
  'name',
  'classCode',
  'description',
  'capacity',
  'currentEnrollment',
  'startDate',
  'endDate',
  'schedule',
  'status',
];

export const CLASS_DATA_SEED_IDS = {
  ID_1: '80808080-0001-4000-8000-000000000001',
  ID_2: '80808080-0002-4000-8000-000000000002',
  ID_3: '80808080-0003-4000-8000-000000000003',
  ID_4: '80808080-0004-4000-8000-000000000004',
  ID_5: '80808080-0005-4000-8000-000000000005',
  ID_6: '80808080-0006-4000-8000-000000000006',
};

export const CLASS_DATA_SEEDS: ClassDataSeed[] = [
  {
    id: CLASS_DATA_SEED_IDS.ID_1,
    name: '<PERSON><PERSON> I',
    classCode: 'MATH101',
    description: 'Introduction to differential and integral calculus with applications',
    capacity: 30,
    currentEnrollment: 28,
    startDate: '2024-08-26T00:00:00.000Z',
    endDate: '2024-12-15T00:00:00.000Z',
    schedule: 'MWF 9:00-10:00 AM',
    status: 'Active',
  },
  {
    id: CLASS_DATA_SEED_IDS.ID_2,
    name: 'Introduction to Programming',
    classCode: 'CS101',
    description: 'Fundamentals of computer programming using Python',
    capacity: 25,
    currentEnrollment: 22,
    startDate: '2024-08-26T00:00:00.000Z',
    endDate: '2024-12-15T00:00:00.000Z',
    schedule: 'TTH 2:00-3:30 PM',
    status: 'Active',
  },
  {
    id: CLASS_DATA_SEED_IDS.ID_3,
    name: 'English Composition',
    classCode: 'ENG101',
    description: 'Academic writing and critical thinking skills development',
    capacity: 20,
    currentEnrollment: 18,
    startDate: '2024-08-26T00:00:00.000Z',
    endDate: '2024-12-15T00:00:00.000Z',
    schedule: 'MWF 11:00-12:00 PM',
    status: 'Active',
  },
  {
    id: CLASS_DATA_SEED_IDS.ID_4,
    name: 'Physics II',
    classCode: 'PHYS201',
    description: 'Electricity, magnetism, and electromagnetic waves',
    capacity: 24,
    currentEnrollment: 0,
    startDate: '2025-01-15T00:00:00.000Z',
    endDate: '2025-05-10T00:00:00.000Z',
    schedule: 'MWF 1:00-2:00 PM',
    status: 'Upcoming',
  },
  {
    id: CLASS_DATA_SEED_IDS.ID_5,
    name: 'Digital Art Fundamentals',
    classCode: 'ART150',
    description: 'Introduction to digital art tools and techniques',
    capacity: 15,
    currentEnrollment: 15,
    startDate: '2024-06-01T00:00:00.000Z',
    endDate: '2024-07-30T00:00:00.000Z',
    schedule: 'TTH 10:00-12:00 PM',
    status: 'Completed',
  },
  {
    id: CLASS_DATA_SEED_IDS.ID_6,
    name: 'Advanced Statistics',
    classCode: 'STAT301',
    description: 'Statistical analysis and research methods',
    capacity: 18,
    currentEnrollment: 5,
    startDate: '2024-08-26T00:00:00.000Z',
    endDate: '2024-12-15T00:00:00.000Z',
    schedule: 'MW 3:00-4:30 PM',
    status: 'Cancelled',
  },
];