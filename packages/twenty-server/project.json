{"name": "twenty-server", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "tags": ["scope:backend"], "targets": {"build": {"executor": "nx:run-commands", "cache": true, "options": {"cwd": "packages/twenty-server", "commands": ["<PERSON><PERSON><PERSON> dist", "nest build --path ./tsconfig.build.json"]}, "dependsOn": ["^build"]}, "test:integration": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-server", "commands": ["NODE_ENV=test NODE_OPTIONS=\"--max-old-space-size=8192\" nx jest --config ./jest-integration.config.ts"]}, "parallel": false, "configurations": {"with-db-reset": {"cwd": "packages/twenty-server", "commands": ["nx database:reset --no-seed", "NODE_ENV=test NODE_OPTIONS=\"--max-old-space-size=8192\" nx jest --config ./jest-integration.config.ts"]}}}, "build:packageJson": {"executor": "@nx/js:tsc", "options": {"main": "packages/twenty-server/dist/src/main.js", "tsConfig": "packages/twenty-server/tsconfig.json", "outputPath": "packages/twenty-server/dist", "updateBuildableProjectDepsInPackageJson": true}}, "typecheck": {}, "start": {"executor": "nx:run-commands", "dependsOn": ["typecheck", "build"], "options": {"cwd": "packages/twenty-server", "command": "NODE_ENV=development && nest start --watch"}}, "start:debug": {"executor": "nx:run-commands", "dependsOn": ["build"], "options": {"cwd": "packages/twenty-server", "command": "nx start --debug"}}, "reset:env": {"executor": "nx:run-commands", "inputs": ["{projectRoot}/.env.example"], "outputs": ["{projectRoot}/.env"], "cache": true, "options": {"cwd": "{projectRoot}", "command": "cp .env.example .env"}}, "command": {"executor": "nx:run-commands", "dependsOn": ["build"], "options": {"cwd": "packages/twenty-server", "command": "node dist/src/command/command.js"}}, "command-no-deps": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-server", "command": "node dist/src/command/command.js"}}, "worker": {"executor": "nx:run-commands", "dependsOn": ["build"], "options": {"cwd": "packages/twenty-server", "command": "node dist/src/queue-worker/queue-worker.js"}}, "typeorm": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-server", "command": "ts-node --transpile-only -P tsconfig.json ../../node_modules/typeorm/cli.js"}}, "ts-node": {"executor": "nx:run-commands", "dependsOn": ["build"], "options": {"cwd": "packages/twenty-server", "command": "ts-node"}}, "ts-node-no-deps": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-server", "command": "ts-node"}}, "ts-node-no-deps-transpile-only": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-server", "command": "ts-node --transpile-only"}}, "lint": {"options": {"lintFilePatterns": ["{projectRoot}/src/**/*.{ts,json}"], "maxWarnings": 0}, "configurations": {"ci": {"lintFilePatterns": ["{projectRoot}/**/*.{ts,json}"]}, "fix": {}}}, "test": {}, "test:debug": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-server", "command": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register ../../node_modules/.bin/jest --runInBand"}}, "jest": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-server", "command": "jest"}}, "database:migrate": {"executor": "nx:run-commands", "dependsOn": ["build"], "options": {"cwd": "packages/twenty-server", "commands": ["nx typeorm -- migration:run -d src/database/typeorm/core/core.datasource"]}}, "database:migrate:revert": {"executor": "nx:run-commands", "dependsOn": ["build"], "options": {"cwd": "packages/twenty-server", "commands": ["nx typeorm -- migration:revert -d src/database/typeorm/core/core.datasource"]}}, "generate:integration-test": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-server", "commands": ["nx ts-node-no-deps -- ./test/integration/graphql/codegen/index.ts"], "parallel": false}}, "database:reset": {"executor": "nx:run-commands", "dependsOn": ["build"], "configurations": {"no-seed": {"cwd": "packages/twenty-server", "commands": ["nx ts-node-no-deps-transpile-only -- ./scripts/truncate-db.ts", "nx ts-node-no-deps-transpile-only -- ./scripts/setup-db.ts", "nx database:migrate", "nx command-no-deps -- cache:flush"], "parallel": false}, "seed": {"cwd": "packages/twenty-server", "commands": ["nx ts-node-no-deps-transpile-only -- ./scripts/truncate-db.ts", "nx ts-node-no-deps-transpile-only -- ./scripts/setup-db.ts", "nx database:migrate", "nx command-no-deps -- cache:flush", "nx command-no-deps -- workspace:seed:dev"], "parallel": false}}, "defaultConfiguration": "seed"}, "clickhouse:migrate": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-server", "command": "nx ts-node-no-deps -- src/database/clickHouse/migrations/run-migrations.ts"}}, "clickhouse:seed": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-server", "command": "nx ts-node-no-deps -- src/database/clickHouse/seeds/run-seeds.ts"}}, "lingui:extract": {"executor": "nx:run-commands", "options": {"cwd": "{projectRoot}", "command": "lingui extract --overwrite"}}, "lingui:compile": {"executor": "nx:run-commands", "options": {"cwd": "{projectRoot}", "command": "lingui compile --typescript"}}}}