**/**/.env
.DS_Store
/.idea
**/node_modules/**
.cache

# yarn is the recommended package manager across the project
**/**/.package-lock.json

.nx/installation
.nx/cache

.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.vercel
.swc

**/**/logs/**

coverage
dist
storybook-static
*.tsbuildinfo
.eslintcache
.nyc_output
test-results/
dump.rdb
.tinyb

.notes
/data/
/.devenv/
/.direnv/
/.pre-commit-config.yaml
/.envrc
/devenv.nix
/flake.lock
/flake.nix

.crowdin.yml
.react-email/

mcp.json