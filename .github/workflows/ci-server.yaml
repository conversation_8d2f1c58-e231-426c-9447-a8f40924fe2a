name: CI Server
on:
  push:
    branches:
      - main

  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  SERVER_SETUP_CACHE_KEY: server-setup

jobs:
  changed-files-check:
    uses: ./.github/workflows/changed-files.yaml
    with:
      files: |
        package.json
        packages/twenty-server/**
        packages/twenty-emails/**
        packages/twenty-shared/**
  server-setup:
    needs: changed-files-check
    if: needs.changed-files-check.outputs.any_changed == 'true'
    timeout-minutes: 30
    runs-on: depot-ubuntu-24.04-8
    env:
      NX_REJECT_UNKNOWN_LOCAL_CACHE: 0
    services:
      postgres:
        image: twentycrm/twenty-postgres-spilo
        env:
          PGUSER_SUPERUSER: postgres
          PGPASSWORD_SUPERUSER: postgres
          ALLOW_NOSSL: 'true'
          SPILO_PROVIDER: 'local'
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis
        ports:
          - 6379:6379
    steps:
      - name: Fetch custom Github Actions and base branch history
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install dependencies
        uses: ./.github/workflows/actions/yarn-install
      - name: Restore server setup
        id: restore-server-setup-cache
        uses: ./.github/workflows/actions/restore-cache
        with:
          key: ${{ env.SERVER_SETUP_CACHE_KEY }}
      - name: Build twenty-shared
        run: npx nx build twenty-shared
      - name: Server / Run lint & typecheck
        uses: ./.github/workflows/actions/nx-affected
        with:
          tag: scope:backend
          tasks: lint,typecheck
      - name: Server / Build
        run: npx nx build twenty-server
      - name: Server / Write .env
        run: npx nx reset:env twenty-server
      - name: Server / Create DB
        run: |
          PGPASSWORD=postgres psql -h localhost -p 5432 -U postgres -d postgres -c 'CREATE DATABASE "default";'
          PGPASSWORD=postgres psql -h localhost -p 5432 -U postgres -d postgres -c 'CREATE DATABASE "test";'
          npx nx run twenty-server:database:init:prod
          npx nx run twenty-server:database:migrate:prod
      - name: Worker / Run
        run: |
          timeout 30s npx nx run twenty-server:worker || exit_code=$?
          if [ $exit_code -eq 124 ]; then
            # If timeout was reached (exit code 124), consider it a success
            exit 0
          elif [ $exit_code -ne 0 ]; then
            # If worker failed for other reasons, fail the build
            exit $exit_code
          fi
      - name: Server / Check for Pending Migrations
        run: |
          CORE_MIGRATION_OUTPUT=$(npx nx run twenty-server:typeorm migration:generate core-migration-check -d src/database/typeorm/core/core.datasource.ts || true)

          CORE_MIGRATION_FILE=$(ls packages/twenty-server/*core-migration-check.ts 2>/dev/null || echo "")

          if [ -n "$CORE_MIGRATION_FILE" ]; then
            echo "::error::Unexpected migration files were generated. Please create a proper migration manually."
            echo "$CORE_MIGRATION_OUTPUT"

            rm -f packages/twenty-server/*core-migration-check.ts
            
            exit 1
          fi
      - name: Save server setup
        uses: ./.github/workflows/actions/save-cache
        with:
          key: ${{ steps.restore-server-setup-cache.outputs.cache-primary-key }}

  graphql-check:
    timeout-minutes: 10
    runs-on: depot-ubuntu-24.04-8
    needs: [server-setup, changed-files-check]
    if: needs.changed-files-check.outputs.any_changed == 'true'
    steps:
      - name: Fetch custom Github Actions and base branch history
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install dependencies
        uses: ./.github/workflows/actions/yarn-install
      - name: GraphQL / Check for Pending Generation
        run: |
          # Run GraphQL generation commands
          npx nx run twenty-front:graphql:generate
          npx nx run twenty-front:graphql:generate --configuration=metadata

          # Check if any files were modified
          if ! git diff --quiet; then
            echo "::error::GraphQL schema changes detected. Please run 'npx nx run twenty-front:graphql:generate' and 'npx nx run twenty-front:graphql:generate --configuration=metadata' and commit the changes."
            exit 1
          fi

  server-test:
    timeout-minutes: 30
    runs-on: depot-ubuntu-24.04-8
    needs: server-setup
    env:
      NX_REJECT_UNKNOWN_LOCAL_CACHE: 0
    steps:
      - name: Fetch custom Github Actions and base branch history
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install dependencies
        uses: ./.github/workflows/actions/yarn-install
      - name: Restore server setup
        uses: ./.github/workflows/actions/restore-cache
        with:
          key: ${{ env.SERVER_SETUP_CACHE_KEY }}
      - name: Server / Run Tests
        uses: ./.github/workflows/actions/nx-affected
        with:
          tag: scope:backend
          tasks: test

  server-integration-test:
    timeout-minutes: 30
    runs-on: depot-ubuntu-24.04-8
    needs: server-setup
    strategy:
      fail-fast: false
      matrix:
        shard: [1, 2, 3, 4]
    services:
      postgres:
        image: twentycrm/twenty-postgres-spilo
        env:
          PGUSER_SUPERUSER: postgres
          PGPASSWORD_SUPERUSER: postgres
          ALLOW_NOSSL: 'true'
          SPILO_PROVIDER: 'local'
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis
        ports:
          - 6379:6379
      clickhouse:
        image: clickhouse/clickhouse-server:latest
        env:
          CLICKHOUSE_PASSWORD: clickhousePassword
          CLICKHOUSE_URL: "************************************************/twenty"
        ports:
          - 8123:8123
          - 9000:9000
        options: >-
          --health-cmd "clickhouse-client --host=localhost --port=9000 --user=default --password=clickhousePassword --query='SELECT 1'" 
          --health-interval 10s 
          --health-timeout 5s 
          --health-retries 5
    env:
      NX_REJECT_UNKNOWN_LOCAL_CACHE: 0
      NODE_ENV: test
      ANALYTICS_ENABLED: true
      CLICKHOUSE_URL: "************************************************/twenty"
      CLICKHOUSE_PASSWORD: clickhousePassword
      SHARD_COUNTER: 4
    steps:
      - name: Fetch custom Github Actions and base branch history
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install dependencies
        uses: ./.github/workflows/actions/yarn-install
      - name: Update .env.test for integrations tests
        run: |
          echo "IS_BILLING_ENABLED=true" >> .env.test
          echo "BILLING_STRIPE_API_KEY=test-api-key" >> .env.test
          echo "BILLING_STRIPE_BASE_PLAN_PRODUCT_ID=test-base-plan-product-id" >> .env.test
          echo "BILLING_STRIPE_WEBHOOK_SECRET=test-webhook-secret" >> .env.test
          echo "BILLING_PLAN_REQUIRED_LINK=http://localhost:3001/stripe-redirection" >> .env.test
      - name: Restore server setup
        uses: ./.github/workflows/actions/restore-cache
        with:
          key: ${{ env.SERVER_SETUP_CACHE_KEY }}
      - name: Server / Build
        run: npx nx build twenty-server
      - name: Build dependencies
        run: |
          npx nx build twenty-shared
          npx nx build twenty-emails
      - name: Server / Create Test DB
        run: |
          PGPASSWORD=postgres psql -h localhost -p 5432 -U postgres -d postgres -c 'CREATE DATABASE "test";'
      - name: Run ClickHouse migrations
        run: npx nx clickhouse:migrate twenty-server
      - name: Run ClickHouse seeds
        run: npx nx clickhouse:seed twenty-server
      - name: Server / Run Integration Tests
        uses: ./.github/workflows/actions/nx-affected
        with:
          tag: scope:backend
          tasks: 'test:integration'
          configuration: 'with-db-reset'
          args: --shard=${{ matrix.shard }}/${{ env.SHARD_COUNTER }}
  ci-server-status-check:
    if: always() && !cancelled()
    timeout-minutes: 5
    runs-on: depot-ubuntu-24.04-8
    needs: [changed-files-check, server-setup, graphql-check, server-test, server-integration-test]
    steps:
      - name: Fail job if any needs failed 
        if: contains(needs.*.result, 'failure')
        run: exit 1
