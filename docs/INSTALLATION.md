# Hướng dẫn Cài đặt và Khởi tạo Hệ thống Twenty CRM

## <PERSON><PERSON><PERSON> lục
- [<PERSON><PERSON><PERSON> cầu hệ thống](#yêu-cầu-hệ-thống)
- [Cài đặt Dependencies](#cài-đặt-dependencies)
- [Thiết lập cơ sở dữ liệu](#thiết-lập-cơ-sở-dữ-liệu)
- [Thi<PERSON>t lập <PERSON>](#thiết-lập-redis)
- [Cấu hình môi trường](#cấu-hình-môi-trường)
- [Khởi động hệ thống](#khởi-động-hệ-thống)
- [Xác minh cài đặt](#xác-minh-cài-đặt)

## Yêu cầu hệ thống

### Phần mềm cần thiết
- **Node.js**: >= 18.0.0
- **npm/yarn**: <PERSON><PERSON>n bản mới nhất
- **Docker**: >= 20.0.0
- **Docker Compose**: >= 2.0.0
- **Git**: <PERSON><PERSON><PERSON> bản mới nhất

### H<PERSON> điều hành được hỗ trợ
- macOS (Intel/Apple Silicon)
- Linux (Ubuntu 20.04+, CentOS 8+)
- Windows 10/11 (với WSL2)

## Cài đặt Dependencies

### 1. Clone Repository
```bash
git clone https://github.com/twentyhq/twenty.git
cd twenty
```

### 2. Cài đặt Node.js Dependencies
```bash
# Sử dụng npm
npm install

# Hoặc sử dụng yarn
yarn install
```

### 3. Build Project
```bash
# Build tất cả packages
nx build twenty-server
```

## Thiết lập Cơ sở dữ liệu

### 1. Khởi động PostgreSQL Container
```bash
make postgres-on-docker
```

**Thông tin kết nối PostgreSQL:**
- Host: `localhost`
- Port: `5432`
- Database: `default`
- Username: `postgres`
- Password: `postgres`

### 2. Xác minh PostgreSQL
```bash
# Kiểm tra container đang chạy
docker ps | grep postgres

# Kiểm tra kết nối database
docker exec twenty_pg psql -U postgres -d default -c "SELECT version();"
```

### 3. Chạy Database Migrations
```bash
nx database:migrate twenty-server
```

## Thiết lập Redis

### 1. Khởi động Redis Container
```bash
make redis-on-docker
```

**Thông tin kết nối Redis:**
- Host: `localhost`
- Port: `6379`
- No password (development)

### 2. Xác minh Redis
```bash
# Kiểm tra Redis đang chạy
docker ps | grep redis

# Hoặc kiểm tra bằng lệnh
lsof -i :6379
```

## Cấu hình Môi trường

### 1. Tạo file Environment
```bash
# Copy file .env mẫu
cp packages/twenty-server/.env.example packages/twenty-server/.env
```

### 2. Cấu hình Database URL
Chỉnh sửa file `.env`:
```env
# Database
PG_DATABASE_URL=postgres://postgres:postgres@localhost:5432/default

# Redis
REDIS_URL=redis://localhost:6379

# Server
SERVER_URL=http://localhost:3000
FRONT_BASE_URL=http://localhost:3001

# JWT
ACCESS_TOKEN_SECRET=replace_me_with_a_random_string_access
LOGIN_TOKEN_SECRET=replace_me_with_a_random_string_login
REFRESH_TOKEN_SECRET=replace_me_with_a_random_string_refresh
FILE_TOKEN_SECRET=replace_me_with_a_random_string_file
```

### 3. Tạo JWT Secrets
```bash
# Tạo random strings cho JWT secrets
openssl rand -base64 32  # Cho ACCESS_TOKEN_SECRET
openssl rand -base64 32  # Cho LOGIN_TOKEN_SECRET
openssl rand -base64 32  # Cho REFRESH_TOKEN_SECRET
openssl rand -base64 32  # Cho FILE_TOKEN_SECRET
```

## Khởi động Hệ thống

### 1. Khởi động Server
```bash
# Development mode
nx start twenty-server

# Hoặc production mode
nx start:prod twenty-server
```

### 2. Khởi động Frontend (Tùy chọn)
```bash
# Trong terminal khác
nx start twenty-front
```

### 3. Kiểm tra Services
```bash
# Kiểm tra server
curl http://localhost:3000/healthz

# Kiểm tra GraphQL
curl -X POST http://localhost:3000/graphql \
  -H "Content-Type: application/json" \
  -d '{"query":"query { checkUserExists { __typename } }"}'
```

## Xác minh Cài đặt

### 1. Kiểm tra Database Connection
```bash
# Kết nối đến database
docker exec twenty_pg psql -U postgres -d default -c "\\dt"
```

### 2. Kiểm tra Redis Connection
```bash
# Test Redis
docker exec twenty_redis redis-cli ping
```

### 3. Kiểm tra Server Logs
```bash
# Xem logs của server
nx start twenty-server --verbose
```

### 4. Truy cập Web Interface
- Server API: http://localhost:3000
- GraphQL Playground: http://localhost:3000/graphql
- Frontend (nếu đã khởi động): http://localhost:3001

## Khắc phục Sự cố

### Lỗi Port đã được sử dụng
```bash
# Kiểm tra process đang sử dụng port
lsof -i :3000
lsof -i :5432
lsof -i :6379

# Kill process nếu cần
kill -9 <PID>
```

### Lỗi Database Connection
```bash
# Restart PostgreSQL container
docker restart twenty_pg

# Hoặc xóa và tạo lại
docker rm -f twenty_pg
make postgres-on-docker
```

### Lỗi Redis Connection
```bash
# Restart Redis container
docker restart twenty_redis

# Hoặc xóa và tạo lại
docker rm -f twenty_redis
make redis-on-docker
```

### Lỗi Build/Dependencies
```bash
# Xóa node_modules và reinstall
rm -rf node_modules
npm install

# Clear build cache
nx reset
```

## Scripts Hữu ích

### Database Management
```bash
# Reset database với seed data
nx database:reset twenty-server

# Reset database không có seed data
nx database:reset twenty-server --no-seed

# Chạy migrations
nx database:migrate twenty-server
```

### Development Commands
```bash
# Chạy tests
nx test twenty-server

# Chạy linting
nx lint twenty-server

# Build production
nx build twenty-server
```

### Docker Management
```bash
# Xem tất cả containers Twenty
docker ps | grep twenty

# Stop tất cả containers Twenty
docker stop twenty_pg twenty_redis

# Remove tất cả containers Twenty
docker rm twenty_pg twenty_redis

# Clean up volumes
docker volume prune
```

## Bước tiếp theo

Sau khi cài đặt thành công, bạn có thể:

1. [Seed Custom Objects](./CUSTOM_OBJECTS_SEEDING.md) - Tạo và seed các object tùy chỉnh
2. Tạo workspace đầu tiên
3. Cấu hình authentication
4. Import dữ liệu từ hệ thống cũ

## Hỗ trợ

Nếu gặp vấn đề trong quá trình cài đặt:

1. Kiểm tra [Troubleshooting Guide](./TROUBLESHOOTING.md)
2. Xem logs chi tiết với `--verbose`
3. Tạo issue trên GitHub repository
4. Liên hệ team development