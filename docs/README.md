# Twenty CRM Documentation

Chào mừng đến với tài liệu hướng dẫn Twenty CRM! Đ<PERSON>y là bộ tài liệu đầy đủ để cài đặt, cấu hình và sử dụng hệ thống Twenty CRM.

## 📋 Mục lục

### 🚀 Bắt đầu
- [**Hướng dẫn Cài đặt**](./INSTALLATION.md) - Cài đặt và khởi tạo hệ thống từ đầu
- [**Custom Objects Seeding**](./CUSTOM_OBJECTS_SEEDING.md) - Seed và quản lý custom objects

### 📚 Hướng dẫn chi tiết
- [Troubleshooting Guide](./TROUBLESHOOTING.md) - Khắc phục sự cố thường gặp
- [API Documentation](./API.md) - Hướng dẫn sử dụng GraphQL và REST API
- [Custom Objects Development](./CUSTOM_OBJECTS_DEV.md) - <PERSON>át triển custom objects

### 🔧 Cấu hình
- [Environment Configuration](./ENVIRONMENT.md) - <PERSON><PERSON><PERSON> hình môi trường
- [Authentication Setup](./AUTHENTICATION.md) - Thiết lập xác thực
- [Database Management](./DATABASE.md) - Quản lý cơ sở dữ liệu

## 🎯 Quick Start

### 1. Cài đặt nhanh
```bash
# Clone repository
git clone https://github.com/twentyhq/twenty.git
cd twenty

# Cài đặt dependencies
npm install

# Khởi động services
make postgres-on-docker
make redis-on-docker

# Build và chạy
nx build twenty-server
nx start twenty-server
```

### 2. Seed Custom Objects
```bash
# Seed tất cả 13 custom objects
nx command-no-deps twenty-server -- workspace:seed:dev
```

### 3. Truy cập hệ thống
- **Server API**: http://localhost:3000
- **GraphQL Playground**: http://localhost:3000/graphql
- **Frontend**: http://localhost:3001

## 🏗️ Kiến trúc hệ thống

### Backend (twenty-server)
- **Framework**: NestJS + TypeScript
- **Database**: PostgreSQL với TypeORM
- **Cache**: Redis
- **API**: GraphQL + REST
- **Queue**: Bull (Redis-based)

### Frontend (twenty-front)
- **Framework**: React + TypeScript
- **State Management**: Recoil
- **UI Components**: Custom design system
- **Build Tool**: Vite

### Infrastructure
- **Containerization**: Docker + Docker Compose
- **Build System**: Nx Monorepo
- **Database Migrations**: TypeORM migrations
- **Testing**: Jest + Testing Library

## 📦 Custom Objects có sẵn

### 🎓 Educational Objects
1. **Student** - Quản lý học sinh
2. **Teacher** - Quản lý giáo viên
3. **Class** - Quản lý lớp học
4. **Program** - Quản lý chương trình học
5. **Room** - Quản lý phòng học
6. **Enrollment** - Quản lý đăng ký học
7. **Attendance** - Theo dõi chuyên cần
8. **Score Template** - Mẫu chấm điểm

### 💼 Business Objects
9. **Appointment** - Quản lý lịch hẹn
10. **Call** - Theo dõi cuộc gọi
11. **Campaign** - Quản lý chiến dịch marketing
12. **Order** - Quản lý đơn hàng
13. **Product** - Quản lý sản phẩm

## 🛠️ Development Workflow

### 1. Setup Development Environment
```bash
# Install dependencies
npm install

# Setup environment
cp packages/twenty-server/.env.example packages/twenty-server/.env

# Start development services
make postgres-on-docker
make redis-on-docker
```

### 2. Database Operations
```bash
# Run migrations
nx database:migrate twenty-server

# Reset database with seed
nx database:reset twenty-server

# Reset without seed
nx database:reset twenty-server --no-seed
```

### 3. Development Commands
```bash
# Start server in development mode
nx start twenty-server

# Start frontend
nx start twenty-front

# Run tests
nx test twenty-server

# Lint code
nx lint twenty-server
```

## 🔍 Troubleshooting Quick Reference

### Port Conflicts
```bash
# Check what's using ports
lsof -i :3000  # Server
lsof -i :5432  # PostgreSQL
lsof -i :6379  # Redis

# Kill process if needed
kill -9 <PID>
```

### Database Issues
```bash
# Restart PostgreSQL
docker restart twenty_pg

# Check database connection
docker exec twenty_pg psql -U postgres -d default -c "SELECT 1;"
```

### Redis Issues
```bash
# Restart Redis
docker restart twenty_redis

# Test Redis connection
docker exec twenty_redis redis-cli ping
```

### Build Issues
```bash
# Clear build cache
nx reset

# Reinstall dependencies
rm -rf node_modules
npm install
```

## 📊 Monitoring và Logging

### Application Logs
```bash
# Server logs with verbose output
nx start twenty-server --verbose

# Specific service logs
docker logs twenty_pg
docker logs twenty_redis
```

### Database Monitoring
```bash
# Check active connections
docker exec twenty_pg psql -U postgres -d default -c \
  "SELECT count(*) FROM pg_stat_activity;"

# Check database size
docker exec twenty_pg psql -U postgres -d default -c \
  "SELECT pg_size_pretty(pg_database_size('default'));"
```

### Performance Metrics
```bash
# Redis info
docker exec twenty_redis redis-cli info

# System resources
docker stats twenty_pg twenty_redis
```

## 🔐 Security Best Practices

### Environment Variables
- Đổi tất cả JWT secrets trong production
- Sử dụng strong passwords cho database
- Không commit sensitive data vào git

### Database Security
- Tạo dedicated database user với quyền hạn chế
- Enable SSL connections trong production
- Regular backup và test restore procedures

### Application Security
- Validate tất cả user inputs
- Implement rate limiting
- Regular security updates

## 🤝 Contributing

### Code Standards
- Follow TypeScript best practices
- Use ESLint và Prettier configurations
- Write unit tests cho new features
- Update documentation khi cần

### Git Workflow
```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push to remote
git push origin feature/your-feature-name

# Create pull request
```

## 📞 Support

### Documentation
- [Installation Guide](./INSTALLATION.md)
- [Custom Objects Seeding](./CUSTOM_OBJECTS_SEEDING.md)
- [Troubleshooting](./TROUBLESHOOTING.md)

### Community
- GitHub Issues: https://github.com/twentyhq/twenty/issues
- Discussions: https://github.com/twentyhq/twenty/discussions
- Discord: [Twenty Community Discord]

### Professional Support
- Enterprise support available
- Custom development services
- Training và consulting

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.

---

**Cập nhật lần cuối**: July 7, 2025  
**Phiên bản tài liệu**: 1.0.0  
**Phiên bản Twenty**: Latest