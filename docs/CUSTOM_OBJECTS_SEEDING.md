# Hướng dẫn Seed Custom Objects và Data

## Mụ<PERSON> lục
- [Tổng quan](#tổng-quan)
- [Cấu trúc Custom Objects](#cấu-trúc-custom-objects)
- [Thiết lập Workspace](#thiết-lập-workspace)
- [Seed Custom Objects](#seed-custom-objects)
- [Seed Sample Data](#seed-sample-data)
- [<PERSON><PERSON>c minh kết quả](#xác-minh-kết-qu<PERSON>)
- [Tùy chỉnh Objects](#tùy-chỉnh-objects)
- [Khắc phục sự cố](#khắc-phục-sự-cố)

## Tổng quan

Twenty CRM được trang bị sẵn 13 custom objects được thiết kế để quản lý:
- **G<PERSON><PERSON><PERSON> dục**: Student, Teacher, Class, Program, Room, Enrollment, Attendance, Score Template
- **Kinh doanh**: Appointment, Call, Campaign, Order, Product

Tài liệu này hướng dẫn cách seed các objects này vào workspace của bạn.

## C<PERSON>u trúc Custom Objects

### Educational Objects

#### 1. Student (<PERSON><PERSON><PERSON> sinh)
- **Mô tả**: Quản lý thông tin học sinh
- **Fields**: Tên, tuổi, lớp, địa chỉ, liên hệ, trạng thái học tập
- **Table**: `_student`

#### 2. Teacher (Giáo viên)
- **Mô tả**: Quản lý thông tin giáo viên
- **Fields**: Tên, bộ môn, kinh nghiệm, trình độ, lương
- **Table**: `_teacher`

#### 3. Class (Lớp học)
- **Mô tả**: Quản lý thông tin lớp học
- **Fields**: Tên lớp, sĩ số, phòng học, thời gian, giáo viên
- **Table**: `_class`

#### 4. Program (Chương trình học)
- **Mô tả**: Quản lý chương trình đào tạo
- **Fields**: Tên chương trình, thời lượng, học phí, mô tả
- **Table**: `_program`

#### 5. Room (Phòng học)
- **Mô tả**: Quản lý phòng học và cơ sở vật chất
- **Fields**: Tên phòng, sức chứa, trang thiết bị, trạng thái
- **Table**: `_room`

#### 6. Enrollment (Đăng ký học)
- **Mô tả**: Quản lý việc đăng ký học của học sinh
- **Fields**: Học sinh, chương trình, ngày đăng ký, trạng thái, học phí
- **Table**: `_enrollment`

#### 7. Attendance (Chuyên cần)
- **Mô tả**: Theo dõi chuyên cần của học sinh
- **Fields**: Học sinh, lớp, ngày, trạng thái có mặt, ghi chú
- **Table**: `_attendance`

#### 8. Score Template (Mẫu chấm điểm)
- **Mô tả**: Mẫu đánh giá và chấm điểm
- **Fields**: Tên mẫu, môn học, thang điểm, trọng số
- **Table**: `_scoreTemplate`

### Business Objects

#### 9. Appointment (Lịch hẹn)
- **Mô tả**: Quản lý lịch hẹn và cuộc họp
- **Fields**: Tiêu đề, thời gian, địa điểm, người tham gia, trạng thái
- **Table**: `_appointment`

#### 10. Call (Cuộc gọi)
- **Mô tả**: Theo dõi cuộc gọi khách hàng
- **Fields**: Người gọi, thời gian, thời lượng, mục đích, kết quả
- **Table**: `_call`

#### 11. Campaign (Chiến dịch)
- **Mô tả**: Quản lý chiến dịch marketing
- **Fields**: Tên chiến dịch, ngân sách, thời gian, kênh, hiệu quả
- **Table**: `_campaign`

#### 12. Order (Đơn hàng)
- **Mô tả**: Quản lý đơn hàng và giao dịch
- **Fields**: Mã đơn, khách hàng, tổng tiền, trạng thái, ngày tạo
- **Table**: `_order`

#### 13. Product (Sản phẩm)
- **Mô tả**: Quản lý danh mục sản phẩm
- **Fields**: Tên sản phẩm, SKU, giá, mô tả, danh mục
- **Table**: `_product`

## Thiết lập Workspace

### 1. Xác định Workspace ID

Trước khi seed, bạn cần biết workspace ID hiện tại:

```bash
# Kết nối database và lấy workspace info
docker exec twenty-postgres-dev psql -U postgres -d default -c \
  "SELECT id, \"displayName\", subdomain FROM core.workspace;"
```

### 2. Cấu hình Seeding Service

Workspace ID sẽ được sử dụng trong seeding configuration. Hệ thống đã được cấu hình sẵn cho workspace hiện tại.

## Seed Custom Objects

### 1. Chạy Lệnh Seeding

```bash
# Seed tất cả custom objects
nx command-no-deps twenty-server -- workspace:seed:dev
```

### 2. Theo dõi Progress

Lệnh seeding sẽ hiển thị progress chi tiết:

```
📦 Starting to seed 13 custom objects for workspace [workspace-id]
🔄 [1/13] Creating custom object: appointment
✅ Successfully created object: appointment
🔧 Adding 9 custom fields to appointment
✅ Successfully added fields to: appointment
...
🎉 Completed seeding all custom objects and fields
```

### 3. Xử lý Objects đã tồn tại

Nếu objects đã tồn tại, hệ thống sẽ skip và tiếp tục:

```
⚠️  Object appointment already exists, skipping creation
🔧 Adding 9 custom fields to existing object: appointment
```

## Seed Sample Data

### 1. Kiểm tra Data Seeding Configuration

Sample data được định nghĩa trong:
```
packages/twenty-server/src/engine/workspace-manager/dev-seeder/data/constants/
├── appointment-data-seeds.constant.ts
├── student-data-seeds.constant.ts
├── teacher-data-seeds.constant.ts
└── ...
```

### 2. Sample Data được Seed tự động

Khi chạy `workspace:seed:dev`, sample data sẽ được tự động seed cho:
- Tất cả 13 custom objects
- Standard objects (Company, Person)
- System objects (Workspace Members, API Keys, etc.)

### 3. Dữ liệu mẫu bao gồm:

#### Students (6 records)
- Nguyễn Văn An (Lớp 10A)
- Trần Thị Bình (Lớp 10B)
- Phạm Văn Cường (Lớp 11A)
- Lê Thị Dung (Lớp 11B)
- Hoàng Văn Em (Lớp 12A)
- Đặng Thị Phượng (Lớp 12B)

#### Teachers (6 records)
- Cô Nguyễn Thị Lan (Toán)
- Thầy Trần Văn Minh (Lý)
- Cô Phạm Thị Nga (Hóa)
- Thầy Lê Văn Quang (Sinh)
- Cô Hoàng Thị Hoa (Văn)
- Thầy Đặng Văn Tuấn (Sử)

#### Classes (6 records)
- Lớp 10A, 10B (35-38 học sinh)
- Lớp 11A, 11B (32-36 học sinh)
- Lớp 12A, 12B (30-33 học sinh)

*Và dữ liệu mẫu cho tất cả objects khác...*

## Xác minh Kết quả

### 1. Kiểm tra Objects được tạo

```bash
# Liệt kê tất cả custom objects
docker exec twenty-postgres-dev psql -U postgres -d default -c \
  "\\dt workspace_[schema_name]._*"
```

Kết quả mong đợi: 13 tables với prefix `_`

### 2. Kiểm tra Sample Data

```bash
# Kiểm tra số lượng records trong một table
docker exec twenty-postgres-dev psql -U postgres -d default -c \
  "SELECT COUNT(*) FROM workspace_[schema_name]._student;"

# Xem sample data
docker exec twenty-postgres-dev psql -U postgres -d default -c \
  "SELECT * FROM workspace_[schema_name]._student LIMIT 3;"
```

### 3. Kiểm tra qua GraphQL API

```bash
# Test GraphQL query
curl -X POST http://localhost:3000/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "query": "query { students { edges { node { id firstName lastName } } } }"
  }'
```

### 4. Kiểm tra qua UI

- Truy cập http://localhost:3001
- Login vào workspace
- Kiểm tra menu sidebar để thấy các custom objects mới

## Tùy chỉnh Objects

### 1. Thêm Custom Object mới

Để thêm object mới:

1. **Tạo Object Seed**:
```typescript
// packages/twenty-server/src/engine/workspace-manager/dev-seeder/metadata/custom-objects/constants/my-object-seed.constant.ts
export const MY_CUSTOM_OBJECT_SEED: ObjectMetadataSeed = {
  nameSingular: 'myObject',
  namePlural: 'myObjects',
  labelSingular: 'My Object',
  labelPlural: 'My Objects',
  description: 'Custom object description',
  icon: 'IconStar',
};
```

2. **Tạo Field Seeds**:
```typescript
// packages/twenty-server/src/engine/workspace-manager/dev-seeder/metadata/custom-fields/constants/my-object-custom-field-seeds.constant.ts
export const MY_OBJECT_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    name: 'name',
    label: 'Name',
    type: FieldMetadataType.TEXT,
    isRequired: true,
  },
  // ... more fields
];
```

3. **Tạo Data Seeds**:
```typescript
// packages/twenty-server/src/engine/workspace-manager/dev-seeder/data/constants/my-object-data-seeds.constant.ts
export const MY_OBJECT_DATA_SEEDS: DataSeed[] = [
  {
    id: 'unique-id-1',
    name: 'Sample Object 1',
    // ... other fields
  },
  // ... more records
];
```

4. **Cập nhật Seeding Configuration**:
Thêm vào `DevSeederMetadataService` và `DevSeederDataService`

### 2. Sửa đổi Fields

Để thêm/sửa fields cho objects hiện có:

1. Cập nhật file `*-custom-field-seeds.constant.ts`
2. Chạy lại seeding command
3. System sẽ tự động handle existing fields

### 3. Cập nhật Sample Data

1. Sửa file `*-data-seeds.constant.ts`
2. Xóa data cũ nếu cần:
```bash
docker exec twenty-postgres-dev psql -U postgres -d default -c \
  "DELETE FROM workspace_[schema_name]._myobject;"
```
3. Chạy lại seeding

## Khắc phục Sự cố

### Lỗi "Object already exists"

**Triệu chứng**: Seeding dừng với error object đã tồn tại

**Giải pháp**: Hệ thống đã được cập nhật để tự động handle, chỉ cần chạy lại:
```bash
nx command-no-deps twenty-server -- workspace:seed:dev
```

### Lỗi "Options are required for enum fields"

**Triệu chứng**: Lỗi khi tạo enum fields

**Giải pháp**: Thêm options cho enum fields:
```typescript
{
  name: 'status',
  type: FieldMetadataType.SELECT,
  options: [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
  ],
}
```

### Lỗi "Field name not available"

**Triệu chứng**: Trùng tên field

**Giải pháp**: 
1. Kiểm tra existing fields
2. Đổi tên field trong seed file
3. Hoặc xóa field cũ trước khi seed

### Workspace schema không tìm thấy

**Triệu chứng**: Error về schema name

**Giải pháp**:
```bash
# Tìm schema name chính xác
docker exec twenty-postgres-dev psql -U postgres -d default -c \
  "SELECT schema_name FROM information_schema.schemata WHERE schema_name LIKE 'workspace_%';"
```

### Sample data không được seed

**Triệu chứng**: Objects tạo được nhưng không có data

**Nguyên nhân**: Enum fields chưa có options

**Giải pháp**:
1. Fix enum fields với options
2. Chạy lại seeding
3. Hoặc seed data riêng

### Database connection errors

**Giải pháp**:
```bash
# Restart containers
docker restart twenty_pg twenty_redis

# Kiểm tra connections
docker exec twenty_pg psql -U postgres -d default -c "SELECT 1;"
```

## Advanced Usage

### 1. Seed cho Workspace khác

Để seed cho workspace ID khác:

1. Cập nhật `SEED_CURRENT_WORKSPACE_ID` trong `seed-workspaces.util.ts`
2. Hoặc tạo configuration mới trong `DevSeederMetadataService`

### 2. Chỉ seed Objects cụ thể

Modify `DevSeederMetadataService` để chỉ include objects cần thiết

### 3. Production Seeding

**Cảnh báo**: Không chạy dev seeding trên production

Để seed production:
1. Tạo production-specific seeds
2. Remove sample data
3. Use environment-specific configurations

### 4. Backup trước khi Seed

```bash
# Backup database
docker exec twenty_pg pg_dump -U postgres default > backup.sql

# Restore nếu cần
docker exec -i twenty_pg psql -U postgres default < backup.sql
```

## Best Practices

1. **Luôn backup** trước khi seed
2. **Test trên development** trước khi apply production
3. **Review sample data** để đảm bảo phù hợp
4. **Monitor logs** trong quá trình seeding
5. **Verify results** sau khi hoàn thành

## Tài liệu tham khảo

- [Installation Guide](./INSTALLATION.md)
- [Troubleshooting Guide](./TROUBLESHOOTING.md)
- [GraphQL API Documentation](./API.md)
- [Custom Object Development Guide](./CUSTOM_OBJECTS_DEV.md)