{"name": "rehype-minify-whitespace", "version": "6.0.0", "description": "rehype plugin to collapse whitespace", "license": "MIT", "keywords": ["collapse", "html", "mangle", "minify", "plugin", "rehype", "rehype-plugin", "space", "unified", "white", "whitespace"], "repository": "https://github.com/rehypejs/rehype-minify/tree/main/packages/rehype-minify-whitespace", "bugs": "https://github.com/rehypejs/rehype-minify/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["index.d.ts", "index.js", "lib/"], "dependencies": {"@types/hast": "^3.0.0", "hast-util-embedded": "^3.0.0", "hast-util-is-element": "^3.0.0", "hast-util-whitespace": "^3.0.0", "unist-util-is": "^6.0.0"}, "scripts": {}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": false}