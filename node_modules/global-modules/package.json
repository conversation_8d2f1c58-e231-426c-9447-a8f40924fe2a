{"name": "global-modules", "description": "The directory used by npm for globally installed npm modules.", "version": "0.2.3", "homepage": "https://github.com/jonschlinkert/global-modules", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/global-modules", "bugs": {"url": "https://github.com/jonschlinkert/global-modules/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"global-prefix": "^0.1.4", "is-windows": "^0.2.0"}, "devDependencies": {"fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.9", "mocha": "^2.5.3"}, "keywords": ["directory", "dirname", "global", "module", "package", "path", "prefix", "resolve"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["global-prefix", "git-config-path", "npm-paths", "contains-path"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}}