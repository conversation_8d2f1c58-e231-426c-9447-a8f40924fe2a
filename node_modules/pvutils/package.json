{"author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>"}, "contributors": [{"email": "<EMAIL>", "name": "<PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}], "repository": {"type": "git", "url": "https://github.com/PeculiarVentures/pvutils.git"}, "description": "Common utilities for products from Peculiar Ventures", "engines": {"node": ">=6.0.0"}, "scripts": {"prepare": "npm run build", "test": "mocha", "lint": "eslint . --ext .ts", "lint:fix": "eslint --fix . --ext .ts", "build": "rollup -c", "coverage": "nyc npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["build", "README.md", "LICENSE"], "module": "./build/utils.es.js", "main": "./build/utils.js", "types": "./build/index.d.ts", "devDependencies": {"@types/mocha": "^9.1.0", "@types/node": "^17.0.19", "@typescript-eslint/eslint-plugin": "^5.12.1", "@typescript-eslint/parser": "^5.12.1", "eslint": "^8.9.0", "eslint-plugin-import": "^2.25.4", "mocha": "^9.2.1", "nyc": "^15.1.0", "rollup": "2.68.0", "rollup-plugin-dts": "^4.1.0", "rollup-plugin-typescript2": "^0.31.2", "ts-node": "^10.5.0", "typescript": "^4.5.5"}, "name": "pvutils", "version": "1.1.3", "license": "MIT"}