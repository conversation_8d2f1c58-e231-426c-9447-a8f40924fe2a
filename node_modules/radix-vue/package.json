{"name": "radix-vue", "type": "module", "version": "1.9.17", "description": "Vue port for Radix UI Primitives.", "author": "Radix Vue Contributors (https://github.com/unovue/radix-vue)", "license": "MIT", "homepage": "https://github.com/unovue/radix-vue", "repository": {"type": "git", "url": "git+https://github.com/unovue/radix-vue.git"}, "bugs": {"url": "https://github.com/unovue/radix-vue/issues"}, "keywords": ["ui", "vue", "accessibility", "headless", "nuxt", "primitives", "components", "radix", "unstyled"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.umd.cjs"}, "./date": {"import": {"types": "./dist/date/index.d.ts", "default": "./dist/date/index.js"}, "require": {"types": "./dist/date/index.d.ts", "default": "./dist/date/index.umd.cjs"}}, "./namespaced": {"import": {"types": "./dist/namespaced/index.d.mts", "default": "./dist/namespaced/index.mjs"}, "require": {"types": "./dist/namespaced/index.d.cts", "default": "./dist/namespaced/index.cjs"}}, "./nuxt": {"import": {"types": "./dist/nuxt/index.d.mts", "default": "./dist/nuxt/index.mjs"}, "require": {"types": "./dist/nuxt/index.d.cts", "default": "./dist/nuxt/index.cjs"}}, "./resolver": {"import": {"types": "./dist/resolver/index.d.mts", "default": "./dist/resolver/index.mjs"}, "require": {"types": "./dist/resolver/index.d.cts", "default": "./dist/resolver/index.cjs"}}}, "main": "./dist/index.umd.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "typings": "./dist/index.d.ts", "files": ["./LICENSE", "./dist"], "peerDependencies": {"vue": ">= 3.2.0"}, "dependencies": {"@floating-ui/dom": "^1.6.7", "@floating-ui/vue": "^1.1.0", "@internationalized/date": "^3.5.4", "@internationalized/number": "^3.5.3", "@tanstack/vue-virtual": "^3.8.1", "@vueuse/core": "^10.11.0", "@vueuse/shared": "^10.11.0", "aria-hidden": "^1.2.4", "defu": "^6.1.4", "fast-deep-equal": "^3.1.3", "nanoid": "^5.0.7"}, "devDependencies": {"@iconify/vue": "^4.1.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/user-event": "^14.5.2", "@testing-library/vue": "^8.1.0", "@tsconfig/node18": "^18.2.4", "@types/node": "^20.14.9", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vitest/coverage-istanbul": "^2.1.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "jsdom": "^25.0.1", "vite": "^5.4.8", "vite-plugin-dts": "^4.2.3", "vitest": "^2.1.1", "vitest-axe": "0.1.0", "vitest-canvas-mock": "^0.3.3", "vue": "3.4.31", "vue-component-type-helpers": "^2.0.24", "vue-tsc": "2.0.24"}, "scripts": {"build": "pnpm type-check && pnpm build-only", "build-only": "vite build", "watch": "vite build --watch", "type-check": "vue-tsc -p tsconfig.check.json --noEmit", "type-gen": "vue-tsc --declaration  --emitDeclarationOnly", "test": "vitest", "pub:release": "pnpm publish --access public"}}