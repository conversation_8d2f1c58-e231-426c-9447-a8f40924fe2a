<br />
<p align="center">
  <a href="https://github.com/unovue/radix-vue">
    <img src="https://www.radix-vue.com/logo.svg" alt="Logo" width="150" />
  </a>

<h1 align="center">
Radix Vue
</h1>
<p align="center">
An unofficial Vue port of Radix UI. <br>
Radix is an unstyled, customisable UI Library with built in accessibility for building top quality design systems.

<p>

<p align="center">
<a href='https://github.com/unovue/radix-vue/actions/workflows/test.yml'>
</a>
<a href="https://www.npmjs.com/package/radix-vue" target="__blank"><img src="https://img.shields.io/npm/v/radix-vue?style=flat&colorA=002438&colorB=41c399" alt="NPM version"></a>
<a href="https://www.npmjs.com/package/radix-vue" target="__blank"><img alt="NPM Downloads" src="https://img.shields.io/npm/dm/radix-vue?flat&colorA=002438&colorB=41c399"></a>
<a href="https://github.com/unovue/radix-vue" target="__blank"><img alt="GitHub stars" src="https://img.shields.io/github/stars/unovue/radix-vue?flat&colorA=002438&colorB=41c399"></a>
</p>

<p align="center">
<a href="https://chat.radix-vue.com"><b>Get involved!</b></a>
</p>
<p align="center">
 <a href="https://radix-vue.com">Documentation</a> | <a href="https://www.radix-vue.com/overview/getting-started.html">Getting Started</a> | <a href="https://www.radix-vue.com/">Examples</a> | <a href="https://www.radix-vue.com/overview/introduction.html">Why Radix Vue?</a>
</p>

![hero image](https://www.radix-vue.com/og.jpg)
 <em>design by: [icarusgkx](https://twitter.com/icarusgkx), [melkam](https://github.com/MellKam)</em>

## Installation

```bash
pnpm add radix-vue
```
```bash
npm install radix-vue
```
```bash
yarn add radix-vue
```

## Documentation

For full documentation, visit [radix-vue.com](https://radix-vue.com).

## Releases

For changelog, visit [releases](https://github.com/unovue/radix-vue/releases).

## Contributing

We would love to have your contributions! All PRs all welcomed! We need help building the core components, docs, tests, stories! Join our discord and we will get you up and running!

## Dev Setup

### Docs

1. Clone the repo
2. Run `pnpm i`
3. Run `pnpm docs:dev` to run vitepress
4. Open `http://localhost:5173`

### Package

1. Clone the repo
2. Run `pnpm i`
3. Run `pnpm story:dev` to run histoire (storybook)
4. Open `http://localhost:6006`

## Authors

- [Khairul Haaziq](https://github.com/khairulhaaziq)
- [Mujahid Anuar](https://github.com/mujahidfa)
- [Zernonia](https://github.com/zernonia)

## Credits

All credits go to these open-source works and resources

- [Radix UI](https://radix-ui.com) for doing all the hard work to make sure components are accessible
- [Floating UI](https://floating-ui.com) for creating powerful components that as the base of many Radix Vue components
- [VueUse](https://vueuse.org) for providing many useful utilities.
- [Ark UI](https://ark-ui.com) for the `<Primitive>` component
- [Radix Svelte](https://radix-svelte.com)
- [Headless UI](https://headlessui.com)
