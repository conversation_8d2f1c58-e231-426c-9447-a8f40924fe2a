{"name": "validate.io-number", "version": "1.0.3", "description": "Validates if a value is a number.", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "./node_modules/.bin/mocha", "test-cov": "./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --dir ./reports/coverage -- -R spec", "coveralls": "./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --dir ./reports/coveralls/coverage --report lcovonly -- -R spec && cat ./reports/coveralls/coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js && rm -rf ./reports/coveralls"}, "main": "./lib", "repository": {"type": "git", "url": "git://github.com/validate-io/number.git"}, "keywords": ["validate.io", "validate", "validation", "validator", "number", "numeric", "is", "type", "check", "isnumber"], "bugs": {"url": "https://github.com/validate-io/number/issues"}, "dependencies": {}, "devDependencies": {"chai": "1.x.x", "mocha": "1.x.x", "coveralls": "^2.11.1", "istanbul": "^0.3.0", "jshint": "^2.5.10", "jshint-stylish": "^1.0.0"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}]}