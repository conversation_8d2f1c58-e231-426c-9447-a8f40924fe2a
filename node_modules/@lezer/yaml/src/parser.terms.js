// This file was generated by lezer-generator. You probably shouldn't edit it.
export const
  blockEnd = 63,
  eof = 64,
  DirectiveEnd = 1,
  DocEnd = 2,
  sequenceStartMark = 3,
  sequenceContinueMark = 4,
  explicitMapStartMark = 5,
  explicitMapContinueMark = 6,
  flowMapMark = 7,
  mapStartMark = 65,
  mapContinueMark = 66,
  Literal = 8,
  QuotedLiteral = 9,
  Anchor = 10,
  Alias = 11,
  Tag = 12,
  BlockLiteralContent = 13,
  Comment = 14,
  Stream = 15,
  BOM = 16,
  Document = 17,
  BracketL = 19,
  FlowSequence = 20,
  flowSeqElement = 21,
  Key = 28,
  Colon = 29,
  BraceL = 33,
  FlowMapping = 34,
  BlockSequence = 37,
  BlockMapping = 40,
  explicitKey = 43,
  BlockLiteral = 46,
  BlockLiteralHeader = 47,
  Directive = 52,
  DirectiveName = 53,
  DirectiveContent = 54,
  directiveDocument = 55
