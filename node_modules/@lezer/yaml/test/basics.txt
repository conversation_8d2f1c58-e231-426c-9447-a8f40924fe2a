# Simple flow values

{foo: [bar, baz],
 bar: "one
two three"}

==>

Stream(Document(FlowMapping(
  Pair(Key(Literal),FlowSequence(Item(Literal),Item(Literal))),
  Pair(Key(Literal),QuotedLiteral))))

# Pairs

[one: two, ? : four, x: ]

==>

Stream(Document(FlowSequence(
  Item(FlowMapping(Pair(Key(Literal),Literal))),
  Item(FlowMapping(Pair(Literal))),
  Item(FlowMapping(Pair(Key(Literal)))))))

# Simple sequence

- one
- two
- three

==>

Stream(Document(BlockSequence(Item(Literal),Item(Literal),Item(Literal))))

# Nested sequence

- - one
  - two
- three

==>

Stream(Document(BlockSequence(Item(BlockSequence(Item(Literal),Item(Literal))),Item(Literal))))

# Simple Mapping

One: A
Two: B

==>

Stream(Document(BlockMapping(Pair(Key(Literal),Literal),Pair(Key(Literal),Literal))))

# Semicolon in atom

foo:bar

==>

Stream(Document(Literal))

# Explicitly Indented Block Literal

foo: |2
      one
  two

==>

Stream(Document(BlockMapping(
  Pair(Key(Literal),BlockLiteral(BlockLiteralHeader,BlockLiteralContent)))))
