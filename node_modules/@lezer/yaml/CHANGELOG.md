## 1.0.3 (2024-04-30)

### Bug fixes

Avoid using class property syntax since many tools still can't handle that.

## 1.0.2 (2024-01-28)

### Bug fixes

Properly tokenize block literals with explicit indentation whose first line is indented deeper.

## 1.0.1 (2024-01-24)

### Bug fixes

Fix an incremental parsing bug caused by the way indentation context was being tracked.

## 1.0.0 (2024-01-22)

### New Features

First versioned release.
