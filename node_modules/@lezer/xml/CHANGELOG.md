## 1.0.6 (2024-12-27)

### Bug fixes

Support single-quoted attribute values.

## 1.0.5 (2024-03-04)

### Bug fixes

Fix a mistake that broke highlighting for mismatched tag names.

## 1.0.4 (2023-12-28)

### Bug fixes

Mark tags, attributes, and comments as isolating for bidirectional text.

## 1.0.3 (2023-10-26)

### Bug fixes

The parser is now more precise about what is parsed as an entity.

Fix a bug that caused the end of a CDATA block, comment, or processing instruction to not be recognized when the character starting the end marker occured right before it.

## 1.0.2 (2023-07-03)

### Bug fixes

Make the package work with new TS resolution styles.

## 1.0.1 (2022-12-19)

### Bug fixes

Fix a bug where single-character comments, processing instructions, and CDATA blocks were not parsed correctly.

## 1.0.0 (2022-06-06)

### Bug fixes

Fix a bug that would cause the tokenizer to miss `]]>` and `-->` tokens when they were preceded by `]` or `-`.

### New features

First stable version.

## 0.16.0 (2022-04-20)

### Breaking changes

Move to 0.16 serialized parser format.

### New features

The parser now includes syntax highlighting information in its node types.

## 0.15.1 (2021-10-30)

### Bug fixes

Fix parsing of CDATA sections.

## 0.15.0 (2021-08-11)

### Breaking changes

The module's name changed from `lezer-xml` to `@lezer/xml`.

Upgrade to the 0.15.0 lezer interfaces.

## 0.13.4 (2021-02-17)

### Bug fixes

Fix an issue that cause improper tokenizing during some types of error recovery.

## 0.13.3 (2021-02-17)

### Bug fixes

Optimize tokenizing with a context tracker.

## 0.13.2 (2021-01-22)

### Bug fixes

Make comments, processing instructions, and cdata consist of multiple tokens to avoid freezing the parser on huge inputs with unfinished elements of those types.

## 0.13.1 (2020-12-04)

### Bug fixes

Fix versions of lezer packages depended on.

## 0.13.0 (2020-12-04)

## 0.12.0 (2020-10-23)

### Breaking changes

Adjust to changed serialized parser format.

## 0.11.1 (2020-09-26)

### Bug fixes

Fix lezer depencency versions

## 0.11.0 (2020-09-26)

### Breaking changes

Follow change in serialized parser format.

## 0.10.1 (2020-09-16)

### New features

Make sure mismatched close tags name tokens have a different name than matching ones.

## 0.10.0 (2020-08-07)

### Breaking changes

Upgrade to 0.10 parser serialization

## 0.9.0 (2020-06-08)

### Breaking changes

Upgrade to 0.9 parser serialization

### New features

Tag start/end tokens now have `NodeProp.openedBy`/`closedBy` props.

## 0.8.2 (2020-04-09)

### Bug fixes

Regenerate parser with a fix in lezer-generator so that the top node prop is properly assigned.

## 0.8.1 (2020-04-01)

### Bug fixes

Make the package load as an ES module on node

## 0.8.0 (2020-02-03)

### New features

Follow 0.8.0 release of the library.

## 0.7.0 (2020-01-20)

### Breaking changes

Use the lezer 0.7.0 parser format.

## 0.5.1 (2019-10-22)

### Bug fixes

Fix top prop missing from build output.

## 0.5.0 (2019-10-22)

### Breaking changes

Move from `lang` to `top` prop on document node.

## 0.4.0 (2019-09-10)

### Breaking changes

Adjust to 0.4.0 parse table format.

## 0.3.0 (2019-08-22)

### New features

First numbered release.
