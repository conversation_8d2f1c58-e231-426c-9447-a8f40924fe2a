@top Document { (entity | DoctypeDecl)+ }

entity {
  Text |
  EntityReference |
  CharacterReference |
  Cdata |
  Element |
  Comment |
  ProcessingInst |
  MismatchedCloseTag |
  incompleteStartCloseTag
}

Element {
  OpenTag entity* (CloseTag | MissingCloseTag) |
  SelfClosingTag
}

OpenTag[closedBy="CloseTag MissingCloseTag",isolate] {
  StartTag space* TagName space* (Attribute space*)* EndTag
}

SelfClosingTag[isolate] {
  StartTag space* TagName space* (Attribute space*)* SelfCloseEndTag
}

CloseTag[openedBy=OpenTag,isolate] {
  StartCloseTag space* TagName space* EndTag
}

MismatchedCloseTag[isolate] {
  mismatchedStartCloseTag space* TagName space* EndTag
}

Attribute {
  AttributeName space* Is space* AttributeValue
}

AttributeValue[isolate] {
  "\"" (attributeContentDouble | EntityReference | CharacterReference)* "\"" |
  "\'" (attributeContentSingle | EntityReference | CharacterReference)* "\'"
}

Comment[isolate] { "<!--" commentContent* "-->" }

ProcessingInst { "<?" piContent* "?>" }

Cdata { cdataStart cdataContent* "]]>" }

@context elementContext from "./tokens.js"

@external tokens startTag from "./tokens.js" {
  StartTag[closedBy="SelfCloseEndTag EndTag"]
  StartCloseTag
  MissingCloseTag
  mismatchedStartCloseTag[@name=StartCloseTag]
  incompleteStartCloseTag[@name=StartCloseTag]
}

@external tokens commentContent from "./tokens.js" { commentContent }
@external tokens piContent from "./tokens.js" { piContent }
@external tokens cdataContent from "./tokens.js" { cdataContent }

@tokens {
  EndTag[openedBy="StartTag StartCloseTag"] { ">" }

  SelfCloseEndTag[openedBy=StartTag] { "/>" }

  nameStart {
    ":" | @asciiLetter | "_" |
    $[\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D] |
    $[\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u{10000}-\u{EFFFF}]
  }

  nameChar {
    nameStart | "-" | "." | @digit | $[\u00B7\u0300-\u036F\u203F-\u2040]
  }

  identifier { nameStart nameChar* }

  TagName { identifier }

  AttributeName { identifier }

  attributeContentDouble { !["&]+ }

  attributeContentSingle { !['&]+ }

  Is { "=" }

   // See https://www.w3.org/TR/2006/REC-xml11-20060816/#sec-references
   EntityReference { "&" identifier ";" }

  CharacterReference { "&#" ("x" $[0-9a-fA-F]+ | $[0-9]+) ";" }

  Text { ![<&]+ }

  DoctypeDecl { "<!" ("doctype" | "DOCTYPE") ![>]* ">" }

  cdataStart { "<![" ("cdata" | "CDATA") "[" }

  space { (" " | "\t" | "\r" | "\n")+ }
}

@external propSource xmlHighlighting from "./highlight"

@detectDelim
