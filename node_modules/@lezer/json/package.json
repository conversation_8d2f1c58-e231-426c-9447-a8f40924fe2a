{"name": "@lezer/json", "version": "1.0.3", "description": "lezer-based JSON grammar", "main": "dist/index.cjs", "type": "module", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "module": "dist/index.js", "types": "dist/index.d.ts", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@lezer/generator": "^1.0.0", "mocha": "^10.2.0", "rollup": "^2.52.2", "@rollup/plugin-node-resolve": "^9.0.0"}, "dependencies": {"@lezer/common": "^1.2.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0"}, "repository": {"type": "git", "url": "https://github.com/lezer-parser/json.git"}, "scripts": {"build": "lezer-generator src/json.grammar -o src/parser && rollup -c", "build-debug": "lezer-generator src/json.grammar --names -o src/parser && rollup -c", "prepare": "npm run build", "test": "npm run build && mocha test/test-json.js"}}