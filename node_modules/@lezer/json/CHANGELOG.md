## 1.0.3 (2024-12-29)

### Bug fixes

Emit tokens for colons and commas.

## 1.0.2 (2023-12-28)

### Bug fixes

Tag strings as isolating for the purpose of bidirectional text.

## 1.0.1 (2023-07-03)

### Bug fixes

Make the package work with new TS resolution styles.

## 1.0.0 (2022-06-06)

### New features

First stable version.

## 0.16.0 (2022-04-20)

### Breaking changes

Move to 0.16 serialized parser format.

### New features

The parser now includes syntax highlighting information in its node types.

## 0.15.0 (2021-08-11)

### Breaking changes

The module's name changed from `lezer-json` to `@lezer/json`.

Upgrade to the 0.15.0 lezer interfaces.

## 0.13.2 (2021-06-14)

### Bug fixes

Include nodes for brackets and braces in the tree.

## 0.13.1 (2020-12-04)

### Bug fixes

Fix versions of lezer packages depended on.

## 0.13.0 (2020-12-04)

## 0.12.0 (2020-10-23)

### Breaking changes

Adjust to changed serialized parser format.

## 0.11.1 (2020-09-26)

### Bug fixes

Fix lezer depencency versions

## 0.11.0 (2020-09-26)

### Breaking changes

Follow change in serialized parser format.

## 0.10.0 (2020-09-23)

### Breaking changes

Upgrade to current parser package format.
