# Simple Integer

42

==>

JsonText(Number)

# Zero By Itself Is Ok

0

==>

JsonText(Number)

# Leading Zeros Aren't Ok

[0123]

==>

JsonText(Array(Number, ⚠(Number)))

# Optional Minus Sign

-53

==>

JsonText(Number)

# Decimal Digits

123.4

==>

JsonText(Number)

# Must Have Digits After Decimal

123.

==>

JsonText(Number, ⚠)

# Exponent: Lowercase e

1e5

==>

JsonText(Number)

# Exponent: Uppercase E

1E5

==>

JsonText(Number)

# Exponent: Optional Plus Sign

1e+5

==>

JsonText(Number)

# Exponent: Optional Minus Sign

1E-5

==>

JsonText(Number)

# Exponent Without Digit Is Not Ok

42e

==>

JsonText(Number, ⚠)
