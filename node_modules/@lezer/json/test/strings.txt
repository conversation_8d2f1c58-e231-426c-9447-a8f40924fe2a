# Empty String

""

==>

JsonText(String)

# Non-empty String

"This is a boring old string"

==>

JsonText(String)

# All The Valid One-Character Escapes

"\"\\\/\b\f\n\rt\t"

==>

JsonText(String)

# Unicode Escape

"\u005C"

==>

JsonText(String)

# XXX

{
  "description": "Some description",
  "keywords": [],
  "author": "",
  "license": "ISC",
  "scripts": {
    "test": "echo \"no test specified\" && exit 1"
  },
  "dependencies": {
    "@lezer/common": "^1.2.1"
  }
}

==>

JsonText
