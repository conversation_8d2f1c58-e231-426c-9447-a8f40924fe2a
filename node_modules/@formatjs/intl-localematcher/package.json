{"name": "@formatjs/intl-localematcher", "version": "0.6.0", "description": "Intl.LocaleMatcher ponyfill", "keywords": ["intl", "locale", "formatjs", "react-intl", "i18n", "ecma402", "tc39"], "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/formatjs/formatjs#readme", "license": "MIT", "main": "index.js", "module": "lib/index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/formatjs/formatjs.git"}, "bugs": {"url": "https://github.com/formatjs/formatjs/issues"}, "dependencies": {"tslib": "2"}}