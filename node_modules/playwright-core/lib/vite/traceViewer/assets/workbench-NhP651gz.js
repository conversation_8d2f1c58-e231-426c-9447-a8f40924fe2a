function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["./codeMirrorModule-Bhx_foC8.js","../codeMirrorModule.ez37Vkbh.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
var cm=Object.defineProperty;var dm=(e,t,n)=>t in e?cm(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var B=(e,t,n)=>(dm(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();var I1=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function fm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var nd={exports:{}},Hs={},rd={exports:{}},z={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var di=Symbol.for("react.element"),hm=Symbol.for("react.portal"),pm=Symbol.for("react.fragment"),mm=Symbol.for("react.strict_mode"),gm=Symbol.for("react.profiler"),vm=Symbol.for("react.provider"),ym=Symbol.for("react.context"),wm=Symbol.for("react.forward_ref"),xm=Symbol.for("react.suspense"),Sm=Symbol.for("react.memo"),_m=Symbol.for("react.lazy"),uu=Symbol.iterator;function Em(e){return e===null||typeof e!="object"?null:(e=uu&&e[uu]||e["@@iterator"],typeof e=="function"?e:null)}var id={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},sd=Object.assign,od={};function or(e,t,n){this.props=e,this.context=t,this.refs=od,this.updater=n||id}or.prototype.isReactComponent={};or.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};or.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function ld(){}ld.prototype=or.prototype;function Gl(e,t,n){this.props=e,this.context=t,this.refs=od,this.updater=n||id}var Yl=Gl.prototype=new ld;Yl.constructor=Gl;sd(Yl,or.prototype);Yl.isPureReactComponent=!0;var cu=Array.isArray,ad=Object.prototype.hasOwnProperty,Jl={current:null},ud={key:!0,ref:!0,__self:!0,__source:!0};function cd(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)ad.call(t,r)&&!ud.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:di,type:e,key:s,ref:o,props:i,_owner:Jl.current}}function Tm(e,t){return{$$typeof:di,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Zl(e){return typeof e=="object"&&e!==null&&e.$$typeof===di}function km(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var du=/\/+/g;function fo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?km(""+e.key):t.toString(36)}function Vi(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case di:case hm:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+fo(o,0):r,cu(i)?(n="",e!=null&&(n=e.replace(du,"$&/")+"/"),Vi(i,t,n,"",function(u){return u})):i!=null&&(Zl(i)&&(i=Tm(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(du,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",cu(e))for(var l=0;l<e.length;l++){s=e[l];var a=r+fo(s,l);o+=Vi(s,t,n,a,i)}else if(a=Em(e),typeof a=="function")for(e=a.call(e),l=0;!(s=e.next()).done;)s=s.value,a=r+fo(s,l++),o+=Vi(s,t,n,a,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Si(e,t,n){if(e==null)return e;var r=[],i=0;return Vi(e,r,"","",function(s){return t.call(n,s,i++)}),r}function Nm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ce={current:null},Wi={transition:null},Cm={ReactCurrentDispatcher:Ce,ReactCurrentBatchConfig:Wi,ReactCurrentOwner:Jl};z.Children={map:Si,forEach:function(e,t,n){Si(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Si(e,function(){t++}),t},toArray:function(e){return Si(e,function(t){return t})||[]},only:function(e){if(!Zl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};z.Component=or;z.Fragment=pm;z.Profiler=gm;z.PureComponent=Gl;z.StrictMode=mm;z.Suspense=xm;z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Cm;z.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=sd({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=Jl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)ad.call(t,a)&&!ud.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:di,type:e.type,key:i,ref:s,props:r,_owner:o}};z.createContext=function(e){return e={$$typeof:ym,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:vm,_context:e},e.Consumer=e};z.createElement=cd;z.createFactory=function(e){var t=cd.bind(null,e);return t.type=e,t};z.createRef=function(){return{current:null}};z.forwardRef=function(e){return{$$typeof:wm,render:e}};z.isValidElement=Zl;z.lazy=function(e){return{$$typeof:_m,_payload:{_status:-1,_result:e},_init:Nm}};z.memo=function(e,t){return{$$typeof:Sm,type:e,compare:t===void 0?null:t}};z.startTransition=function(e){var t=Wi.transition;Wi.transition={};try{e()}finally{Wi.transition=t}};z.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};z.useCallback=function(e,t){return Ce.current.useCallback(e,t)};z.useContext=function(e){return Ce.current.useContext(e)};z.useDebugValue=function(){};z.useDeferredValue=function(e){return Ce.current.useDeferredValue(e)};z.useEffect=function(e,t){return Ce.current.useEffect(e,t)};z.useId=function(){return Ce.current.useId()};z.useImperativeHandle=function(e,t,n){return Ce.current.useImperativeHandle(e,t,n)};z.useInsertionEffect=function(e,t){return Ce.current.useInsertionEffect(e,t)};z.useLayoutEffect=function(e,t){return Ce.current.useLayoutEffect(e,t)};z.useMemo=function(e,t){return Ce.current.useMemo(e,t)};z.useReducer=function(e,t,n){return Ce.current.useReducer(e,t,n)};z.useRef=function(e){return Ce.current.useRef(e)};z.useState=function(e){return Ce.current.useState(e)};z.useSyncExternalStore=function(e,t,n){return Ce.current.useSyncExternalStore(e,t,n)};z.useTransition=function(){return Ce.current.useTransition()};z.version="18.2.0";rd.exports=z;var b=rd.exports;const mt=fm(b);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bm=b,Lm=Symbol.for("react.element"),Am=Symbol.for("react.fragment"),jm=Object.prototype.hasOwnProperty,Mm=bm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Im={key:!0,ref:!0,__self:!0,__source:!0};function dd(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)jm.call(t,r)&&!Im.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Lm,type:e,key:s,ref:o,props:i,_owner:Mm.current}}Hs.Fragment=Am;Hs.jsx=dd;Hs.jsxs=dd;nd.exports=Hs;var d=nd.exports;function Pm(e,t,n,r){const[i,s]=mt.useState(n);return mt.useEffect(()=>{let o=!1;return r!==void 0&&s(r),e().then(l=>{o||s(l)}),()=>{o=!0}},t),i}function _n(){const e=mt.useRef(null),[t,n]=mt.useState(new DOMRect(0,0,10,10));return mt.useLayoutEffect(()=>{const r=e.current;if(!r)return;const i=new ResizeObserver(s=>{const o=s[s.length-1];o&&o.contentRect&&n(o.contentRect)});return i.observe(r),()=>i.disconnect()},[e]),[t,e]}function Ye(e){if(e<0||!isFinite(e))return"-";if(e===0)return"0";if(e<1e3)return e.toFixed(0)+"ms";const t=e/1e3;if(t<60)return t.toFixed(1)+"s";const n=t/60;if(n<60)return n.toFixed(1)+"m";const r=n/60;return r<24?r.toFixed(1)+"h":(r/24).toFixed(1)+"d"}function Rm(e){if(e<0||!isFinite(e))return"-";if(e===0)return"0";if(e<1e3)return e.toFixed(0);const t=e/1024;if(t<1e3)return t.toFixed(1)+"K";const n=t/1024;return n<1e3?n.toFixed(1)+"M":(n/1024).toFixed(1)+"G"}function fd(e,t,n,r,i){let s=r||0,o=i!==void 0?i:e.length;for(;s<o;){const l=s+o>>1;n(t,e[l])>=0?s=l+1:o=l}return o}function $m(e){const t=document.createElement("textarea");t.style.position="absolute",t.style.zIndex="-1000",t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),t.remove()}function Rr(e,t,n){e&&(t=Wr.getObject(e,t));const[r,i]=mt.useState(t),s=mt.useCallback(l=>{e&&Wr.setObject(e,l),i(l)},[e,i]);return[r,s,[r,s,n||e||""]]}class Om{getString(t,n){return localStorage[t]||n}setString(t,n){localStorage[t]=n,window.saveSettings&&window.saveSettings()}getObject(t,n){if(!localStorage[t])return n;try{return JSON.parse(localStorage[t])}catch{return n}}setObject(t,n){localStorage[t]=JSON.stringify(n),window.saveSettings&&window.saveSettings()}}const Wr=new Om,fu="\\u0000-\\u0020\\u007f-\\u009f",hd=new RegExp("(?:[a-zA-Z][a-zA-Z0-9+.-]{2,}:\\/\\/|www\\.)[^\\s"+fu+'"]{2,}[^\\s'+fu+`"')}\\],:;.!?]`,"ug");function P1(){if(document.playwrightThemeInitialized)return;document.playwrightThemeInitialized=!0,document.defaultView.addEventListener("focus",n=>{n.target.document.nodeType===Node.DOCUMENT_NODE&&document.body.classList.remove("inactive")},!1),document.defaultView.addEventListener("blur",n=>{document.body.classList.add("inactive")},!1);const e=Wr.getString("theme","light-mode"),t=window.matchMedia("(prefers-color-scheme: dark)");(e==="dark-mode"||t.matches)&&document.body.classList.add("dark-mode")}const ea=new Set;function Dm(){const e=Wr.getString("theme","light-mode");let t;e==="dark-mode"?t="light-mode":t="dark-mode",e&&document.body.classList.remove(e),document.body.classList.add(t),Wr.setString("theme",t);for(const n of ea)n(t)}function R1(e){ea.add(e)}function $1(e){ea.delete(e)}function hu(){return document.body.classList.contains("dark-mode")?"dark-mode":"light-mode"}function O1(){const[e,t]=mt.useState(hu()==="dark-mode");return[e,n=>{hu()==="dark-mode"!==n&&Dm(),t(n)},"Dark mode"]}var pd={exports:{}},Be={},md={exports:{}},gd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(I,R){var D=I.length;I.push(R);e:for(;0<D;){var K=D-1>>>1,re=I[K];if(0<i(re,R))I[K]=R,I[D]=re,D=K;else break e}}function n(I){return I.length===0?null:I[0]}function r(I){if(I.length===0)return null;var R=I[0],D=I.pop();if(D!==R){I[0]=D;e:for(var K=0,re=I.length,sn=re>>>1;K<sn;){var yt=2*(K+1)-1,tt=I[yt],wt=yt+1,on=I[wt];if(0>i(tt,D))wt<re&&0>i(on,tt)?(I[K]=on,I[wt]=D,K=wt):(I[K]=tt,I[yt]=D,K=yt);else if(wt<re&&0>i(on,D))I[K]=on,I[wt]=D,K=wt;else break e}}return R}function i(I,R){var D=I.sortIndex-R.sortIndex;return D!==0?D:I.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,l=o.now();e.unstable_now=function(){return o.now()-l}}var a=[],u=[],c=1,h=null,f=3,g=!1,w=!1,y=!1,x=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(I){for(var R=n(u);R!==null;){if(R.callback===null)r(u);else if(R.startTime<=I)r(u),R.sortIndex=R.expirationTime,t(a,R);else break;R=n(u)}}function S(I){if(y=!1,v(I),!w)if(n(a)!==null)w=!0,Oe(C);else{var R=n(u);R!==null&&me(S,R.startTime-I)}}function C(I,R){w=!1,y&&(y=!1,p(j),j=-1),g=!0;var D=f;try{for(v(R),h=n(a);h!==null&&(!(h.expirationTime>R)||I&&!L());){var K=h.callback;if(typeof K=="function"){h.callback=null,f=h.priorityLevel;var re=K(h.expirationTime<=R);R=e.unstable_now(),typeof re=="function"?h.callback=re:h===n(a)&&r(a),v(R)}else r(a);h=n(a)}if(h!==null)var sn=!0;else{var yt=n(u);yt!==null&&me(S,yt.startTime-R),sn=!1}return sn}finally{h=null,f=D,g=!1}}var E=!1,N=null,j=-1,_=5,T=-1;function L(){return!(e.unstable_now()-T<_)}function k(){if(N!==null){var I=e.unstable_now();T=I;var R=!0;try{R=N(!0,I)}finally{R?M():(E=!1,N=null)}}else E=!1}var M;if(typeof m=="function")M=function(){m(k)};else if(typeof MessageChannel<"u"){var $=new MessageChannel,Q=$.port2;$.port1.onmessage=k,M=function(){Q.postMessage(null)}}else M=function(){x(k,0)};function Oe(I){N=I,E||(E=!0,M())}function me(I,R){j=x(function(){I(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(I){I.callback=null},e.unstable_continueExecution=function(){w||g||(w=!0,Oe(C))},e.unstable_forceFrameRate=function(I){0>I||125<I?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<I?Math.floor(1e3/I):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(I){switch(f){case 1:case 2:case 3:var R=3;break;default:R=f}var D=f;f=R;try{return I()}finally{f=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(I,R){switch(I){case 1:case 2:case 3:case 4:case 5:break;default:I=3}var D=f;f=I;try{return R()}finally{f=D}},e.unstable_scheduleCallback=function(I,R,D){var K=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?K+D:K):D=K,I){case 1:var re=-1;break;case 2:re=250;break;case 5:re=**********;break;case 4:re=1e4;break;default:re=5e3}return re=D+re,I={id:c++,callback:R,priorityLevel:I,startTime:D,expirationTime:re,sortIndex:-1},D>K?(I.sortIndex=D,t(u,I),n(a)===null&&I===n(u)&&(y?(p(j),j=-1):y=!0,me(S,D-K))):(I.sortIndex=re,t(a,I),w||g||(w=!0,Oe(C))),I},e.unstable_shouldYield=L,e.unstable_wrapCallback=function(I){var R=f;return function(){var D=f;f=R;try{return I.apply(this,arguments)}finally{f=D}}}})(gd);md.exports=gd;var zm=md.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vd=b,He=zm;function A(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var yd=new Set,qr={};function En(e,t){Zn(e,t),Zn(e+"Capture",t)}function Zn(e,t){for(qr[e]=t,e=0;e<t.length;e++)yd.add(t[e])}var Lt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Xo=Object.prototype.hasOwnProperty,Fm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,pu={},mu={};function Hm(e){return Xo.call(mu,e)?!0:Xo.call(pu,e)?!1:Fm.test(e)?mu[e]=!0:(pu[e]=!0,!1)}function Um(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Bm(e,t,n,r){if(t===null||typeof t>"u"||Um(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function be(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var pe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){pe[e]=new be(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];pe[t]=new be(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){pe[e]=new be(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){pe[e]=new be(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){pe[e]=new be(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){pe[e]=new be(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){pe[e]=new be(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){pe[e]=new be(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){pe[e]=new be(e,5,!1,e.toLowerCase(),null,!1,!1)});var ta=/[\-:]([a-z])/g;function na(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ta,na);pe[t]=new be(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ta,na);pe[t]=new be(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ta,na);pe[t]=new be(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){pe[e]=new be(e,1,!1,e.toLowerCase(),null,!1,!1)});pe.xlinkHref=new be("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){pe[e]=new be(e,1,!1,e.toLowerCase(),null,!0,!0)});function ra(e,t,n,r){var i=pe.hasOwnProperty(t)?pe[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Bm(t,n,i,r)&&(n=null),r||i===null?Hm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var It=vd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,_i=Symbol.for("react.element"),In=Symbol.for("react.portal"),Pn=Symbol.for("react.fragment"),ia=Symbol.for("react.strict_mode"),Qo=Symbol.for("react.profiler"),wd=Symbol.for("react.provider"),xd=Symbol.for("react.context"),sa=Symbol.for("react.forward_ref"),Ko=Symbol.for("react.suspense"),Go=Symbol.for("react.suspense_list"),oa=Symbol.for("react.memo"),Rt=Symbol.for("react.lazy"),Sd=Symbol.for("react.offscreen"),gu=Symbol.iterator;function mr(e){return e===null||typeof e!="object"?null:(e=gu&&e[gu]||e["@@iterator"],typeof e=="function"?e:null)}var J=Object.assign,ho;function br(e){if(ho===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ho=t&&t[1]||""}return`
`+ho+e}var po=!1;function mo(e,t){if(!e||po)return"";po=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,l=s.length-1;1<=o&&0<=l&&i[o]!==s[l];)l--;for(;1<=o&&0<=l;o--,l--)if(i[o]!==s[l]){if(o!==1||l!==1)do if(o--,l--,0>l||i[o]!==s[l]){var a=`
`+i[o].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=o&&0<=l);break}}}finally{po=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?br(e):""}function Vm(e){switch(e.tag){case 5:return br(e.type);case 16:return br("Lazy");case 13:return br("Suspense");case 19:return br("SuspenseList");case 0:case 2:case 15:return e=mo(e.type,!1),e;case 11:return e=mo(e.type.render,!1),e;case 1:return e=mo(e.type,!0),e;default:return""}}function Yo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Pn:return"Fragment";case In:return"Portal";case Qo:return"Profiler";case ia:return"StrictMode";case Ko:return"Suspense";case Go:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case xd:return(e.displayName||"Context")+".Consumer";case wd:return(e._context.displayName||"Context")+".Provider";case sa:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case oa:return t=e.displayName||null,t!==null?t:Yo(e.type)||"Memo";case Rt:t=e._payload,e=e._init;try{return Yo(e(t))}catch{}}return null}function Wm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Yo(t);case 8:return t===ia?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Jt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function _d(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function qm(e){var t=_d(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ei(e){e._valueTracker||(e._valueTracker=qm(e))}function Ed(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=_d(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ds(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Jo(e,t){var n=t.checked;return J({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function vu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Jt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Td(e,t){t=t.checked,t!=null&&ra(e,"checked",t,!1)}function Zo(e,t){Td(e,t);var n=Jt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?el(e,t.type,n):t.hasOwnProperty("defaultValue")&&el(e,t.type,Jt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function yu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function el(e,t,n){(t!=="number"||ds(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Lr=Array.isArray;function Xn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Jt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function tl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(A(91));return J({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function wu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(A(92));if(Lr(n)){if(1<n.length)throw Error(A(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Jt(n)}}function kd(e,t){var n=Jt(t.value),r=Jt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function xu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Nd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function nl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Nd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ti,Cd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ti=Ti||document.createElement("div"),Ti.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ti.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Xr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var $r={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Xm=["Webkit","ms","Moz","O"];Object.keys($r).forEach(function(e){Xm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),$r[t]=$r[e]})});function bd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||$r.hasOwnProperty(e)&&$r[e]?(""+t).trim():t+"px"}function Ld(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=bd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Qm=J({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function rl(e,t){if(t){if(Qm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(A(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(A(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(A(61))}if(t.style!=null&&typeof t.style!="object")throw Error(A(62))}}function il(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var sl=null;function la(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ol=null,Qn=null,Kn=null;function Su(e){if(e=pi(e)){if(typeof ol!="function")throw Error(A(280));var t=e.stateNode;t&&(t=qs(t),ol(e.stateNode,e.type,t))}}function Ad(e){Qn?Kn?Kn.push(e):Kn=[e]:Qn=e}function jd(){if(Qn){var e=Qn,t=Kn;if(Kn=Qn=null,Su(e),t)for(e=0;e<t.length;e++)Su(t[e])}}function Md(e,t){return e(t)}function Id(){}var go=!1;function Pd(e,t,n){if(go)return e(t,n);go=!0;try{return Md(e,t,n)}finally{go=!1,(Qn!==null||Kn!==null)&&(Id(),jd())}}function Qr(e,t){var n=e.stateNode;if(n===null)return null;var r=qs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(A(231,t,typeof n));return n}var ll=!1;if(Lt)try{var gr={};Object.defineProperty(gr,"passive",{get:function(){ll=!0}}),window.addEventListener("test",gr,gr),window.removeEventListener("test",gr,gr)}catch{ll=!1}function Km(e,t,n,r,i,s,o,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Or=!1,fs=null,hs=!1,al=null,Gm={onError:function(e){Or=!0,fs=e}};function Ym(e,t,n,r,i,s,o,l,a){Or=!1,fs=null,Km.apply(Gm,arguments)}function Jm(e,t,n,r,i,s,o,l,a){if(Ym.apply(this,arguments),Or){if(Or){var u=fs;Or=!1,fs=null}else throw Error(A(198));hs||(hs=!0,al=u)}}function Tn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Rd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function _u(e){if(Tn(e)!==e)throw Error(A(188))}function Zm(e){var t=e.alternate;if(!t){if(t=Tn(e),t===null)throw Error(A(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return _u(i),e;if(s===r)return _u(i),t;s=s.sibling}throw Error(A(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,l=i.child;l;){if(l===n){o=!0,n=i,r=s;break}if(l===r){o=!0,r=i,n=s;break}l=l.sibling}if(!o){for(l=s.child;l;){if(l===n){o=!0,n=s,r=i;break}if(l===r){o=!0,r=s,n=i;break}l=l.sibling}if(!o)throw Error(A(189))}}if(n.alternate!==r)throw Error(A(190))}if(n.tag!==3)throw Error(A(188));return n.stateNode.current===n?e:t}function $d(e){return e=Zm(e),e!==null?Od(e):null}function Od(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Od(e);if(t!==null)return t;e=e.sibling}return null}var Dd=He.unstable_scheduleCallback,Eu=He.unstable_cancelCallback,eg=He.unstable_shouldYield,tg=He.unstable_requestPaint,te=He.unstable_now,ng=He.unstable_getCurrentPriorityLevel,aa=He.unstable_ImmediatePriority,zd=He.unstable_UserBlockingPriority,ps=He.unstable_NormalPriority,rg=He.unstable_LowPriority,Fd=He.unstable_IdlePriority,Us=null,gt=null;function ig(e){if(gt&&typeof gt.onCommitFiberRoot=="function")try{gt.onCommitFiberRoot(Us,e,void 0,(e.current.flags&128)===128)}catch{}}var ot=Math.clz32?Math.clz32:lg,sg=Math.log,og=Math.LN2;function lg(e){return e>>>=0,e===0?32:31-(sg(e)/og|0)|0}var ki=64,Ni=4194304;function Ar(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ms(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var l=o&~i;l!==0?r=Ar(l):(s&=o,s!==0&&(r=Ar(s)))}else o=n&~i,o!==0?r=Ar(o):s!==0&&(r=Ar(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-ot(t),i=1<<n,r|=e[n],t&=~i;return r}function ag(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ug(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-ot(s),l=1<<o,a=i[o];a===-1?(!(l&n)||l&r)&&(i[o]=ag(l,t)):a<=t&&(e.expiredLanes|=l),s&=~l}}function ul(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Hd(){var e=ki;return ki<<=1,!(ki&4194240)&&(ki=64),e}function vo(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function fi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ot(t),e[t]=n}function cg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-ot(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function ua(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var H=0;function Ud(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Bd,ca,Vd,Wd,qd,cl=!1,Ci=[],Bt=null,Vt=null,Wt=null,Kr=new Map,Gr=new Map,zt=[],dg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Tu(e,t){switch(e){case"focusin":case"focusout":Bt=null;break;case"dragenter":case"dragleave":Vt=null;break;case"mouseover":case"mouseout":Wt=null;break;case"pointerover":case"pointerout":Kr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Gr.delete(t.pointerId)}}function vr(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=pi(t),t!==null&&ca(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function fg(e,t,n,r,i){switch(t){case"focusin":return Bt=vr(Bt,e,t,n,r,i),!0;case"dragenter":return Vt=vr(Vt,e,t,n,r,i),!0;case"mouseover":return Wt=vr(Wt,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return Kr.set(s,vr(Kr.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Gr.set(s,vr(Gr.get(s)||null,e,t,n,r,i)),!0}return!1}function Xd(e){var t=cn(e.target);if(t!==null){var n=Tn(t);if(n!==null){if(t=n.tag,t===13){if(t=Rd(n),t!==null){e.blockedOn=t,qd(e.priority,function(){Vd(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function qi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=dl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);sl=r,n.target.dispatchEvent(r),sl=null}else return t=pi(n),t!==null&&ca(t),e.blockedOn=n,!1;t.shift()}return!0}function ku(e,t,n){qi(e)&&n.delete(t)}function hg(){cl=!1,Bt!==null&&qi(Bt)&&(Bt=null),Vt!==null&&qi(Vt)&&(Vt=null),Wt!==null&&qi(Wt)&&(Wt=null),Kr.forEach(ku),Gr.forEach(ku)}function yr(e,t){e.blockedOn===t&&(e.blockedOn=null,cl||(cl=!0,He.unstable_scheduleCallback(He.unstable_NormalPriority,hg)))}function Yr(e){function t(i){return yr(i,e)}if(0<Ci.length){yr(Ci[0],e);for(var n=1;n<Ci.length;n++){var r=Ci[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Bt!==null&&yr(Bt,e),Vt!==null&&yr(Vt,e),Wt!==null&&yr(Wt,e),Kr.forEach(t),Gr.forEach(t),n=0;n<zt.length;n++)r=zt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<zt.length&&(n=zt[0],n.blockedOn===null);)Xd(n),n.blockedOn===null&&zt.shift()}var Gn=It.ReactCurrentBatchConfig,gs=!0;function pg(e,t,n,r){var i=H,s=Gn.transition;Gn.transition=null;try{H=1,da(e,t,n,r)}finally{H=i,Gn.transition=s}}function mg(e,t,n,r){var i=H,s=Gn.transition;Gn.transition=null;try{H=4,da(e,t,n,r)}finally{H=i,Gn.transition=s}}function da(e,t,n,r){if(gs){var i=dl(e,t,n,r);if(i===null)Co(e,t,r,vs,n),Tu(e,r);else if(fg(i,e,t,n,r))r.stopPropagation();else if(Tu(e,r),t&4&&-1<dg.indexOf(e)){for(;i!==null;){var s=pi(i);if(s!==null&&Bd(s),s=dl(e,t,n,r),s===null&&Co(e,t,r,vs,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else Co(e,t,r,null,n)}}var vs=null;function dl(e,t,n,r){if(vs=null,e=la(r),e=cn(e),e!==null)if(t=Tn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Rd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return vs=e,null}function Qd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(ng()){case aa:return 1;case zd:return 4;case ps:case rg:return 16;case Fd:return 536870912;default:return 16}default:return 16}}var Ht=null,fa=null,Xi=null;function Kd(){if(Xi)return Xi;var e,t=fa,n=t.length,r,i="value"in Ht?Ht.value:Ht.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return Xi=i.slice(e,1<r?1-r:void 0)}function Qi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function bi(){return!0}function Nu(){return!1}function Ve(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?bi:Nu,this.isPropagationStopped=Nu,this}return J(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=bi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=bi)},persist:function(){},isPersistent:bi}),t}var lr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ha=Ve(lr),hi=J({},lr,{view:0,detail:0}),gg=Ve(hi),yo,wo,wr,Bs=J({},hi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pa,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==wr&&(wr&&e.type==="mousemove"?(yo=e.screenX-wr.screenX,wo=e.screenY-wr.screenY):wo=yo=0,wr=e),yo)},movementY:function(e){return"movementY"in e?e.movementY:wo}}),Cu=Ve(Bs),vg=J({},Bs,{dataTransfer:0}),yg=Ve(vg),wg=J({},hi,{relatedTarget:0}),xo=Ve(wg),xg=J({},lr,{animationName:0,elapsedTime:0,pseudoElement:0}),Sg=Ve(xg),_g=J({},lr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Eg=Ve(_g),Tg=J({},lr,{data:0}),bu=Ve(Tg),kg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ng={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Cg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function bg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Cg[e])?!!t[e]:!1}function pa(){return bg}var Lg=J({},hi,{key:function(e){if(e.key){var t=kg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Qi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Ng[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pa,charCode:function(e){return e.type==="keypress"?Qi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Qi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Ag=Ve(Lg),jg=J({},Bs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Lu=Ve(jg),Mg=J({},hi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pa}),Ig=Ve(Mg),Pg=J({},lr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Rg=Ve(Pg),$g=J({},Bs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Og=Ve($g),Dg=[9,13,27,32],ma=Lt&&"CompositionEvent"in window,Dr=null;Lt&&"documentMode"in document&&(Dr=document.documentMode);var zg=Lt&&"TextEvent"in window&&!Dr,Gd=Lt&&(!ma||Dr&&8<Dr&&11>=Dr),Au=" ",ju=!1;function Yd(e,t){switch(e){case"keyup":return Dg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Jd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Rn=!1;function Fg(e,t){switch(e){case"compositionend":return Jd(t);case"keypress":return t.which!==32?null:(ju=!0,Au);case"textInput":return e=t.data,e===Au&&ju?null:e;default:return null}}function Hg(e,t){if(Rn)return e==="compositionend"||!ma&&Yd(e,t)?(e=Kd(),Xi=fa=Ht=null,Rn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Gd&&t.locale!=="ko"?null:t.data;default:return null}}var Ug={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Mu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ug[e.type]:t==="textarea"}function Zd(e,t,n,r){Ad(r),t=ys(t,"onChange"),0<t.length&&(n=new ha("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var zr=null,Jr=null;function Bg(e){df(e,0)}function Vs(e){var t=Dn(e);if(Ed(t))return e}function Vg(e,t){if(e==="change")return t}var ef=!1;if(Lt){var So;if(Lt){var _o="oninput"in document;if(!_o){var Iu=document.createElement("div");Iu.setAttribute("oninput","return;"),_o=typeof Iu.oninput=="function"}So=_o}else So=!1;ef=So&&(!document.documentMode||9<document.documentMode)}function Pu(){zr&&(zr.detachEvent("onpropertychange",tf),Jr=zr=null)}function tf(e){if(e.propertyName==="value"&&Vs(Jr)){var t=[];Zd(t,Jr,e,la(e)),Pd(Bg,t)}}function Wg(e,t,n){e==="focusin"?(Pu(),zr=t,Jr=n,zr.attachEvent("onpropertychange",tf)):e==="focusout"&&Pu()}function qg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Vs(Jr)}function Xg(e,t){if(e==="click")return Vs(t)}function Qg(e,t){if(e==="input"||e==="change")return Vs(t)}function Kg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var at=typeof Object.is=="function"?Object.is:Kg;function Zr(e,t){if(at(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Xo.call(t,i)||!at(e[i],t[i]))return!1}return!0}function Ru(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function $u(e,t){var n=Ru(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ru(n)}}function nf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?nf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function rf(){for(var e=window,t=ds();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ds(e.document)}return t}function ga(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Gg(e){var t=rf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&nf(n.ownerDocument.documentElement,n)){if(r!==null&&ga(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=$u(n,s);var o=$u(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Yg=Lt&&"documentMode"in document&&11>=document.documentMode,$n=null,fl=null,Fr=null,hl=!1;function Ou(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;hl||$n==null||$n!==ds(r)||(r=$n,"selectionStart"in r&&ga(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Fr&&Zr(Fr,r)||(Fr=r,r=ys(fl,"onSelect"),0<r.length&&(t=new ha("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=$n)))}function Li(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var On={animationend:Li("Animation","AnimationEnd"),animationiteration:Li("Animation","AnimationIteration"),animationstart:Li("Animation","AnimationStart"),transitionend:Li("Transition","TransitionEnd")},Eo={},sf={};Lt&&(sf=document.createElement("div").style,"AnimationEvent"in window||(delete On.animationend.animation,delete On.animationiteration.animation,delete On.animationstart.animation),"TransitionEvent"in window||delete On.transitionend.transition);function Ws(e){if(Eo[e])return Eo[e];if(!On[e])return e;var t=On[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in sf)return Eo[e]=t[n];return e}var of=Ws("animationend"),lf=Ws("animationiteration"),af=Ws("animationstart"),uf=Ws("transitionend"),cf=new Map,Du="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function tn(e,t){cf.set(e,t),En(t,[e])}for(var To=0;To<Du.length;To++){var ko=Du[To],Jg=ko.toLowerCase(),Zg=ko[0].toUpperCase()+ko.slice(1);tn(Jg,"on"+Zg)}tn(of,"onAnimationEnd");tn(lf,"onAnimationIteration");tn(af,"onAnimationStart");tn("dblclick","onDoubleClick");tn("focusin","onFocus");tn("focusout","onBlur");tn(uf,"onTransitionEnd");Zn("onMouseEnter",["mouseout","mouseover"]);Zn("onMouseLeave",["mouseout","mouseover"]);Zn("onPointerEnter",["pointerout","pointerover"]);Zn("onPointerLeave",["pointerout","pointerover"]);En("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));En("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));En("onBeforeInput",["compositionend","keypress","textInput","paste"]);En("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));En("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));En("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var jr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ev=new Set("cancel close invalid load scroll toggle".split(" ").concat(jr));function zu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Jm(r,t,void 0,e),e.currentTarget=null}function df(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var l=r[o],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==s&&i.isPropagationStopped())break e;zu(i,l,u),s=a}else for(o=0;o<r.length;o++){if(l=r[o],a=l.instance,u=l.currentTarget,l=l.listener,a!==s&&i.isPropagationStopped())break e;zu(i,l,u),s=a}}}if(hs)throw e=al,hs=!1,al=null,e}function V(e,t){var n=t[yl];n===void 0&&(n=t[yl]=new Set);var r=e+"__bubble";n.has(r)||(ff(t,e,2,!1),n.add(r))}function No(e,t,n){var r=0;t&&(r|=4),ff(n,e,r,t)}var Ai="_reactListening"+Math.random().toString(36).slice(2);function ei(e){if(!e[Ai]){e[Ai]=!0,yd.forEach(function(n){n!=="selectionchange"&&(ev.has(n)||No(n,!1,e),No(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ai]||(t[Ai]=!0,No("selectionchange",!1,t))}}function ff(e,t,n,r){switch(Qd(t)){case 1:var i=pg;break;case 4:i=mg;break;default:i=da}n=i.bind(null,t,n,e),i=void 0,!ll||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Co(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var a=o.tag;if((a===3||a===4)&&(a=o.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;o=o.return}for(;l!==null;){if(o=cn(l),o===null)return;if(a=o.tag,a===5||a===6){r=s=o;continue e}l=l.parentNode}}r=r.return}Pd(function(){var u=s,c=la(n),h=[];e:{var f=cf.get(e);if(f!==void 0){var g=ha,w=e;switch(e){case"keypress":if(Qi(n)===0)break e;case"keydown":case"keyup":g=Ag;break;case"focusin":w="focus",g=xo;break;case"focusout":w="blur",g=xo;break;case"beforeblur":case"afterblur":g=xo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Cu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=yg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=Ig;break;case of:case lf:case af:g=Sg;break;case uf:g=Rg;break;case"scroll":g=gg;break;case"wheel":g=Og;break;case"copy":case"cut":case"paste":g=Eg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Lu}var y=(t&4)!==0,x=!y&&e==="scroll",p=y?f!==null?f+"Capture":null:f;y=[];for(var m=u,v;m!==null;){v=m;var S=v.stateNode;if(v.tag===5&&S!==null&&(v=S,p!==null&&(S=Qr(m,p),S!=null&&y.push(ti(m,S,v)))),x)break;m=m.return}0<y.length&&(f=new g(f,w,null,n,c),h.push({event:f,listeners:y}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",f&&n!==sl&&(w=n.relatedTarget||n.fromElement)&&(cn(w)||w[At]))break e;if((g||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,g?(w=n.relatedTarget||n.toElement,g=u,w=w?cn(w):null,w!==null&&(x=Tn(w),w!==x||w.tag!==5&&w.tag!==6)&&(w=null)):(g=null,w=u),g!==w)){if(y=Cu,S="onMouseLeave",p="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(y=Lu,S="onPointerLeave",p="onPointerEnter",m="pointer"),x=g==null?f:Dn(g),v=w==null?f:Dn(w),f=new y(S,m+"leave",g,n,c),f.target=x,f.relatedTarget=v,S=null,cn(c)===u&&(y=new y(p,m+"enter",w,n,c),y.target=v,y.relatedTarget=x,S=y),x=S,g&&w)t:{for(y=g,p=w,m=0,v=y;v;v=Cn(v))m++;for(v=0,S=p;S;S=Cn(S))v++;for(;0<m-v;)y=Cn(y),m--;for(;0<v-m;)p=Cn(p),v--;for(;m--;){if(y===p||p!==null&&y===p.alternate)break t;y=Cn(y),p=Cn(p)}y=null}else y=null;g!==null&&Fu(h,f,g,y,!1),w!==null&&x!==null&&Fu(h,x,w,y,!0)}}e:{if(f=u?Dn(u):window,g=f.nodeName&&f.nodeName.toLowerCase(),g==="select"||g==="input"&&f.type==="file")var C=Vg;else if(Mu(f))if(ef)C=Qg;else{C=qg;var E=Wg}else(g=f.nodeName)&&g.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(C=Xg);if(C&&(C=C(e,u))){Zd(h,C,n,c);break e}E&&E(e,f,u),e==="focusout"&&(E=f._wrapperState)&&E.controlled&&f.type==="number"&&el(f,"number",f.value)}switch(E=u?Dn(u):window,e){case"focusin":(Mu(E)||E.contentEditable==="true")&&($n=E,fl=u,Fr=null);break;case"focusout":Fr=fl=$n=null;break;case"mousedown":hl=!0;break;case"contextmenu":case"mouseup":case"dragend":hl=!1,Ou(h,n,c);break;case"selectionchange":if(Yg)break;case"keydown":case"keyup":Ou(h,n,c)}var N;if(ma)e:{switch(e){case"compositionstart":var j="onCompositionStart";break e;case"compositionend":j="onCompositionEnd";break e;case"compositionupdate":j="onCompositionUpdate";break e}j=void 0}else Rn?Yd(e,n)&&(j="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(j="onCompositionStart");j&&(Gd&&n.locale!=="ko"&&(Rn||j!=="onCompositionStart"?j==="onCompositionEnd"&&Rn&&(N=Kd()):(Ht=c,fa="value"in Ht?Ht.value:Ht.textContent,Rn=!0)),E=ys(u,j),0<E.length&&(j=new bu(j,e,null,n,c),h.push({event:j,listeners:E}),N?j.data=N:(N=Jd(n),N!==null&&(j.data=N)))),(N=zg?Fg(e,n):Hg(e,n))&&(u=ys(u,"onBeforeInput"),0<u.length&&(c=new bu("onBeforeInput","beforeinput",null,n,c),h.push({event:c,listeners:u}),c.data=N))}df(h,t)})}function ti(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ys(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Qr(e,n),s!=null&&r.unshift(ti(e,s,i)),s=Qr(e,t),s!=null&&r.push(ti(e,s,i))),e=e.return}return r}function Cn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Fu(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Qr(n,s),a!=null&&o.unshift(ti(n,a,l))):i||(a=Qr(n,s),a!=null&&o.push(ti(n,a,l)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var tv=/\r\n?/g,nv=/\u0000|\uFFFD/g;function Hu(e){return(typeof e=="string"?e:""+e).replace(tv,`
`).replace(nv,"")}function ji(e,t,n){if(t=Hu(t),Hu(e)!==t&&n)throw Error(A(425))}function ws(){}var pl=null,ml=null;function gl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var vl=typeof setTimeout=="function"?setTimeout:void 0,rv=typeof clearTimeout=="function"?clearTimeout:void 0,Uu=typeof Promise=="function"?Promise:void 0,iv=typeof queueMicrotask=="function"?queueMicrotask:typeof Uu<"u"?function(e){return Uu.resolve(null).then(e).catch(sv)}:vl;function sv(e){setTimeout(function(){throw e})}function bo(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Yr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Yr(t)}function qt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Bu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ar=Math.random().toString(36).slice(2),pt="__reactFiber$"+ar,ni="__reactProps$"+ar,At="__reactContainer$"+ar,yl="__reactEvents$"+ar,ov="__reactListeners$"+ar,lv="__reactHandles$"+ar;function cn(e){var t=e[pt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[At]||n[pt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Bu(e);e!==null;){if(n=e[pt])return n;e=Bu(e)}return t}e=n,n=e.parentNode}return null}function pi(e){return e=e[pt]||e[At],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Dn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(A(33))}function qs(e){return e[ni]||null}var wl=[],zn=-1;function nn(e){return{current:e}}function W(e){0>zn||(e.current=wl[zn],wl[zn]=null,zn--)}function U(e,t){zn++,wl[zn]=e.current,e.current=t}var Zt={},Se=nn(Zt),Pe=nn(!1),gn=Zt;function er(e,t){var n=e.type.contextTypes;if(!n)return Zt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Re(e){return e=e.childContextTypes,e!=null}function xs(){W(Pe),W(Se)}function Vu(e,t,n){if(Se.current!==Zt)throw Error(A(168));U(Se,t),U(Pe,n)}function hf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(A(108,Wm(e)||"Unknown",i));return J({},n,r)}function Ss(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Zt,gn=Se.current,U(Se,e),U(Pe,Pe.current),!0}function Wu(e,t,n){var r=e.stateNode;if(!r)throw Error(A(169));n?(e=hf(e,t,gn),r.__reactInternalMemoizedMergedChildContext=e,W(Pe),W(Se),U(Se,e)):W(Pe),U(Pe,n)}var kt=null,Xs=!1,Lo=!1;function pf(e){kt===null?kt=[e]:kt.push(e)}function av(e){Xs=!0,pf(e)}function rn(){if(!Lo&&kt!==null){Lo=!0;var e=0,t=H;try{var n=kt;for(H=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}kt=null,Xs=!1}catch(i){throw kt!==null&&(kt=kt.slice(e+1)),Dd(aa,rn),i}finally{H=t,Lo=!1}}return null}var Fn=[],Hn=0,_s=null,Es=0,qe=[],Xe=0,vn=null,Nt=1,Ct="";function ln(e,t){Fn[Hn++]=Es,Fn[Hn++]=_s,_s=e,Es=t}function mf(e,t,n){qe[Xe++]=Nt,qe[Xe++]=Ct,qe[Xe++]=vn,vn=e;var r=Nt;e=Ct;var i=32-ot(r)-1;r&=~(1<<i),n+=1;var s=32-ot(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,Nt=1<<32-ot(t)+i|n<<i|r,Ct=s+e}else Nt=1<<s|n<<i|r,Ct=e}function va(e){e.return!==null&&(ln(e,1),mf(e,1,0))}function ya(e){for(;e===_s;)_s=Fn[--Hn],Fn[Hn]=null,Es=Fn[--Hn],Fn[Hn]=null;for(;e===vn;)vn=qe[--Xe],qe[Xe]=null,Ct=qe[--Xe],qe[Xe]=null,Nt=qe[--Xe],qe[Xe]=null}var Fe=null,ze=null,X=!1,st=null;function gf(e,t){var n=Ke(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function qu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Fe=e,ze=qt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Fe=e,ze=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=vn!==null?{id:Nt,overflow:Ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ke(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Fe=e,ze=null,!0):!1;default:return!1}}function xl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Sl(e){if(X){var t=ze;if(t){var n=t;if(!qu(e,t)){if(xl(e))throw Error(A(418));t=qt(n.nextSibling);var r=Fe;t&&qu(e,t)?gf(r,n):(e.flags=e.flags&-4097|2,X=!1,Fe=e)}}else{if(xl(e))throw Error(A(418));e.flags=e.flags&-4097|2,X=!1,Fe=e}}}function Xu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Fe=e}function Mi(e){if(e!==Fe)return!1;if(!X)return Xu(e),X=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!gl(e.type,e.memoizedProps)),t&&(t=ze)){if(xl(e))throw vf(),Error(A(418));for(;t;)gf(e,t),t=qt(t.nextSibling)}if(Xu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(A(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ze=qt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ze=null}}else ze=Fe?qt(e.stateNode.nextSibling):null;return!0}function vf(){for(var e=ze;e;)e=qt(e.nextSibling)}function tr(){ze=Fe=null,X=!1}function wa(e){st===null?st=[e]:st.push(e)}var uv=It.ReactCurrentBatchConfig;function rt(e,t){if(e&&e.defaultProps){t=J({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}var Ts=nn(null),ks=null,Un=null,xa=null;function Sa(){xa=Un=ks=null}function _a(e){var t=Ts.current;W(Ts),e._currentValue=t}function _l(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Yn(e,t){ks=e,xa=Un=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ie=!0),e.firstContext=null)}function Ze(e){var t=e._currentValue;if(xa!==e)if(e={context:e,memoizedValue:t,next:null},Un===null){if(ks===null)throw Error(A(308));Un=e,ks.dependencies={lanes:0,firstContext:e}}else Un=Un.next=e;return t}var dn=null;function Ea(e){dn===null?dn=[e]:dn.push(e)}function yf(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Ea(t)):(n.next=i.next,i.next=n),t.interleaved=n,jt(e,r)}function jt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var $t=!1;function Ta(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function wf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function bt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Xt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,F&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,jt(e,n)}return i=r.interleaved,i===null?(t.next=t,Ea(r)):(t.next=i.next,i.next=t),r.interleaved=t,jt(e,n)}function Ki(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ua(e,n)}}function Qu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ns(e,t,n,r){var i=e.updateQueue;$t=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,o===null?s=u:o.next=u,o=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==o&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(s!==null){var h=i.baseState;o=0,c=u=a=null,l=s;do{var f=l.lane,g=l.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:g,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var w=e,y=l;switch(f=t,g=n,y.tag){case 1:if(w=y.payload,typeof w=="function"){h=w.call(g,h,f);break e}h=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=y.payload,f=typeof w=="function"?w.call(g,h,f):w,f==null)break e;h=J({},h,f);break e;case 2:$t=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,f=i.effects,f===null?i.effects=[l]:f.push(l))}else g={eventTime:g,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=g,a=h):c=c.next=g,o|=f;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;f=l,l=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(!0);if(c===null&&(a=h),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);wn|=o,e.lanes=o,e.memoizedState=h}}function Ku(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(A(191,i));i.call(r)}}}var xf=new vd.Component().refs;function El(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:J({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Qs={isMounted:function(e){return(e=e._reactInternals)?Tn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ke(),i=Kt(e),s=bt(r,i);s.payload=t,n!=null&&(s.callback=n),t=Xt(e,s,i),t!==null&&(lt(t,e,i,r),Ki(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ke(),i=Kt(e),s=bt(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Xt(e,s,i),t!==null&&(lt(t,e,i,r),Ki(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ke(),r=Kt(e),i=bt(n,r);i.tag=2,t!=null&&(i.callback=t),t=Xt(e,i,r),t!==null&&(lt(t,e,r,n),Ki(t,e,r))}};function Gu(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!Zr(n,r)||!Zr(i,s):!0}function Sf(e,t,n){var r=!1,i=Zt,s=t.contextType;return typeof s=="object"&&s!==null?s=Ze(s):(i=Re(t)?gn:Se.current,r=t.contextTypes,s=(r=r!=null)?er(e,i):Zt),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Qs,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function Yu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Qs.enqueueReplaceState(t,t.state,null)}function Tl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs=xf,Ta(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=Ze(s):(s=Re(t)?gn:Se.current,i.context=er(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(El(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Qs.enqueueReplaceState(i,i.state,null),Ns(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function xr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(A(309));var r=n.stateNode}if(!r)throw Error(A(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var l=i.refs;l===xf&&(l=i.refs={}),o===null?delete l[s]:l[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(A(284));if(!n._owner)throw Error(A(290,e))}return e}function Ii(e,t){throw e=Object.prototype.toString.call(t),Error(A(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ju(e){var t=e._init;return t(e._payload)}function _f(e){function t(p,m){if(e){var v=p.deletions;v===null?(p.deletions=[m],p.flags|=16):v.push(m)}}function n(p,m){if(!e)return null;for(;m!==null;)t(p,m),m=m.sibling;return null}function r(p,m){for(p=new Map;m!==null;)m.key!==null?p.set(m.key,m):p.set(m.index,m),m=m.sibling;return p}function i(p,m){return p=Gt(p,m),p.index=0,p.sibling=null,p}function s(p,m,v){return p.index=v,e?(v=p.alternate,v!==null?(v=v.index,v<m?(p.flags|=2,m):v):(p.flags|=2,m)):(p.flags|=1048576,m)}function o(p){return e&&p.alternate===null&&(p.flags|=2),p}function l(p,m,v,S){return m===null||m.tag!==6?(m=$o(v,p.mode,S),m.return=p,m):(m=i(m,v),m.return=p,m)}function a(p,m,v,S){var C=v.type;return C===Pn?c(p,m,v.props.children,S,v.key):m!==null&&(m.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Rt&&Ju(C)===m.type)?(S=i(m,v.props),S.ref=xr(p,m,v),S.return=p,S):(S=ts(v.type,v.key,v.props,null,p.mode,S),S.ref=xr(p,m,v),S.return=p,S)}function u(p,m,v,S){return m===null||m.tag!==4||m.stateNode.containerInfo!==v.containerInfo||m.stateNode.implementation!==v.implementation?(m=Oo(v,p.mode,S),m.return=p,m):(m=i(m,v.children||[]),m.return=p,m)}function c(p,m,v,S,C){return m===null||m.tag!==7?(m=pn(v,p.mode,S,C),m.return=p,m):(m=i(m,v),m.return=p,m)}function h(p,m,v){if(typeof m=="string"&&m!==""||typeof m=="number")return m=$o(""+m,p.mode,v),m.return=p,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case _i:return v=ts(m.type,m.key,m.props,null,p.mode,v),v.ref=xr(p,null,m),v.return=p,v;case In:return m=Oo(m,p.mode,v),m.return=p,m;case Rt:var S=m._init;return h(p,S(m._payload),v)}if(Lr(m)||mr(m))return m=pn(m,p.mode,v,null),m.return=p,m;Ii(p,m)}return null}function f(p,m,v,S){var C=m!==null?m.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return C!==null?null:l(p,m,""+v,S);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case _i:return v.key===C?a(p,m,v,S):null;case In:return v.key===C?u(p,m,v,S):null;case Rt:return C=v._init,f(p,m,C(v._payload),S)}if(Lr(v)||mr(v))return C!==null?null:c(p,m,v,S,null);Ii(p,v)}return null}function g(p,m,v,S,C){if(typeof S=="string"&&S!==""||typeof S=="number")return p=p.get(v)||null,l(m,p,""+S,C);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case _i:return p=p.get(S.key===null?v:S.key)||null,a(m,p,S,C);case In:return p=p.get(S.key===null?v:S.key)||null,u(m,p,S,C);case Rt:var E=S._init;return g(p,m,v,E(S._payload),C)}if(Lr(S)||mr(S))return p=p.get(v)||null,c(m,p,S,C,null);Ii(m,S)}return null}function w(p,m,v,S){for(var C=null,E=null,N=m,j=m=0,_=null;N!==null&&j<v.length;j++){N.index>j?(_=N,N=null):_=N.sibling;var T=f(p,N,v[j],S);if(T===null){N===null&&(N=_);break}e&&N&&T.alternate===null&&t(p,N),m=s(T,m,j),E===null?C=T:E.sibling=T,E=T,N=_}if(j===v.length)return n(p,N),X&&ln(p,j),C;if(N===null){for(;j<v.length;j++)N=h(p,v[j],S),N!==null&&(m=s(N,m,j),E===null?C=N:E.sibling=N,E=N);return X&&ln(p,j),C}for(N=r(p,N);j<v.length;j++)_=g(N,p,j,v[j],S),_!==null&&(e&&_.alternate!==null&&N.delete(_.key===null?j:_.key),m=s(_,m,j),E===null?C=_:E.sibling=_,E=_);return e&&N.forEach(function(L){return t(p,L)}),X&&ln(p,j),C}function y(p,m,v,S){var C=mr(v);if(typeof C!="function")throw Error(A(150));if(v=C.call(v),v==null)throw Error(A(151));for(var E=C=null,N=m,j=m=0,_=null,T=v.next();N!==null&&!T.done;j++,T=v.next()){N.index>j?(_=N,N=null):_=N.sibling;var L=f(p,N,T.value,S);if(L===null){N===null&&(N=_);break}e&&N&&L.alternate===null&&t(p,N),m=s(L,m,j),E===null?C=L:E.sibling=L,E=L,N=_}if(T.done)return n(p,N),X&&ln(p,j),C;if(N===null){for(;!T.done;j++,T=v.next())T=h(p,T.value,S),T!==null&&(m=s(T,m,j),E===null?C=T:E.sibling=T,E=T);return X&&ln(p,j),C}for(N=r(p,N);!T.done;j++,T=v.next())T=g(N,p,j,T.value,S),T!==null&&(e&&T.alternate!==null&&N.delete(T.key===null?j:T.key),m=s(T,m,j),E===null?C=T:E.sibling=T,E=T);return e&&N.forEach(function(k){return t(p,k)}),X&&ln(p,j),C}function x(p,m,v,S){if(typeof v=="object"&&v!==null&&v.type===Pn&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case _i:e:{for(var C=v.key,E=m;E!==null;){if(E.key===C){if(C=v.type,C===Pn){if(E.tag===7){n(p,E.sibling),m=i(E,v.props.children),m.return=p,p=m;break e}}else if(E.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Rt&&Ju(C)===E.type){n(p,E.sibling),m=i(E,v.props),m.ref=xr(p,E,v),m.return=p,p=m;break e}n(p,E);break}else t(p,E);E=E.sibling}v.type===Pn?(m=pn(v.props.children,p.mode,S,v.key),m.return=p,p=m):(S=ts(v.type,v.key,v.props,null,p.mode,S),S.ref=xr(p,m,v),S.return=p,p=S)}return o(p);case In:e:{for(E=v.key;m!==null;){if(m.key===E)if(m.tag===4&&m.stateNode.containerInfo===v.containerInfo&&m.stateNode.implementation===v.implementation){n(p,m.sibling),m=i(m,v.children||[]),m.return=p,p=m;break e}else{n(p,m);break}else t(p,m);m=m.sibling}m=Oo(v,p.mode,S),m.return=p,p=m}return o(p);case Rt:return E=v._init,x(p,m,E(v._payload),S)}if(Lr(v))return w(p,m,v,S);if(mr(v))return y(p,m,v,S);Ii(p,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,m!==null&&m.tag===6?(n(p,m.sibling),m=i(m,v),m.return=p,p=m):(n(p,m),m=$o(v,p.mode,S),m.return=p,p=m),o(p)):n(p,m)}return x}var nr=_f(!0),Ef=_f(!1),mi={},vt=nn(mi),ri=nn(mi),ii=nn(mi);function fn(e){if(e===mi)throw Error(A(174));return e}function ka(e,t){switch(U(ii,t),U(ri,e),U(vt,mi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:nl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=nl(t,e)}W(vt),U(vt,t)}function rr(){W(vt),W(ri),W(ii)}function Tf(e){fn(ii.current);var t=fn(vt.current),n=nl(t,e.type);t!==n&&(U(ri,e),U(vt,n))}function Na(e){ri.current===e&&(W(vt),W(ri))}var G=nn(0);function Cs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ao=[];function Ca(){for(var e=0;e<Ao.length;e++)Ao[e]._workInProgressVersionPrimary=null;Ao.length=0}var Gi=It.ReactCurrentDispatcher,jo=It.ReactCurrentBatchConfig,yn=0,Y=null,le=null,ue=null,bs=!1,Hr=!1,si=0,cv=0;function ge(){throw Error(A(321))}function ba(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!at(e[n],t[n]))return!1;return!0}function La(e,t,n,r,i,s){if(yn=s,Y=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Gi.current=e===null||e.memoizedState===null?pv:mv,e=n(r,i),Hr){s=0;do{if(Hr=!1,si=0,25<=s)throw Error(A(301));s+=1,ue=le=null,t.updateQueue=null,Gi.current=gv,e=n(r,i)}while(Hr)}if(Gi.current=Ls,t=le!==null&&le.next!==null,yn=0,ue=le=Y=null,bs=!1,t)throw Error(A(300));return e}function Aa(){var e=si!==0;return si=0,e}function dt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ue===null?Y.memoizedState=ue=e:ue=ue.next=e,ue}function et(){if(le===null){var e=Y.alternate;e=e!==null?e.memoizedState:null}else e=le.next;var t=ue===null?Y.memoizedState:ue.next;if(t!==null)ue=t,le=e;else{if(e===null)throw Error(A(310));le=e,e={memoizedState:le.memoizedState,baseState:le.baseState,baseQueue:le.baseQueue,queue:le.queue,next:null},ue===null?Y.memoizedState=ue=e:ue=ue.next=e}return ue}function oi(e,t){return typeof t=="function"?t(e):t}function Mo(e){var t=et(),n=t.queue;if(n===null)throw Error(A(311));n.lastRenderedReducer=e;var r=le,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var l=o=null,a=null,u=s;do{var c=u.lane;if((yn&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var h={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=h,o=r):a=a.next=h,Y.lanes|=c,wn|=c}u=u.next}while(u!==null&&u!==s);a===null?o=r:a.next=l,at(r,t.memoizedState)||(Ie=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,Y.lanes|=s,wn|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Io(e){var t=et(),n=t.queue;if(n===null)throw Error(A(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);at(s,t.memoizedState)||(Ie=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function kf(){}function Nf(e,t){var n=Y,r=et(),i=t(),s=!at(r.memoizedState,i);if(s&&(r.memoizedState=i,Ie=!0),r=r.queue,ja(Lf.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||ue!==null&&ue.memoizedState.tag&1){if(n.flags|=2048,li(9,bf.bind(null,n,r,i,t),void 0,null),ce===null)throw Error(A(349));yn&30||Cf(n,t,i)}return i}function Cf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function bf(e,t,n,r){t.value=n,t.getSnapshot=r,Af(t)&&jf(e)}function Lf(e,t,n){return n(function(){Af(t)&&jf(e)})}function Af(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!at(e,n)}catch{return!0}}function jf(e){var t=jt(e,1);t!==null&&lt(t,e,1,-1)}function Zu(e){var t=dt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:oi,lastRenderedState:e},t.queue=e,e=e.dispatch=hv.bind(null,Y,e),[t.memoizedState,e]}function li(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Mf(){return et().memoizedState}function Yi(e,t,n,r){var i=dt();Y.flags|=e,i.memoizedState=li(1|t,n,void 0,r===void 0?null:r)}function Ks(e,t,n,r){var i=et();r=r===void 0?null:r;var s=void 0;if(le!==null){var o=le.memoizedState;if(s=o.destroy,r!==null&&ba(r,o.deps)){i.memoizedState=li(t,n,s,r);return}}Y.flags|=e,i.memoizedState=li(1|t,n,s,r)}function ec(e,t){return Yi(8390656,8,e,t)}function ja(e,t){return Ks(2048,8,e,t)}function If(e,t){return Ks(4,2,e,t)}function Pf(e,t){return Ks(4,4,e,t)}function Rf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function $f(e,t,n){return n=n!=null?n.concat([e]):null,Ks(4,4,Rf.bind(null,t,e),n)}function Ma(){}function Of(e,t){var n=et();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ba(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Df(e,t){var n=et();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ba(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function zf(e,t,n){return yn&21?(at(n,t)||(n=Hd(),Y.lanes|=n,wn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ie=!0),e.memoizedState=n)}function dv(e,t){var n=H;H=n!==0&&4>n?n:4,e(!0);var r=jo.transition;jo.transition={};try{e(!1),t()}finally{H=n,jo.transition=r}}function Ff(){return et().memoizedState}function fv(e,t,n){var r=Kt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Hf(e))Uf(t,n);else if(n=yf(e,t,n,r),n!==null){var i=ke();lt(n,e,r,i),Bf(n,t,r)}}function hv(e,t,n){var r=Kt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Hf(e))Uf(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,l=s(o,n);if(i.hasEagerState=!0,i.eagerState=l,at(l,o)){var a=t.interleaved;a===null?(i.next=i,Ea(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=yf(e,t,i,r),n!==null&&(i=ke(),lt(n,e,r,i),Bf(n,t,r))}}function Hf(e){var t=e.alternate;return e===Y||t!==null&&t===Y}function Uf(e,t){Hr=bs=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Bf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ua(e,n)}}var Ls={readContext:Ze,useCallback:ge,useContext:ge,useEffect:ge,useImperativeHandle:ge,useInsertionEffect:ge,useLayoutEffect:ge,useMemo:ge,useReducer:ge,useRef:ge,useState:ge,useDebugValue:ge,useDeferredValue:ge,useTransition:ge,useMutableSource:ge,useSyncExternalStore:ge,useId:ge,unstable_isNewReconciler:!1},pv={readContext:Ze,useCallback:function(e,t){return dt().memoizedState=[e,t===void 0?null:t],e},useContext:Ze,useEffect:ec,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Yi(4194308,4,Rf.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Yi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Yi(4,2,e,t)},useMemo:function(e,t){var n=dt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=dt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=fv.bind(null,Y,e),[r.memoizedState,e]},useRef:function(e){var t=dt();return e={current:e},t.memoizedState=e},useState:Zu,useDebugValue:Ma,useDeferredValue:function(e){return dt().memoizedState=e},useTransition:function(){var e=Zu(!1),t=e[0];return e=dv.bind(null,e[1]),dt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Y,i=dt();if(X){if(n===void 0)throw Error(A(407));n=n()}else{if(n=t(),ce===null)throw Error(A(349));yn&30||Cf(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,ec(Lf.bind(null,r,s,e),[e]),r.flags|=2048,li(9,bf.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=dt(),t=ce.identifierPrefix;if(X){var n=Ct,r=Nt;n=(r&~(1<<32-ot(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=si++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=cv++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},mv={readContext:Ze,useCallback:Of,useContext:Ze,useEffect:ja,useImperativeHandle:$f,useInsertionEffect:If,useLayoutEffect:Pf,useMemo:Df,useReducer:Mo,useRef:Mf,useState:function(){return Mo(oi)},useDebugValue:Ma,useDeferredValue:function(e){var t=et();return zf(t,le.memoizedState,e)},useTransition:function(){var e=Mo(oi)[0],t=et().memoizedState;return[e,t]},useMutableSource:kf,useSyncExternalStore:Nf,useId:Ff,unstable_isNewReconciler:!1},gv={readContext:Ze,useCallback:Of,useContext:Ze,useEffect:ja,useImperativeHandle:$f,useInsertionEffect:If,useLayoutEffect:Pf,useMemo:Df,useReducer:Io,useRef:Mf,useState:function(){return Io(oi)},useDebugValue:Ma,useDeferredValue:function(e){var t=et();return le===null?t.memoizedState=e:zf(t,le.memoizedState,e)},useTransition:function(){var e=Io(oi)[0],t=et().memoizedState;return[e,t]},useMutableSource:kf,useSyncExternalStore:Nf,useId:Ff,unstable_isNewReconciler:!1};function ir(e,t){try{var n="",r=t;do n+=Vm(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function Po(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function kl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var vv=typeof WeakMap=="function"?WeakMap:Map;function Vf(e,t,n){n=bt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){js||(js=!0,Rl=r),kl(e,t)},n}function Wf(e,t,n){n=bt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){kl(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){kl(e,t),typeof r!="function"&&(Qt===null?Qt=new Set([this]):Qt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function tc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new vv;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=jv.bind(null,e,t,n),t.then(e,e))}function nc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function rc(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=bt(-1,1),t.tag=2,Xt(n,t,1))),n.lanes|=1),e)}var yv=It.ReactCurrentOwner,Ie=!1;function _e(e,t,n,r){t.child=e===null?Ef(t,null,n,r):nr(t,e.child,n,r)}function ic(e,t,n,r,i){n=n.render;var s=t.ref;return Yn(t,i),r=La(e,t,n,r,s,i),n=Aa(),e!==null&&!Ie?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Mt(e,t,i)):(X&&n&&va(t),t.flags|=1,_e(e,t,r,i),t.child)}function sc(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!Fa(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,qf(e,t,s,r,i)):(e=ts(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:Zr,n(o,r)&&e.ref===t.ref)return Mt(e,t,i)}return t.flags|=1,e=Gt(s,r),e.ref=t.ref,e.return=t,t.child=e}function qf(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(Zr(s,r)&&e.ref===t.ref)if(Ie=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(Ie=!0);else return t.lanes=e.lanes,Mt(e,t,i)}return Nl(e,t,n,r,i)}function Xf(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},U(Vn,De),De|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,U(Vn,De),De|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,U(Vn,De),De|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,U(Vn,De),De|=r;return _e(e,t,i,n),t.child}function Qf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Nl(e,t,n,r,i){var s=Re(n)?gn:Se.current;return s=er(t,s),Yn(t,i),n=La(e,t,n,r,s,i),r=Aa(),e!==null&&!Ie?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Mt(e,t,i)):(X&&r&&va(t),t.flags|=1,_e(e,t,n,i),t.child)}function oc(e,t,n,r,i){if(Re(n)){var s=!0;Ss(t)}else s=!1;if(Yn(t,i),t.stateNode===null)Ji(e,t),Sf(t,n,r),Tl(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,l=t.memoizedProps;o.props=l;var a=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=Ze(u):(u=Re(n)?gn:Se.current,u=er(t,u));var c=n.getDerivedStateFromProps,h=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Yu(t,o,r,u),$t=!1;var f=t.memoizedState;o.state=f,Ns(t,r,o,i),a=t.memoizedState,l!==r||f!==a||Pe.current||$t?(typeof c=="function"&&(El(t,n,c,r),a=t.memoizedState),(l=$t||Gu(t,n,l,r,f,a,u))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),o.props=r,o.state=a,o.context=u,r=l):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,wf(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:rt(t.type,l),o.props=u,h=t.pendingProps,f=o.context,a=n.contextType,typeof a=="object"&&a!==null?a=Ze(a):(a=Re(n)?gn:Se.current,a=er(t,a));var g=n.getDerivedStateFromProps;(c=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==h||f!==a)&&Yu(t,o,r,a),$t=!1,f=t.memoizedState,o.state=f,Ns(t,r,o,i);var w=t.memoizedState;l!==h||f!==w||Pe.current||$t?(typeof g=="function"&&(El(t,n,g,r),w=t.memoizedState),(u=$t||Gu(t,n,u,r,f,w,a)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,w,a),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,w,a)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),o.props=r,o.state=w,o.context=a,r=u):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Cl(e,t,n,r,s,i)}function Cl(e,t,n,r,i,s){Qf(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&Wu(t,n,!1),Mt(e,t,s);r=t.stateNode,yv.current=t;var l=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=nr(t,e.child,null,s),t.child=nr(t,null,l,s)):_e(e,t,l,s),t.memoizedState=r.state,i&&Wu(t,n,!0),t.child}function Kf(e){var t=e.stateNode;t.pendingContext?Vu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Vu(e,t.context,!1),ka(e,t.containerInfo)}function lc(e,t,n,r,i){return tr(),wa(i),t.flags|=256,_e(e,t,n,r),t.child}var bl={dehydrated:null,treeContext:null,retryLane:0};function Ll(e){return{baseLanes:e,cachePool:null,transitions:null}}function Gf(e,t,n){var r=t.pendingProps,i=G.current,s=!1,o=(t.flags&128)!==0,l;if((l=o)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),U(G,i&1),e===null)return Sl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=Js(o,r,0,null),e=pn(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Ll(n),t.memoizedState=bl,e):Ia(t,o));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return wv(e,t,o,r,l,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Gt(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?s=Gt(l,s):(s=pn(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?Ll(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=bl,r}return s=e.child,e=s.sibling,r=Gt(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ia(e,t){return t=Js({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Pi(e,t,n,r){return r!==null&&wa(r),nr(t,e.child,null,n),e=Ia(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function wv(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=Po(Error(A(422))),Pi(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=Js({mode:"visible",children:r.children},i,0,null),s=pn(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&nr(t,e.child,null,o),t.child.memoizedState=Ll(o),t.memoizedState=bl,s);if(!(t.mode&1))return Pi(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,s=Error(A(419)),r=Po(s,r,void 0),Pi(e,t,o,r)}if(l=(o&e.childLanes)!==0,Ie||l){if(r=ce,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,jt(e,i),lt(r,e,i,-1))}return za(),r=Po(Error(A(421))),Pi(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Mv.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,ze=qt(i.nextSibling),Fe=t,X=!0,st=null,e!==null&&(qe[Xe++]=Nt,qe[Xe++]=Ct,qe[Xe++]=vn,Nt=e.id,Ct=e.overflow,vn=t),t=Ia(t,r.children),t.flags|=4096,t)}function ac(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),_l(e.return,t,n)}function Ro(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function Yf(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(_e(e,t,r.children,n),r=G.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ac(e,n,t);else if(e.tag===19)ac(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(U(G,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Cs(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Ro(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Cs(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Ro(t,!0,n,null,s);break;case"together":Ro(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ji(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Mt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),wn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(A(153));if(t.child!==null){for(e=t.child,n=Gt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Gt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function xv(e,t,n){switch(t.tag){case 3:Kf(t),tr();break;case 5:Tf(t);break;case 1:Re(t.type)&&Ss(t);break;case 4:ka(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;U(Ts,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(U(G,G.current&1),t.flags|=128,null):n&t.child.childLanes?Gf(e,t,n):(U(G,G.current&1),e=Mt(e,t,n),e!==null?e.sibling:null);U(G,G.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Yf(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),U(G,G.current),r)break;return null;case 22:case 23:return t.lanes=0,Xf(e,t,n)}return Mt(e,t,n)}var Jf,Al,Zf,eh;Jf=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Al=function(){};Zf=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,fn(vt.current);var s=null;switch(n){case"input":i=Jo(e,i),r=Jo(e,r),s=[];break;case"select":i=J({},i,{value:void 0}),r=J({},r,{value:void 0}),s=[];break;case"textarea":i=tl(e,i),r=tl(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ws)}rl(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(qr.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(o in l)!l.hasOwnProperty(o)||a&&a.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in a)a.hasOwnProperty(o)&&l[o]!==a[o]&&(n||(n={}),n[o]=a[o])}else n||(s||(s=[]),s.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(s=s||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(s=s||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(qr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&V("scroll",e),s||l===a||(s=[])):(s=s||[]).push(u,a))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};eh=function(e,t,n,r){n!==r&&(t.flags|=4)};function Sr(e,t){if(!X)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ve(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Sv(e,t,n){var r=t.pendingProps;switch(ya(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ve(t),null;case 1:return Re(t.type)&&xs(),ve(t),null;case 3:return r=t.stateNode,rr(),W(Pe),W(Se),Ca(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Mi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,st!==null&&(Dl(st),st=null))),Al(e,t),ve(t),null;case 5:Na(t);var i=fn(ii.current);if(n=t.type,e!==null&&t.stateNode!=null)Zf(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(A(166));return ve(t),null}if(e=fn(vt.current),Mi(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[pt]=t,r[ni]=s,e=(t.mode&1)!==0,n){case"dialog":V("cancel",r),V("close",r);break;case"iframe":case"object":case"embed":V("load",r);break;case"video":case"audio":for(i=0;i<jr.length;i++)V(jr[i],r);break;case"source":V("error",r);break;case"img":case"image":case"link":V("error",r),V("load",r);break;case"details":V("toggle",r);break;case"input":vu(r,s),V("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},V("invalid",r);break;case"textarea":wu(r,s),V("invalid",r)}rl(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var l=s[o];o==="children"?typeof l=="string"?r.textContent!==l&&(s.suppressHydrationWarning!==!0&&ji(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(s.suppressHydrationWarning!==!0&&ji(r.textContent,l,e),i=["children",""+l]):qr.hasOwnProperty(o)&&l!=null&&o==="onScroll"&&V("scroll",r)}switch(n){case"input":Ei(r),yu(r,s,!0);break;case"textarea":Ei(r),xu(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=ws)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Nd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[pt]=t,e[ni]=r,Jf(e,t,!1,!1),t.stateNode=e;e:{switch(o=il(n,r),n){case"dialog":V("cancel",e),V("close",e),i=r;break;case"iframe":case"object":case"embed":V("load",e),i=r;break;case"video":case"audio":for(i=0;i<jr.length;i++)V(jr[i],e);i=r;break;case"source":V("error",e),i=r;break;case"img":case"image":case"link":V("error",e),V("load",e),i=r;break;case"details":V("toggle",e),i=r;break;case"input":vu(e,r),i=Jo(e,r),V("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=J({},r,{value:void 0}),V("invalid",e);break;case"textarea":wu(e,r),i=tl(e,r),V("invalid",e);break;default:i=r}rl(n,i),l=i;for(s in l)if(l.hasOwnProperty(s)){var a=l[s];s==="style"?Ld(e,a):s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Cd(e,a)):s==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Xr(e,a):typeof a=="number"&&Xr(e,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(qr.hasOwnProperty(s)?a!=null&&s==="onScroll"&&V("scroll",e):a!=null&&ra(e,s,a,o))}switch(n){case"input":Ei(e),yu(e,r,!1);break;case"textarea":Ei(e),xu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Jt(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Xn(e,!!r.multiple,s,!1):r.defaultValue!=null&&Xn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=ws)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ve(t),null;case 6:if(e&&t.stateNode!=null)eh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(A(166));if(n=fn(ii.current),fn(vt.current),Mi(t)){if(r=t.stateNode,n=t.memoizedProps,r[pt]=t,(s=r.nodeValue!==n)&&(e=Fe,e!==null))switch(e.tag){case 3:ji(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ji(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[pt]=t,t.stateNode=r}return ve(t),null;case 13:if(W(G),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(X&&ze!==null&&t.mode&1&&!(t.flags&128))vf(),tr(),t.flags|=98560,s=!1;else if(s=Mi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(A(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(A(317));s[pt]=t}else tr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ve(t),s=!1}else st!==null&&(Dl(st),st=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||G.current&1?ae===0&&(ae=3):za())),t.updateQueue!==null&&(t.flags|=4),ve(t),null);case 4:return rr(),Al(e,t),e===null&&ei(t.stateNode.containerInfo),ve(t),null;case 10:return _a(t.type._context),ve(t),null;case 17:return Re(t.type)&&xs(),ve(t),null;case 19:if(W(G),s=t.memoizedState,s===null)return ve(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)Sr(s,!1);else{if(ae!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Cs(e),o!==null){for(t.flags|=128,Sr(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return U(G,G.current&1|2),t.child}e=e.sibling}s.tail!==null&&te()>sr&&(t.flags|=128,r=!0,Sr(s,!1),t.lanes=4194304)}else{if(!r)if(e=Cs(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Sr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!X)return ve(t),null}else 2*te()-s.renderingStartTime>sr&&n!==1073741824&&(t.flags|=128,r=!0,Sr(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=te(),t.sibling=null,n=G.current,U(G,r?n&1|2:n&1),t):(ve(t),null);case 22:case 23:return Da(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?De&1073741824&&(ve(t),t.subtreeFlags&6&&(t.flags|=8192)):ve(t),null;case 24:return null;case 25:return null}throw Error(A(156,t.tag))}function _v(e,t){switch(ya(t),t.tag){case 1:return Re(t.type)&&xs(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return rr(),W(Pe),W(Se),Ca(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Na(t),null;case 13:if(W(G),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(A(340));tr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return W(G),null;case 4:return rr(),null;case 10:return _a(t.type._context),null;case 22:case 23:return Da(),null;case 24:return null;default:return null}}var Ri=!1,we=!1,Ev=typeof WeakSet=="function"?WeakSet:Set,P=null;function Bn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ee(e,t,r)}else n.current=null}function jl(e,t,n){try{n()}catch(r){ee(e,t,r)}}var uc=!1;function Tv(e,t){if(pl=gs,e=rf(),ga(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,l=-1,a=-1,u=0,c=0,h=e,f=null;t:for(;;){for(var g;h!==n||i!==0&&h.nodeType!==3||(l=o+i),h!==s||r!==0&&h.nodeType!==3||(a=o+r),h.nodeType===3&&(o+=h.nodeValue.length),(g=h.firstChild)!==null;)f=h,h=g;for(;;){if(h===e)break t;if(f===n&&++u===i&&(l=o),f===s&&++c===r&&(a=o),(g=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=g}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(ml={focusedElem:e,selectionRange:n},gs=!1,P=t;P!==null;)if(t=P,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,P=e;else for(;P!==null;){t=P;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var y=w.memoizedProps,x=w.memoizedState,p=t.stateNode,m=p.getSnapshotBeforeUpdate(t.elementType===t.type?y:rt(t.type,y),x);p.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(A(163))}}catch(S){ee(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,P=e;break}P=t.return}return w=uc,uc=!1,w}function Ur(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&jl(t,n,s)}i=i.next}while(i!==r)}}function Gs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ml(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function th(e){var t=e.alternate;t!==null&&(e.alternate=null,th(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[pt],delete t[ni],delete t[yl],delete t[ov],delete t[lv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function nh(e){return e.tag===5||e.tag===3||e.tag===4}function cc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||nh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Il(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ws));else if(r!==4&&(e=e.child,e!==null))for(Il(e,t,n),e=e.sibling;e!==null;)Il(e,t,n),e=e.sibling}function Pl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Pl(e,t,n),e=e.sibling;e!==null;)Pl(e,t,n),e=e.sibling}var de=null,it=!1;function Pt(e,t,n){for(n=n.child;n!==null;)rh(e,t,n),n=n.sibling}function rh(e,t,n){if(gt&&typeof gt.onCommitFiberUnmount=="function")try{gt.onCommitFiberUnmount(Us,n)}catch{}switch(n.tag){case 5:we||Bn(n,t);case 6:var r=de,i=it;de=null,Pt(e,t,n),de=r,it=i,de!==null&&(it?(e=de,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):de.removeChild(n.stateNode));break;case 18:de!==null&&(it?(e=de,n=n.stateNode,e.nodeType===8?bo(e.parentNode,n):e.nodeType===1&&bo(e,n),Yr(e)):bo(de,n.stateNode));break;case 4:r=de,i=it,de=n.stateNode.containerInfo,it=!0,Pt(e,t,n),de=r,it=i;break;case 0:case 11:case 14:case 15:if(!we&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&jl(n,t,o),i=i.next}while(i!==r)}Pt(e,t,n);break;case 1:if(!we&&(Bn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){ee(n,t,l)}Pt(e,t,n);break;case 21:Pt(e,t,n);break;case 22:n.mode&1?(we=(r=we)||n.memoizedState!==null,Pt(e,t,n),we=r):Pt(e,t,n);break;default:Pt(e,t,n)}}function dc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Ev),t.forEach(function(r){var i=Iv.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function nt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,l=o;e:for(;l!==null;){switch(l.tag){case 5:de=l.stateNode,it=!1;break e;case 3:de=l.stateNode.containerInfo,it=!0;break e;case 4:de=l.stateNode.containerInfo,it=!0;break e}l=l.return}if(de===null)throw Error(A(160));rh(s,o,i),de=null,it=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){ee(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)ih(t,e),t=t.sibling}function ih(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(nt(t,e),ut(e),r&4){try{Ur(3,e,e.return),Gs(3,e)}catch(y){ee(e,e.return,y)}try{Ur(5,e,e.return)}catch(y){ee(e,e.return,y)}}break;case 1:nt(t,e),ut(e),r&512&&n!==null&&Bn(n,n.return);break;case 5:if(nt(t,e),ut(e),r&512&&n!==null&&Bn(n,n.return),e.flags&32){var i=e.stateNode;try{Xr(i,"")}catch(y){ee(e,e.return,y)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&s.type==="radio"&&s.name!=null&&Td(i,s),il(l,o);var u=il(l,s);for(o=0;o<a.length;o+=2){var c=a[o],h=a[o+1];c==="style"?Ld(i,h):c==="dangerouslySetInnerHTML"?Cd(i,h):c==="children"?Xr(i,h):ra(i,c,h,u)}switch(l){case"input":Zo(i,s);break;case"textarea":kd(i,s);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var g=s.value;g!=null?Xn(i,!!s.multiple,g,!1):f!==!!s.multiple&&(s.defaultValue!=null?Xn(i,!!s.multiple,s.defaultValue,!0):Xn(i,!!s.multiple,s.multiple?[]:"",!1))}i[ni]=s}catch(y){ee(e,e.return,y)}}break;case 6:if(nt(t,e),ut(e),r&4){if(e.stateNode===null)throw Error(A(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(y){ee(e,e.return,y)}}break;case 3:if(nt(t,e),ut(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Yr(t.containerInfo)}catch(y){ee(e,e.return,y)}break;case 4:nt(t,e),ut(e);break;case 13:nt(t,e),ut(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||($a=te())),r&4&&dc(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(we=(u=we)||c,nt(t,e),we=u):nt(t,e),ut(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(P=e,c=e.child;c!==null;){for(h=P=c;P!==null;){switch(f=P,g=f.child,f.tag){case 0:case 11:case 14:case 15:Ur(4,f,f.return);break;case 1:Bn(f,f.return);var w=f.stateNode;if(typeof w.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(y){ee(r,n,y)}}break;case 5:Bn(f,f.return);break;case 22:if(f.memoizedState!==null){hc(h);continue}}g!==null?(g.return=f,P=g):hc(h)}c=c.sibling}e:for(c=null,h=e;;){if(h.tag===5){if(c===null){c=h;try{i=h.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(l=h.stateNode,a=h.memoizedProps.style,o=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=bd("display",o))}catch(y){ee(e,e.return,y)}}}else if(h.tag===6){if(c===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(y){ee(e,e.return,y)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;c===h&&(c=null),h=h.return}c===h&&(c=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:nt(t,e),ut(e),r&4&&dc(e);break;case 21:break;default:nt(t,e),ut(e)}}function ut(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(nh(n)){var r=n;break e}n=n.return}throw Error(A(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Xr(i,""),r.flags&=-33);var s=cc(e);Pl(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,l=cc(e);Il(e,l,o);break;default:throw Error(A(161))}}catch(a){ee(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function kv(e,t,n){P=e,sh(e)}function sh(e,t,n){for(var r=(e.mode&1)!==0;P!==null;){var i=P,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||Ri;if(!o){var l=i.alternate,a=l!==null&&l.memoizedState!==null||we;l=Ri;var u=we;if(Ri=o,(we=a)&&!u)for(P=i;P!==null;)o=P,a=o.child,o.tag===22&&o.memoizedState!==null?pc(i):a!==null?(a.return=o,P=a):pc(i);for(;s!==null;)P=s,sh(s),s=s.sibling;P=i,Ri=l,we=u}fc(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,P=s):fc(e)}}function fc(e){for(;P!==null;){var t=P;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:we||Gs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!we)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:rt(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&Ku(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Ku(t,o,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var h=c.dehydrated;h!==null&&Yr(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(A(163))}we||t.flags&512&&Ml(t)}catch(f){ee(t,t.return,f)}}if(t===e){P=null;break}if(n=t.sibling,n!==null){n.return=t.return,P=n;break}P=t.return}}function hc(e){for(;P!==null;){var t=P;if(t===e){P=null;break}var n=t.sibling;if(n!==null){n.return=t.return,P=n;break}P=t.return}}function pc(e){for(;P!==null;){var t=P;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Gs(4,t)}catch(a){ee(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){ee(t,i,a)}}var s=t.return;try{Ml(t)}catch(a){ee(t,s,a)}break;case 5:var o=t.return;try{Ml(t)}catch(a){ee(t,o,a)}}}catch(a){ee(t,t.return,a)}if(t===e){P=null;break}var l=t.sibling;if(l!==null){l.return=t.return,P=l;break}P=t.return}}var Nv=Math.ceil,As=It.ReactCurrentDispatcher,Pa=It.ReactCurrentOwner,Je=It.ReactCurrentBatchConfig,F=0,ce=null,se=null,he=0,De=0,Vn=nn(0),ae=0,ai=null,wn=0,Ys=0,Ra=0,Br=null,Ae=null,$a=0,sr=1/0,Tt=null,js=!1,Rl=null,Qt=null,$i=!1,Ut=null,Ms=0,Vr=0,$l=null,Zi=-1,es=0;function ke(){return F&6?te():Zi!==-1?Zi:Zi=te()}function Kt(e){return e.mode&1?F&2&&he!==0?he&-he:uv.transition!==null?(es===0&&(es=Hd()),es):(e=H,e!==0||(e=window.event,e=e===void 0?16:Qd(e.type)),e):1}function lt(e,t,n,r){if(50<Vr)throw Vr=0,$l=null,Error(A(185));fi(e,n,r),(!(F&2)||e!==ce)&&(e===ce&&(!(F&2)&&(Ys|=n),ae===4&&Ft(e,he)),$e(e,r),n===1&&F===0&&!(t.mode&1)&&(sr=te()+500,Xs&&rn()))}function $e(e,t){var n=e.callbackNode;ug(e,t);var r=ms(e,e===ce?he:0);if(r===0)n!==null&&Eu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Eu(n),t===1)e.tag===0?av(mc.bind(null,e)):pf(mc.bind(null,e)),iv(function(){!(F&6)&&rn()}),n=null;else{switch(Ud(r)){case 1:n=aa;break;case 4:n=zd;break;case 16:n=ps;break;case 536870912:n=Fd;break;default:n=ps}n=hh(n,oh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function oh(e,t){if(Zi=-1,es=0,F&6)throw Error(A(327));var n=e.callbackNode;if(Jn()&&e.callbackNode!==n)return null;var r=ms(e,e===ce?he:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Is(e,r);else{t=r;var i=F;F|=2;var s=ah();(ce!==e||he!==t)&&(Tt=null,sr=te()+500,hn(e,t));do try{Lv();break}catch(l){lh(e,l)}while(!0);Sa(),As.current=s,F=i,se!==null?t=0:(ce=null,he=0,t=ae)}if(t!==0){if(t===2&&(i=ul(e),i!==0&&(r=i,t=Ol(e,i))),t===1)throw n=ai,hn(e,0),Ft(e,r),$e(e,te()),n;if(t===6)Ft(e,r);else{if(i=e.current.alternate,!(r&30)&&!Cv(i)&&(t=Is(e,r),t===2&&(s=ul(e),s!==0&&(r=s,t=Ol(e,s))),t===1))throw n=ai,hn(e,0),Ft(e,r),$e(e,te()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(A(345));case 2:an(e,Ae,Tt);break;case 3:if(Ft(e,r),(r&130023424)===r&&(t=$a+500-te(),10<t)){if(ms(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){ke(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=vl(an.bind(null,e,Ae,Tt),t);break}an(e,Ae,Tt);break;case 4:if(Ft(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-ot(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=te()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Nv(r/1960))-r,10<r){e.timeoutHandle=vl(an.bind(null,e,Ae,Tt),r);break}an(e,Ae,Tt);break;case 5:an(e,Ae,Tt);break;default:throw Error(A(329))}}}return $e(e,te()),e.callbackNode===n?oh.bind(null,e):null}function Ol(e,t){var n=Br;return e.current.memoizedState.isDehydrated&&(hn(e,t).flags|=256),e=Is(e,t),e!==2&&(t=Ae,Ae=n,t!==null&&Dl(t)),e}function Dl(e){Ae===null?Ae=e:Ae.push.apply(Ae,e)}function Cv(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!at(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ft(e,t){for(t&=~Ra,t&=~Ys,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function mc(e){if(F&6)throw Error(A(327));Jn();var t=ms(e,0);if(!(t&1))return $e(e,te()),null;var n=Is(e,t);if(e.tag!==0&&n===2){var r=ul(e);r!==0&&(t=r,n=Ol(e,r))}if(n===1)throw n=ai,hn(e,0),Ft(e,t),$e(e,te()),n;if(n===6)throw Error(A(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,an(e,Ae,Tt),$e(e,te()),null}function Oa(e,t){var n=F;F|=1;try{return e(t)}finally{F=n,F===0&&(sr=te()+500,Xs&&rn())}}function xn(e){Ut!==null&&Ut.tag===0&&!(F&6)&&Jn();var t=F;F|=1;var n=Je.transition,r=H;try{if(Je.transition=null,H=1,e)return e()}finally{H=r,Je.transition=n,F=t,!(F&6)&&rn()}}function Da(){De=Vn.current,W(Vn)}function hn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,rv(n)),se!==null)for(n=se.return;n!==null;){var r=n;switch(ya(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&xs();break;case 3:rr(),W(Pe),W(Se),Ca();break;case 5:Na(r);break;case 4:rr();break;case 13:W(G);break;case 19:W(G);break;case 10:_a(r.type._context);break;case 22:case 23:Da()}n=n.return}if(ce=e,se=e=Gt(e.current,null),he=De=t,ae=0,ai=null,Ra=Ys=wn=0,Ae=Br=null,dn!==null){for(t=0;t<dn.length;t++)if(n=dn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}dn=null}return e}function lh(e,t){do{var n=se;try{if(Sa(),Gi.current=Ls,bs){for(var r=Y.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}bs=!1}if(yn=0,ue=le=Y=null,Hr=!1,si=0,Pa.current=null,n===null||n.return===null){ae=1,ai=t,se=null;break}e:{var s=e,o=n.return,l=n,a=t;if(t=he,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,h=c.tag;if(!(c.mode&1)&&(h===0||h===11||h===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var g=nc(o);if(g!==null){g.flags&=-257,rc(g,o,l,s,t),g.mode&1&&tc(s,u,t),t=g,a=u;var w=t.updateQueue;if(w===null){var y=new Set;y.add(a),t.updateQueue=y}else w.add(a);break e}else{if(!(t&1)){tc(s,u,t),za();break e}a=Error(A(426))}}else if(X&&l.mode&1){var x=nc(o);if(x!==null){!(x.flags&65536)&&(x.flags|=256),rc(x,o,l,s,t),wa(ir(a,l));break e}}s=a=ir(a,l),ae!==4&&(ae=2),Br===null?Br=[s]:Br.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var p=Vf(s,a,t);Qu(s,p);break e;case 1:l=a;var m=s.type,v=s.stateNode;if(!(s.flags&128)&&(typeof m.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(Qt===null||!Qt.has(v)))){s.flags|=65536,t&=-t,s.lanes|=t;var S=Wf(s,l,t);Qu(s,S);break e}}s=s.return}while(s!==null)}ch(n)}catch(C){t=C,se===n&&n!==null&&(se=n=n.return);continue}break}while(!0)}function ah(){var e=As.current;return As.current=Ls,e===null?Ls:e}function za(){(ae===0||ae===3||ae===2)&&(ae=4),ce===null||!(wn&268435455)&&!(Ys&268435455)||Ft(ce,he)}function Is(e,t){var n=F;F|=2;var r=ah();(ce!==e||he!==t)&&(Tt=null,hn(e,t));do try{bv();break}catch(i){lh(e,i)}while(!0);if(Sa(),F=n,As.current=r,se!==null)throw Error(A(261));return ce=null,he=0,ae}function bv(){for(;se!==null;)uh(se)}function Lv(){for(;se!==null&&!eg();)uh(se)}function uh(e){var t=fh(e.alternate,e,De);e.memoizedProps=e.pendingProps,t===null?ch(e):se=t,Pa.current=null}function ch(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=_v(n,t),n!==null){n.flags&=32767,se=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ae=6,se=null;return}}else if(n=Sv(n,t,De),n!==null){se=n;return}if(t=t.sibling,t!==null){se=t;return}se=t=e}while(t!==null);ae===0&&(ae=5)}function an(e,t,n){var r=H,i=Je.transition;try{Je.transition=null,H=1,Av(e,t,n,r)}finally{Je.transition=i,H=r}return null}function Av(e,t,n,r){do Jn();while(Ut!==null);if(F&6)throw Error(A(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(A(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(cg(e,s),e===ce&&(se=ce=null,he=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||$i||($i=!0,hh(ps,function(){return Jn(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=Je.transition,Je.transition=null;var o=H;H=1;var l=F;F|=4,Pa.current=null,Tv(e,n),ih(n,e),Gg(ml),gs=!!pl,ml=pl=null,e.current=n,kv(n),tg(),F=l,H=o,Je.transition=s}else e.current=n;if($i&&($i=!1,Ut=e,Ms=i),s=e.pendingLanes,s===0&&(Qt=null),ig(n.stateNode),$e(e,te()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(js)throw js=!1,e=Rl,Rl=null,e;return Ms&1&&e.tag!==0&&Jn(),s=e.pendingLanes,s&1?e===$l?Vr++:(Vr=0,$l=e):Vr=0,rn(),null}function Jn(){if(Ut!==null){var e=Ud(Ms),t=Je.transition,n=H;try{if(Je.transition=null,H=16>e?16:e,Ut===null)var r=!1;else{if(e=Ut,Ut=null,Ms=0,F&6)throw Error(A(331));var i=F;for(F|=4,P=e.current;P!==null;){var s=P,o=s.child;if(P.flags&16){var l=s.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(P=u;P!==null;){var c=P;switch(c.tag){case 0:case 11:case 15:Ur(8,c,s)}var h=c.child;if(h!==null)h.return=c,P=h;else for(;P!==null;){c=P;var f=c.sibling,g=c.return;if(th(c),c===u){P=null;break}if(f!==null){f.return=g,P=f;break}P=g}}}var w=s.alternate;if(w!==null){var y=w.child;if(y!==null){w.child=null;do{var x=y.sibling;y.sibling=null,y=x}while(y!==null)}}P=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,P=o;else e:for(;P!==null;){if(s=P,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Ur(9,s,s.return)}var p=s.sibling;if(p!==null){p.return=s.return,P=p;break e}P=s.return}}var m=e.current;for(P=m;P!==null;){o=P;var v=o.child;if(o.subtreeFlags&2064&&v!==null)v.return=o,P=v;else e:for(o=m;P!==null;){if(l=P,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Gs(9,l)}}catch(C){ee(l,l.return,C)}if(l===o){P=null;break e}var S=l.sibling;if(S!==null){S.return=l.return,P=S;break e}P=l.return}}if(F=i,rn(),gt&&typeof gt.onPostCommitFiberRoot=="function")try{gt.onPostCommitFiberRoot(Us,e)}catch{}r=!0}return r}finally{H=n,Je.transition=t}}return!1}function gc(e,t,n){t=ir(n,t),t=Vf(e,t,1),e=Xt(e,t,1),t=ke(),e!==null&&(fi(e,1,t),$e(e,t))}function ee(e,t,n){if(e.tag===3)gc(e,e,n);else for(;t!==null;){if(t.tag===3){gc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Qt===null||!Qt.has(r))){e=ir(n,e),e=Wf(t,e,1),t=Xt(t,e,1),e=ke(),t!==null&&(fi(t,1,e),$e(t,e));break}}t=t.return}}function jv(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ke(),e.pingedLanes|=e.suspendedLanes&n,ce===e&&(he&n)===n&&(ae===4||ae===3&&(he&130023424)===he&&500>te()-$a?hn(e,0):Ra|=n),$e(e,t)}function dh(e,t){t===0&&(e.mode&1?(t=Ni,Ni<<=1,!(Ni&130023424)&&(Ni=4194304)):t=1);var n=ke();e=jt(e,t),e!==null&&(fi(e,t,n),$e(e,n))}function Mv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),dh(e,n)}function Iv(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(A(314))}r!==null&&r.delete(t),dh(e,n)}var fh;fh=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Pe.current)Ie=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ie=!1,xv(e,t,n);Ie=!!(e.flags&131072)}else Ie=!1,X&&t.flags&1048576&&mf(t,Es,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ji(e,t),e=t.pendingProps;var i=er(t,Se.current);Yn(t,n),i=La(null,t,r,e,i,n);var s=Aa();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Re(r)?(s=!0,Ss(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Ta(t),i.updater=Qs,t.stateNode=i,i._reactInternals=t,Tl(t,r,e,n),t=Cl(null,t,r,!0,s,n)):(t.tag=0,X&&s&&va(t),_e(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ji(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=Rv(r),e=rt(r,e),i){case 0:t=Nl(null,t,r,e,n);break e;case 1:t=oc(null,t,r,e,n);break e;case 11:t=ic(null,t,r,e,n);break e;case 14:t=sc(null,t,r,rt(r.type,e),n);break e}throw Error(A(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:rt(r,i),Nl(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:rt(r,i),oc(e,t,r,i,n);case 3:e:{if(Kf(t),e===null)throw Error(A(387));r=t.pendingProps,s=t.memoizedState,i=s.element,wf(e,t),Ns(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=ir(Error(A(423)),t),t=lc(e,t,r,n,i);break e}else if(r!==i){i=ir(Error(A(424)),t),t=lc(e,t,r,n,i);break e}else for(ze=qt(t.stateNode.containerInfo.firstChild),Fe=t,X=!0,st=null,n=Ef(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(tr(),r===i){t=Mt(e,t,n);break e}_e(e,t,r,n)}t=t.child}return t;case 5:return Tf(t),e===null&&Sl(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,gl(r,i)?o=null:s!==null&&gl(r,s)&&(t.flags|=32),Qf(e,t),_e(e,t,o,n),t.child;case 6:return e===null&&Sl(t),null;case 13:return Gf(e,t,n);case 4:return ka(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=nr(t,null,r,n):_e(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:rt(r,i),ic(e,t,r,i,n);case 7:return _e(e,t,t.pendingProps,n),t.child;case 8:return _e(e,t,t.pendingProps.children,n),t.child;case 12:return _e(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,U(Ts,r._currentValue),r._currentValue=o,s!==null)if(at(s.value,o)){if(s.children===i.children&&!Pe.current){t=Mt(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var l=s.dependencies;if(l!==null){o=s.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(s.tag===1){a=bt(-1,n&-n),a.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),_l(s.return,n,t),l.lanes|=n;break}a=a.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(A(341));o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),_l(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}_e(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Yn(t,n),i=Ze(i),r=r(i),t.flags|=1,_e(e,t,r,n),t.child;case 14:return r=t.type,i=rt(r,t.pendingProps),i=rt(r.type,i),sc(e,t,r,i,n);case 15:return qf(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:rt(r,i),Ji(e,t),t.tag=1,Re(r)?(e=!0,Ss(t)):e=!1,Yn(t,n),Sf(t,r,i),Tl(t,r,i,n),Cl(null,t,r,!0,e,n);case 19:return Yf(e,t,n);case 22:return Xf(e,t,n)}throw Error(A(156,t.tag))};function hh(e,t){return Dd(e,t)}function Pv(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ke(e,t,n,r){return new Pv(e,t,n,r)}function Fa(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Rv(e){if(typeof e=="function")return Fa(e)?1:0;if(e!=null){if(e=e.$$typeof,e===sa)return 11;if(e===oa)return 14}return 2}function Gt(e,t){var n=e.alternate;return n===null?(n=Ke(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ts(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")Fa(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Pn:return pn(n.children,i,s,t);case ia:o=8,i|=8;break;case Qo:return e=Ke(12,n,t,i|2),e.elementType=Qo,e.lanes=s,e;case Ko:return e=Ke(13,n,t,i),e.elementType=Ko,e.lanes=s,e;case Go:return e=Ke(19,n,t,i),e.elementType=Go,e.lanes=s,e;case Sd:return Js(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case wd:o=10;break e;case xd:o=9;break e;case sa:o=11;break e;case oa:o=14;break e;case Rt:o=16,r=null;break e}throw Error(A(130,e==null?e:typeof e,""))}return t=Ke(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function pn(e,t,n,r){return e=Ke(7,e,r,t),e.lanes=n,e}function Js(e,t,n,r){return e=Ke(22,e,r,t),e.elementType=Sd,e.lanes=n,e.stateNode={isHidden:!1},e}function $o(e,t,n){return e=Ke(6,e,null,t),e.lanes=n,e}function Oo(e,t,n){return t=Ke(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function $v(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=vo(0),this.expirationTimes=vo(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vo(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Ha(e,t,n,r,i,s,o,l,a){return e=new $v(e,t,n,l,a),t===1?(t=1,s===!0&&(t|=8)):t=0,s=Ke(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ta(s),e}function Ov(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:In,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function ph(e){if(!e)return Zt;e=e._reactInternals;e:{if(Tn(e)!==e||e.tag!==1)throw Error(A(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Re(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(A(171))}if(e.tag===1){var n=e.type;if(Re(n))return hf(e,n,t)}return t}function mh(e,t,n,r,i,s,o,l,a){return e=Ha(n,r,!0,e,i,s,o,l,a),e.context=ph(null),n=e.current,r=ke(),i=Kt(n),s=bt(r,i),s.callback=t??null,Xt(n,s,i),e.current.lanes=i,fi(e,i,r),$e(e,r),e}function Zs(e,t,n,r){var i=t.current,s=ke(),o=Kt(i);return n=ph(n),t.context===null?t.context=n:t.pendingContext=n,t=bt(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Xt(i,t,o),e!==null&&(lt(e,i,o,s),Ki(e,i,o)),o}function Ps(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function vc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ua(e,t){vc(e,t),(e=e.alternate)&&vc(e,t)}function Dv(){return null}var gh=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ba(e){this._internalRoot=e}eo.prototype.render=Ba.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(A(409));Zs(e,t,null,null)};eo.prototype.unmount=Ba.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;xn(function(){Zs(null,e,null,null)}),t[At]=null}};function eo(e){this._internalRoot=e}eo.prototype.unstable_scheduleHydration=function(e){if(e){var t=Wd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<zt.length&&t!==0&&t<zt[n].priority;n++);zt.splice(n,0,e),n===0&&Xd(e)}};function Va(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function to(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function yc(){}function zv(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=Ps(o);s.call(u)}}var o=mh(t,r,e,0,null,!1,!1,"",yc);return e._reactRootContainer=o,e[At]=o.current,ei(e.nodeType===8?e.parentNode:e),xn(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=Ps(a);l.call(u)}}var a=Ha(e,0,!1,null,null,!1,!1,"",yc);return e._reactRootContainer=a,e[At]=a.current,ei(e.nodeType===8?e.parentNode:e),xn(function(){Zs(t,a,n,r)}),a}function no(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var l=i;i=function(){var a=Ps(o);l.call(a)}}Zs(t,o,e,i)}else o=zv(n,t,e,i,r);return Ps(o)}Bd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Ar(t.pendingLanes);n!==0&&(ua(t,n|1),$e(t,te()),!(F&6)&&(sr=te()+500,rn()))}break;case 13:xn(function(){var r=jt(e,1);if(r!==null){var i=ke();lt(r,e,1,i)}}),Ua(e,1)}};ca=function(e){if(e.tag===13){var t=jt(e,134217728);if(t!==null){var n=ke();lt(t,e,134217728,n)}Ua(e,134217728)}};Vd=function(e){if(e.tag===13){var t=Kt(e),n=jt(e,t);if(n!==null){var r=ke();lt(n,e,t,r)}Ua(e,t)}};Wd=function(){return H};qd=function(e,t){var n=H;try{return H=e,t()}finally{H=n}};ol=function(e,t,n){switch(t){case"input":if(Zo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=qs(r);if(!i)throw Error(A(90));Ed(r),Zo(r,i)}}}break;case"textarea":kd(e,n);break;case"select":t=n.value,t!=null&&Xn(e,!!n.multiple,t,!1)}};Md=Oa;Id=xn;var Fv={usingClientEntryPoint:!1,Events:[pi,Dn,qs,Ad,jd,Oa]},_r={findFiberByHostInstance:cn,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},Hv={bundleType:_r.bundleType,version:_r.version,rendererPackageName:_r.rendererPackageName,rendererConfig:_r.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:It.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=$d(e),e===null?null:e.stateNode},findFiberByHostInstance:_r.findFiberByHostInstance||Dv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Oi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Oi.isDisabled&&Oi.supportsFiber)try{Us=Oi.inject(Hv),gt=Oi}catch{}}Be.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Fv;Be.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Va(t))throw Error(A(200));return Ov(e,t,null,n)};Be.createRoot=function(e,t){if(!Va(e))throw Error(A(299));var n=!1,r="",i=gh;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Ha(e,1,!1,null,null,n,!1,r,i),e[At]=t.current,ei(e.nodeType===8?e.parentNode:e),new Ba(t)};Be.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(A(188)):(e=Object.keys(e).join(","),Error(A(268,e)));return e=$d(t),e=e===null?null:e.stateNode,e};Be.flushSync=function(e){return xn(e)};Be.hydrate=function(e,t,n){if(!to(t))throw Error(A(200));return no(null,e,t,!0,n)};Be.hydrateRoot=function(e,t,n){if(!Va(e))throw Error(A(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=gh;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=mh(t,null,e,1,n??null,i,!1,s,o),e[At]=t.current,ei(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new eo(t)};Be.render=function(e,t,n){if(!to(t))throw Error(A(200));return no(null,e,t,!1,n)};Be.unmountComponentAtNode=function(e){if(!to(e))throw Error(A(40));return e._reactRootContainer?(xn(function(){no(null,null,e,!1,function(){e._reactRootContainer=null,e[At]=null})}),!0):!1};Be.unstable_batchedUpdates=Oa;Be.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!to(n))throw Error(A(200));if(e==null||e._reactInternals===void 0)throw Error(A(38));return no(e,t,n,!1,r)};Be.version="18.2.0-next-9e3b772b8-20220608";function vh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(vh)}catch(e){console.error(e)}}vh(),pd.exports=Be;var D1=pd.exports;const en=({children:e,title:t="",icon:n,disabled:r=!1,toggled:i=!1,onClick:s=()=>{},style:o,testId:l,className:a})=>(a=(a||"")+` toolbar-button ${n}`,i&&(a+=" toggled"),d.jsxs("button",{className:a,onMouseDown:wc,onClick:s,onDoubleClick:wc,title:t,disabled:!!r,style:o,"data-testId":l,children:[n&&d.jsx("span",{className:`codicon codicon-${n}`,style:e?{marginRight:5}:{}}),e]})),wc=e=>{e.stopPropagation(),e.preventDefault()},Mr=Symbol("context"),yh=Symbol("next"),wh=Symbol("prev"),xc=Symbol("events");class z1{constructor(t){B(this,"startTime");B(this,"endTime");B(this,"browserName");B(this,"channel");B(this,"platform");B(this,"wallTime");B(this,"title");B(this,"options");B(this,"pages");B(this,"actions");B(this,"events");B(this,"stdio");B(this,"errors");B(this,"errorDescriptors");B(this,"hasSource");B(this,"hasStepData");B(this,"sdkLanguage");B(this,"testIdAttributeName");B(this,"sources");B(this,"resources");t.forEach(r=>Uv(r));const n=t.find(r=>r.origin==="library");this.browserName=(n==null?void 0:n.browserName)||"",this.sdkLanguage=n==null?void 0:n.sdkLanguage,this.channel=n==null?void 0:n.channel,this.testIdAttributeName=n==null?void 0:n.testIdAttributeName,this.platform=(n==null?void 0:n.platform)||"",this.title=(n==null?void 0:n.title)||"",this.options=(n==null?void 0:n.options)||{},this.actions=Bv(t),this.pages=[].concat(...t.map(r=>r.pages)),this.wallTime=t.map(r=>r.wallTime).reduce((r,i)=>Math.min(r||Number.MAX_VALUE,i),Number.MAX_VALUE),this.startTime=t.map(r=>r.startTime).reduce((r,i)=>Math.min(r,i),Number.MAX_VALUE),this.endTime=t.map(r=>r.endTime).reduce((r,i)=>Math.max(r,i),Number.MIN_VALUE),this.events=[].concat(...t.map(r=>r.events)),this.stdio=[].concat(...t.map(r=>r.stdio)),this.errors=[].concat(...t.map(r=>r.errors)),this.hasSource=t.some(r=>r.hasSource),this.hasStepData=t.some(r=>r.origin==="testRunner"),this.resources=[...t.map(r=>r.resources)].flat(),this.events.sort((r,i)=>r.time-i.time),this.resources.sort((r,i)=>r._monotonicTime-i._monotonicTime),this.errorDescriptors=this.hasStepData?this._errorDescriptorsFromTestRunner():this._errorDescriptorsFromActions(),this.sources=Zv(this.actions,this.errorDescriptors)}failedAction(){return this.actions.findLast(t=>t.error)}_errorDescriptorsFromActions(){var n;const t=[];for(const r of this.actions||[])(n=r.error)!=null&&n.message&&t.push({action:r,stack:r.stack,message:r.error.message});return t}_errorDescriptorsFromTestRunner(){const t=[];for(const n of this.errors||[])n.message&&t.push({stack:n.stack,message:n.message});return t}}function Uv(e){for(const n of e.pages)n[Mr]=e;for(let n=0;n<e.actions.length;++n){const r=e.actions[n];r[Mr]=e}let t;for(let n=e.actions.length-1;n>=0;n--){const r=e.actions[n];r[yh]=t,r.apiName.includes("route.")||(t=r)}for(const n of e.events)n[Mr]=e;for(const n of e.resources)n[Mr]=e}function Bv(e){const t=new Map;for(const i of e){const s=i.traceUrl;let o=t.get(s);o||(o=[],t.set(s,o)),o.push(i)}const n=[];let r=0;for(const[,i]of t){t.size>1&&Vv(i,++r);const s=Wv(i);n.push(...s)}n.sort((i,s)=>s.parentId===i.callId?-1:i.parentId===s.callId?1:i.startTime-s.startTime);for(let i=1;i<n.length;++i)n[i][wh]=n[i-1];return n}function Vv(e,t){for(const n of e)for(const r of n.actions)r.callId&&(r.callId=`${t}:${r.callId}`),r.parentId&&(r.parentId=`${t}:${r.parentId}`)}function Wv(e){const t=new Map,n=e.filter(l=>l.origin==="library"),r=e.filter(l=>l.origin==="testRunner");if(!r.length||!n.length)return e.map(l=>l.actions.map(a=>({...a,context:l}))).flat();const i=n.some(l=>l.actions.some(a=>!!a.stepId));for(const l of n)for(const a of l.actions){const u=i?a.stepId:`${a.apiName}@${a.wallTime}`;t.set(u,{...a,context:l})}const s=Xv(r,t,i);s&&qv(n,s);const o=new Map;for(const l of r)for(const a of l.actions){const u=i?a.callId:`${a.apiName}@${a.wallTime}`,c=t.get(u);if(c){o.set(a.callId,c.callId),a.error&&(c.error=a.error),a.attachments&&(c.attachments=a.attachments),a.parentId&&(c.parentId=o.get(a.parentId)??a.parentId),c.startTime=a.startTime,c.endTime=a.endTime;continue}a.parentId&&(a.parentId=o.get(a.parentId)??a.parentId),t.set(u,{...a,context:l})}return[...t.values()]}function qv(e,t){for(const n of e){n.startTime+=t,n.endTime+=t;for(const r of n.actions)r.startTime&&(r.startTime+=t),r.endTime&&(r.endTime+=t);for(const r of n.events)r.time+=t;for(const r of n.stdio)r.timestamp+=t;for(const r of n.pages)for(const i of r.screencastFrames)i.timestamp+=t;for(const r of n.resources)r._monotonicTime&&(r._monotonicTime+=t)}}function Xv(e,t,n){for(const r of e)for(const i of r.actions){if(!i.startTime)continue;const s=n?i.stepId:`${i.apiName}@${i.wallTime}`,o=t.get(s);if(o)return i.startTime-o.startTime}return 0}function Qv(e){const t=new Map;for(const r of e)t.set(r.callId,{id:r.callId,parent:void 0,children:[],action:r});const n={id:"",parent:void 0,children:[]};for(const r of t.values()){const i=r.action.parentId&&t.get(r.action.parentId)||n;i.children.push(r),r.parent=i}return{rootItem:n,itemMap:t}}function F1(e){return`${e.pageId||"none"}:${e.callId}`}function Rs(e){return e[Mr]}function Kv(e){return e[yh]}function Gv(e){return e[wh]}function Yv(e){let t=0,n=0;for(const r of Jv(e)){if(r.type==="console"){const i=r.messageType;i==="warning"?++n:i==="error"&&++t}r.type==="event"&&r.method==="pageError"&&++t}return{errors:t,warnings:n}}function Jv(e){let t=e[xc];if(t)return t;const n=Kv(e);return t=Rs(e).events.filter(r=>r.time>=e.startTime&&(!n||r.time<n.startTime)),e[xc]=t,t}function Zv(e,t){var r;const n=new Map;for(const i of e)for(const s of i.stack||[]){let o=n.get(s.file);o||(o={errors:[],content:void 0},n.set(s.file,o))}for(const i of t){const{action:s,stack:o,message:l}=i;!s||!o||(r=n.get(o[0].file))==null||r.errors.push({line:o[0].line||0,message:l})}return n}const ns=new Set(["page.route","page.routefromhar","page.unroute","page.unrouteall","browsercontext.route","browsercontext.routefromhar","browsercontext.unroute","browsercontext.unrouteall"]);{for(const e of[...ns])ns.add(e+"async");for(const e of["page.route_from_har","page.unroute_all","context.route_from_har","context.unroute_all"])ns.add(e)}function e0(e){return e.class==="Route"||ns.has(e.apiName.toLowerCase())}const t0=50,$s=({sidebarSize:e,sidebarHidden:t=!1,sidebarIsFirst:n=!1,orientation:r="vertical",minSidebarSize:i=t0,settingName:s,children:o})=>{const[l,a]=Rr(s?s+"."+r+":size":void 0,Math.max(i,e)*window.devicePixelRatio),[u,c]=Rr(s?s+"."+r+":size":void 0,Math.max(i,e)*window.devicePixelRatio),[h,f]=b.useState(null),[g,w]=_n();let y;r==="vertical"?(y=u/window.devicePixelRatio,g&&g.height<y&&(y=g.height-10)):(y=l/window.devicePixelRatio,g&&g.width<y&&(y=g.width-10));const x=b.Children.toArray(o);document.body.style.userSelect=h?"none":"inherit";let p={};return r==="vertical"?n?p={top:h?0:y-4,bottom:h?0:void 0,height:h?"initial":8}:p={bottom:h?0:y-4,top:h?0:void 0,height:h?"initial":8}:n?p={left:h?0:y-4,right:h?0:void 0,width:h?"initial":8}:p={right:h?0:y-4,left:h?0:void 0,width:h?"initial":8},d.jsxs("div",{className:"split-view "+r+(n?" sidebar-first":""),ref:w,children:[d.jsx("div",{className:"split-view-main",children:x[0]}),!t&&d.jsx("div",{style:{flexBasis:y},className:"split-view-sidebar",children:x[1]}),!t&&d.jsx("div",{style:p,className:"split-view-resizer",onMouseDown:m=>f({offset:r==="vertical"?m.clientY:m.clientX,size:y}),onMouseUp:()=>f(null),onMouseMove:m=>{if(!m.buttons)f(null);else if(h){const S=(r==="vertical"?m.clientY:m.clientX)-h.offset,C=n?h.size+S:h.size-S,N=m.target.parentElement.getBoundingClientRect(),j=Math.min(Math.max(i,C),(r==="vertical"?N.height:N.width)-i);r==="vertical"?c(j*window.devicePixelRatio):a(j*window.devicePixelRatio)}}})]})};function ro(e,t="'"){const n=JSON.stringify(e),r=n.substring(1,n.length-1).replace(/\\"/g,'"');if(t==="'")return t+r.replace(/[']/g,"\\'")+t;if(t==='"')return t+r.replace(/["]/g,'\\"')+t;if(t==="`")return t+r.replace(/[`]/g,"`")+t;throw new Error("Invalid escape char")}function Os(e){return e.charAt(0).toUpperCase()+e.substring(1)}function xh(e){return e.replace(/([a-z0-9])([A-Z])/g,"$1_$2").replace(/([A-Z])([A-Z][a-z])/g,"$1_$2").toLowerCase()}function Qe(e){let t="";for(let n=0;n<e.length;n++)t+=n0(e,n);return t}function Er(e){return`"${Qe(e).replace(/\\ /g," ")}"`}function n0(e,t){const n=e.charCodeAt(t);return n===0?"�":n>=1&&n<=31||n>=48&&n<=57&&(t===0||t===1&&e.charCodeAt(0)===45)?"\\"+n.toString(16)+" ":t===0&&n===45&&e.length===1?"\\"+e.charAt(t):n>=128||n===45||n===95||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122?e.charAt(t):"\\"+e.charAt(t)}let un;function r0(){un=new Map}function Ue(e){let t=un==null?void 0:un.get(e);return t===void 0&&(t=e.replace(/\u200b/g,"").trim().replace(/\s+/g," "),un==null||un.set(e,t)),t}function io(e){return e.replace(/(^|[^\\])(\\\\)*\\(['"`])/g,"$1$2$3")}function Sh(e){return e.unicode||e.unicodeSets?String(e):String(e).replace(/(^|[^\\])(\\\\)*(["'`])/g,"$1$2\\$3").replace(/>>/g,"\\>\\>")}function Ge(e,t){return typeof e!="string"?Sh(e):`${JSON.stringify(e)}${t?"s":"i"}`}function Ee(e,t){return typeof e!="string"?Sh(e):`"${e.replace(/\\/g,"\\\\").replace(/["]/g,'\\"')}"${t?"s":"i"}`}function i0(e,t,n=""){if(e.length<=t)return e;const r=[...e];return r.length>t?r.slice(0,t-n.length).join("")+n:r.join("")}function Sc(e,t){return i0(e,t,"…")}function s0(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}const ie=function(e,t,n){return e>=t&&e<=n};function Le(e){return ie(e,48,57)}function _c(e){return Le(e)||ie(e,65,70)||ie(e,97,102)}function o0(e){return ie(e,65,90)}function l0(e){return ie(e,97,122)}function a0(e){return o0(e)||l0(e)}function u0(e){return e>=128}function rs(e){return a0(e)||u0(e)||e===95}function Ec(e){return rs(e)||Le(e)||e===45}function c0(e){return ie(e,0,8)||e===11||ie(e,14,31)||e===127}function is(e){return e===10}function St(e){return is(e)||e===9||e===32}const d0=1114111;class Wa extends Error{constructor(t){super(t),this.name="InvalidCharacterError"}}function f0(e){const t=[];for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);if(r===13&&e.charCodeAt(n+1)===10&&(r=10,n++),(r===13||r===12)&&(r=10),r===0&&(r=65533),ie(r,55296,56319)&&ie(e.charCodeAt(n+1),56320,57343)){const i=r-55296,s=e.charCodeAt(n+1)-56320;r=Math.pow(2,16)+i*Math.pow(2,10)+s,n++}t.push(r)}return t}function oe(e){if(e<=65535)return String.fromCharCode(e);e-=Math.pow(2,16);const t=Math.floor(e/Math.pow(2,10))+55296,n=e%Math.pow(2,10)+56320;return String.fromCharCode(t)+String.fromCharCode(n)}function h0(e){const t=f0(e);let n=-1;const r=[];let i;const s=function(k){return k>=t.length?-1:t[k]},o=function(k){if(k===void 0&&(k=1),k>3)throw"Spec Error: no more than three codepoints of lookahead.";return s(n+k)},l=function(k){return k===void 0&&(k=1),n+=k,i=s(n),!0},a=function(){return n-=1,!0},u=function(k){return k===void 0&&(k=i),k===-1},c=function(){if(h(),l(),St(i)){for(;St(o());)l();return new zl}else{if(i===34)return w();if(i===35)if(Ec(o())||p(o(1),o(2))){const k=new $h("");return v(o(1),o(2),o(3))&&(k.type="id"),k.value=N(),k}else return new ye(i);else return i===36?o()===61?(l(),new v0):new ye(i):i===39?w():i===40?new jh:i===41?new Mh:i===42?o()===61?(l(),new y0):new ye(i):i===43?E()?(a(),f()):new ye(i):i===44?new Ch:i===45?E()?(a(),f()):o(1)===45&&o(2)===62?(l(2),new Th):S()?(a(),g()):new ye(i):i===46?E()?(a(),f()):new ye(i):i===58?new kh:i===59?new Nh:i===60?o(1)===33&&o(2)===45&&o(3)===45?(l(3),new Eh):new ye(i):i===64?v(o(1),o(2),o(3))?new Rh(N()):new ye(i):i===91?new Ah:i===92?m()?(a(),g()):new ye(i):i===93?new Fl:i===94?o()===61?(l(),new g0):new ye(i):i===123?new bh:i===124?o()===61?(l(),new m0):o()===124?(l(),new Ih):new ye(i):i===125?new Lh:i===126?o()===61?(l(),new p0):new ye(i):Le(i)?(a(),f()):rs(i)?(a(),g()):u()?new os:new ye(i)}},h=function(){for(;o(1)===47&&o(2)===42;)for(l(2);;)if(l(),i===42&&o()===47){l();break}else if(u())return},f=function(){const k=j();if(v(o(1),o(2),o(3))){const M=new w0;return M.value=k.value,M.repr=k.repr,M.type=k.type,M.unit=N(),M}else if(o()===37){l();const M=new Fh;return M.value=k.value,M.repr=k.repr,M}else{const M=new zh;return M.value=k.value,M.repr=k.repr,M.type=k.type,M}},g=function(){const k=N();if(k.toLowerCase()==="url"&&o()===40){for(l();St(o(1))&&St(o(2));)l();return o()===34||o()===39?new ls(k):St(o())&&(o(2)===34||o(2)===39)?new ls(k):y()}else return o()===40?(l(),new ls(k)):new Ph(k)},w=function(k){k===void 0&&(k=i);let M="";for(;l();){if(i===k||u())return new Oh(M);if(is(i))return a(),new _h;i===92?u(o())||(is(o())?l():M+=oe(x())):M+=oe(i)}throw new Error("Internal error")},y=function(){const k=new Dh("");for(;St(o());)l();if(u(o()))return k;for(;l();){if(i===41||u())return k;if(St(i)){for(;St(o());)l();return o()===41||u(o())?(l(),k):(T(),new ss)}else{if(i===34||i===39||i===40||c0(i))return T(),new ss;if(i===92)if(m())k.value+=oe(x());else return T(),new ss;else k.value+=oe(i)}}throw new Error("Internal error")},x=function(){if(l(),_c(i)){const k=[i];for(let $=0;$<5&&_c(o());$++)l(),k.push(i);St(o())&&l();let M=parseInt(k.map(function($){return String.fromCharCode($)}).join(""),16);return M>d0&&(M=65533),M}else return u()?65533:i},p=function(k,M){return!(k!==92||is(M))},m=function(){return p(i,o())},v=function(k,M,$){return k===45?rs(M)||M===45||p(M,$):rs(k)?!0:k===92?p(k,M):!1},S=function(){return v(i,o(1),o(2))},C=function(k,M,$){return k===43||k===45?!!(Le(M)||M===46&&Le($)):k===46?!!Le(M):!!Le(k)},E=function(){return C(i,o(1),o(2))},N=function(){let k="";for(;l();)if(Ec(i))k+=oe(i);else if(m())k+=oe(x());else return a(),k;throw new Error("Internal parse error")},j=function(){let k="",M="integer";for((o()===43||o()===45)&&(l(),k+=oe(i));Le(o());)l(),k+=oe(i);if(o(1)===46&&Le(o(2)))for(l(),k+=oe(i),l(),k+=oe(i),M="number";Le(o());)l(),k+=oe(i);const $=o(1),Q=o(2),Oe=o(3);if(($===69||$===101)&&Le(Q))for(l(),k+=oe(i),l(),k+=oe(i),M="number";Le(o());)l(),k+=oe(i);else if(($===69||$===101)&&(Q===43||Q===45)&&Le(Oe))for(l(),k+=oe(i),l(),k+=oe(i),l(),k+=oe(i),M="number";Le(o());)l(),k+=oe(i);const me=_(k);return{type:M,value:me,repr:k}},_=function(k){return+k},T=function(){for(;l();){if(i===41||u())return;m()&&x()}};let L=0;for(;!u(o());)if(r.push(c()),L++,L>t.length*2)throw new Error("I'm infinite-looping!");return r}class ne{constructor(){this.tokenType=""}toJSON(){return{token:this.tokenType}}toString(){return this.tokenType}toSource(){return""+this}}class _h extends ne{constructor(){super(...arguments),this.tokenType="BADSTRING"}}class ss extends ne{constructor(){super(...arguments),this.tokenType="BADURL"}}class zl extends ne{constructor(){super(...arguments),this.tokenType="WHITESPACE"}toString(){return"WS"}toSource(){return" "}}class Eh extends ne{constructor(){super(...arguments),this.tokenType="CDO"}toSource(){return"<!--"}}class Th extends ne{constructor(){super(...arguments),this.tokenType="CDC"}toSource(){return"-->"}}class kh extends ne{constructor(){super(...arguments),this.tokenType=":"}}class Nh extends ne{constructor(){super(...arguments),this.tokenType=";"}}class Ch extends ne{constructor(){super(...arguments),this.tokenType=","}}class ur extends ne{constructor(){super(...arguments),this.value="",this.mirror=""}}class bh extends ur{constructor(){super(),this.tokenType="{",this.value="{",this.mirror="}"}}class Lh extends ur{constructor(){super(),this.tokenType="}",this.value="}",this.mirror="{"}}class Ah extends ur{constructor(){super(),this.tokenType="[",this.value="[",this.mirror="]"}}class Fl extends ur{constructor(){super(),this.tokenType="]",this.value="]",this.mirror="["}}class jh extends ur{constructor(){super(),this.tokenType="(",this.value="(",this.mirror=")"}}class Mh extends ur{constructor(){super(),this.tokenType=")",this.value=")",this.mirror="("}}class p0 extends ne{constructor(){super(...arguments),this.tokenType="~="}}class m0 extends ne{constructor(){super(...arguments),this.tokenType="|="}}class g0 extends ne{constructor(){super(...arguments),this.tokenType="^="}}class v0 extends ne{constructor(){super(...arguments),this.tokenType="$="}}class y0 extends ne{constructor(){super(...arguments),this.tokenType="*="}}class Ih extends ne{constructor(){super(...arguments),this.tokenType="||"}}class os extends ne{constructor(){super(...arguments),this.tokenType="EOF"}toSource(){return""}}class ye extends ne{constructor(t){super(),this.tokenType="DELIM",this.value="",this.value=oe(t)}toString(){return"DELIM("+this.value+")"}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t}toSource(){return this.value==="\\"?`\\
`:this.value}}class cr extends ne{constructor(){super(...arguments),this.value=""}ASCIIMatch(t){return this.value.toLowerCase()===t.toLowerCase()}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t}}class Ph extends cr{constructor(t){super(),this.tokenType="IDENT",this.value=t}toString(){return"IDENT("+this.value+")"}toSource(){return gi(this.value)}}class ls extends cr{constructor(t){super(),this.tokenType="FUNCTION",this.value=t,this.mirror=")"}toString(){return"FUNCTION("+this.value+")"}toSource(){return gi(this.value)+"("}}class Rh extends cr{constructor(t){super(),this.tokenType="AT-KEYWORD",this.value=t}toString(){return"AT("+this.value+")"}toSource(){return"@"+gi(this.value)}}class $h extends cr{constructor(t){super(),this.tokenType="HASH",this.value=t,this.type="unrestricted"}toString(){return"HASH("+this.value+")"}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t.type=this.type,t}toSource(){return this.type==="id"?"#"+gi(this.value):"#"+x0(this.value)}}class Oh extends cr{constructor(t){super(),this.tokenType="STRING",this.value=t}toString(){return'"'+Hh(this.value)+'"'}}class Dh extends cr{constructor(t){super(),this.tokenType="URL",this.value=t}toString(){return"URL("+this.value+")"}toSource(){return'url("'+Hh(this.value)+'")'}}class zh extends ne{constructor(){super(),this.tokenType="NUMBER",this.type="integer",this.repr=""}toString(){return this.type==="integer"?"INT("+this.value+")":"NUMBER("+this.value+")"}toJSON(){const t=super.toJSON();return t.value=this.value,t.type=this.type,t.repr=this.repr,t}toSource(){return this.repr}}class Fh extends ne{constructor(){super(),this.tokenType="PERCENTAGE",this.repr=""}toString(){return"PERCENTAGE("+this.value+")"}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t.repr=this.repr,t}toSource(){return this.repr+"%"}}class w0 extends ne{constructor(){super(),this.tokenType="DIMENSION",this.type="integer",this.repr="",this.unit=""}toString(){return"DIM("+this.value+","+this.unit+")"}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t.type=this.type,t.repr=this.repr,t.unit=this.unit,t}toSource(){const t=this.repr;let n=gi(this.unit);return n[0].toLowerCase()==="e"&&(n[1]==="-"||ie(n.charCodeAt(1),48,57))&&(n="\\65 "+n.slice(1,n.length)),t+n}}function gi(e){e=""+e;let t="";const n=e.charCodeAt(0);for(let r=0;r<e.length;r++){const i=e.charCodeAt(r);if(i===0)throw new Wa("Invalid character: the input contains U+0000.");ie(i,1,31)||i===127||r===0&&ie(i,48,57)||r===1&&ie(i,48,57)&&n===45?t+="\\"+i.toString(16)+" ":i>=128||i===45||i===95||ie(i,48,57)||ie(i,65,90)||ie(i,97,122)?t+=e[r]:t+="\\"+e[r]}return t}function x0(e){e=""+e;let t="";for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);if(r===0)throw new Wa("Invalid character: the input contains U+0000.");r>=128||r===45||r===95||ie(r,48,57)||ie(r,65,90)||ie(r,97,122)?t+=e[n]:t+="\\"+r.toString(16)+" "}return t}function Hh(e){e=""+e;let t="";for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);if(r===0)throw new Wa("Invalid character: the input contains U+0000.");ie(r,1,31)||r===127?t+="\\"+r.toString(16)+" ":r===34||r===92?t+="\\"+e[n]:t+=e[n]}return t}class je extends Error{}function S0(e,t){let n;try{n=h0(e),n[n.length-1]instanceof os||n.push(new os)}catch(_){const T=_.message+` while parsing selector "${e}"`,L=(_.stack||"").indexOf(_.message);throw L!==-1&&(_.stack=_.stack.substring(0,L)+T+_.stack.substring(L+_.message.length)),_.message=T,_}const r=n.find(_=>_ instanceof Rh||_ instanceof _h||_ instanceof ss||_ instanceof Ih||_ instanceof Eh||_ instanceof Th||_ instanceof Nh||_ instanceof bh||_ instanceof Lh||_ instanceof Dh||_ instanceof Fh);if(r)throw new je(`Unsupported token "${r.toSource()}" while parsing selector "${e}"`);let i=0;const s=new Set;function o(){return new je(`Unexpected token "${n[i].toSource()}" while parsing selector "${e}"`)}function l(){for(;n[i]instanceof zl;)i++}function a(_=i){return n[_]instanceof Ph}function u(_=i){return n[_]instanceof Oh}function c(_=i){return n[_]instanceof zh}function h(_=i){return n[_]instanceof Ch}function f(_=i){return n[_]instanceof jh}function g(_=i){return n[_]instanceof Mh}function w(_=i){return n[_]instanceof ls}function y(_=i){return n[_]instanceof ye&&n[_].value==="*"}function x(_=i){return n[_]instanceof os}function p(_=i){return n[_]instanceof ye&&[">","+","~"].includes(n[_].value)}function m(_=i){return h(_)||g(_)||x(_)||p(_)||n[_]instanceof zl}function v(){const _=[S()];for(;l(),!!h();)i++,_.push(S());return _}function S(){return l(),c()||u()?n[i++].value:C()}function C(){const _={simples:[]};for(l(),p()?_.simples.push({selector:{functions:[{name:"scope",args:[]}]},combinator:""}):_.simples.push({selector:E(),combinator:""});;){if(l(),p())_.simples[_.simples.length-1].combinator=n[i++].value,l();else if(m())break;_.simples.push({combinator:"",selector:E()})}return _}function E(){let _="";const T=[];for(;!m();)if(a()||y())_+=n[i++].toSource();else if(n[i]instanceof $h)_+=n[i++].toSource();else if(n[i]instanceof ye&&n[i].value===".")if(i++,a())_+="."+n[i++].toSource();else throw o();else if(n[i]instanceof kh)if(i++,a())if(!t.has(n[i].value.toLowerCase()))_+=":"+n[i++].toSource();else{const L=n[i++].value.toLowerCase();T.push({name:L,args:[]}),s.add(L)}else if(w()){const L=n[i++].value.toLowerCase();if(t.has(L)?(T.push({name:L,args:v()}),s.add(L)):_+=`:${L}(${N()})`,l(),!g())throw o();i++}else throw o();else if(n[i]instanceof Ah){for(_+="[",i++;!(n[i]instanceof Fl)&&!x();)_+=n[i++].toSource();if(!(n[i]instanceof Fl))throw o();_+="]",i++}else throw o();if(!_&&!T.length)throw o();return{css:_||void 0,functions:T}}function N(){let _="",T=1;for(;!x()&&((f()||w())&&T++,g()&&T--,!!T);)_+=n[i++].toSource();return _}const j=v();if(!x())throw o();if(j.some(_=>typeof _!="object"||!("simples"in _)))throw new je(`Error while parsing selector "${e}"`);return{selector:j,names:Array.from(s)}}const Hl=new Set(["internal:has","internal:has-not","internal:and","internal:or","internal:chain","left-of","right-of","above","below","near"]),_0=new Set(["left-of","right-of","above","below","near"]),Uh=new Set(["not","is","where","has","scope","light","visible","text","text-matches","text-is","has-text","above","below","right-of","left-of","near","nth-match"]);function so(e){const t=k0(e),n=[];for(const r of t.parts){if(r.name==="css"||r.name==="css:light"){r.name==="css:light"&&(r.body=":light("+r.body+")");const i=S0(r.body,Uh);n.push({name:"css",body:i.selector,source:r.body});continue}if(Hl.has(r.name)){let i,s;try{const u=JSON.parse("["+r.body+"]");if(!Array.isArray(u)||u.length<1||u.length>2||typeof u[0]!="string")throw new je(`Malformed selector: ${r.name}=`+r.body);if(i=u[0],u.length===2){if(typeof u[1]!="number"||!_0.has(r.name))throw new je(`Malformed selector: ${r.name}=`+r.body);s=u[1]}}catch{throw new je(`Malformed selector: ${r.name}=`+r.body)}const o={name:r.name,source:r.body,body:{parsed:so(i),distance:s}},l=[...o.body.parsed.parts].reverse().find(u=>u.name==="internal:control"&&u.body==="enter-frame"),a=l?o.body.parsed.parts.indexOf(l):-1;a!==-1&&E0(o.body.parsed.parts.slice(0,a+1),n.slice(0,a+1))&&o.body.parsed.parts.splice(0,a+1),n.push(o);continue}n.push({...r,source:r.body})}if(Hl.has(n[0].name))throw new je(`"${n[0].name}" selector cannot be first`);return{capture:t.capture,parts:n}}function E0(e,t){return Sn({parts:e})===Sn({parts:t})}function Sn(e,t){return typeof e=="string"?e:e.parts.map((n,r)=>{let i=!0;!t&&r!==e.capture&&(n.name==="css"||n.name==="xpath"&&n.source.startsWith("//")||n.source.startsWith(".."))&&(i=!1);const s=i?n.name+"=":"";return`${r===e.capture?"*":""}${s}${n.source}`}).join(" >> ")}function T0(e,t){const n=(r,i)=>{for(const s of r.parts)t(s,i),Hl.has(s.name)&&n(s.body.parsed,!0)};n(e,!1)}function k0(e){let t=0,n,r=0;const i={parts:[]},s=()=>{const l=e.substring(r,t).trim(),a=l.indexOf("=");let u,c;a!==-1&&l.substring(0,a).trim().match(/^[a-zA-Z_0-9-+:*]+$/)?(u=l.substring(0,a).trim(),c=l.substring(a+1)):l.length>1&&l[0]==='"'&&l[l.length-1]==='"'||l.length>1&&l[0]==="'"&&l[l.length-1]==="'"?(u="text",c=l):/^\(*\/\//.test(l)||l.startsWith("..")?(u="xpath",c=l):(u="css",c=l);let h=!1;if(u[0]==="*"&&(h=!0,u=u.substring(1)),i.parts.push({name:u,body:c}),h){if(i.capture!==void 0)throw new je("Only one of the selectors can capture using * modifier");i.capture=i.parts.length-1}};if(!e.includes(">>"))return t=e.length,s(),i;const o=()=>{const a=e.substring(r,t).match(/^\s*text\s*=(.*)$/);return!!a&&!!a[1]};for(;t<e.length;){const l=e[t];l==="\\"&&t+1<e.length?t+=2:l===n?(n=void 0,t++):!n&&(l==='"'||l==="'"||l==="`")&&!o()?(n=l,t++):!n&&l===">"&&e[t+1]===">"?(s(),t+=2,r=t):t++}return s(),i}function mn(e,t){let n=0,r=e.length===0;const i=()=>e[n]||"",s=()=>{const x=i();return++n,r=n>=e.length,x},o=x=>{throw r?new je(`Unexpected end of selector while parsing selector \`${e}\``):new je(`Error while parsing selector \`${e}\` - unexpected symbol "${i()}" at position ${n}`+(x?" during "+x:""))};function l(){for(;!r&&/\s/.test(i());)s()}function a(x){return x>=""||x>="0"&&x<="9"||x>="A"&&x<="Z"||x>="a"&&x<="z"||x>="0"&&x<="9"||x==="_"||x==="-"}function u(){let x="";for(l();!r&&a(i());)x+=s();return x}function c(x){let p=s();for(p!==x&&o("parsing quoted string");!r&&i()!==x;)i()==="\\"&&s(),p+=s();return i()!==x&&o("parsing quoted string"),p+=s(),p}function h(){s()!=="/"&&o("parsing regular expression");let x="",p=!1;for(;!r;){if(i()==="\\")x+=s(),r&&o("parsing regular expression");else if(p&&i()==="]")p=!1;else if(!p&&i()==="[")p=!0;else if(!p&&i()==="/")break;x+=s()}s()!=="/"&&o("parsing regular expression");let m="";for(;!r&&i().match(/[dgimsuy]/);)m+=s();try{return new RegExp(x,m)}catch(v){throw new je(`Error while parsing selector \`${e}\`: ${v.message}`)}}function f(){let x="";return l(),i()==="'"||i()==='"'?x=c(i()).slice(1,-1):x=u(),x||o("parsing property path"),x}function g(){l();let x="";return r||(x+=s()),!r&&x!=="="&&(x+=s()),["=","*=","^=","$=","|=","~="].includes(x)||o("parsing operator"),x}function w(){s();const x=[];for(x.push(f()),l();i()===".";)s(),x.push(f()),l();if(i()==="]")return s(),{name:x.join("."),jsonPath:x,op:"<truthy>",value:null,caseSensitive:!1};const p=g();let m,v=!0;if(l(),i()==="/"){if(p!=="=")throw new je(`Error while parsing selector \`${e}\` - cannot use ${p} in attribute with regular expression`);m=h()}else if(i()==="'"||i()==='"')m=c(i()).slice(1,-1),l(),i()==="i"||i()==="I"?(v=!1,s()):(i()==="s"||i()==="S")&&(v=!0,s());else{for(m="";!r&&(a(i())||i()==="+"||i()===".");)m+=s();m==="true"?m=!0:m==="false"?m=!1:t||(m=+m,Number.isNaN(m)&&o("parsing attribute value"))}if(l(),i()!=="]"&&o("parsing attribute value"),s(),p!=="="&&typeof m!="string")throw new je(`Error while parsing selector \`${e}\` - cannot use ${p} in attribute with non-string matching value - ${m}`);return{name:x.join("."),jsonPath:x,op:p,value:m,caseSensitive:v}}const y={name:"",attributes:[]};for(y.name=u(),l();i()==="[";)y.attributes.push(w()),l();if(r||o(void 0),!y.name&&!y.attributes.length)throw new je(`Error while parsing selector \`${e}\` - selector cannot be empty`);return y}function Yt(e,t,n=!1){return Bh(e,t,n)[0]}function Bh(e,t,n=!1,r=20,i){try{return jn(new M0[e](i),so(t),n,r)}catch{return[t]}}function jn(e,t,n=!1,r=20){const i=[...t.parts];for(let l=0;l<i.length-1;l++)if(i[l].name==="nth"&&i[l+1].name==="internal:control"&&i[l+1].body==="enter-frame"){const[a]=i.splice(l,1);i.splice(l+1,0,a)}const s=[];let o=n?"frame-locator":"page";for(let l=0;l<i.length;l++){const a=i[l],u=o;if(o="locator",a.name==="nth"){a.body==="0"?s.push([e.generateLocator(u,"first",""),e.generateLocator(u,"nth","0")]):a.body==="-1"?s.push([e.generateLocator(u,"last",""),e.generateLocator(u,"nth","-1")]):s.push([e.generateLocator(u,"nth",a.body)]);continue}if(a.name==="internal:text"){const{exact:y,text:x}=Tr(a.body);s.push([e.generateLocator(u,"text",x,{exact:y})]);continue}if(a.name==="internal:has-text"){const{exact:y,text:x}=Tr(a.body);if(!y){s.push([e.generateLocator(u,"has-text",x,{exact:y})]);continue}}if(a.name==="internal:has-not-text"){const{exact:y,text:x}=Tr(a.body);if(!y){s.push([e.generateLocator(u,"has-not-text",x,{exact:y})]);continue}}if(a.name==="internal:has"){const y=jn(e,a.body.parsed,!1,r);s.push(y.map(x=>e.generateLocator(u,"has",x)));continue}if(a.name==="internal:has-not"){const y=jn(e,a.body.parsed,!1,r);s.push(y.map(x=>e.generateLocator(u,"hasNot",x)));continue}if(a.name==="internal:and"){const y=jn(e,a.body.parsed,!1,r);s.push(y.map(x=>e.generateLocator(u,"and",x)));continue}if(a.name==="internal:or"){const y=jn(e,a.body.parsed,!1,r);s.push(y.map(x=>e.generateLocator(u,"or",x)));continue}if(a.name==="internal:chain"){const y=jn(e,a.body.parsed,!1,r);s.push(y.map(x=>e.generateLocator(u,"chain",x)));continue}if(a.name==="internal:label"){const{exact:y,text:x}=Tr(a.body);s.push([e.generateLocator(u,"label",x,{exact:y})]);continue}if(a.name==="internal:role"){const y=mn(a.body,!0),x={attrs:[]};for(const p of y.attributes)p.name==="name"?(x.exact=p.caseSensitive,x.name=p.value):(p.name==="level"&&typeof p.value=="string"&&(p.value=+p.value),x.attrs.push({name:p.name==="include-hidden"?"includeHidden":p.name,value:p.value}));s.push([e.generateLocator(u,"role",y.name,x)]);continue}if(a.name==="internal:testid"){const y=mn(a.body,!0),{value:x}=y.attributes[0];s.push([e.generateLocator(u,"test-id",x)]);continue}if(a.name==="internal:attr"){const y=mn(a.body,!0),{name:x,value:p,caseSensitive:m}=y.attributes[0],v=p,S=!!m;if(x==="placeholder"){s.push([e.generateLocator(u,"placeholder",v,{exact:S})]);continue}if(x==="alt"){s.push([e.generateLocator(u,"alt",v,{exact:S})]);continue}if(x==="title"){s.push([e.generateLocator(u,"title",v,{exact:S})]);continue}}let c="default";const h=i[l+1];h&&h.name==="internal:control"&&h.body==="enter-frame"&&(c="frame",o="frame-locator",l++);const f=Sn({parts:[a]}),g=e.generateLocator(u,c,f);if(c==="default"&&h&&["internal:has-text","internal:has-not-text"].includes(h.name)){const{exact:y,text:x}=Tr(h.body);if(!y){const p=e.generateLocator("locator",h.name==="internal:has-text"?"has-text":"has-not-text",x,{exact:y}),m={};h.name==="internal:has-text"?m.hasText=x:m.hasNotText=x;const v=e.generateLocator(u,"default",f,m);s.push([e.chainLocators([g,p]),v]),l++;continue}}let w;if(["xpath","css"].includes(a.name)){const y=Sn({parts:[a]},!0);w=e.generateLocator(u,c,y)}s.push([g,w].filter(Boolean))}return N0(e,s,r)}function N0(e,t,n){const r=t.map(()=>""),i=[],s=o=>{if(o===t.length)return i.push(e.chainLocators(r)),r.length<n;for(const l of t[o])if(r[o]=l,!s(o+1))return!1;return!0};return s(0),i}function Tr(e){let t=!1;const n=e.match(/^\/(.*)\/([igm]*)$/);return n?{text:new RegExp(n[1],n[2])}:(e.endsWith('"')?(e=JSON.parse(e),t=!0):e.endsWith('"s')?(e=JSON.parse(e.substring(0,e.length-1)),t=!0):e.endsWith('"i')&&(e=JSON.parse(e.substring(0,e.length-1)),t=!1),{exact:t,text:e})}class C0{constructor(t){this.preferredQuote=t}generateLocator(t,n,r,i={}){switch(n){case"default":return i.hasText!==void 0?`locator(${this.quote(r)}, { hasText: ${this.toHasText(i.hasText)} })`:i.hasNotText!==void 0?`locator(${this.quote(r)}, { hasNotText: ${this.toHasText(i.hasNotText)} })`:`locator(${this.quote(r)})`;case"frame":return`frameLocator(${this.quote(r)})`;case"nth":return`nth(${r})`;case"first":return"first()";case"last":return"last()";case"role":const s=[];fe(i.name)?s.push(`name: ${this.regexToSourceString(i.name)}`):typeof i.name=="string"&&(s.push(`name: ${this.quote(i.name)}`),i.exact&&s.push("exact: true"));for(const{name:l,value:a}of i.attrs)s.push(`${l}: ${typeof a=="string"?this.quote(a):a}`);const o=s.length?`, { ${s.join(", ")} }`:"";return`getByRole(${this.quote(r)}${o})`;case"has-text":return`filter({ hasText: ${this.toHasText(r)} })`;case"has-not-text":return`filter({ hasNotText: ${this.toHasText(r)} })`;case"has":return`filter({ has: ${r} })`;case"hasNot":return`filter({ hasNot: ${r} })`;case"and":return`and(${r})`;case"or":return`or(${r})`;case"chain":return`locator(${r})`;case"test-id":return`getByTestId(${this.toTestIdValue(r)})`;case"text":return this.toCallWithExact("getByText",r,!!i.exact);case"alt":return this.toCallWithExact("getByAltText",r,!!i.exact);case"placeholder":return this.toCallWithExact("getByPlaceholder",r,!!i.exact);case"label":return this.toCallWithExact("getByLabel",r,!!i.exact);case"title":return this.toCallWithExact("getByTitle",r,!!i.exact);default:throw new Error("Unknown selector kind "+n)}}chainLocators(t){return t.join(".")}regexToSourceString(t){return io(String(t))}toCallWithExact(t,n,r){return fe(n)?`${t}(${this.regexToSourceString(n)})`:r?`${t}(${this.quote(n)}, { exact: true })`:`${t}(${this.quote(n)})`}toHasText(t){return fe(t)?this.regexToSourceString(t):this.quote(t)}toTestIdValue(t){return fe(t)?this.regexToSourceString(t):this.quote(t)}quote(t){return ro(t,this.preferredQuote??"'")}}class b0{generateLocator(t,n,r,i={}){switch(n){case"default":return i.hasText!==void 0?`locator(${this.quote(r)}, has_text=${this.toHasText(i.hasText)})`:i.hasNotText!==void 0?`locator(${this.quote(r)}, has_not_text=${this.toHasText(i.hasNotText)})`:`locator(${this.quote(r)})`;case"frame":return`frame_locator(${this.quote(r)})`;case"nth":return`nth(${r})`;case"first":return"first";case"last":return"last";case"role":const s=[];fe(i.name)?s.push(`name=${this.regexToString(i.name)}`):typeof i.name=="string"&&(s.push(`name=${this.quote(i.name)}`),i.exact&&s.push("exact=True"));for(const{name:l,value:a}of i.attrs){let u=typeof a=="string"?this.quote(a):a;typeof a=="boolean"&&(u=a?"True":"False"),s.push(`${xh(l)}=${u}`)}const o=s.length?`, ${s.join(", ")}`:"";return`get_by_role(${this.quote(r)}${o})`;case"has-text":return`filter(has_text=${this.toHasText(r)})`;case"has-not-text":return`filter(has_not_text=${this.toHasText(r)})`;case"has":return`filter(has=${r})`;case"hasNot":return`filter(has_not=${r})`;case"and":return`and_(${r})`;case"or":return`or_(${r})`;case"chain":return`locator(${r})`;case"test-id":return`get_by_test_id(${this.toTestIdValue(r)})`;case"text":return this.toCallWithExact("get_by_text",r,!!i.exact);case"alt":return this.toCallWithExact("get_by_alt_text",r,!!i.exact);case"placeholder":return this.toCallWithExact("get_by_placeholder",r,!!i.exact);case"label":return this.toCallWithExact("get_by_label",r,!!i.exact);case"title":return this.toCallWithExact("get_by_title",r,!!i.exact);default:throw new Error("Unknown selector kind "+n)}}chainLocators(t){return t.join(".")}regexToString(t){const n=t.flags.includes("i")?", re.IGNORECASE":"";return`re.compile(r"${io(t.source).replace(/\\\//,"/").replace(/"/g,'\\"')}"${n})`}toCallWithExact(t,n,r){return fe(n)?`${t}(${this.regexToString(n)})`:r?`${t}(${this.quote(n)}, exact=True)`:`${t}(${this.quote(n)})`}toHasText(t){return fe(t)?this.regexToString(t):`${this.quote(t)}`}toTestIdValue(t){return fe(t)?this.regexToString(t):this.quote(t)}quote(t){return ro(t,'"')}}class L0{generateLocator(t,n,r,i={}){let s;switch(t){case"page":s="Page";break;case"frame-locator":s="FrameLocator";break;case"locator":s="Locator";break}switch(n){case"default":return i.hasText!==void 0?`locator(${this.quote(r)}, new ${s}.LocatorOptions().setHasText(${this.toHasText(i.hasText)}))`:i.hasNotText!==void 0?`locator(${this.quote(r)}, new ${s}.LocatorOptions().setHasNotText(${this.toHasText(i.hasNotText)}))`:`locator(${this.quote(r)})`;case"frame":return`frameLocator(${this.quote(r)})`;case"nth":return`nth(${r})`;case"first":return"first()";case"last":return"last()";case"role":const o=[];fe(i.name)?o.push(`.setName(${this.regexToString(i.name)})`):typeof i.name=="string"&&(o.push(`.setName(${this.quote(i.name)})`),i.exact&&o.push(".setExact(true)"));for(const{name:a,value:u}of i.attrs)o.push(`.set${Os(a)}(${typeof u=="string"?this.quote(u):u})`);const l=o.length?`, new ${s}.GetByRoleOptions()${o.join("")}`:"";return`getByRole(AriaRole.${xh(r).toUpperCase()}${l})`;case"has-text":return`filter(new ${s}.FilterOptions().setHasText(${this.toHasText(r)}))`;case"has-not-text":return`filter(new ${s}.FilterOptions().setHasNotText(${this.toHasText(r)}))`;case"has":return`filter(new ${s}.FilterOptions().setHas(${r}))`;case"hasNot":return`filter(new ${s}.FilterOptions().setHasNot(${r}))`;case"and":return`and(${r})`;case"or":return`or(${r})`;case"chain":return`locator(${r})`;case"test-id":return`getByTestId(${this.toTestIdValue(r)})`;case"text":return this.toCallWithExact(s,"getByText",r,!!i.exact);case"alt":return this.toCallWithExact(s,"getByAltText",r,!!i.exact);case"placeholder":return this.toCallWithExact(s,"getByPlaceholder",r,!!i.exact);case"label":return this.toCallWithExact(s,"getByLabel",r,!!i.exact);case"title":return this.toCallWithExact(s,"getByTitle",r,!!i.exact);default:throw new Error("Unknown selector kind "+n)}}chainLocators(t){return t.join(".")}regexToString(t){const n=t.flags.includes("i")?", Pattern.CASE_INSENSITIVE":"";return`Pattern.compile(${this.quote(io(t.source))}${n})`}toCallWithExact(t,n,r,i){return fe(r)?`${n}(${this.regexToString(r)})`:i?`${n}(${this.quote(r)}, new ${t}.${Os(n)}Options().setExact(true))`:`${n}(${this.quote(r)})`}toHasText(t){return fe(t)?this.regexToString(t):this.quote(t)}toTestIdValue(t){return fe(t)?this.regexToString(t):this.quote(t)}quote(t){return ro(t,'"')}}class A0{generateLocator(t,n,r,i={}){switch(n){case"default":return i.hasText!==void 0?`Locator(${this.quote(r)}, new() { ${this.toHasText(i.hasText)} })`:i.hasNotText!==void 0?`Locator(${this.quote(r)}, new() { ${this.toHasNotText(i.hasNotText)} })`:`Locator(${this.quote(r)})`;case"frame":return`FrameLocator(${this.quote(r)})`;case"nth":return`Nth(${r})`;case"first":return"First";case"last":return"Last";case"role":const s=[];fe(i.name)?s.push(`NameRegex = ${this.regexToString(i.name)}`):typeof i.name=="string"&&(s.push(`Name = ${this.quote(i.name)}`),i.exact&&s.push("Exact = true"));for(const{name:l,value:a}of i.attrs)s.push(`${Os(l)} = ${typeof a=="string"?this.quote(a):a}`);const o=s.length?`, new() { ${s.join(", ")} }`:"";return`GetByRole(AriaRole.${Os(r)}${o})`;case"has-text":return`Filter(new() { ${this.toHasText(r)} })`;case"has-not-text":return`Filter(new() { ${this.toHasNotText(r)} })`;case"has":return`Filter(new() { Has = ${r} })`;case"hasNot":return`Filter(new() { HasNot = ${r} })`;case"and":return`And(${r})`;case"or":return`Or(${r})`;case"chain":return`Locator(${r})`;case"test-id":return`GetByTestId(${this.toTestIdValue(r)})`;case"text":return this.toCallWithExact("GetByText",r,!!i.exact);case"alt":return this.toCallWithExact("GetByAltText",r,!!i.exact);case"placeholder":return this.toCallWithExact("GetByPlaceholder",r,!!i.exact);case"label":return this.toCallWithExact("GetByLabel",r,!!i.exact);case"title":return this.toCallWithExact("GetByTitle",r,!!i.exact);default:throw new Error("Unknown selector kind "+n)}}chainLocators(t){return t.join(".")}regexToString(t){const n=t.flags.includes("i")?", RegexOptions.IgnoreCase":"";return`new Regex(${this.quote(io(t.source))}${n})`}toCallWithExact(t,n,r){return fe(n)?`${t}(${this.regexToString(n)})`:r?`${t}(${this.quote(n)}, new() { Exact = true })`:`${t}(${this.quote(n)})`}toHasText(t){return fe(t)?`HasTextRegex = ${this.regexToString(t)}`:`HasText = ${this.quote(t)}`}toTestIdValue(t){return fe(t)?this.regexToString(t):this.quote(t)}toHasNotText(t){return fe(t)?`HasNotTextRegex = ${this.regexToString(t)}`:`HasNotText = ${this.quote(t)}`}quote(t){return ro(t,'"')}}class j0{generateLocator(t,n,r,i={}){return JSON.stringify({kind:n,body:r,options:i})}chainLocators(t){const n=t.map(r=>JSON.parse(r));for(let r=0;r<n.length-1;++r)n[r].next=n[r+1];return JSON.stringify(n[0])}}const M0={javascript:C0,python:b0,java:L0,csharp:A0,jsonl:j0};function fe(e){return e instanceof RegExp}const Tc=new Map;function vi({name:e,items:t=[],id:n,render:r,icon:i,isError:s,isWarning:o,isInfo:l,indent:a,selectedItem:u,onAccepted:c,onSelected:h,onLeftArrow:f,onRightArrow:g,onHighlighted:w,onIconClicked:y,noItemsMessage:x,dataTestId:p,noHighlightOnHover:m}){const v=b.useRef(null),[S,C]=b.useState();return b.useEffect(()=>{w==null||w(S)},[w,S]),b.useEffect(()=>{const E=v.current;if(!E)return;const N=()=>{Tc.set(e,E.scrollTop)};return E.addEventListener("scroll",N,{passive:!0}),()=>E.removeEventListener("scroll",N)},[e]),b.useEffect(()=>{v.current&&(v.current.scrollTop=Tc.get(e)||0)},[e]),d.jsx("div",{className:"list-view vbox "+e+"-list-view",role:t.length>0?"list":void 0,"data-testid":p||e+"-list",children:d.jsxs("div",{className:"list-view-content",tabIndex:0,onKeyDown:E=>{var T;if(u&&E.key==="Enter"){c==null||c(u,t.indexOf(u));return}if(E.key!=="ArrowDown"&&E.key!=="ArrowUp"&&E.key!=="ArrowLeft"&&E.key!=="ArrowRight")return;if(E.stopPropagation(),E.preventDefault(),u&&E.key==="ArrowLeft"){f==null||f(u,t.indexOf(u));return}if(u&&E.key==="ArrowRight"){g==null||g(u,t.indexOf(u));return}const N=u?t.indexOf(u):-1;let j=N;E.key==="ArrowDown"&&(N===-1?j=0:j=Math.min(N+1,t.length-1)),E.key==="ArrowUp"&&(N===-1?j=t.length-1:j=Math.max(N-1,0));const _=(T=v.current)==null?void 0:T.children.item(j);I0(_||void 0),w==null||w(void 0),h==null||h(t[j],j)},ref:v,children:[x&&t.length===0&&d.jsx("div",{className:"list-view-empty",children:x}),t.map((E,N)=>{const j=u===E?" selected":"",_=!m&&S===E?" highlighted":"",T=s!=null&&s(E,N)?" error":"",L=o!=null&&o(E,N)?" warning":"",k=l!=null&&l(E,N)?" info":"",M=(a==null?void 0:a(E,N))||0,$=r(E,N);return d.jsxs("div",{onDoubleClick:()=>c==null?void 0:c(E,N),role:"listitem",className:"list-view-entry"+j+_+T+L+k,onClick:()=>h==null?void 0:h(E,N),onMouseEnter:()=>C(E),onMouseLeave:()=>C(void 0),children:[M?new Array(M).fill(0).map(()=>d.jsx("div",{className:"list-view-indent"})):void 0,i&&d.jsx("div",{className:"codicon "+(i(E,N)||"codicon-blank"),style:{minWidth:16,marginRight:4},onDoubleClick:Q=>{Q.preventDefault(),Q.stopPropagation()},onClick:Q=>{Q.stopPropagation(),Q.preventDefault(),y==null||y(E,N)}}),typeof $=="string"?d.jsx("div",{style:{textOverflow:"ellipsis",overflow:"hidden"},children:$}):$]},(n==null?void 0:n(E,N))||N)})]})})}function I0(e){e&&(e!=null&&e.scrollIntoViewIfNeeded?e.scrollIntoViewIfNeeded(!1):e==null||e.scrollIntoView())}const P0=vi;function R0({name:e,rootItem:t,render:n,icon:r,isError:i,isVisible:s,selectedItem:o,onAccepted:l,onSelected:a,onHighlighted:u,treeState:c,setTreeState:h,noItemsMessage:f,dataTestId:g,autoExpandDepth:w}){const y=b.useMemo(()=>$0(t,o,c.expandedItems,w||0),[t,o,c,w]),x=b.useMemo(()=>{if(!s)return[...y.keys()];const p=new Map,m=S=>{const C=p.get(S);if(C!==void 0)return C;let E=S.children.some(j=>m(j));for(const j of S.children){const _=m(j);E=E||_}const N=s(S)||E;return p.set(S,N),N};for(const S of y.keys())m(S);const v=[];for(const S of y.keys())s(S)&&v.push(S);return v},[y,s]);return d.jsx(P0,{name:e,items:x,id:p=>p.id,dataTestId:g||e+"-tree",render:p=>{const m=n(p);return d.jsxs(d.Fragment,{children:[r&&d.jsx("div",{className:"codicon "+(r(p)||"blank"),style:{minWidth:16,marginRight:4}}),typeof m=="string"?d.jsx("div",{style:{textOverflow:"ellipsis",overflow:"hidden"},children:m}):m]})},icon:p=>{const m=y.get(p).expanded;if(typeof m=="boolean")return m?"codicon-chevron-down":"codicon-chevron-right"},isError:p=>(i==null?void 0:i(p))||!1,indent:p=>y.get(p).depth,selectedItem:o,onAccepted:p=>l==null?void 0:l(p),onSelected:p=>a==null?void 0:a(p),onHighlighted:p=>u==null?void 0:u(p),onLeftArrow:p=>{const{expanded:m,parent:v}=y.get(p);m?(c.expandedItems.set(p.id,!1),h({...c})):v&&(a==null||a(v))},onRightArrow:p=>{p.children.length&&(c.expandedItems.set(p.id,!0),h({...c}))},onIconClicked:p=>{const{expanded:m}=y.get(p);if(m){for(let v=o;v;v=v.parent)if(v===p){a==null||a(p);break}c.expandedItems.set(p.id,!1)}else c.expandedItems.set(p.id,!0);h({...c})},noItemsMessage:f})}function $0(e,t,n,r){const i=new Map,s=new Set;for(let l=t==null?void 0:t.parent;l;l=l.parent)s.add(l.id);const o=(l,a)=>{for(const u of l.children){const c=s.has(u.id)||n.get(u.id),h=r>a&&i.size<25&&c!==!1,f=u.children.length?c??h:void 0;i.set(u,{depth:a,expanded:f,parent:e===l?null:l}),f&&o(u,a+1)}};return o(e,0),i}const O0=R0,D0=({actions:e,selectedAction:t,selectedTime:n,setSelectedTime:r,sdkLanguage:i,onSelected:s,onHighlighted:o,revealConsole:l,isLive:a})=>{const[u,c]=b.useState({expandedItems:new Map}),{rootItem:h,itemMap:f}=b.useMemo(()=>Qv(e),[e]),{selectedItem:g}=b.useMemo(()=>({selectedItem:t?f.get(t.callId):void 0}),[f,t]);return d.jsxs("div",{className:"vbox",children:[n&&d.jsxs("div",{className:"action-list-show-all",onClick:()=>r(void 0),children:[d.jsx("span",{className:"codicon codicon-triangle-left"}),"Show all"]}),d.jsx(O0,{name:"actions",rootItem:h,treeState:u,setTreeState:c,selectedItem:g,onSelected:w=>s(w.action),onHighlighted:w=>o(w==null?void 0:w.action),onAccepted:w=>r({minimum:w.action.startTime,maximum:w.action.endTime}),isError:w=>{var y,x;return!!((x=(y=w.action)==null?void 0:y.error)!=null&&x.message)},isVisible:w=>!n||w.action.startTime<=n.maximum&&w.action.endTime>=n.minimum,render:w=>qa(w.action,{sdkLanguage:i,revealConsole:l,isLive:a,showDuration:!0,showBadges:!0})})]})},qa=(e,t)=>{const{sdkLanguage:n,revealConsole:r,isLive:i,showDuration:s,showBadges:o}=t,{errors:l,warnings:a}=Yv(e),u=e.params.selector?Yt(n||"javascript",e.params.selector):void 0;let c="";return e.endTime?c=Ye(e.endTime-e.startTime):e.error?c="Timed out":i||(c="-"),d.jsxs(d.Fragment,{children:[d.jsxs("div",{className:"action-title",title:e.apiName,children:[d.jsx("span",{children:e.apiName}),u&&d.jsx("div",{className:"action-selector",title:u,children:u}),e.method==="goto"&&e.params.url&&d.jsx("div",{className:"action-url",title:e.params.url,children:e.params.url}),e.class==="APIRequestContext"&&e.params.url&&d.jsx("div",{className:"action-url",title:e.params.url,children:z0(e.params.url)})]}),(s||o)&&d.jsx("div",{className:"spacer"}),s&&d.jsx("div",{className:"action-duration",children:c||d.jsx("span",{className:"codicon codicon-loading"})}),o&&d.jsxs("div",{className:"action-icons",onClick:()=>r==null?void 0:r(),children:[!!l&&d.jsxs("div",{className:"action-icon",children:[d.jsx("span",{className:"codicon codicon-error"}),d.jsx("span",{className:"action-icon-value",children:l})]}),!!a&&d.jsxs("div",{className:"action-icon",children:[d.jsx("span",{className:"codicon codicon-warning"}),d.jsx("span",{className:"action-icon-value",children:a})]})]})]})};function z0(e){try{const t=new URL(e);return t.pathname+t.search}catch{return e}}const Vh=({value:e,description:t})=>{const[n,r]=b.useState("copy"),i=b.useCallback(()=>{navigator.clipboard.writeText(e).then(()=>{r("check"),setTimeout(()=>{r("copy")},3e3)},()=>{r("close")})},[e]);return d.jsx(en,{title:t||"Copy",icon:n,onClick:i})},kn=({text:e})=>d.jsx("div",{className:"fill",style:{display:"flex",alignItems:"center",justifyContent:"center",fontSize:24,fontWeight:"bold",opacity:.5},children:e}),F0=({action:e,sdkLanguage:t})=>{if(!e)return d.jsx(kn,{text:"No action selected"});const n={...e.params};delete n.info;const r=Object.keys(n),i=e.startTime+(e.context.wallTime-e.context.startTime),s=new Date(i).toLocaleString(),o=e.endTime?Ye(e.endTime-e.startTime):"Timed Out";return d.jsxs("div",{className:"call-tab",children:[d.jsx("div",{className:"call-line",children:e.apiName}),d.jsxs(d.Fragment,{children:[d.jsx("div",{className:"call-section",children:"Time"}),s&&d.jsxs("div",{className:"call-line",children:["wall time:",d.jsx("span",{className:"call-value datetime",title:s,children:s})]}),d.jsxs("div",{className:"call-line",children:["duration:",d.jsx("span",{className:"call-value datetime",title:o,children:o})]})]}),!!r.length&&d.jsx("div",{className:"call-section",children:"Parameters"}),!!r.length&&r.map((l,a)=>kc(Nc(e,l,n[l],t),"param-"+a)),!!e.result&&d.jsx("div",{className:"call-section",children:"Return value"}),!!e.result&&Object.keys(e.result).map((l,a)=>kc(Nc(e,l,e.result[l],t),"result-"+a))]})};function kc(e,t){let n=e.text.replace(/\n/g,"↵");return e.type==="string"&&(n=`"${n}"`),d.jsxs("div",{className:"call-line",children:[e.name,":",d.jsx("span",{className:`call-value ${e.type}`,title:e.text,children:n}),["string","number","object","locator"].includes(e.type)&&d.jsx(Vh,{value:e.text})]},t)}function Nc(e,t,n,r){const i=e.method.includes("eval")||e.method==="waitForFunction";if(t==="files")return{text:"<files>",type:"string",name:t};if((t==="eventInit"||t==="expectedValue"||t==="arg"&&i)&&(n=Ds(n.value,new Array(10).fill({handle:"<handle>"}))),(t==="value"&&i||t==="received"&&e.method==="expect")&&(n=Ds(n,new Array(10).fill({handle:"<handle>"}))),t==="selector")return{text:Yt(r||"javascript",e.params.selector),type:"locator",name:"locator"};const s=typeof n;return s!=="object"||n===null?{text:String(n),type:s,name:t}:n.guid?{text:"<handle>",type:"handle",name:t}:{text:JSON.stringify(n).slice(0,1e3),type:"object",name:t}}function Ds(e,t){if(e.n!==void 0)return e.n;if(e.s!==void 0)return e.s;if(e.b!==void 0)return e.b;if(e.v!==void 0){if(e.v==="undefined")return;if(e.v==="null")return null;if(e.v==="NaN")return NaN;if(e.v==="Infinity")return 1/0;if(e.v==="-Infinity")return-1/0;if(e.v==="-0")return-0}if(e.d!==void 0)return new Date(e.d);if(e.r!==void 0)return new RegExp(e.r.p,e.r.f);if(e.a!==void 0)return e.a.map(n=>Ds(n,t));if(e.o!==void 0){const n={};for(const{k:r,v:i}of e.o)n[r]=Ds(i,t);return n}return e.h!==void 0?t===void 0?"<object>":t[e.h]:"<object>"}const H0=vi,U0=({action:e,isLive:t})=>{const n=b.useMemo(()=>{var o;if(!e||!e.log.length)return[];const r=e.log,i=e.context.wallTime-e.context.startTime,s=[];for(let l=0;l<r.length;++l){let a="";if(r[l].time!==-1){const u=(o=r[l])==null?void 0:o.time;l+1<r.length?a=Ye(r[l+1].time-u):e.endTime>0?a=Ye(e.endTime-u):t?a=Ye(Date.now()-i-u):a="-"}s.push({message:r[l].message,time:a})}return s},[e,t]);return n.length?d.jsx(H0,{name:"log",items:n,render:r=>d.jsxs("div",{className:"log-list-item",children:[d.jsx("span",{className:"log-list-duration",children:r.time}),r.message]}),noHighlightOnHover:!0}):d.jsx(kn,{text:"No log entries"})};function ui(e){const t=/(\x1b\[(\d+(;\d+)*)m)|([^\x1b]+)/g,n=[];let r,i={};for(;(r=t.exec(e))!==null;){const[,,s,,o]=r;if(s){const l=+s;switch(l){case 0:i={};break;case 1:i["font-weight"]="bold";break;case 3:i["font-style"]="italic";break;case 4:i["text-decoration"]="underline";break;case 8:i.display="none";break;case 9:i["text-decoration"]="line-through";break;case 22:i={...i,"font-weight":void 0,"font-style":void 0,"text-decoration":void 0};break;case 23:i={...i,"font-weight":void 0,"font-style":void 0};break;case 24:i={...i,"text-decoration":void 0};break;case 30:case 31:case 32:case 33:case 34:case 35:case 36:case 37:i.color=Cc[l-30];break;case 39:i={...i,color:void 0};break;case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:i["background-color"]=Cc[l-40];break;case 49:i={...i,"background-color":void 0};break;case 53:i["text-decoration"]="overline";break;case 90:case 91:case 92:case 93:case 94:case 95:case 96:case 97:i.color=bc[l-90];break;case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:i["background-color"]=bc[l-100];break}}else o&&n.push(`<span style="${V0(i)}">${B0(o)}</span>`)}return n.join("")}const Cc={0:"var(--vscode-terminal-ansiBlack)",1:"var(--vscode-terminal-ansiRed)",2:"var(--vscode-terminal-ansiGreen)",3:"var(--vscode-terminal-ansiYellow)",4:"var(--vscode-terminal-ansiBlue)",5:"var(--vscode-terminal-ansiMagenta)",6:"var(--vscode-terminal-ansiCyan)",7:"var(--vscode-terminal-ansiWhite)"},bc={0:"var(--vscode-terminal-ansiBrightBlack)",1:"var(--vscode-terminal-ansiBrightRed)",2:"var(--vscode-terminal-ansiBrightGreen)",3:"var(--vscode-terminal-ansiBrightYellow)",4:"var(--vscode-terminal-ansiBrightBlue)",5:"var(--vscode-terminal-ansiBrightMagenta)",6:"var(--vscode-terminal-ansiBrightCyan)",7:"var(--vscode-terminal-ansiBrightWhite)"};function B0(e){return e.replace(/[&"<>]/g,t=>({"&":"&amp;",'"':"&quot;","<":"&lt;",">":"&gt;"})[t])}function V0(e){return Object.entries(e).map(([t,n])=>`${t}: ${n}`).join("; ")}const W0=({error:e})=>{const t=b.useMemo(()=>ui(e),[e]);return d.jsx("div",{className:"error-message",dangerouslySetInnerHTML:{__html:t||""}})};function q0(e){return b.useMemo(()=>{if(!e)return{errors:new Map};const t=new Map;for(const n of e.errorDescriptors)t.set(n.message,n);return{errors:t}},[e])}const X0=({errorsModel:e,sdkLanguage:t,revealInSource:n})=>e.errors.size?d.jsx("div",{className:"fill",style:{overflow:"auto"},children:[...e.errors.entries()].map(([r,i])=>{var a;let s,o;const l=(a=i.stack)==null?void 0:a[0];return l&&(s=l.file.replace(/.*[/\\](.*)/,"$1")+":"+l.line,o=l.file+":"+l.line),d.jsxs("div",{children:[d.jsxs("div",{className:"hbox",style:{alignItems:"center",padding:"5px 10px",minHeight:36,fontWeight:"bold",color:"var(--vscode-errorForeground)"},children:[i.action&&qa(i.action,{sdkLanguage:t}),s&&d.jsxs("div",{className:"action-location",children:["@ ",d.jsx("span",{title:o,onClick:()=>n(i),children:s})]})]}),d.jsx(W0,{error:r})]},r)})}):d.jsx(kn,{text:"No errors"}),Q0=vi;function K0(e,t){const{entries:n}=b.useMemo(()=>{if(!e)return{entries:[]};const i=[];for(const s of e.events){if(s.type==="console"){const o=s.args&&s.args.length?Y0(s.args):Wh(s.text),l=s.location.url,u=`${l?l.substring(l.lastIndexOf("/")+1):"<anonymous>"}:${s.location.lineNumber}`;i.push({browserMessage:{body:o,location:u},isError:s.messageType==="error",isWarning:s.messageType==="warning",timestamp:s.time})}s.type==="event"&&s.method==="pageError"&&i.push({browserError:s.params.error,isError:!0,isWarning:!1,timestamp:s.time})}for(const s of e.stdio){let o="";s.text&&(o=ui(s.text.trim())||""),s.base64&&(o=ui(atob(s.base64).trim())||""),i.push({nodeMessage:{html:o},isError:s.type==="stderr",isWarning:!1,timestamp:s.timestamp})}return i.sort((s,o)=>s.timestamp-o.timestamp),{entries:i}},[e]);return{entries:b.useMemo(()=>t?n.filter(i=>i.timestamp>=t.minimum&&i.timestamp<=t.maximum):n,[n,t])}}const G0=({consoleModel:e,boundaries:t,onEntryHovered:n,onAccepted:r})=>e.entries.length?d.jsx("div",{className:"console-tab",children:d.jsx(Q0,{name:"console",onAccepted:r,onHighlighted:n,items:e.entries,isError:i=>i.isError,isWarning:i=>i.isWarning,render:i=>{const s=Ye(i.timestamp-t.minimum),o=d.jsx("span",{className:"console-time",children:s}),l=i.isError?" status-error":i.isWarning?" status-warning":" status-none",a=i.browserMessage||i.browserError?d.jsx("span",{className:"codicon codicon-browser"+l,title:"Browser message"}):d.jsx("span",{className:"codicon codicon-file"+l,title:"Runner message"});let u,c,h,f;const{browserMessage:g,browserError:w,nodeMessage:y}=i;if(g&&(u=g.location,c=g.body),w){const{error:x,value:p}=w;x?(c=x.message,f=x.stack):c=String(p)}return y&&(h=y.html),d.jsxs("div",{className:"console-line",children:[o,a,u&&d.jsx("span",{className:"console-location",children:u}),c&&d.jsx("span",{className:"console-line-message",children:c}),h&&d.jsx("span",{className:"console-line-message",dangerouslySetInnerHTML:{__html:h}}),f&&d.jsx("div",{className:"console-stack",children:f})]})}})}):d.jsx(kn,{text:"No console entries"});function Y0(e){if(e.length===1)return Wh(e[0].preview);const t=typeof e[0].value=="string"&&e[0].value.includes("%"),n=t?e[0].value:"",r=t?e.slice(1):e;let i=0;const s=/%([%sdifoOc])/g;let o;const l=[];let a=[];l.push(d.jsx("span",{children:a}));let u=0;for(;(o=s.exec(n))!==null;){const c=n.substring(u,o.index);a.push(d.jsx("span",{children:c})),u=o.index+2;const h=o[0][1];if(h==="%")a.push(d.jsx("span",{children:"%"}));else if(h==="s"||h==="o"||h==="O"||h==="d"||h==="i"||h==="f"){const f=r[i++],g={};typeof(f==null?void 0:f.value)!="string"&&(g.color="var(--vscode-debugTokenExpression-number)"),a.push(d.jsx("span",{style:g,children:(f==null?void 0:f.preview)||""}))}else if(h==="c"){a=[];const f=r[i++],g=f?J0(f.preview):{};l.push(d.jsx("span",{style:g,children:a}))}}for(u<n.length&&a.push(d.jsx("span",{children:n.substring(u)}));i<r.length;i++){const c=r[i],h={};a.length&&a.push(d.jsx("span",{children:" "})),typeof(c==null?void 0:c.value)!="string"&&(h.color="var(--vscode-debugTokenExpression-number)"),a.push(d.jsx("span",{style:h,children:(c==null?void 0:c.preview)||""}))}return l}function Wh(e){return[d.jsx("span",{dangerouslySetInnerHTML:{__html:ui(e.trim())}})]}function J0(e){try{const t={},n=e.split(";");for(const r of n){const i=r.trim();if(!i)continue;let[s,o]=i.split(":");if(s=s.trim(),o=o.trim(),!Z0(s))continue;const l=s.replace(/-([a-z])/g,a=>a[1].toUpperCase());t[l]=o}return t}catch{return{}}}function Z0(e){return["background","border","color","font","line","margin","padding","text"].some(n=>e.startsWith(n))}const Xa=({noShadow:e,children:t,noMinHeight:n,className:r,onClick:i})=>d.jsx("div",{className:"toolbar"+(e?" no-shadow":"")+(n?" no-min-height":"")+" "+(r||""),onClick:i,children:t}),Ul=({tabs:e,selectedTab:t,setSelectedTab:n,leftToolbar:r,rightToolbar:i,dataTestId:s,mode:o})=>(o||(o="default"),d.jsx("div",{className:"tabbed-pane","data-testid":s,children:d.jsxs("div",{className:"vbox",children:[d.jsxs(Xa,{children:[r&&d.jsxs("div",{style:{flex:"none",display:"flex",margin:"0 4px",alignItems:"center"},children:[...r]}),o==="default"&&d.jsx("div",{style:{flex:"auto",display:"flex",height:"100%",overflow:"hidden"},children:[...e.map(l=>d.jsx(qh,{id:l.id,title:l.title,count:l.count,errorCount:l.errorCount,selected:t===l.id,onSelect:n}))]}),o==="select"&&d.jsx("div",{style:{flex:"auto",display:"flex",height:"100%",overflow:"hidden"},children:d.jsx("select",{style:{width:"100%",background:"none",cursor:"pointer"},onChange:l=>{n(e[l.currentTarget.selectedIndex].id)},children:e.map(l=>{let a="";return l.count&&(a=` (${l.count})`),l.errorCount&&(a=` (${l.errorCount})`),d.jsxs("option",{value:l.id,selected:l.id===t,children:[l.title,a]})})})}),i&&d.jsxs("div",{style:{flex:"none",display:"flex",alignItems:"center"},children:[...i]})]}),e.map(l=>{const a="tab-content tab-"+l.id;if(l.component)return d.jsx("div",{className:a,style:{display:t===l.id?"inherit":"none"},children:l.component},l.id);if(t===l.id)return d.jsx("div",{className:a,children:l.render()},l.id)})]})})),qh=({id:e,title:t,count:n,errorCount:r,selected:i,onSelect:s})=>d.jsxs("div",{className:"tabbed-pane-tab "+(i?"selected":""),onClick:()=>s(e),title:t,children:[d.jsx("div",{className:"tabbed-pane-tab-label",children:t}),!!n&&d.jsx("div",{className:"tabbed-pane-tab-counter",children:n}),!!r&&d.jsx("div",{className:"tabbed-pane-tab-counter error",children:r})]},e),ey="modulepreload",ty=function(e,t){return new URL(e,t).href},Lc={},ny=function(t,n,r){let i=Promise.resolve();if(n&&n.length>0){const s=document.getElementsByTagName("link"),o=document.querySelector("meta[property=csp-nonce]"),l=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));i=Promise.all(n.map(a=>{if(a=ty(a,r),a in Lc)return;Lc[a]=!0;const u=a.endsWith(".css"),c=u?'[rel="stylesheet"]':"";if(!!r)for(let g=s.length-1;g>=0;g--){const w=s[g];if(w.href===a&&(!u||w.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${a}"]${c}`))return;const f=document.createElement("link");if(f.rel=u?"stylesheet":ey,u||(f.as="script",f.crossOrigin=""),f.href=a,l&&f.setAttribute("nonce",l),document.head.appendChild(f),u)return new Promise((g,w)=>{f.addEventListener("load",g),f.addEventListener("error",()=>w(new Error(`Unable to preload CSS for ${a}`)))})}))}return i.then(()=>t()).catch(s=>{const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=s,window.dispatchEvent(o),!o.defaultPrevented)throw s})},yi=({text:e,language:t,mimeType:n,linkify:r,readOnly:i,highlight:s,revealLine:o,lineNumbers:l,isFocused:a,focusOnChange:u,wrapLines:c,onChange:h})=>{const[f,g]=_n(),[w]=b.useState(ny(()=>import("./codeMirrorModule-Bhx_foC8.js"),__vite__mapDeps([0,1]),import.meta.url).then(m=>m.default)),y=b.useRef(null),[x,p]=b.useState();return b.useEffect(()=>{(async()=>{var E,N;const m=await w;iy(m);const v=g.current;if(!v)return;const S=oy(t)||sy(n)||(r?"text/linkified":"");if(y.current&&S===y.current.cm.getOption("mode")&&!!i===y.current.cm.getOption("readOnly")&&l===y.current.cm.getOption("lineNumbers")&&c===y.current.cm.getOption("lineWrapping"))return;(N=(E=y.current)==null?void 0:E.cm)==null||N.getWrapperElement().remove();const C=m(v,{value:"",mode:S,readOnly:!!i,lineNumbers:l,lineWrapping:c});return y.current={cm:C},a&&C.focus(),p(C),C})()},[w,x,g,t,n,r,l,c,i,a]),b.useEffect(()=>{y.current&&y.current.cm.setSize(f.width,f.height)},[f]),b.useLayoutEffect(()=>{var S;if(!x)return;let m=!1;if(x.getValue()!==e&&(x.setValue(e),m=!0,u&&(x.execCommand("selectAll"),x.focus())),m||JSON.stringify(s)!==JSON.stringify(y.current.highlight)){for(const E of y.current.highlight||[])x.removeLineClass(E.line-1,"wrap");for(const E of s||[])x.addLineClass(E.line-1,"wrap",`source-line-${E.type}`);for(const E of y.current.widgets||[])x.removeLineWidget(E);const C=[];for(const E of s||[]){if(E.type!=="error")continue;const N=(S=y.current)==null?void 0:S.cm.getLine(E.line-1);if(N){const _=document.createElement("div");_.className="source-line-error-underline",_.innerHTML="&nbsp;".repeat(N.length||1),C.push(x.addLineWidget(E.line,_,{above:!0,coverGutter:!1}))}const j=document.createElement("div");j.innerHTML=ui(E.message||""),j.className="source-line-error-widget",C.push(x.addLineWidget(E.line,j,{above:!0,coverGutter:!1}))}y.current.highlight=s,y.current.widgets=C}typeof o=="number"&&y.current.cm.lineCount()>=o&&x.scrollIntoView({line:Math.max(0,o-1),ch:0},50);let v;return h&&(v=()=>h(x.getValue()),x.on("change",v)),()=>{v&&x.off("change",v)}},[x,e,s,o,u,h]),d.jsx("div",{className:"cm-wrapper",ref:g,onClick:ry})};function ry(e){var n;if(!(e.target instanceof HTMLElement))return;let t;e.target.classList.contains("cm-linkified")?t=e.target.textContent:e.target.classList.contains("cm-link")&&((n=e.target.nextElementSibling)!=null&&n.classList.contains("cm-url"))&&(t=e.target.nextElementSibling.textContent.slice(1,-1)),t&&(e.preventDefault(),e.stopPropagation(),window.open(t,"_blank"))}let Ac=!1;function iy(e){Ac||(Ac=!0,e.defineSimpleMode("text/linkified",{start:[{regex:hd,token:"linkified"}]}))}function sy(e){if(e){if(e.includes("javascript")||e.includes("json"))return"javascript";if(e.includes("python"))return"python";if(e.includes("csharp"))return"text/x-csharp";if(e.includes("java"))return"text/x-java";if(e.includes("markdown"))return"markdown";if(e.includes("html")||e.includes("svg"))return"htmlmixed";if(e.includes("css"))return"css"}}function oy(e){if(e)return{javascript:"javascript",jsonl:"javascript",python:"python",csharp:"text/x-csharp",java:"text/x-java",markdown:"markdown",html:"htmlmixed",css:"css"}[e]}const ly=({resource:e,onClose:t})=>{const[n,r]=b.useState("request");return d.jsx(Ul,{dataTestId:"network-request-details",leftToolbar:[d.jsx(en,{icon:"close",title:"Close",onClick:t})],tabs:[{id:"request",title:"Request",render:()=>d.jsx(ay,{resource:e})},{id:"response",title:"Response",render:()=>d.jsx(uy,{resource:e})},{id:"body",title:"Body",render:()=>d.jsx(cy,{resource:e})}],selectedTab:n,setSelectedTab:r})},ay=({resource:e})=>{const[t,n]=b.useState(null);return b.useEffect(()=>{(async()=>{if(e.request.postData){const i=e.request.headers.find(o=>o.name==="Content-Type"),s=i?i.value:"";if(e.request.postData._sha1){const o=await fetch(`sha1/${e.request.postData._sha1}`);n({text:Bl(await o.text(),s),mimeType:s})}else n({text:Bl(e.request.postData.text,s),mimeType:s})}else n(null)})()},[e]),d.jsxs("div",{className:"network-request-details-tab",children:[d.jsx("div",{className:"network-request-details-header",children:"General"}),d.jsx("div",{className:"network-request-details-url",children:`URL: ${e.request.url}`}),d.jsx("div",{className:"network-request-details-general",children:`Method: ${e.request.method}`}),d.jsxs("div",{className:"network-request-details-general",style:{display:"flex"},children:["Status Code: ",d.jsx("span",{className:dy(e.response.status),style:{display:"inline-flex"},children:`${e.response.status} ${e.response.statusText}`})]}),d.jsx("div",{className:"network-request-details-header",children:"Request Headers"}),d.jsx("div",{className:"network-request-details-headers",children:e.request.headers.map(r=>`${r.name}: ${r.value}`).join(`
`)}),t&&d.jsx("div",{className:"network-request-details-header",children:"Request Body"}),t&&d.jsx(yi,{text:t.text,mimeType:t.mimeType,readOnly:!0,lineNumbers:!0})]})},uy=({resource:e})=>d.jsxs("div",{className:"network-request-details-tab",children:[d.jsx("div",{className:"network-request-details-header",children:"Response Headers"}),d.jsx("div",{className:"network-request-details-headers",children:e.response.headers.map(t=>`${t.name}: ${t.value}`).join(`
`)})]}),cy=({resource:e})=>{const[t,n]=b.useState(null);return b.useEffect(()=>{(async()=>{if(e.response.content._sha1){const i=e.response.content.mimeType.includes("image"),s=await fetch(`sha1/${e.response.content._sha1}`);if(i){const o=await s.blob(),l=new FileReader,a=new Promise(u=>l.onload=u);l.readAsDataURL(o),n({dataUrl:(await a).target.result})}else{const o=Bl(await s.text(),e.response.content.mimeType);n({text:o,mimeType:e.response.content.mimeType})}}})()},[e]),d.jsxs("div",{className:"network-request-details-tab",children:[!e.response.content._sha1&&d.jsx("div",{children:"Response body is not available for this request."}),t&&t.dataUrl&&d.jsx("img",{draggable:"false",src:t.dataUrl}),t&&t.text&&d.jsx(yi,{text:t.text,mimeType:t.mimeType,readOnly:!0,lineNumbers:!0})]})};function dy(e){return e<300||e===304?"green-circle":e<400?"yellow-circle":"red-circle"}function Bl(e,t){if(e===null)return"Loading...";const n=e;if(n==="")return"<Empty>";if(t.includes("application/json"))try{return JSON.stringify(JSON.parse(n),null,2)}catch{return n}return t.includes("application/x-www-form-urlencoded")?decodeURIComponent(n):n}const Xh=({cursor:e,onPaneMouseMove:t,onPaneMouseUp:n,onPaneDoubleClick:r})=>(mt.useEffect(()=>{const i=document.createElement("div");return i.style.position="fixed",i.style.top="0",i.style.right="0",i.style.bottom="0",i.style.left="0",i.style.zIndex="9999",i.style.cursor=e,document.body.appendChild(i),t&&i.addEventListener("mousemove",t),n&&i.addEventListener("mouseup",n),r&&document.body.addEventListener("dblclick",r),()=>{t&&i.removeEventListener("mousemove",t),n&&i.removeEventListener("mouseup",n),r&&document.body.removeEventListener("dblclick",r),document.body.removeChild(i)}},[e,t,n,r]),d.jsx(d.Fragment,{})),fy={position:"absolute",top:0,right:0,bottom:0,left:0},Qh=({orientation:e,offsets:t,setOffsets:n,resizerColor:r,resizerWidth:i,minColumnWidth:s})=>{const o=s||0,[l,a]=mt.useState(null),[u,c]=_n(),h={position:"absolute",right:e==="horizontal"?void 0:0,bottom:e==="horizontal"?0:void 0,width:e==="horizontal"?7:void 0,height:e==="horizontal"?void 0:7,borderTopWidth:e==="horizontal"?void 0:(7-i)/2,borderRightWidth:e==="horizontal"?(7-i)/2:void 0,borderBottomWidth:e==="horizontal"?void 0:(7-i)/2,borderLeftWidth:e==="horizontal"?(7-i)/2:void 0,borderColor:"transparent",borderStyle:"solid",cursor:e==="horizontal"?"ew-resize":"ns-resize"};return d.jsxs("div",{style:{position:"absolute",top:0,right:0,bottom:0,left:-(7-i)/2,zIndex:100,pointerEvents:"none"},ref:c,children:[!!l&&d.jsx(Xh,{cursor:e==="horizontal"?"ew-resize":"ns-resize",onPaneMouseUp:()=>a(null),onPaneMouseMove:f=>{if(!f.buttons)a(null);else if(l){const g=e==="horizontal"?f.clientX-l.clientX:f.clientY-l.clientY,w=l.offset+g,y=l.index>0?t[l.index-1]:0,x=e==="horizontal"?u.width:u.height,p=Math.min(Math.max(y+o,w),x-o)-t[l.index];for(let m=l.index;m<t.length;++m)t[m]=t[m]+p;n([...t])}}}),t.map((f,g)=>d.jsx("div",{style:{...h,top:e==="horizontal"?0:f,left:e==="horizontal"?f:0,pointerEvents:"initial"},onMouseDown:w=>a({clientX:w.clientX,clientY:w.clientY,offset:f,index:g}),children:d.jsx("div",{style:{...fy,background:r}})}))]})};function hy(e){const[t,n]=b.useState([]);b.useEffect(()=>{const s=[];for(let o=0;o<e.columns.length-1;++o){const l=e.columns[o];s[o]=(s[o-1]||0)+e.columnWidths.get(l)}n(s)},[e.columns,e.columnWidths]);function r(s){const o=new Map(e.columnWidths.entries());for(let l=0;l<s.length;++l){const a=s[l]-(s[l-1]||0),u=e.columns[l];o.set(u,a)}e.setColumnWidths(o)}const i=b.useCallback(s=>{var o,l;(l=e.setSorting)==null||l.call(e,{by:s,negate:((o=e.sorting)==null?void 0:o.by)===s?!e.sorting.negate:!1})},[e]);return d.jsxs("div",{className:`grid-view ${e.name}-grid-view`,children:[d.jsx(Qh,{orientation:"horizontal",offsets:t,setOffsets:r,resizerColor:"var(--vscode-panel-border)",resizerWidth:1,minColumnWidth:25}),d.jsxs("div",{className:"vbox",children:[d.jsx("div",{className:"grid-view-header",children:e.columns.map((s,o)=>d.jsxs("div",{className:"grid-view-header-cell "+py(s,e.sorting),style:{width:o<e.columns.length-1?e.columnWidths.get(s):void 0},onClick:()=>e.setSorting&&i(s),children:[d.jsx("span",{className:"grid-view-header-cell-title",children:e.columnTitle(s)}),d.jsx("span",{className:"codicon codicon-triangle-up"}),d.jsx("span",{className:"codicon codicon-triangle-down"})]}))}),d.jsx(vi,{name:e.name,items:e.items,id:e.id,render:(s,o)=>d.jsx(d.Fragment,{children:e.columns.map((l,a)=>{const{body:u,title:c}=e.render(s,l,o);return d.jsx("div",{className:`grid-view-cell grid-view-column-${String(l)}`,title:c,style:{width:a<e.columns.length-1?e.columnWidths.get(l):void 0},children:u})})}),icon:e.icon,indent:e.indent,isError:e.isError,isWarning:e.isWarning,isInfo:e.isInfo,selectedItem:e.selectedItem,onAccepted:e.onAccepted,onSelected:e.onSelected,onLeftArrow:e.onLeftArrow,onRightArrow:e.onRightArrow,onHighlighted:e.onHighlighted,onIconClicked:e.onIconClicked,noItemsMessage:e.noItemsMessage,dataTestId:e.dataTestId,noHighlightOnHover:e.noHighlightOnHover})]})]})}function py(e,t){return e===(t==null?void 0:t.by)?" filter-"+(t.negate?"negative":"positive"):""}const my=hy;function gy(e,t){const n=b.useMemo(()=>((e==null?void 0:e.resources)||[]).filter(o=>t?!!o._monotonicTime&&o._monotonicTime>=t.minimum&&o._monotonicTime<=t.maximum:!0),[e,t]),r=b.useMemo(()=>new _y(e),[e]);return{resources:n,contextIdMap:r}}const vy=({boundaries:e,networkModel:t,onEntryHovered:n})=>{const[r,i]=b.useState(void 0),[s,o]=b.useState(void 0),{renderedEntries:l}=b.useMemo(()=>{const h=t.resources.map(f=>Ey(f,e,t.contextIdMap));return r&&ky(h,r),{renderedEntries:h}},[t.resources,t.contextIdMap,r,e]),[a,u]=b.useState(()=>new Map(Kh().map(h=>[h,wy(h)])));if(!t.resources.length)return d.jsx(kn,{text:"No network calls"});const c=d.jsx(my,{name:"network",items:l,selectedItem:s,onSelected:h=>o(h),onHighlighted:h=>n(h==null?void 0:h.resource),columns:xy(!!s,l),columnTitle:yy,columnWidths:a,setColumnWidths:u,isError:h=>h.status.code>=400,isInfo:h=>!!h.route,render:(h,f)=>Sy(h,f),sorting:r,setSorting:i});return d.jsxs(d.Fragment,{children:[!s&&c,s&&d.jsxs($s,{sidebarSize:a.get("name"),sidebarIsFirst:!0,orientation:"horizontal",settingName:"networkResourceDetails",children:[d.jsx(ly,{resource:s.resource,onClose:()=>o(void 0)}),c]})]})},yy=e=>e==="contextId"?"Source":e==="name"?"Name":e==="method"?"Method":e==="status"?"Status":e==="contentType"?"Content Type":e==="duration"?"Duration":e==="size"?"Size":e==="start"?"Start":e==="route"?"Route":"",wy=e=>e==="name"?200:e==="method"||e==="status"?60:e==="contentType"?200:e==="contextId"?60:100;function xy(e,t){if(e){const r=["name"];return jc(t)&&r.unshift("contextId"),r}let n=Kh();return jc(t)||(n=n.filter(r=>r!=="contextId")),n}function Kh(){return["contextId","name","method","status","contentType","duration","size","start","route"]}const Sy=(e,t)=>t==="contextId"?{body:e.contextId,title:e.name.url}:t==="name"?{body:e.name.name,title:e.name.url}:t==="method"?{body:e.method}:t==="status"?{body:e.status.code>0?e.status.code:"",title:e.status.text}:t==="contentType"?{body:e.contentType}:t==="duration"?{body:Ye(e.duration)}:t==="size"?{body:Rm(e.size)}:t==="start"?{body:Ye(e.start)}:t==="route"?{body:e.route}:{body:""};class _y{constructor(t){B(this,"_pagerefToShortId",new Map);B(this,"_contextToId",new Map);B(this,"_lastPageId",0);B(this,"_lastApiRequestContextId",0)}contextId(t){return t.pageref?this._pageId(t.pageref):t._apiRequest?this._apiRequestContextId(t):""}_pageId(t){let n=this._pagerefToShortId.get(t);return n||(++this._lastPageId,n="page#"+this._lastPageId,this._pagerefToShortId.set(t,n)),n}_apiRequestContextId(t){const n=Rs(t);if(!n)return"";let r=this._contextToId.get(n);return r||(++this._lastApiRequestContextId,r="api#"+this._lastApiRequestContextId,this._contextToId.set(n,r)),r}}function jc(e){const t=new Set;for(const n of e)if(t.add(n.contextId),t.size>1)return!0;return!1}const Ey=(e,t,n)=>{const r=Ty(e);let i;try{const l=new URL(e.request.url);i=l.pathname.substring(l.pathname.lastIndexOf("/")+1),i||(i=l.host)}catch{i=e.request.url}let s=e.response.content.mimeType;const o=s.match(/^(.*);\s*charset=.*$/);return o&&(s=o[1]),{name:{name:i,url:e.request.url},method:e.request.method,status:{code:e.response.status,text:e.response.statusText},contentType:s,duration:e.time,size:e.response._transferSize>0?e.response._transferSize:e.response.bodySize,start:e._monotonicTime-t.minimum,route:r,resource:e,contextId:n.contextId(e)}};function Ty(e){return e._wasAborted?"aborted":e._wasContinued?"continued":e._wasFulfilled?"fulfilled":e._apiRequest?"api":""}function ky(e,t){const n=Ny(t==null?void 0:t.by);n&&e.sort(n),t.negate&&e.reverse()}function Ny(e){if(e==="start")return(t,n)=>t.start-n.start;if(e==="duration")return(t,n)=>t.duration-n.duration;if(e==="status")return(t,n)=>t.status.code-n.status.code;if(e==="method")return(t,n)=>{const r=t.method,i=n.method;return r.localeCompare(i)};if(e==="size")return(t,n)=>t.size-n.size;if(e==="contentType")return(t,n)=>t.contentType.localeCompare(n.contentType);if(e==="name")return(t,n)=>t.name.name.localeCompare(n.name.name);if(e==="route")return(t,n)=>t.route.localeCompare(n.route);if(e==="contextId")return(t,n)=>t.contextId.localeCompare(n.contextId)}const Mc={queryAll(e,t){t.startsWith("/")&&e.nodeType!==Node.DOCUMENT_NODE&&(t="."+t);const n=[],r=e.ownerDocument||e;if(!r)return n;const i=r.evaluate(t,e,null,XPathResult.ORDERED_NODE_ITERATOR_TYPE);for(let s=i.iterateNext();s;s=i.iterateNext())s.nodeType===Node.ELEMENT_NODE&&n.push(s);return n}};let Gh="";function Cy(e){Gh=e}function oo(e,t){for(;t;){if(e.contains(t))return!0;t=Jh(t)}return!1}function Te(e){if(e.parentElement)return e.parentElement;if(e.parentNode&&e.parentNode.nodeType===11&&e.parentNode.host)return e.parentNode.host}function Yh(e){let t=e;for(;t.parentNode;)t=t.parentNode;if(t.nodeType===11||t.nodeType===9)return t}function Jh(e){for(;e.parentElement;)e=e.parentElement;return Te(e)}function Ir(e,t,n){for(;e;){const r=e.closest(t);if(n&&r!==n&&(r!=null&&r.contains(n)))return;if(r)return r;e=Jh(e)}}function dr(e,t){return e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView.getComputedStyle(e,t):void 0}function Zh(e,t){if(t=t??dr(e),!t)return!0;if(Element.prototype.checkVisibility&&Gh!=="webkit"){if(!e.checkVisibility())return!1}else{const n=e.closest("details,summary");if(n!==e&&(n==null?void 0:n.nodeName)==="DETAILS"&&!n.open)return!1}return t.visibility==="visible"}function ci(e){const t=dr(e);if(!t)return!0;if(t.display==="contents"){for(let r=e.firstChild;r;r=r.nextSibling)if(r.nodeType===1&&ci(r)||r.nodeType===3&&ep(r))return!0;return!1}if(!Zh(e,t))return!1;const n=e.getBoundingClientRect();return n.width>0&&n.height>0}function ep(e){const t=e.ownerDocument.createRange();t.selectNode(e);const n=t.getBoundingClientRect();return n.width>0&&n.height>0}function xe(e){return e instanceof HTMLFormElement?"FORM":e.tagName.toUpperCase()}function Ic(e){return e.hasAttribute("aria-label")||e.hasAttribute("aria-labelledby")}const Pc="article:not([role]), aside:not([role]), main:not([role]), nav:not([role]), section:not([role]), [role=article], [role=complementary], [role=main], [role=navigation], [role=region]",by=new Map([["aria-atomic",void 0],["aria-busy",void 0],["aria-controls",void 0],["aria-current",void 0],["aria-describedby",void 0],["aria-details",void 0],["aria-dropeffect",void 0],["aria-flowto",void 0],["aria-grabbed",void 0],["aria-hidden",void 0],["aria-keyshortcuts",void 0],["aria-label",new Set(["caption","code","deletion","emphasis","generic","insertion","paragraph","presentation","strong","subscript","superscript"])],["aria-labelledby",new Set(["caption","code","deletion","emphasis","generic","insertion","paragraph","presentation","strong","subscript","superscript"])],["aria-live",void 0],["aria-owns",void 0],["aria-relevant",void 0],["aria-roledescription",new Set(["generic"])]]);function tp(e,t){return[...by].some(([n,r])=>!(r!=null&&r.has(t||""))&&e.hasAttribute(n))}function np(e){return!Number.isNaN(Number(String(e.getAttribute("tabindex"))))}function Ly(e){return!pp(e)&&(Ay(e)||np(e))}function Ay(e){const t=xe(e);return["BUTTON","DETAILS","SELECT","TEXTAREA"].includes(t)?!0:t==="A"||t==="AREA"?e.hasAttribute("href"):t==="INPUT"?!e.hidden:!1}const Do={A:e=>e.hasAttribute("href")?"link":null,AREA:e=>e.hasAttribute("href")?"link":null,ARTICLE:()=>"article",ASIDE:()=>"complementary",BLOCKQUOTE:()=>"blockquote",BUTTON:()=>"button",CAPTION:()=>"caption",CODE:()=>"code",DATALIST:()=>"listbox",DD:()=>"definition",DEL:()=>"deletion",DETAILS:()=>"group",DFN:()=>"term",DIALOG:()=>"dialog",DT:()=>"term",EM:()=>"emphasis",FIELDSET:()=>"group",FIGURE:()=>"figure",FOOTER:e=>Ir(e,Pc)?null:"contentinfo",FORM:e=>Ic(e)?"form":null,H1:()=>"heading",H2:()=>"heading",H3:()=>"heading",H4:()=>"heading",H5:()=>"heading",H6:()=>"heading",HEADER:e=>Ir(e,Pc)?null:"banner",HR:()=>"separator",HTML:()=>"document",IMG:e=>e.getAttribute("alt")===""&&!e.getAttribute("title")&&!tp(e)&&!np(e)?"presentation":"img",INPUT:e=>{const t=e.type.toLowerCase();if(t==="search")return e.hasAttribute("list")?"combobox":"searchbox";if(["email","tel","text","url",""].includes(t)){const n=wi(e,e.getAttribute("list"))[0];return n&&xe(n)==="DATALIST"?"combobox":"textbox"}return t==="hidden"?"":{button:"button",checkbox:"checkbox",image:"button",number:"spinbutton",radio:"radio",range:"slider",reset:"button",submit:"button"}[t]||"textbox"},INS:()=>"insertion",LI:()=>"listitem",MAIN:()=>"main",MARK:()=>"mark",MATH:()=>"math",MENU:()=>"list",METER:()=>"meter",NAV:()=>"navigation",OL:()=>"list",OPTGROUP:()=>"group",OPTION:()=>"option",OUTPUT:()=>"status",P:()=>"paragraph",PROGRESS:()=>"progressbar",SECTION:e=>Ic(e)?"region":null,SELECT:e=>e.hasAttribute("multiple")||e.size>1?"listbox":"combobox",STRONG:()=>"strong",SUB:()=>"subscript",SUP:()=>"superscript",SVG:()=>"img",TABLE:()=>"table",TBODY:()=>"rowgroup",TD:e=>{const t=Ir(e,"table"),n=t?zs(t):"";return n==="grid"||n==="treegrid"?"gridcell":"cell"},TEXTAREA:()=>"textbox",TFOOT:()=>"rowgroup",TH:e=>{if(e.getAttribute("scope")==="col")return"columnheader";if(e.getAttribute("scope")==="row")return"rowheader";const t=Ir(e,"table"),n=t?zs(t):"";return n==="grid"||n==="treegrid"?"gridcell":"cell"},THEAD:()=>"rowgroup",TIME:()=>"time",TR:()=>"row",UL:()=>"list"},jy={DD:["DL","DIV"],DIV:["DL"],DT:["DL","DIV"],LI:["OL","UL"],TBODY:["TABLE"],TD:["TR"],TFOOT:["TABLE"],TH:["TR"],THEAD:["TABLE"],TR:["THEAD","TBODY","TFOOT","TABLE"]};function Rc(e){var r;const t=((r=Do[xe(e)])==null?void 0:r.call(Do,e))||"";if(!t)return null;let n=e;for(;n;){const i=Te(n),s=jy[xe(n)];if(!s||!i||!s.includes(xe(i)))break;const o=zs(i);if((o==="none"||o==="presentation")&&!rp(i,o))return o;n=i}return t}const My=["alert","alertdialog","application","article","banner","blockquote","button","caption","cell","checkbox","code","columnheader","combobox","command","complementary","composite","contentinfo","definition","deletion","dialog","directory","document","emphasis","feed","figure","form","generic","grid","gridcell","group","heading","img","input","insertion","landmark","link","list","listbox","listitem","log","main","marquee","math","meter","menu","menubar","menuitem","menuitemcheckbox","menuitemradio","navigation","none","note","option","paragraph","presentation","progressbar","radio","radiogroup","range","region","roletype","row","rowgroup","rowheader","scrollbar","search","searchbox","section","sectionhead","select","separator","slider","spinbutton","status","strong","structure","subscript","superscript","switch","tab","table","tablist","tabpanel","term","textbox","time","timer","toolbar","tooltip","tree","treegrid","treeitem","widget","window"],Iy=["command","composite","input","landmark","range","roletype","section","sectionhead","select","structure","widget","window"],Py=My.filter(e=>!Iy.includes(e));function zs(e){return(e.getAttribute("role")||"").split(" ").map(n=>n.trim()).find(n=>Py.includes(n))||null}function rp(e,t){return tp(e,t)||Ly(e)}function Ne(e){const t=zs(e);if(!t)return Rc(e);if(t==="none"||t==="presentation"){const n=Rc(e);if(rp(e,n))return n}return t}function ip(e){return e===null?void 0:e.toLowerCase()==="true"}function ht(e){if(["STYLE","SCRIPT","NOSCRIPT","TEMPLATE"].includes(xe(e)))return!0;const t=dr(e),n=e.nodeName==="SLOT";if((t==null?void 0:t.display)==="contents"&&!n){for(let i=e.firstChild;i;i=i.nextSibling)if(i.nodeType===1&&!ht(i)||i.nodeType===3&&ep(i))return!1;return!0}return!(e.nodeName==="OPTION"&&!!e.closest("select"))&&!n&&!Zh(e,t)?!0:sp(e)}function sp(e){let t=Ot==null?void 0:Ot.get(e);if(t===void 0){if(t=!1,e.parentElement&&e.parentElement.shadowRoot&&!e.assignedSlot&&(t=!0),!t){const n=dr(e);t=!n||n.display==="none"||ip(e.getAttribute("aria-hidden"))===!0}if(!t){const n=Te(e);n&&(t=sp(n))}Ot==null||Ot.set(e,t)}return t}function wi(e,t){if(!t)return[];const n=Yh(e);if(!n)return[];try{const r=t.split(" ").filter(s=>!!s),i=new Set;for(const s of r){const o=n.querySelector("#"+CSS.escape(s));o&&i.add(o)}return[...i]}catch{return[]}}function _t(e){return e.trim()}function as(e){return e.split(" ").map(t=>t.replace(/\r\n/g,`
`).replace(/\s\s*/g," ")).join(" ").trim()}function $c(e,t){const n=[...e.querySelectorAll(t)];for(const r of wi(e,e.getAttribute("aria-owns")))r.matches(t)&&n.push(r),n.push(...r.querySelectorAll(t));return n}function Oc(e,t){const n=t==="::before"?Ja:Za;if(n!=null&&n.has(e))return(n==null?void 0:n.get(e))||"";const r=dr(e,t),i=Ry(r);return n&&n.set(e,i),i}function Ry(e){if(!e)return"";const t=e.content;if(t[0]==="'"&&t[t.length-1]==="'"||t[0]==='"'&&t[t.length-1]==='"'){const n=t.substring(1,t.length-1);return(e.display||"inline")!=="inline"?" "+n+" ":n}return""}function op(e){const t=e.getAttribute("aria-labelledby");return t===null?null:wi(e,t)}function $y(e,t){const n=["button","cell","checkbox","columnheader","gridcell","heading","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","row","rowheader","switch","tab","tooltip","treeitem"].includes(e),r=t&&["","caption","code","contentinfo","definition","deletion","emphasis","insertion","list","listitem","mark","none","paragraph","presentation","region","row","rowgroup","section","strong","subscript","superscript","table","term","time"].includes(e);return n||r}function Fs(e,t){const n=t?Ka:Qa;let r=n==null?void 0:n.get(e);return r===void 0&&(r="",["caption","code","definition","deletion","emphasis","generic","insertion","mark","paragraph","presentation","strong","subscript","suggestion","superscript","term","time"].includes(Ne(e)||"")||(r=as(ft(e,{includeHidden:t,visitedElements:new Set,embeddedInDescribedBy:void 0,embeddedInLabelledBy:void 0,embeddedInLabel:void 0,embeddedInNativeTextAlternative:void 0,embeddedInTargetElement:"self"}))),n==null||n.set(e,r)),r}function Dc(e,t){const n=t?Ya:Ga;let r=n==null?void 0:n.get(e);if(r===void 0){if(r="",e.hasAttribute("aria-describedby")){const i=wi(e,e.getAttribute("aria-describedby"));r=as(i.map(s=>ft(s,{includeHidden:t,visitedElements:new Set,embeddedInLabelledBy:void 0,embeddedInLabel:void 0,embeddedInNativeTextAlternative:void 0,embeddedInTargetElement:"none",embeddedInDescribedBy:{element:s,hidden:ht(s)}})).join(" "))}else e.hasAttribute("aria-description")?r=as(e.getAttribute("aria-description")||""):r=as(e.getAttribute("title")||"");n==null||n.set(e,r)}return r}function ft(e,t){var a,u,c,h;if(t.visitedElements.has(e))return"";const n={...t,embeddedInTargetElement:t.embeddedInTargetElement==="self"?"descendant":t.embeddedInTargetElement};if(!t.includeHidden&&!((a=t.embeddedInLabelledBy)!=null&&a.hidden)&&!((u=t.embeddedInDescribedBy)!=null&&u.hidden)&&!((c=t==null?void 0:t.embeddedInNativeTextAlternative)!=null&&c.hidden)&&!((h=t==null?void 0:t.embeddedInLabel)!=null&&h.hidden)&&ht(e))return t.visitedElements.add(e),"";const r=op(e);if(!t.embeddedInLabelledBy){const f=(r||[]).map(g=>ft(g,{...t,embeddedInLabelledBy:{element:g,hidden:ht(g)},embeddedInDescribedBy:void 0,embeddedInTargetElement:"none",embeddedInLabel:void 0,embeddedInNativeTextAlternative:void 0})).join(" ");if(f)return f}const i=Ne(e)||"",s=xe(e);if(t.embeddedInLabel||t.embeddedInLabelledBy||t.embeddedInTargetElement==="descendant"){const f=[...e.labels||[]].includes(e),g=(r||[]).includes(e);if(!f&&!g){if(i==="textbox")return t.visitedElements.add(e),s==="INPUT"||s==="TEXTAREA"?e.value:e.textContent||"";if(["combobox","listbox"].includes(i)){t.visitedElements.add(e);let w;if(s==="SELECT")w=[...e.selectedOptions],!w.length&&e.options.length&&w.push(e.options[0]);else{const y=i==="combobox"?$c(e,"*").find(x=>Ne(x)==="listbox"):e;w=y?$c(y,'[aria-selected="true"]').filter(x=>Ne(x)==="option"):[]}return!w.length&&s==="INPUT"?e.value:w.map(y=>ft(y,n)).join(" ")}if(["progressbar","scrollbar","slider","spinbutton","meter"].includes(i))return t.visitedElements.add(e),e.hasAttribute("aria-valuetext")?e.getAttribute("aria-valuetext")||"":e.hasAttribute("aria-valuenow")?e.getAttribute("aria-valuenow")||"":e.getAttribute("value")||"";if(["menu"].includes(i))return t.visitedElements.add(e),""}}const o=e.getAttribute("aria-label")||"";if(_t(o))return t.visitedElements.add(e),o;if(!["presentation","none"].includes(i)){if(s==="INPUT"&&["button","submit","reset"].includes(e.type)){t.visitedElements.add(e);const f=e.value||"";return _t(f)?f:e.type==="submit"?"Submit":e.type==="reset"?"Reset":e.getAttribute("title")||""}if(s==="INPUT"&&e.type==="image"){t.visitedElements.add(e);const f=e.labels||[];if(f.length&&!t.embeddedInLabelledBy)return Di(f,t);const g=e.getAttribute("alt")||"";if(_t(g))return g;const w=e.getAttribute("title")||"";return _t(w)?w:"Submit"}if(!r&&s==="BUTTON"){t.visitedElements.add(e);const f=e.labels||[];if(f.length)return Di(f,t)}if(!r&&s==="OUTPUT"){t.visitedElements.add(e);const f=e.labels||[];return f.length?Di(f,t):e.getAttribute("title")||""}if(!r&&(s==="TEXTAREA"||s==="SELECT"||s==="INPUT")){t.visitedElements.add(e);const f=e.labels||[];if(f.length)return Di(f,t);const g=s==="INPUT"&&["text","password","search","tel","email","url"].includes(e.type)||s==="TEXTAREA",w=e.getAttribute("placeholder")||"",y=e.getAttribute("title")||"";return!g||y?y:w}if(!r&&s==="FIELDSET"){t.visitedElements.add(e);for(let g=e.firstElementChild;g;g=g.nextElementSibling)if(xe(g)==="LEGEND")return ft(g,{...n,embeddedInNativeTextAlternative:{element:g,hidden:ht(g)}});return e.getAttribute("title")||""}if(!r&&s==="FIGURE"){t.visitedElements.add(e);for(let g=e.firstElementChild;g;g=g.nextElementSibling)if(xe(g)==="FIGCAPTION")return ft(g,{...n,embeddedInNativeTextAlternative:{element:g,hidden:ht(g)}});return e.getAttribute("title")||""}if(s==="IMG"){t.visitedElements.add(e);const f=e.getAttribute("alt")||"";return _t(f)?f:e.getAttribute("title")||""}if(s==="TABLE"){t.visitedElements.add(e);for(let g=e.firstElementChild;g;g=g.nextElementSibling)if(xe(g)==="CAPTION")return ft(g,{...n,embeddedInNativeTextAlternative:{element:g,hidden:ht(g)}});const f=e.getAttribute("summary")||"";if(f)return f}if(s==="AREA"){t.visitedElements.add(e);const f=e.getAttribute("alt")||"";return _t(f)?f:e.getAttribute("title")||""}if(s==="SVG"||e.ownerSVGElement){t.visitedElements.add(e);for(let f=e.firstElementChild;f;f=f.nextElementSibling)if(xe(f)==="TITLE"&&f.ownerSVGElement)return ft(f,{...n,embeddedInLabelledBy:{element:f,hidden:ht(f)}})}if(e.ownerSVGElement&&s==="A"){const f=e.getAttribute("xlink:title")||"";if(_t(f))return t.visitedElements.add(e),f}}const l=s==="SUMMARY"&&!["presentation","none"].includes(i);if($y(i,t.embeddedInTargetElement==="descendant")||l||t.embeddedInLabelledBy||t.embeddedInDescribedBy||t.embeddedInLabel||t.embeddedInNativeTextAlternative){t.visitedElements.add(e);const f=[],g=(p,m)=>{var v;if(!(m&&p.assignedSlot))if(p.nodeType===1){const S=((v=dr(p))==null?void 0:v.display)||"inline";let C=ft(p,n);(S!=="inline"||p.nodeName==="BR")&&(C=" "+C+" "),f.push(C)}else p.nodeType===3&&f.push(p.textContent||"")};f.push(Oc(e,"::before"));const w=e.nodeName==="SLOT"?e.assignedNodes():[];if(w.length)for(const p of w)g(p,!1);else{for(let p=e.firstChild;p;p=p.nextSibling)g(p,!0);if(e.shadowRoot)for(let p=e.shadowRoot.firstChild;p;p=p.nextSibling)g(p,!0);for(const p of wi(e,e.getAttribute("aria-owns")))g(p,!0)}f.push(Oc(e,"::after"));const y=f.join("");if(t.embeddedInTargetElement==="self"?_t(y):y)return y}if(!["presentation","none"].includes(i)||s==="IFRAME"){t.visitedElements.add(e);const f=e.getAttribute("title")||"";if(_t(f))return f}return t.visitedElements.add(e),""}const lp=["gridcell","option","row","tab","rowheader","columnheader","treeitem"];function Oy(e){return xe(e)==="OPTION"?e.selected:lp.includes(Ne(e)||"")?ip(e.getAttribute("aria-selected"))===!0:!1}const ap=["checkbox","menuitemcheckbox","option","radio","switch","menuitemradio","treeitem"];function Dy(e){const t=up(e,!0);return t==="error"?!1:t}function up(e,t){const n=xe(e);if(t&&n==="INPUT"&&e.indeterminate)return"mixed";if(n==="INPUT"&&["checkbox","radio"].includes(e.type))return e.checked;if(ap.includes(Ne(e)||"")){const r=e.getAttribute("aria-checked");return r==="true"?!0:t&&r==="mixed"?"mixed":!1}return"error"}const cp=["button"];function zy(e){if(cp.includes(Ne(e)||"")){const t=e.getAttribute("aria-pressed");if(t==="true")return!0;if(t==="mixed")return"mixed"}return!1}const dp=["application","button","checkbox","combobox","gridcell","link","listbox","menuitem","row","rowheader","tab","treeitem","columnheader","menuitemcheckbox","menuitemradio","rowheader","switch"];function Fy(e){if(xe(e)==="DETAILS")return e.open;if(dp.includes(Ne(e)||"")){const t=e.getAttribute("aria-expanded");return t===null?"none":t==="true"}return"none"}const fp=["heading","listitem","row","treeitem"];function Hy(e){const t={H1:1,H2:2,H3:3,H4:4,H5:5,H6:6}[xe(e)];if(t)return t;if(fp.includes(Ne(e)||"")){const n=e.getAttribute("aria-level"),r=n===null?Number.NaN:Number(n);if(Number.isInteger(r)&&r>=1)return r}return 0}const Uy=["application","button","composite","gridcell","group","input","link","menuitem","scrollbar","separator","tab","checkbox","columnheader","combobox","grid","listbox","menu","menubar","menuitemcheckbox","menuitemradio","option","radio","radiogroup","row","rowheader","searchbox","select","slider","spinbutton","switch","tablist","textbox","toolbar","tree","treegrid","treeitem"];function hp(e){return pp(e)||gp(e)}function pp(e){return["BUTTON","INPUT","SELECT","TEXTAREA","OPTION","OPTGROUP"].includes(e.tagName)&&(e.hasAttribute("disabled")||mp(e))}function mp(e){return e?xe(e)==="FIELDSET"&&e.hasAttribute("disabled")?!0:mp(e.parentElement):!1}function gp(e){if(!e)return!1;if(Uy.includes(Ne(e)||"")){const t=(e.getAttribute("aria-disabled")||"").toLowerCase();if(t==="true")return!0;if(t==="false")return!1}return gp(Te(e))}function Di(e,t){return[...e].map(n=>ft(n,{...t,embeddedInLabel:{element:n,hidden:ht(n)},embeddedInNativeTextAlternative:void 0,embeddedInLabelledBy:void 0,embeddedInDescribedBy:void 0,embeddedInTargetElement:"none"})).filter(n=>!!n).join(" ")}let Qa,Ka,Ga,Ya,Ot,Ja,Za,vp=0;function yp(){++vp,Qa??(Qa=new Map),Ka??(Ka=new Map),Ga??(Ga=new Map),Ya??(Ya=new Map),Ot??(Ot=new Map),Ja??(Ja=new Map),Za??(Za=new Map)}function wp(){--vp||(Qa=void 0,Ka=void 0,Ga=void 0,Ya=void 0,Ot=void 0,Ja=void 0,Za=void 0)}function xp(e,t){for(const n of t.jsonPath)e!=null&&(e=e[n]);return Sp(e,t)}function Sp(e,t){const n=typeof e=="string"&&!t.caseSensitive?e.toUpperCase():e,r=typeof t.value=="string"&&!t.caseSensitive?t.value.toUpperCase():t.value;return t.op==="<truthy>"?!!n:t.op==="="?r instanceof RegExp?typeof n=="string"&&!!n.match(r):n===r:typeof n!="string"||typeof r!="string"?!1:t.op==="*="?n.includes(r):t.op==="^="?n.startsWith(r):t.op==="$="?n.endsWith(r):t.op==="|="?n===r||n.startsWith(r+"-"):t.op==="~="?n.split(" ").includes(r):!1}function eu(e){const t=e.ownerDocument;return e.nodeName==="SCRIPT"||e.nodeName==="NOSCRIPT"||e.nodeName==="STYLE"||t.head&&t.head.contains(e)}function Me(e,t){let n=e.get(t);if(n===void 0){if(n={full:"",normalized:"",immediate:[]},!eu(t)){let r="";if(t instanceof HTMLInputElement&&(t.type==="submit"||t.type==="button"))n={full:t.value,normalized:Ue(t.value),immediate:[t.value]};else{for(let i=t.firstChild;i;i=i.nextSibling)i.nodeType===Node.TEXT_NODE?(n.full+=i.nodeValue||"",r+=i.nodeValue||""):(r&&n.immediate.push(r),r="",i.nodeType===Node.ELEMENT_NODE&&(n.full+=Me(e,i).full));r&&n.immediate.push(r),t.shadowRoot&&(n.full+=Me(e,t.shadowRoot).full),n.full&&(n.normalized=Ue(n.full))}}e.set(t,n)}return n}function lo(e,t,n){if(eu(t)||!n(Me(e,t)))return"none";for(let r=t.firstChild;r;r=r.nextSibling)if(r.nodeType===Node.ELEMENT_NODE&&n(Me(e,r)))return"selfAndChildren";return t.shadowRoot&&n(Me(e,t.shadowRoot))?"selfAndChildren":"self"}function _p(e,t){const n=op(t);if(n)return n.map(s=>Me(e,s));const r=t.getAttribute("aria-label");if(r!==null&&r.trim())return[{full:r,normalized:Ue(r),immediate:[r]}];const i=t.nodeName==="INPUT"&&t.type!=="hidden";if(["BUTTON","METER","OUTPUT","PROGRESS","SELECT","TEXTAREA"].includes(t.nodeName)||i){const s=t.labels;if(s)return[...s].map(o=>Me(e,o))}return[]}function zc(e){return e.displayName||e.name||"Anonymous"}function By(e){if(e.type)switch(typeof e.type){case"function":return zc(e.type);case"string":return e.type;case"object":return e.type.displayName||(e.type.render?zc(e.type.render):"")}if(e._currentElement){const t=e._currentElement.type;if(typeof t=="string")return t;if(typeof t=="function")return t.displayName||t.name||"Anonymous"}return""}function Vy(e){var t;return e.key??((t=e._currentElement)==null?void 0:t.key)}function Wy(e){if(e.child){const n=[];for(let r=e.child;r;r=r.sibling)n.push(r);return n}if(!e._currentElement)return[];const t=n=>{var i;const r=(i=n._currentElement)==null?void 0:i.type;return typeof r=="function"||typeof r=="string"};if(e._renderedComponent){const n=e._renderedComponent;return t(n)?[n]:[]}return e._renderedChildren?[...Object.values(e._renderedChildren)].filter(t):[]}function qy(e){var r;const t=e.memoizedProps||((r=e._currentElement)==null?void 0:r.props);if(!t||typeof t=="string")return t;const n={...t};return delete n.children,n}function Ep(e){var r;const t={key:Vy(e),name:By(e),children:Wy(e).map(Ep),rootElements:[],props:qy(e)},n=e.stateNode||e._hostNode||((r=e._renderedComponent)==null?void 0:r._hostNode);if(n instanceof Element)t.rootElements.push(n);else for(const i of t.children)t.rootElements.push(...i.rootElements);return t}function Tp(e,t,n=[]){t(e)&&n.push(e);for(const r of e.children)Tp(r,t,n);return n}function kp(e,t=[]){const r=(e.ownerDocument||e).createTreeWalker(e,NodeFilter.SHOW_ELEMENT);do{const i=r.currentNode,s=i,o=Object.keys(s).find(a=>a.startsWith("__reactContainer")&&s[a]!==null);if(o)t.push(s[o].stateNode.current);else{const a="_reactRootContainer";s.hasOwnProperty(a)&&s[a]!==null&&t.push(s[a]._internalRoot.current)}if(i instanceof Element&&i.hasAttribute("data-reactroot"))for(const a of Object.keys(i))(a.startsWith("__reactInternalInstance")||a.startsWith("__reactFiber"))&&t.push(i[a]);const l=i instanceof Element?i.shadowRoot:null;l&&kp(l,t)}while(r.nextNode());return t}const Xy={queryAll(e,t){const{name:n,attributes:r}=mn(t,!1),o=kp(e.ownerDocument||e).map(a=>Ep(a)).map(a=>Tp(a,u=>{const c=u.props??{};if(u.key!==void 0&&(c.key=u.key),n&&u.name!==n||u.rootElements.some(h=>!oo(e,h)))return!1;for(const h of r)if(!xp(c,h))return!1;return!0})).flat(),l=new Set;for(const a of o)for(const u of a.rootElements)l.add(u);return[...l]}};function Np(e,t){const n=e.replace(/^[a-zA-Z]:/,"").replace(/\\/g,"/");let r=n.substring(n.lastIndexOf("/")+1);return t&&r.endsWith(t)&&(r=r.substring(0,r.length-t.length)),r}function Qy(e,t){return t?t.toUpperCase():""}const Ky=/(?:^|[-_/])(\w)/g,Cp=e=>e&&e.replace(Ky,Qy);function Gy(e){function t(c){const h=c.name||c._componentTag||c.__playwright_guessedName;if(h)return h;const f=c.__file;if(f)return Cp(Np(f,".vue"))}function n(c,h){return c.type.__playwright_guessedName=h,h}function r(c){var f,g,w,y;const h=t(c.type||{});if(h)return h;if(c.root===c)return"Root";for(const x in(g=(f=c.parent)==null?void 0:f.type)==null?void 0:g.components)if(((w=c.parent)==null?void 0:w.type.components[x])===c.type)return n(c,x);for(const x in(y=c.appContext)==null?void 0:y.components)if(c.appContext.components[x]===c.type)return n(c,x);return"Anonymous Component"}function i(c){return c._isBeingDestroyed||c.isUnmounted}function s(c){return c.subTree.type.toString()==="Symbol(Fragment)"}function o(c){const h=[];return c.component&&h.push(c.component),c.suspense&&h.push(...o(c.suspense.activeBranch)),Array.isArray(c.children)&&c.children.forEach(f=>{f.component?h.push(f.component):h.push(...o(f))}),h.filter(f=>{var g;return!i(f)&&!((g=f.type.devtools)!=null&&g.hide)})}function l(c){return s(c)?a(c.subTree):[c.subTree.el]}function a(c){if(!c.children)return[];const h=[];for(let f=0,g=c.children.length;f<g;f++){const w=c.children[f];w.component?h.push(...l(w.component)):w.el&&h.push(w.el)}return h}function u(c){return{name:r(c),children:o(c.subTree).map(u),rootElements:l(c),props:c.props}}return u(e)}function Yy(e){function t(s){const o=s.displayName||s.name||s._componentTag;if(o)return o;const l=s.__file;if(l)return Cp(Np(l,".vue"))}function n(s){const o=t(s.$options||s.fnOptions||{});return o||(s.$root===s?"Root":"Anonymous Component")}function r(s){return s.$children?s.$children:Array.isArray(s.subTree.children)?s.subTree.children.filter(o=>!!o.component).map(o=>o.component):[]}function i(s){return{name:n(s),children:r(s).map(i),rootElements:[s.$el],props:s._props}}return i(e)}function bp(e,t,n=[]){t(e)&&n.push(e);for(const r of e.children)bp(r,t,n);return n}function Lp(e,t=[]){const r=(e.ownerDocument||e).createTreeWalker(e,NodeFilter.SHOW_ELEMENT),i=new Set;do{const s=r.currentNode;s.__vue__&&i.add(s.__vue__.$root),s.__vue_app__&&s._vnode&&s._vnode.component&&t.push({root:s._vnode.component,version:3});const o=s instanceof Element?s.shadowRoot:null;o&&Lp(o,t)}while(r.nextNode());for(const s of i)t.push({version:2,root:s});return t}const Jy={queryAll(e,t){const n=e.ownerDocument||e,{name:r,attributes:i}=mn(t,!1),l=Lp(n).map(u=>u.version===3?Gy(u.root):Yy(u.root)).map(u=>bp(u,c=>{if(r&&c.name!==r||c.rootElements.some(h=>!oo(e,h)))return!1;for(const h of i)if(!xp(c.props,h))return!1;return!0})).flat(),a=new Set;for(const u of l)for(const c of u.rootElements)a.add(c);return[...a]}},Ap=["selected","checked","pressed","expanded","level","disabled","name","include-hidden"];Ap.sort();function kr(e,t,n){if(!t.includes(n))throw new Error(`"${e}" attribute is only supported for roles: ${t.slice().sort().map(r=>`"${r}"`).join(", ")}`)}function bn(e,t){if(e.op!=="<truthy>"&&!t.includes(e.value))throw new Error(`"${e.name}" must be one of ${t.map(n=>JSON.stringify(n)).join(", ")}`)}function Ln(e,t){if(!t.includes(e.op))throw new Error(`"${e.name}" does not support "${e.op}" matcher`)}function Zy(e,t){const n={role:t};for(const r of e)switch(r.name){case"checked":{kr(r.name,ap,t),bn(r,[!0,!1,"mixed"]),Ln(r,["<truthy>","="]),n.checked=r.op==="<truthy>"?!0:r.value;break}case"pressed":{kr(r.name,cp,t),bn(r,[!0,!1,"mixed"]),Ln(r,["<truthy>","="]),n.pressed=r.op==="<truthy>"?!0:r.value;break}case"selected":{kr(r.name,lp,t),bn(r,[!0,!1]),Ln(r,["<truthy>","="]),n.selected=r.op==="<truthy>"?!0:r.value;break}case"expanded":{kr(r.name,dp,t),bn(r,[!0,!1]),Ln(r,["<truthy>","="]),n.expanded=r.op==="<truthy>"?!0:r.value;break}case"level":{if(kr(r.name,fp,t),typeof r.value=="string"&&(r.value=+r.value),r.op!=="="||typeof r.value!="number"||Number.isNaN(r.value))throw new Error('"level" attribute must be compared to a number');n.level=r.value;break}case"disabled":{bn(r,[!0,!1]),Ln(r,["<truthy>","="]),n.disabled=r.op==="<truthy>"?!0:r.value;break}case"name":{if(r.op==="<truthy>")throw new Error('"name" attribute must have a value');if(typeof r.value!="string"&&!(r.value instanceof RegExp))throw new Error('"name" attribute must be a string or a regular expression');n.name=r.value,n.nameOp=r.op,n.exact=r.caseSensitive;break}case"include-hidden":{bn(r,[!0,!1]),Ln(r,["<truthy>","="]),n.includeHidden=r.op==="<truthy>"?!0:r.value;break}default:throw new Error(`Unknown attribute "${r.name}", must be one of ${Ap.map(i=>`"${i}"`).join(", ")}.`)}return n}function ew(e,t,n){const r=[],i=o=>{if(Ne(o)===t.role&&!(t.selected!==void 0&&Oy(o)!==t.selected)&&!(t.checked!==void 0&&Dy(o)!==t.checked)&&!(t.pressed!==void 0&&zy(o)!==t.pressed)&&!(t.expanded!==void 0&&Fy(o)!==t.expanded)&&!(t.level!==void 0&&Hy(o)!==t.level)&&!(t.disabled!==void 0&&hp(o)!==t.disabled)&&!(!t.includeHidden&&ht(o))){if(t.name!==void 0){const l=Ue(Fs(o,!!t.includeHidden));if(typeof t.name=="string"&&(t.name=Ue(t.name)),n&&!t.exact&&t.nameOp==="="&&(t.nameOp="*="),!Sp(l,{name:"",jsonPath:[],op:t.nameOp||"=",value:t.name,caseSensitive:!!t.exact}))return}r.push(o)}},s=o=>{const l=[];o.shadowRoot&&l.push(o.shadowRoot);for(const a of o.querySelectorAll("*"))i(a),a.shadowRoot&&l.push(a.shadowRoot);l.forEach(s)};return s(e),r}function Fc(e){return{queryAll:(t,n)=>{const r=mn(n,!0),i=r.name.toLowerCase();if(!i)throw new Error("Role must not be empty");const s=Zy(r.attributes,i);yp();try{return ew(t,s,e)}finally{wp()}}}}function tw(e,t,n){const r=e.left-t.right;if(!(r<0||n!==void 0&&r>n))return r+Math.max(t.bottom-e.bottom,0)+Math.max(e.top-t.top,0)}function nw(e,t,n){const r=t.left-e.right;if(!(r<0||n!==void 0&&r>n))return r+Math.max(t.bottom-e.bottom,0)+Math.max(e.top-t.top,0)}function rw(e,t,n){const r=t.top-e.bottom;if(!(r<0||n!==void 0&&r>n))return r+Math.max(e.left-t.left,0)+Math.max(t.right-e.right,0)}function iw(e,t,n){const r=e.top-t.bottom;if(!(r<0||n!==void 0&&r>n))return r+Math.max(e.left-t.left,0)+Math.max(t.right-e.right,0)}function sw(e,t,n){const r=n===void 0?50:n;let i=0;return e.left-t.right>=0&&(i+=e.left-t.right),t.left-e.right>=0&&(i+=t.left-e.right),t.top-e.bottom>=0&&(i+=t.top-e.bottom),e.top-t.bottom>=0&&(i+=e.top-t.bottom),i>r?void 0:i}const ow=["left-of","right-of","above","below","near"];function jp(e,t,n,r){const i=t.getBoundingClientRect(),s={"left-of":nw,"right-of":tw,above:rw,below:iw,near:sw}[e];let o;for(const l of n){if(l===t)continue;const a=s(i,l.getBoundingClientRect(),r);a!==void 0&&(o===void 0||a<o)&&(o=a)}return o}class lw{constructor(t){this._engines=new Map,this._cacheQueryCSS=new Map,this._cacheMatches=new Map,this._cacheQuery=new Map,this._cacheMatchesSimple=new Map,this._cacheMatchesParents=new Map,this._cacheCallMatches=new Map,this._cacheCallQuery=new Map,this._cacheQuerySimple=new Map,this._cacheText=new Map,this._retainCacheCounter=0;for(const[i,s]of t)this._engines.set(i,s);this._engines.set("not",cw),this._engines.set("is",Pr),this._engines.set("where",Pr),this._engines.set("has",aw),this._engines.set("scope",uw),this._engines.set("light",dw),this._engines.set("visible",fw),this._engines.set("text",hw),this._engines.set("text-is",pw),this._engines.set("text-matches",mw),this._engines.set("has-text",gw),this._engines.set("right-of",Nr("right-of")),this._engines.set("left-of",Nr("left-of")),this._engines.set("above",Nr("above")),this._engines.set("below",Nr("below")),this._engines.set("near",Nr("near")),this._engines.set("nth-match",vw);const n=[...this._engines.keys()];n.sort();const r=[...Uh];if(r.sort(),n.join("|")!==r.join("|"))throw new Error(`Please keep customCSSNames in sync with evaluator engines: ${n.join("|")} vs ${r.join("|")}`)}begin(){++this._retainCacheCounter}end(){--this._retainCacheCounter,this._retainCacheCounter||(this._cacheQueryCSS.clear(),this._cacheMatches.clear(),this._cacheQuery.clear(),this._cacheMatchesSimple.clear(),this._cacheMatchesParents.clear(),this._cacheCallMatches.clear(),this._cacheCallQuery.clear(),this._cacheQuerySimple.clear(),this._cacheText.clear())}_cached(t,n,r,i){t.has(n)||t.set(n,[]);const s=t.get(n),o=s.find(a=>r.every((u,c)=>a.rest[c]===u));if(o)return o.result;const l=i();return s.push({rest:r,result:l}),l}_checkSelector(t){if(!(typeof t=="object"&&t&&(Array.isArray(t)||"simples"in t&&t.simples.length)))throw new Error(`Malformed selector "${t}"`);return t}matches(t,n,r){const i=this._checkSelector(n);this.begin();try{return this._cached(this._cacheMatches,t,[i,r.scope,r.pierceShadow,r.originalScope],()=>Array.isArray(i)?this._matchesEngine(Pr,t,i,r):(this._hasScopeClause(i)&&(r=this._expandContextForScopeMatching(r)),this._matchesSimple(t,i.simples[i.simples.length-1].selector,r)?this._matchesParents(t,i,i.simples.length-2,r):!1))}finally{this.end()}}query(t,n){const r=this._checkSelector(n);this.begin();try{return this._cached(this._cacheQuery,r,[t.scope,t.pierceShadow,t.originalScope],()=>{if(Array.isArray(r))return this._queryEngine(Pr,t,r);this._hasScopeClause(r)&&(t=this._expandContextForScopeMatching(t));const i=this._scoreMap;this._scoreMap=new Map;let s=this._querySimple(t,r.simples[r.simples.length-1].selector);return s=s.filter(o=>this._matchesParents(o,r,r.simples.length-2,t)),this._scoreMap.size&&s.sort((o,l)=>{const a=this._scoreMap.get(o),u=this._scoreMap.get(l);return a===u?0:a===void 0?1:u===void 0?-1:a-u}),this._scoreMap=i,s})}finally{this.end()}}_markScore(t,n){this._scoreMap&&this._scoreMap.set(t,n)}_hasScopeClause(t){return t.simples.some(n=>n.selector.functions.some(r=>r.name==="scope"))}_expandContextForScopeMatching(t){if(t.scope.nodeType!==1)return t;const n=Te(t.scope);return n?{...t,scope:n,originalScope:t.originalScope||t.scope}:t}_matchesSimple(t,n,r){return this._cached(this._cacheMatchesSimple,t,[n,r.scope,r.pierceShadow,r.originalScope],()=>{if(t===r.scope||n.css&&!this._matchesCSS(t,n.css))return!1;for(const i of n.functions)if(!this._matchesEngine(this._getEngine(i.name),t,i.args,r))return!1;return!0})}_querySimple(t,n){return n.functions.length?this._cached(this._cacheQuerySimple,n,[t.scope,t.pierceShadow,t.originalScope],()=>{let r=n.css;const i=n.functions;r==="*"&&i.length&&(r=void 0);let s,o=-1;r!==void 0?s=this._queryCSS(t,r):(o=i.findIndex(l=>this._getEngine(l.name).query!==void 0),o===-1&&(o=0),s=this._queryEngine(this._getEngine(i[o].name),t,i[o].args));for(let l=0;l<i.length;l++){if(l===o)continue;const a=this._getEngine(i[l].name);a.matches!==void 0&&(s=s.filter(u=>this._matchesEngine(a,u,i[l].args,t)))}for(let l=0;l<i.length;l++){if(l===o)continue;const a=this._getEngine(i[l].name);a.matches===void 0&&(s=s.filter(u=>this._matchesEngine(a,u,i[l].args,t)))}return s}):this._queryCSS(t,n.css||"*")}_matchesParents(t,n,r,i){return r<0?!0:this._cached(this._cacheMatchesParents,t,[n,r,i.scope,i.pierceShadow,i.originalScope],()=>{const{selector:s,combinator:o}=n.simples[r];if(o===">"){const l=zi(t,i);return!l||!this._matchesSimple(l,s,i)?!1:this._matchesParents(l,n,r-1,i)}if(o==="+"){const l=zo(t,i);return!l||!this._matchesSimple(l,s,i)?!1:this._matchesParents(l,n,r-1,i)}if(o===""){let l=zi(t,i);for(;l;){if(this._matchesSimple(l,s,i)){if(this._matchesParents(l,n,r-1,i))return!0;if(n.simples[r-1].combinator==="")break}l=zi(l,i)}return!1}if(o==="~"){let l=zo(t,i);for(;l;){if(this._matchesSimple(l,s,i)){if(this._matchesParents(l,n,r-1,i))return!0;if(n.simples[r-1].combinator==="~")break}l=zo(l,i)}return!1}if(o===">="){let l=t;for(;l;){if(this._matchesSimple(l,s,i)){if(this._matchesParents(l,n,r-1,i))return!0;if(n.simples[r-1].combinator==="")break}l=zi(l,i)}return!1}throw new Error(`Unsupported combinator "${o}"`)})}_matchesEngine(t,n,r,i){if(t.matches)return this._callMatches(t,n,r,i);if(t.query)return this._callQuery(t,r,i).includes(n);throw new Error('Selector engine should implement "matches" or "query"')}_queryEngine(t,n,r){if(t.query)return this._callQuery(t,r,n);if(t.matches)return this._queryCSS(n,"*").filter(i=>this._callMatches(t,i,r,n));throw new Error('Selector engine should implement "matches" or "query"')}_callMatches(t,n,r,i){return this._cached(this._cacheCallMatches,n,[t,i.scope,i.pierceShadow,i.originalScope,...r],()=>t.matches(n,r,i,this))}_callQuery(t,n,r){return this._cached(this._cacheCallQuery,t,[r.scope,r.pierceShadow,r.originalScope,...n],()=>t.query(r,n,this))}_matchesCSS(t,n){return t.matches(n)}_queryCSS(t,n){return this._cached(this._cacheQueryCSS,n,[t.scope,t.pierceShadow,t.originalScope],()=>{let r=[];function i(s){if(r=r.concat([...s.querySelectorAll(n)]),!!t.pierceShadow){s.shadowRoot&&i(s.shadowRoot);for(const o of s.querySelectorAll("*"))o.shadowRoot&&i(o.shadowRoot)}}return i(t.scope),r})}_getEngine(t){const n=this._engines.get(t);if(!n)throw new Error(`Unknown selector engine "${t}"`);return n}}const Pr={matches(e,t,n,r){if(t.length===0)throw new Error('"is" engine expects non-empty selector list');return t.some(i=>r.matches(e,i,n))},query(e,t,n){if(t.length===0)throw new Error('"is" engine expects non-empty selector list');let r=[];for(const i of t)r=r.concat(n.query(e,i));return t.length===1?r:Mp(r)}},aw={matches(e,t,n,r){if(t.length===0)throw new Error('"has" engine expects non-empty selector list');return r.query({...n,scope:e},t).length>0}},uw={matches(e,t,n,r){if(t.length!==0)throw new Error('"scope" engine expects no arguments');const i=n.originalScope||n.scope;return i.nodeType===9?e===i.documentElement:e===i},query(e,t,n){if(t.length!==0)throw new Error('"scope" engine expects no arguments');const r=e.originalScope||e.scope;if(r.nodeType===9){const i=r.documentElement;return i?[i]:[]}return r.nodeType===1?[r]:[]}},cw={matches(e,t,n,r){if(t.length===0)throw new Error('"not" engine expects non-empty selector list');return!r.matches(e,t,n)}},dw={query(e,t,n){return n.query({...e,pierceShadow:!1},t)},matches(e,t,n,r){return r.matches(e,t,{...n,pierceShadow:!1})}},fw={matches(e,t,n,r){if(t.length)throw new Error('"visible" engine expects no arguments');return ci(e)}},hw={matches(e,t,n,r){if(t.length!==1||typeof t[0]!="string")throw new Error('"text" engine expects a single string');const i=Ue(t[0]).toLowerCase(),s=o=>o.normalized.toLowerCase().includes(i);return lo(r._cacheText,e,s)==="self"}},pw={matches(e,t,n,r){if(t.length!==1||typeof t[0]!="string")throw new Error('"text-is" engine expects a single string');const i=Ue(t[0]),s=o=>!i&&!o.immediate.length?!0:o.immediate.some(l=>Ue(l)===i);return lo(r._cacheText,e,s)!=="none"}},mw={matches(e,t,n,r){if(t.length===0||typeof t[0]!="string"||t.length>2||t.length===2&&typeof t[1]!="string")throw new Error('"text-matches" engine expects a regexp body and optional regexp flags');const i=new RegExp(t[0],t.length===2?t[1]:void 0),s=o=>i.test(o.full);return lo(r._cacheText,e,s)==="self"}},gw={matches(e,t,n,r){if(t.length!==1||typeof t[0]!="string")throw new Error('"has-text" engine expects a single string');if(eu(e))return!1;const i=Ue(t[0]).toLowerCase();return(o=>o.normalized.toLowerCase().includes(i))(Me(r._cacheText,e))}};function Nr(e){return{matches(t,n,r,i){const s=n.length&&typeof n[n.length-1]=="number"?n[n.length-1]:void 0,o=s===void 0?n:n.slice(0,n.length-1);if(n.length<1+(s===void 0?0:1))throw new Error(`"${e}" engine expects a selector list and optional maximum distance in pixels`);const l=i.query(r,o),a=jp(e,t,l,s);return a===void 0?!1:(i._markScore(t,a),!0)}}}const vw={query(e,t,n){let r=t[t.length-1];if(t.length<2)throw new Error('"nth-match" engine expects non-empty selector list and an index argument');if(typeof r!="number"||r<1)throw new Error('"nth-match" engine expects a one-based index as the last argument');const i=Pr.query(e,t.slice(0,t.length-1),n);return r--,r<i.length?[i[r]]:[]}};function zi(e,t){if(e!==t.scope)return t.pierceShadow?Te(e):e.parentElement||void 0}function zo(e,t){if(e!==t.scope)return e.previousElementSibling||void 0}function Mp(e){const t=new Map,n=[],r=[];function i(o){let l=t.get(o);if(l)return l;const a=Te(o);return a?i(a).children.push(o):n.push(o),l={children:[],taken:!1},t.set(o,l),l}for(const o of e)i(o).taken=!0;function s(o){const l=t.get(o);if(l.taken&&r.push(o),l.children.length>1){const a=new Set(l.children);l.children=[];let u=o.firstElementChild;for(;u&&l.children.length<a.size;)a.has(u)&&l.children.push(u),u=u.nextElementSibling;for(u=o.shadowRoot?o.shadowRoot.firstElementChild:null;u&&l.children.length<a.size;)a.has(u)&&l.children.push(u),u=u.nextElementSibling}l.children.forEach(s)}return n.forEach(s),r}const Vl=new Map,Wl=new Map,Ip=10,fr=Ip/2,Hc=1,yw=2,ww=10,xw=50,Pp=100,Rp=120,$p=140,Op=160,ql=180,Dp=200,Sw=250,_w=Pp+fr,Ew=Rp+fr,Tw=$p+fr,kw=Op+fr,Nw=ql+fr,Cw=Dp+fr,bw=300,Lw=500,Aw=510,Fo=520,zp=530,Fp=1e4,jw=1e7,Mw=1e3;function Uc(e,t,n){e._evaluator.begin(),yp();try{let r=[];if(n.forTextExpect){let o=Fi(e,t.ownerDocument.documentElement,n);for(let l=t;l;l=Te(l)){const a=An(e,l,{...n,noText:!0});if(!a)continue;if(Dt(a)<=Mw){o=a;break}}r=[us(o)]}else{if(!t.matches("input,textarea,select")&&!t.isContentEditable){const o=Ir(t,"button,select,input,[role=button],[role=checkbox],[role=radio],a,[role=link]",n.root);o&&ci(o)&&(t=o)}if(n.multiple){const o=An(e,t,n),l=An(e,t,{...n,noText:!0});let a=[o,l];if(Vl.clear(),Wl.clear(),o&&Ho(o)&&a.push(An(e,t,{...n,noCSSId:!0})),l&&Ho(l)&&a.push(An(e,t,{...n,noText:!0,noCSSId:!0})),a=a.filter(Boolean),!a.length){const u=Fi(e,t,n);a.push(u),Ho(u)&&a.push(Fi(e,t,{...n,noCSSId:!0}))}r=[...new Set(a.map(u=>us(u)))]}else{const o=An(e,t,n)||Fi(e,t,n);r=[us(o)]}}const i=r[0],s=e.parseSelector(i);return{selector:i,selectors:r,elements:e.querySelectorAll(s,n.root??t.ownerDocument)}}finally{Vl.clear(),Wl.clear(),wp(),e._evaluator.end()}}function Bc(e){return e.filter(t=>t[0].selector[0]!=="/")}function An(e,t,n){if(n.root&&!oo(n.root,t))throw new Error("Target element must belong to the root's subtree");if(t===n.root)return[{engine:"css",selector:":scope",score:1}];if(t.ownerDocument.documentElement===t)return[{engine:"css",selector:"html",score:1}];const r=(s,o)=>{const l=s===t;let a=o?Pw(e,s,s===t):[];s!==t&&(a=Bc(a));const u=Iw(e,s,n).filter(f=>!n.omitInternalEngines||!f.engine.startsWith("internal:")).map(f=>[f]);let c=Vc(e,n.root??t.ownerDocument,s,[...a,...u],l);a=Bc(a);const h=f=>{const g=o&&!f.length,w=[...f,...u].filter(x=>c?Dt(x)<Dt(c):!0);let y=w[0];if(y)for(let x=Te(s);x&&x!==n.root;x=Te(x)){const p=i(x,g);if(!p||c&&Dt([...p,...y])>=Dt(c))continue;if(y=Vc(e,x,s,w,l),!y)return;const m=[...p,...y];(!c||Dt(m)<Dt(c))&&(c=m)}};return h(a),s===t&&a.length&&h([]),c},i=(s,o)=>{const l=o?Vl:Wl;let a=l.get(s);return a===void 0&&(a=r(s,o),l.set(s,a)),a};return r(t,!n.noText)}function Iw(e,t,n){const r=[];{for(const o of["data-testid","data-test-id","data-test"])o!==n.testIdAttributeName&&t.getAttribute(o)&&r.push({engine:"css",selector:`[${o}=${Er(t.getAttribute(o))}]`,score:yw});if(!n.noCSSId){const o=t.getAttribute("id");o&&!Rw(o)&&r.push({engine:"css",selector:Hp(o),score:Lw})}r.push({engine:"css",selector:Qe(t.nodeName.toLowerCase()),score:zp})}if(t.nodeName==="IFRAME"){for(const o of["name","title"])t.getAttribute(o)&&r.push({engine:"css",selector:`${Qe(t.nodeName.toLowerCase())}[${o}=${Er(t.getAttribute(o))}]`,score:ww});return t.getAttribute(n.testIdAttributeName)&&r.push({engine:"css",selector:`[${n.testIdAttributeName}=${Er(t.getAttribute(n.testIdAttributeName))}]`,score:Hc}),Xl([r]),r}if(t.getAttribute(n.testIdAttributeName)&&r.push({engine:"internal:testid",selector:`[${n.testIdAttributeName}=${Ee(t.getAttribute(n.testIdAttributeName),!0)}]`,score:Hc}),t.nodeName==="INPUT"||t.nodeName==="TEXTAREA"){const o=t;if(o.placeholder){r.push({engine:"internal:attr",selector:`[placeholder=${Ee(o.placeholder,!0)}]`,score:_w});for(const l of Wn(o.placeholder))r.push({engine:"internal:attr",selector:`[placeholder=${Ee(l.text,!1)}]`,score:Pp-l.scoreBouns})}}const i=_p(e._evaluator._cacheText,t);for(const o of i){const l=o.normalized;r.push({engine:"internal:label",selector:Ge(l,!0),score:Ew});for(const a of Wn(l))r.push({engine:"internal:label",selector:Ge(a.text,!1),score:Rp-a.scoreBouns})}const s=Ne(t);return s&&!["none","presentation"].includes(s)&&r.push({engine:"internal:role",selector:s,score:Aw}),t.getAttribute("name")&&["BUTTON","FORM","FIELDSET","FRAME","IFRAME","INPUT","KEYGEN","OBJECT","OUTPUT","SELECT","TEXTAREA","MAP","META","PARAM"].includes(t.nodeName)&&r.push({engine:"css",selector:`${Qe(t.nodeName.toLowerCase())}[name=${Er(t.getAttribute("name"))}]`,score:Fo}),["INPUT","TEXTAREA"].includes(t.nodeName)&&t.getAttribute("type")!=="hidden"&&t.getAttribute("type")&&r.push({engine:"css",selector:`${Qe(t.nodeName.toLowerCase())}[type=${Er(t.getAttribute("type"))}]`,score:Fo}),["INPUT","TEXTAREA","SELECT"].includes(t.nodeName)&&t.getAttribute("type")!=="hidden"&&r.push({engine:"css",selector:Qe(t.nodeName.toLowerCase()),score:Fo+1}),Xl([r]),r}function Pw(e,t,n){if(t.nodeName==="SELECT")return[];const r=[],i=t.getAttribute("title");if(i){r.push([{engine:"internal:attr",selector:`[title=${Ee(i,!0)}]`,score:Cw}]);for(const a of Wn(i))r.push([{engine:"internal:attr",selector:`[title=${Ee(a.text,!1)}]`,score:Dp-a.scoreBouns}])}const s=t.getAttribute("alt");if(s&&["APPLET","AREA","IMG","INPUT"].includes(t.nodeName)){r.push([{engine:"internal:attr",selector:`[alt=${Ee(s,!0)}]`,score:kw}]);for(const a of Wn(s))r.push([{engine:"internal:attr",selector:`[alt=${Ee(a.text,!1)}]`,score:Op-a.scoreBouns}])}const o=Me(e._evaluator._cacheText,t).normalized;if(o){const a=Wn(o);if(n){o.length<=80&&r.push([{engine:"internal:text",selector:Ge(o,!0),score:Nw}]);for(const c of a)r.push([{engine:"internal:text",selector:Ge(c.text,!1),score:ql-c.scoreBouns}])}const u={engine:"css",selector:Qe(t.nodeName.toLowerCase()),score:zp};for(const c of a)r.push([u,{engine:"internal:has-text",selector:Ge(c.text,!1),score:ql-c.scoreBouns}]);if(o.length<=80){const c=new RegExp("^"+s0(o)+"$");r.push([u,{engine:"internal:has-text",selector:Ge(c,!1),score:Sw}])}}const l=Ne(t);if(l&&!["none","presentation"].includes(l)){const a=Fs(t,!1);if(a){r.push([{engine:"internal:role",selector:`${l}[name=${Ee(a,!0)}]`,score:Tw}]);for(const u of Wn(a))r.push([{engine:"internal:role",selector:`${l}[name=${Ee(u.text,!1)}]`,score:$p-u.scoreBouns}])}}return Xl(r),r}function Hp(e){return/^[a-zA-Z][a-zA-Z0-9\-\_]+$/.test(e)?"#"+e:`[id="${Qe(e)}"]`}function Ho(e){return e.some(t=>t.engine==="css"&&(t.selector.startsWith("#")||t.selector.startsWith('[id="')))}function Fi(e,t,n){const r=n.root??t.ownerDocument,i=[];function s(l){const a=i.slice();l&&a.unshift(l);const u=a.join(" > "),c=e.parseSelector(u);return e.querySelector(c,r,!1)===t?u:void 0}function o(l){const a={engine:"css",selector:l,score:jw},u=e.parseSelector(l),c=e.querySelectorAll(u,r);if(c.length===1)return[a];const h={engine:"nth",selector:String(c.indexOf(t)),score:Fp};return[a,h]}for(let l=t;l&&l!==r;l=Te(l)){const a=l.nodeName.toLowerCase();let u="";if(l.id&&!n.noCSSId){const f=Hp(l.id),g=s(f);if(g)return o(g);u=f}const c=l.parentNode,h=[...l.classList];for(let f=0;f<h.length;++f){const g="."+Qe(h.slice(0,f+1).join(".")),w=s(g);if(w)return o(w);!u&&c&&c.querySelectorAll(g).length===1&&(u=g)}if(c){const f=[...c.children],w=f.filter(x=>x.nodeName.toLowerCase()===a).indexOf(l)===0?Qe(a):`${Qe(a)}:nth-child(${1+f.indexOf(l)})`,y=s(w);if(y)return o(y);u||(u=w)}else u||(u=Qe(a));i.unshift(u)}return o(s())}function Xl(e){for(const t of e)for(const n of t)n.score>xw&&n.score<bw&&(n.score+=Math.min(Ip,n.selector.length/10|0))}function us(e){const t=[];let n="";for(const{engine:r,selector:i}of e)t.length&&(n!=="css"||r!=="css"||i.startsWith(":nth-match("))&&t.push(">>"),n=r,r==="css"?t.push(i):t.push(`${r}=${i}`);return t.join(" ")}function Dt(e){let t=0;for(let n=0;n<e.length;n++)t+=e[n].score*(e.length-n);return t}function Vc(e,t,n,r,i){const s=r.map(l=>({tokens:l,score:Dt(l)}));s.sort((l,a)=>l.score-a.score);let o=null;for(const{tokens:l}of s){const a=e.parseSelector(us(l)),u=e.querySelectorAll(a,t);if(u[0]===n&&u.length===1)return l;const c=u.indexOf(n);if(!i||o||c===-1||u.length>5)continue;const h={engine:"nth",selector:String(c),score:Fp};o=[...l,h]}return o}function Rw(e){let t,n=0;for(let r=0;r<e.length;++r){const i=e[r];let s;if(!(i==="-"||i==="_")){if(i>="a"&&i<="z"?s="lower":i>="A"&&i<="Z"?s="upper":i>="0"&&i<="9"?s="digit":s="other",s==="lower"&&t==="upper"){t=s;continue}t&&t!==s&&++n,t=s}}return n>=e.length/4}function Hi(e,t){if(e.length<=t)return e;e=e.substring(0,t);const n=e.match(/^(.*)\b(.+?)$/);return n?n[1].trimEnd():""}function Wn(e){let t=[];{const n=e.match(/^([\d.,]+)[^.,\w]/),r=n?n[1].length:0;if(r){const i=Hi(e.substring(r).trimStart(),80);t.push({text:i,scoreBouns:i.length<=30?2:1})}}{const n=e.match(/[^.,\w]([\d.,]+)$/),r=n?n[1].length:0;if(r){const i=Hi(e.substring(0,e.length-r).trimEnd(),80);t.push({text:i,scoreBouns:i.length<=30?2:1})}}return e.length<=30?t.push({text:e,scoreBouns:0}):(t.push({text:Hi(e,80),scoreBouns:0}),t.push({text:Hi(e,30),scoreBouns:1})),t=t.filter(n=>n.text),t.length||t.push({text:e.substring(0,80),scoreBouns:0}),t}const Wc=":host{font-size:13px;font-family:system-ui,Ubuntu,Droid Sans,sans-serif;color:#333}svg{position:absolute;height:0}x-pw-tooltip{-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);background-color:#fff;border-radius:6px;box-shadow:0 .5rem 1.2rem #0000004d;display:none;font-size:12.8px;font-weight:400;left:0;line-height:1.5;max-width:600px;position:absolute;top:0;padding:0;flex-direction:column;overflow:hidden}x-pw-tooltip-line{display:flex;max-width:600px;padding:6px;-webkit-user-select:none;user-select:none;cursor:pointer}x-pw-tooltip-line.selectable:hover{background-color:#f2f2f2;overflow:hidden}x-pw-tooltip-footer{display:flex;max-width:600px;padding:6px;-webkit-user-select:none;user-select:none;color:#777}x-pw-dialog{background-color:#fff;pointer-events:auto;border-radius:6px;box-shadow:0 .5rem 1.2rem #0000004d;display:flex;flex-direction:column;position:absolute;width:400px;height:150px;z-index:10;font-size:13px}x-pw-dialog-body{display:flex;flex-direction:column;flex:auto}x-pw-dialog-body label{margin:5px 8px;display:flex;flex-direction:row;align-items:center}x-pw-highlight{position:absolute;top:0;left:0;width:0;height:0}x-pw-action-point{position:absolute;width:20px;height:20px;background:red;border-radius:10px;margin:-10px 0 0 -10px;z-index:2}x-pw-separator{height:1px;margin:6px 9px;background:#949494e5}x-pw-tool-gripper{height:28px;width:24px;margin:2px 0;cursor:grab}x-pw-tool-gripper:active{cursor:grabbing}x-pw-tool-gripper>x-div{width:16px;height:16px;margin:6px 4px;clip-path:url(#icon-gripper);background-color:#555}x-pw-tools-list>label{display:flex;align-items:center;margin:0 10px;-webkit-user-select:none;user-select:none}x-pw-tools-list{display:flex;width:100%;border-bottom:1px solid #dddddd}x-pw-tool-item{pointer-events:auto;cursor:pointer;height:28px;width:28px;border-radius:3px}x-pw-tool-item:not(.disabled):hover{background-color:#dbdbdb}x-pw-tool-item.active{background-color:#8acae480}x-pw-tool-item.active:not(.disabled):hover{background-color:#8acae4c4}x-pw-tool-item>x-div{width:16px;height:16px;margin:6px;background-color:#3a3a3a}x-pw-tool-item.disabled>x-div{background-color:#61616180;cursor:default}x-pw-tool-item.record.active{background-color:transparent}x-pw-tool-item.record.active:hover{background-color:#dbdbdb}x-pw-tool-item.record.active>x-div{background-color:#a1260d}x-pw-tool-item.accept>x-div{background-color:#388a34}x-pw-tool-item.record>x-div{clip-path:url(#icon-circle-large-filled)}x-pw-tool-item.pick-locator>x-div{clip-path:url(#icon-inspect)}x-pw-tool-item.text>x-div{clip-path:url(#icon-whole-word)}x-pw-tool-item.visibility>x-div{clip-path:url(#icon-eye)}x-pw-tool-item.value>x-div{clip-path:url(#icon-symbol-constant)}x-pw-tool-item.accept>x-div{clip-path:url(#icon-check)}x-pw-tool-item.cancel>x-div{clip-path:url(#icon-close)}x-pw-tool-item.succeeded>x-div{clip-path:url(#icon-pass);background-color:#388a34!important}x-pw-overlay{position:absolute;top:0;max-width:min-content;z-index:2147483647;background:transparent;pointer-events:auto}x-pw-overlay x-pw-tools-list{background-color:#fffd;box-shadow:#0000001a 0 5px 5px;border-radius:3px;border-bottom:none}x-pw-overlay x-pw-tool-item{margin:2px}textarea.text-editor{font-family:system-ui,Ubuntu,Droid Sans,sans-serif;flex:auto;border:none;margin:6px 10px;color:#333;outline:1px solid transparent!important;resize:none;padding:0;font-size:13px}textarea.text-editor.does-not-match{outline:1px solid red!important}x-div{display:block}x-spacer{flex:auto}*{box-sizing:border-box}*[hidden]{display:none!important}x-locator-editor{flex:none;width:100%;height:60px;padding:4px;border-bottom:1px solid #dddddd;outline:1px solid transparent}x-locator-editor.does-not-match{outline:1px solid red}.CodeMirror{width:100%!important;height:100%!important}";class Uo{constructor(t){this._highlightEntries=[],this._highlightOptions={},this._language="javascript",this._injectedScript=t;const n=t.document;this._isUnderTest=t.isUnderTest,this._glassPaneElement=n.createElement("x-pw-glass"),this._glassPaneElement.style.position="fixed",this._glassPaneElement.style.top="0",this._glassPaneElement.style.right="0",this._glassPaneElement.style.bottom="0",this._glassPaneElement.style.left="0",this._glassPaneElement.style.zIndex="2147483646",this._glassPaneElement.style.pointerEvents="none",this._glassPaneElement.style.display="flex",this._glassPaneElement.style.backgroundColor="transparent";for(const r of["click","auxclick","dragstart","input","keydown","keyup","pointerdown","pointerup","mousedown","mouseup","mouseleave","focus","scroll"])this._glassPaneElement.addEventListener(r,i=>{i.stopPropagation(),i.stopImmediatePropagation(),i.type==="click"&&i.button===0&&this._highlightOptions.tooltipListItemSelected&&this._highlightOptions.tooltipListItemSelected(void 0)});if(this._actionPointElement=n.createElement("x-pw-action-point"),this._actionPointElement.setAttribute("hidden","true"),this._glassPaneShadow=this._glassPaneElement.attachShadow({mode:this._isUnderTest?"open":"closed"}),typeof this._glassPaneShadow.adoptedStyleSheets.push=="function"){const r=new this._injectedScript.window.CSSStyleSheet;r.replaceSync(Wc),this._glassPaneShadow.adoptedStyleSheets.push(r)}else{const r=this._injectedScript.document.createElement("style");r.textContent=Wc,this._glassPaneShadow.appendChild(r)}this._glassPaneShadow.appendChild(this._actionPointElement)}install(){this._injectedScript.document.documentElement.appendChild(this._glassPaneElement)}setLanguage(t){this._language=t}runHighlightOnRaf(t){this._rafRequest&&cancelAnimationFrame(this._rafRequest),this.updateHighlight(this._injectedScript.querySelectorAll(t,this._injectedScript.document.documentElement),{tooltipText:Yt(this._language,Sn(t))}),this._rafRequest=this._injectedScript.builtinRequestAnimationFrame(()=>this.runHighlightOnRaf(t))}uninstall(){this._rafRequest&&cancelAnimationFrame(this._rafRequest),this._glassPaneElement.remove()}showActionPoint(t,n){this._actionPointElement.style.top=n+"px",this._actionPointElement.style.left=t+"px",this._actionPointElement.hidden=!1}hideActionPoint(){this._actionPointElement.hidden=!0}clearHighlight(){var t,n;for(const r of this._highlightEntries)(t=r.highlightElement)==null||t.remove(),(n=r.tooltipElement)==null||n.remove();this._highlightEntries=[],this._highlightOptions={},this._glassPaneElement.style.pointerEvents="none"}updateHighlight(t,n){this._innerUpdateHighlight(t,n)}maskElements(t,n){this._innerUpdateHighlight(t,{color:n})}_innerUpdateHighlight(t,n){let r=n.color;if(r||(r=t.length>1?"#f6b26b7f":"#6fa8dc7f"),!this._highlightIsUpToDate(t,n)){this.clearHighlight(),this._highlightOptions=n,this._glassPaneElement.style.pointerEvents=n.tooltipListItemSelected?"initial":"none";for(let i=0;i<t.length;++i){const s=this._createHighlightElement();this._glassPaneShadow.appendChild(s);let o;if(n.tooltipList||n.tooltipText||n.tooltipFooter){o=this._injectedScript.document.createElement("x-pw-tooltip"),this._glassPaneShadow.appendChild(o),o.style.top="0",o.style.left="0",o.style.display="flex";let l=[];if(n.tooltipList)l=n.tooltipList;else if(n.tooltipText){const a=t.length>1?` [${i+1} of ${t.length}]`:"";l=[n.tooltipText+a]}for(let a=0;a<l.length;a++){const u=this._injectedScript.document.createElement("x-pw-tooltip-line");u.textContent=l[a],o.appendChild(u),n.tooltipListItemSelected&&(u.classList.add("selectable"),u.addEventListener("click",()=>{var c;return(c=n.tooltipListItemSelected)==null?void 0:c.call(n,a)}))}if(n.tooltipFooter){const a=this._injectedScript.document.createElement("x-pw-tooltip-footer");a.textContent=n.tooltipFooter,o.appendChild(a)}}this._highlightEntries.push({targetElement:t[i],tooltipElement:o,highlightElement:s})}for(const i of this._highlightEntries){if(i.box=i.targetElement.getBoundingClientRect(),!i.tooltipElement)continue;const{anchorLeft:s,anchorTop:o}=this.tooltipPosition(i.box,i.tooltipElement);i.tooltipTop=o,i.tooltipLeft=s}for(const i of this._highlightEntries){i.tooltipElement&&(i.tooltipElement.style.top=i.tooltipTop+"px",i.tooltipElement.style.left=i.tooltipLeft+"px");const s=i.box;i.highlightElement.style.backgroundColor=r,i.highlightElement.style.left=s.x+"px",i.highlightElement.style.top=s.y+"px",i.highlightElement.style.width=s.width+"px",i.highlightElement.style.height=s.height+"px",i.highlightElement.style.display="block",this._isUnderTest&&console.error("Highlight box for test: "+JSON.stringify({x:s.x,y:s.y,width:s.width,height:s.height}))}}}firstBox(){var t;return(t=this._highlightEntries[0])==null?void 0:t.box}tooltipPosition(t,n){const r=n.offsetWidth,i=n.offsetHeight,s=this._glassPaneElement.offsetWidth,o=this._glassPaneElement.offsetHeight;let l=t.left;l+r>s-5&&(l=s-r-5);let a=t.bottom+5;return a+i>o-5&&(t.top>i+5?a=t.top-i-5:a=o-5-i),{anchorLeft:l,anchorTop:a}}_highlightIsUpToDate(t,n){var r,i;if(n.tooltipText!==this._highlightOptions.tooltipText||n.tooltipListItemSelected!==this._highlightOptions.tooltipListItemSelected||n.tooltipFooter!==this._highlightOptions.tooltipFooter||((r=n.tooltipList)==null?void 0:r.length)!==((i=this._highlightOptions.tooltipList)==null?void 0:i.length))return!1;if(n.tooltipList&&this._highlightOptions.tooltipList){for(let s=0;s<n.tooltipList.length;s++)if(n.tooltipList[s]!==this._highlightOptions.tooltipList[s])return!1}if(t.length!==this._highlightEntries.length)return!1;for(let s=0;s<this._highlightEntries.length;++s){if(t[s]!==this._highlightEntries[s].targetElement)return!1;const o=this._highlightEntries[s].box;if(!o)return!1;const l=t[s].getBoundingClientRect();if(l.top!==o.top||l.right!==o.right||l.bottom!==o.bottom||l.left!==o.left)return!1}return!0}_createHighlightElement(){return this._injectedScript.document.createElement("x-pw-highlight")}appendChild(t){this._glassPaneShadow.appendChild(t)}}class Up{constructor(t,n,r,i,s,o,l){this.onGlobalListenersRemoved=new Set,this._testIdAttributeNameForStrictErrorAndConsoleCodegen="data-testid",this.utils={isInsideScope:oo,elementText:Me,asLocator:Yt,normalizeWhiteSpace:Ue,cacheNormalizedWhitespaces:r0},this.window=t,this.document=t.document,this.isUnderTest=n,this._sdkLanguage=r,this._testIdAttributeNameForStrictErrorAndConsoleCodegen=i,this._evaluator=new lw(new Map),this._engines=new Map,this._engines.set("xpath",Mc),this._engines.set("xpath:light",Mc),this._engines.set("_react",Xy),this._engines.set("_vue",Jy),this._engines.set("role",Fc(!1)),this._engines.set("text",this._createTextEngine(!0,!1)),this._engines.set("text:light",this._createTextEngine(!1,!1)),this._engines.set("id",this._createAttributeEngine("id",!0)),this._engines.set("id:light",this._createAttributeEngine("id",!1)),this._engines.set("data-testid",this._createAttributeEngine("data-testid",!0)),this._engines.set("data-testid:light",this._createAttributeEngine("data-testid",!1)),this._engines.set("data-test-id",this._createAttributeEngine("data-test-id",!0)),this._engines.set("data-test-id:light",this._createAttributeEngine("data-test-id",!1)),this._engines.set("data-test",this._createAttributeEngine("data-test",!0)),this._engines.set("data-test:light",this._createAttributeEngine("data-test",!1)),this._engines.set("css",this._createCSSEngine()),this._engines.set("nth",{queryAll:()=>[]}),this._engines.set("visible",this._createVisibleEngine()),this._engines.set("internal:control",this._createControlEngine()),this._engines.set("internal:has",this._createHasEngine()),this._engines.set("internal:has-not",this._createHasNotEngine()),this._engines.set("internal:and",{queryAll:()=>[]}),this._engines.set("internal:or",{queryAll:()=>[]}),this._engines.set("internal:chain",this._createInternalChainEngine()),this._engines.set("internal:label",this._createInternalLabelEngine()),this._engines.set("internal:text",this._createTextEngine(!0,!0)),this._engines.set("internal:has-text",this._createInternalHasTextEngine()),this._engines.set("internal:has-not-text",this._createInternalHasNotTextEngine()),this._engines.set("internal:attr",this._createNamedAttributeEngine()),this._engines.set("internal:testid",this._createNamedAttributeEngine()),this._engines.set("internal:role",Fc(!0));for(const{name:a,engine:u}of l)this._engines.set(a,u);this._stableRafCount=s,this._browserName=o,Cy(o),this._setupGlobalListenersRemovalDetection(),this._setupHitTargetInterceptors(),n&&(this.window.__injectedScript=this)}builtinSetTimeout(t,n){var r;return(r=this.window.__pwClock)!=null&&r.builtin?this.window.__pwClock.builtin.setTimeout(t,n):setTimeout(t,n)}builtinRequestAnimationFrame(t){var n;return(n=this.window.__pwClock)!=null&&n.builtin?this.window.__pwClock.builtin.requestAnimationFrame(t):requestAnimationFrame(t)}eval(t){return this.window.eval(t)}testIdAttributeNameForStrictErrorAndConsoleCodegen(){return this._testIdAttributeNameForStrictErrorAndConsoleCodegen}parseSelector(t){const n=so(t);return T0(n,r=>{if(!this._engines.has(r.name))throw this.createStacklessError(`Unknown engine "${r.name}" while parsing selector ${t}`)}),n}generateSelector(t,n){return Uc(this,t,n)}generateSelectorSimple(t,n){return Uc(this,t,{...n,testIdAttributeName:this._testIdAttributeNameForStrictErrorAndConsoleCodegen}).selector}querySelector(t,n,r){const i=this.querySelectorAll(t,n);if(r&&i.length>1)throw this.strictModeViolationError(t,i);return i[0]}_queryNth(t,n){const r=[...t];let i=+n.body;return i===-1&&(i=r.length-1),new Set(r.slice(i,i+1))}_queryLayoutSelector(t,n,r){const i=n.name,s=n.body,o=[],l=this.querySelectorAll(s.parsed,r);for(const a of t){const u=jp(i,a,l,s.distance);u!==void 0&&o.push({element:a,score:u})}return o.sort((a,u)=>a.score-u.score),new Set(o.map(a=>a.element))}querySelectorAll(t,n){if(t.capture!==void 0){if(t.parts.some(i=>i.name==="nth"))throw this.createStacklessError("Can't query n-th element in a request with the capture.");const r={parts:t.parts.slice(0,t.capture+1)};if(t.capture<t.parts.length-1){const i={parts:t.parts.slice(t.capture+1)},s={name:"internal:has",body:{parsed:i},source:Sn(i)};r.parts.push(s)}return this.querySelectorAll(r,n)}if(!n.querySelectorAll)throw this.createStacklessError("Node is not queryable.");if(t.capture!==void 0)throw this.createStacklessError("Internal error: there should not be a capture in the selector.");if(n.nodeType===11&&t.parts.length===1&&t.parts[0].name==="css"&&t.parts[0].source===":scope")return[n];this._evaluator.begin();try{let r=new Set([n]);for(const i of t.parts)if(i.name==="nth")r=this._queryNth(r,i);else if(i.name==="internal:and"){const s=this.querySelectorAll(i.body.parsed,n);r=new Set(s.filter(o=>r.has(o)))}else if(i.name==="internal:or"){const s=this.querySelectorAll(i.body.parsed,n);r=new Set(Mp(new Set([...r,...s])))}else if(ow.includes(i.name))r=this._queryLayoutSelector(r,i,n);else{const s=new Set;for(const o of r){const l=this._queryEngineAll(i,o);for(const a of l)s.add(a)}r=s}return[...r]}finally{this._evaluator.end()}}_queryEngineAll(t,n){const r=this._engines.get(t.name).queryAll(n,t.body);for(const i of r)if(!("nodeName"in i))throw this.createStacklessError(`Expected a Node but got ${Object.prototype.toString.call(i)}`);return r}_createAttributeEngine(t,n){const r=i=>[{simples:[{selector:{css:`[${t}=${JSON.stringify(i)}]`,functions:[]},combinator:""}]}];return{queryAll:(i,s)=>this._evaluator.query({scope:i,pierceShadow:n},r(s))}}_createCSSEngine(){return{queryAll:(t,n)=>this._evaluator.query({scope:t,pierceShadow:!0},n)}}_createTextEngine(t,n){return{queryAll:(i,s)=>{const{matcher:o,kind:l}=Bi(s,n),a=[];let u=null;const c=f=>{if(l==="lax"&&u&&u.contains(f))return!1;const g=lo(this._evaluator._cacheText,f,o);g==="none"&&(u=f),(g==="self"||g==="selfAndChildren"&&l==="strict"&&!n)&&a.push(f)};i.nodeType===Node.ELEMENT_NODE&&c(i);const h=this._evaluator._queryCSS({scope:i,pierceShadow:t},"*");for(const f of h)c(f);return a}}}_createInternalHasTextEngine(){return{queryAll:(t,n)=>{if(t.nodeType!==1)return[];const r=t,i=Me(this._evaluator._cacheText,r),{matcher:s}=Bi(n,!0);return s(i)?[r]:[]}}}_createInternalHasNotTextEngine(){return{queryAll:(t,n)=>{if(t.nodeType!==1)return[];const r=t,i=Me(this._evaluator._cacheText,r),{matcher:s}=Bi(n,!0);return s(i)?[]:[r]}}}_createInternalLabelEngine(){return{queryAll:(t,n)=>{const{matcher:r}=Bi(n,!0);return this._evaluator._queryCSS({scope:t,pierceShadow:!0},"*").filter(s=>_p(this._evaluator._cacheText,s).some(o=>r(o)))}}}_createNamedAttributeEngine(){return{queryAll:(n,r)=>{const i=mn(r,!0);if(i.name||i.attributes.length!==1)throw new Error("Malformed attribute selector: "+r);const{name:s,value:o,caseSensitive:l}=i.attributes[0],a=l?null:o.toLowerCase();let u;return o instanceof RegExp?u=h=>!!h.match(o):l?u=h=>h===o:u=h=>h.toLowerCase().includes(a),this._evaluator._queryCSS({scope:n,pierceShadow:!0},`[${s}]`).filter(h=>u(h.getAttribute(s)))}}}_createControlEngine(){return{queryAll(t,n){if(n==="enter-frame")return[];if(n==="return-empty")return[];if(n==="component")return t.nodeType!==1?[]:[t.childElementCount===1?t.firstElementChild:t];throw new Error(`Internal error, unknown internal:control selector ${n}`)}}}_createHasEngine(){return{queryAll:(n,r)=>n.nodeType!==1?[]:!!this.querySelector(r.parsed,n,!1)?[n]:[]}}_createHasNotEngine(){return{queryAll:(n,r)=>n.nodeType!==1?[]:!!this.querySelector(r.parsed,n,!1)?[]:[n]}}_createVisibleEngine(){return{queryAll:(n,r)=>n.nodeType!==1?[]:ci(n)===!!r?[n]:[]}}_createInternalChainEngine(){return{queryAll:(n,r)=>this.querySelectorAll(r.parsed,n)}}extend(t,n){const r=this.window.eval(`
    (() => {
      const module = {};
      ${t}
      return module.exports.default();
    })()`);return new r(this,n)}isVisible(t){return ci(t)}async viewportRatio(t){return await new Promise(n=>{const r=new IntersectionObserver(i=>{n(i[0].intersectionRatio),r.disconnect()});r.observe(t),this.builtinRequestAnimationFrame(()=>{})})}getElementBorderWidth(t){if(t.nodeType!==Node.ELEMENT_NODE||!t.ownerDocument||!t.ownerDocument.defaultView)return{left:0,top:0};const n=t.ownerDocument.defaultView.getComputedStyle(t);return{left:parseInt(n.borderLeftWidth||"",10),top:parseInt(n.borderTopWidth||"",10)}}describeIFrameStyle(t){if(!t.ownerDocument||!t.ownerDocument.defaultView)return"error:notconnected";const n=t.ownerDocument.defaultView;for(let i=t;i;i=Te(i))if(n.getComputedStyle(i).transform!=="none")return"transformed";const r=n.getComputedStyle(t);return{left:parseInt(r.borderLeftWidth||"",10)+parseInt(r.paddingLeft||"",10),top:parseInt(r.borderTopWidth||"",10)+parseInt(r.paddingTop||"",10)}}retarget(t,n){let r=t.nodeType===Node.ELEMENT_NODE?t:t.parentElement;return r?(n==="none"||(!r.matches("input, textarea, select")&&!r.isContentEditable&&(n==="button-link"?r=r.closest("button, [role=button], a, [role=link]")||r:r=r.closest("button, [role=button], [role=checkbox], [role=radio]")||r),n==="follow-label"&&(!r.matches("a, input, textarea, button, select, [role=link], [role=button], [role=checkbox], [role=radio]")&&!r.isContentEditable&&(r=r.closest("label")||r),r.nodeName==="LABEL"&&(r=r.control||r))),r):null}async checkElementStates(t,n){if(n.includes("stable")){const r=await this._checkElementIsStable(t);if(r===!1)return{missingState:"stable"};if(r==="error:notconnected")return r}for(const r of n)if(r!=="stable"){const i=this.elementState(t,r);if(i===!1)return{missingState:r};if(i==="error:notconnected")return i}}async _checkElementIsStable(t){const n=Symbol("continuePolling");let r,i=0,s=0;const o=()=>{const h=this.retarget(t,"no-follow-label");if(!h)return"error:notconnected";const f=performance.now();if(this._stableRafCount>1&&f-s<15)return n;s=f;const g=h.getBoundingClientRect(),w={x:g.top,y:g.left,width:g.width,height:g.height};if(r){if(!(w.x===r.x&&w.y===r.y&&w.width===r.width&&w.height===r.height))return!1;if(++i>=this._stableRafCount)return!0}return r=w,n};let l,a;const u=new Promise((h,f)=>{l=h,a=f}),c=()=>{try{const h=o();h!==n?l(h):this.builtinRequestAnimationFrame(c)}catch(h){a(h)}};return this.builtinRequestAnimationFrame(c),u}elementState(t,n){const r=this.retarget(t,["stable","visible","hidden"].includes(n)?"none":"follow-label");if(!r||!r.isConnected)return n==="hidden"?!0:"error:notconnected";if(n==="visible")return this.isVisible(r);if(n==="hidden")return!this.isVisible(r);const i=hp(r);if(n==="disabled")return i;if(n==="enabled")return!i;const s=!(["INPUT","TEXTAREA","SELECT"].includes(r.nodeName)&&r.hasAttribute("readonly"));if(n==="editable")return!i&&s;if(n==="checked"||n==="unchecked"){const o=n==="checked",l=up(r,!1);if(l==="error")throw this.createStacklessError("Not a checkbox or radio button");return o===l}throw this.createStacklessError(`Unexpected element state "${n}"`)}selectOptions(t,n){const r=this.retarget(t,"follow-label");if(!r)return"error:notconnected";if(r.nodeName.toLowerCase()!=="select")throw this.createStacklessError("Element is not a <select> element");const i=r,s=[...i.options],o=[];let l=n.slice();for(let a=0;a<s.length;a++){const u=s[a],c=h=>{if(h instanceof Node)return u===h;let f=!0;return h.valueOrLabel!==void 0&&(f=f&&(h.valueOrLabel===u.value||h.valueOrLabel===u.label)),h.value!==void 0&&(f=f&&h.value===u.value),h.label!==void 0&&(f=f&&h.label===u.label),h.index!==void 0&&(f=f&&h.index===a),f};if(l.some(c))if(o.push(u),i.multiple)l=l.filter(h=>!c(h));else{l=[];break}}return l.length?"error:optionsnotfound":(i.value=void 0,o.forEach(a=>a.selected=!0),i.dispatchEvent(new Event("input",{bubbles:!0,composed:!0})),i.dispatchEvent(new Event("change",{bubbles:!0})),o.map(a=>a.value))}fill(t,n){const r=this.retarget(t,"follow-label");if(!r)return"error:notconnected";if(r.nodeName.toLowerCase()==="input"){const i=r,s=i.type.toLowerCase(),o=new Set(["color","date","time","datetime-local","month","range","week"]);if(!new Set(["","email","number","password","search","tel","text","url"]).has(s)&&!o.has(s))throw this.createStacklessError(`Input of type "${s}" cannot be filled`);if(s==="number"&&(n=n.trim(),isNaN(Number(n))))throw this.createStacklessError("Cannot type text into input[type=number]");if(o.has(s)){if(n=n.trim(),i.focus(),i.value=n,i.value!==n)throw this.createStacklessError("Malformed value");return r.dispatchEvent(new Event("input",{bubbles:!0,composed:!0})),r.dispatchEvent(new Event("change",{bubbles:!0})),"done"}}else if(r.nodeName.toLowerCase()!=="textarea"){if(!r.isContentEditable)throw this.createStacklessError("Element is not an <input>, <textarea> or [contenteditable] element")}return this.selectText(r),"needsinput"}selectText(t){const n=this.retarget(t,"follow-label");if(!n)return"error:notconnected";if(n.nodeName.toLowerCase()==="input"){const s=n;return s.select(),s.focus(),"done"}if(n.nodeName.toLowerCase()==="textarea"){const s=n;return s.selectionStart=0,s.selectionEnd=s.value.length,s.focus(),"done"}const r=n.ownerDocument.createRange();r.selectNodeContents(n);const i=n.ownerDocument.defaultView.getSelection();return i&&(i.removeAllRanges(),i.addRange(r)),n.focus(),"done"}_activelyFocused(t){const n=t.getRootNode().activeElement,r=n===t&&!!t.ownerDocument&&t.ownerDocument.hasFocus();return{activeElement:n,isFocused:r}}focusNode(t,n){if(!t.isConnected)return"error:notconnected";if(t.nodeType!==Node.ELEMENT_NODE)throw this.createStacklessError("Node is not an element");const{activeElement:r,isFocused:i}=this._activelyFocused(t);if(t.isContentEditable&&!i&&r&&r.blur&&r.blur(),t.focus(),t.focus(),n&&!i&&t.nodeName.toLowerCase()==="input")try{t.setSelectionRange(0,0)}catch{}return"done"}blurNode(t){if(!t.isConnected)return"error:notconnected";if(t.nodeType!==Node.ELEMENT_NODE)throw this.createStacklessError("Node is not an element");return t.blur(),"done"}setInputFiles(t,n){if(t.nodeType!==Node.ELEMENT_NODE)return"Node is not of type HTMLElement";const r=t;if(r.nodeName!=="INPUT")return"Not an <input> element";const i=r;if((i.getAttribute("type")||"").toLowerCase()!=="file")return"Not an input[type=file] element";const o=n.map(a=>{const u=Uint8Array.from(atob(a.buffer),c=>c.charCodeAt(0));return new File([u],a.name,{type:a.mimeType,lastModified:a.lastModifiedMs})}),l=new DataTransfer;for(const a of o)l.items.add(a);i.files=l.files,i.dispatchEvent(new Event("input",{bubbles:!0,composed:!0})),i.dispatchEvent(new Event("change",{bubbles:!0}))}expectHitTarget(t,n){const r=[];let i=n;for(;i;){const c=Yh(i);if(!c||(r.push(c),c.nodeType===9))break;i=c.host}let s;for(let c=r.length-1;c>=0;c--){const h=r[c],f=h.elementsFromPoint(t.x,t.y),g=h.elementFromPoint(t.x,t.y);if(g&&f[0]&&Te(g)===f[0]){const y=this.window.getComputedStyle(g);(y==null?void 0:y.display)==="contents"&&f.unshift(g)}f[0]&&f[0].shadowRoot===h&&f[1]===g&&f.shift();const w=f[0];if(!w||(s=w,c&&w!==r[c-1].host))break}const o=[];for(;s&&s!==n;)o.push(s),s=Te(s);if(s===n)return"done";const l=this.previewNode(o[0]||this.document.documentElement);let a,u=n;for(;u;){const c=o.indexOf(u);if(c!==-1){c>1&&(a=this.previewNode(o[c-1]));break}u=Te(u)}return a?{hitTargetDescription:`${l} from ${a} subtree`}:{hitTargetDescription:l}}setupHitTargetInterceptor(t,n,r,i){const s=this.retarget(t,"button-link");if(!s||!s.isConnected)return"error:notconnected";if(r){const c=this.expectHitTarget(r,s);if(c!=="done")return c.hitTargetDescription}if(n==="drag")return{stop:()=>"done"};const o={hover:Bp,tap:Vp,mouse:Wp}[n];let l;const a=c=>{if(!o.has(c.type)||!c.isTrusted)return;const h=this.window.TouchEvent&&c instanceof this.window.TouchEvent?c.touches[0]:c;l===void 0&&h&&(l=this.expectHitTarget({x:h.clientX,y:h.clientY},s)),(i||l!=="done"&&l!==void 0)&&(c.preventDefault(),c.stopPropagation(),c.stopImmediatePropagation())},u=()=>(this._hitTargetInterceptor===a&&(this._hitTargetInterceptor=void 0),l||"done");return this._hitTargetInterceptor=a,{stop:u}}dispatchEvent(t,n,r){let i;switch(r={bubbles:!0,cancelable:!0,composed:!0,...r},Dw.get(n)){case"mouse":i=new MouseEvent(n,r);break;case"keyboard":i=new KeyboardEvent(n,r);break;case"touch":i=new TouchEvent(n,r);break;case"pointer":i=new PointerEvent(n,r);break;case"focus":i=new FocusEvent(n,r);break;case"drag":i=new DragEvent(n,r);break;case"wheel":i=new WheelEvent(n,r);break;case"deviceorientation":try{i=new DeviceOrientationEvent(n,r)}catch{const{bubbles:s,cancelable:o,alpha:l,beta:a,gamma:u,absolute:c}=r;i=this.document.createEvent("DeviceOrientationEvent"),i.initDeviceOrientationEvent(n,s,o,l,a,u,c)}break;case"devicemotion":try{i=new DeviceMotionEvent(n,r)}catch{const{bubbles:s,cancelable:o,acceleration:l,accelerationIncludingGravity:a,rotationRate:u,interval:c}=r;i=this.document.createEvent("DeviceMotionEvent"),i.initDeviceMotionEvent(n,s,o,l,a,u,c)}break;default:i=new Event(n,r);break}t.dispatchEvent(i)}previewNode(t){if(t.nodeType===Node.TEXT_NODE)return Ui(`#text=${t.nodeValue||""}`);if(t.nodeType!==Node.ELEMENT_NODE)return Ui(`<${t.nodeName.toLowerCase()} />`);const n=t,r=[];for(let a=0;a<n.attributes.length;a++){const{name:u,value:c}=n.attributes[a];u!=="style"&&(!c&&Ow.has(u)?r.push(` ${u}`):r.push(` ${u}="${c}"`))}r.sort((a,u)=>a.length-u.length);const i=Sc(r.join(""),500);if($w.has(n.nodeName))return Ui(`<${n.nodeName.toLowerCase()}${i}/>`);const s=n.childNodes;let o=!1;if(s.length<=5){o=!0;for(let a=0;a<s.length;a++)o=o&&s[a].nodeType===Node.TEXT_NODE}const l=o?n.textContent||"":s.length?"…":"";return Ui(`<${n.nodeName.toLowerCase()}${i}>${Sc(l,50)}</${n.nodeName.toLowerCase()}>`)}strictModeViolationError(t,n){const r=n.slice(0,10).map(s=>({preview:this.previewNode(s),selector:this.generateSelectorSimple(s)})),i=r.map((s,o)=>`
    ${o+1}) ${s.preview} aka ${Yt(this._sdkLanguage,s.selector)}`);return r.length<n.length&&i.push(`
    ...`),this.createStacklessError(`strict mode violation: ${Yt(this._sdkLanguage,Sn(t))} resolved to ${n.length} elements:${i.join("")}
`)}createStacklessError(t){if(this._browserName==="firefox"){const r=new Error("Error: "+t);return r.stack="",r}const n=new Error(t);return delete n.stack,n}createHighlight(){return new Uo(this)}maskSelectors(t,n){this._highlight&&this.hideHighlight(),this._highlight=new Uo(this),this._highlight.install();const r=[];for(const i of t)r.push(this.querySelectorAll(i,this.document.documentElement));this._highlight.maskElements(r.flat(),n)}highlight(t){this._highlight||(this._highlight=new Uo(this),this._highlight.install()),this._highlight.runHighlightOnRaf(t)}hideHighlight(){this._highlight&&(this._highlight.uninstall(),delete this._highlight)}markTargetElements(t,n){const r=new CustomEvent("__playwright_target__",{bubbles:!0,cancelable:!0,detail:n,composed:!0});for(const i of t)i.dispatchEvent(r)}_setupGlobalListenersRemovalDetection(){const t="__playwright_global_listeners_check__";let n=!1;const r=()=>n=!0;this.window.addEventListener(t,r),new MutationObserver(i=>{if(i.some(o=>Array.from(o.addedNodes).includes(this.document.documentElement))&&(n=!1,this.window.dispatchEvent(new CustomEvent(t)),!n)){this.window.addEventListener(t,r);for(const o of this.onGlobalListenersRemoved)o()}}).observe(this.document,{childList:!0})}_setupHitTargetInterceptors(){const t=r=>{var i;return(i=this._hitTargetInterceptor)==null?void 0:i.call(this,r)},n=()=>{for(const r of zw)this.window.addEventListener(r,t,{capture:!0,passive:!1})};n(),this.onGlobalListenersRemoved.add(n)}async expect(t,n,r){return n.expression==="to.have.count"||n.expression.endsWith(".array")?this.expectArray(r,n):t?await this.expectSingleElement(t,n):!n.isNot&&n.expression==="to.be.hidden"?{matches:!0}:n.isNot&&n.expression==="to.be.visible"?{matches:!1}:!n.isNot&&n.expression==="to.be.detached"?{matches:!0}:n.isNot&&n.expression==="to.be.attached"?{matches:!1}:n.isNot&&n.expression==="to.be.in.viewport"?{matches:!1}:{matches:n.isNot,missingReceived:!0}}async expectSingleElement(t,n){var i;const r=n.expression;{let s;if(r==="to.have.attribute"?s=t.hasAttribute(n.expressionArg):r==="to.be.checked"?s=this.elementState(t,"checked"):r==="to.be.unchecked"?s=this.elementState(t,"unchecked"):r==="to.be.disabled"?s=this.elementState(t,"disabled"):r==="to.be.editable"?s=this.elementState(t,"editable"):r==="to.be.readonly"?s=!this.elementState(t,"editable"):r==="to.be.empty"?t.nodeName==="INPUT"||t.nodeName==="TEXTAREA"?s=!t.value:s=!((i=t.textContent)!=null&&i.trim()):r==="to.be.enabled"?s=this.elementState(t,"enabled"):r==="to.be.focused"?s=this._activelyFocused(t).isFocused:r==="to.be.hidden"?s=this.elementState(t,"hidden"):r==="to.be.visible"?s=this.elementState(t,"visible"):r==="to.be.attached"?s=!0:r==="to.be.detached"&&(s=!1),s!==void 0){if(s==="error:notcheckbox")throw this.createStacklessError("Element is not a checkbox");if(s==="error:notconnected")throw this.createStacklessError("Element is not connected");return{received:s,matches:s}}}if(r==="to.have.property"){let s=t;const o=n.expressionArg.split(".");for(let u=0;u<o.length-1;u++){if(typeof s!="object"||!(o[u]in s))return{received:void 0,matches:!1};s=s[o[u]]}const l=s[o[o.length-1]],a=Ql(l,n.expectedValue);return{received:l,matches:a}}if(r==="to.be.in.viewport"){const s=await this.viewportRatio(t);return{received:`viewport ratio ${s}`,matches:s>0&&s>(n.expectedNumber??0)-1e-9}}if(r==="to.have.values"){if(t=this.retarget(t,"follow-label"),t.nodeName!=="SELECT"||!t.multiple)throw this.createStacklessError("Not a select element with a multiple attribute");const s=[...t.selectedOptions].map(o=>o.value);return s.length!==n.expectedText.length?{received:s,matches:!1}:{received:s,matches:s.map((o,l)=>new Bo(n.expectedText[l]).matches(o)).every(Boolean)}}{let s;if(r==="to.have.attribute.value"){const o=t.getAttribute(n.expressionArg);if(o===null)return{received:null,matches:!1};s=o}else if(r==="to.have.class")s=t.classList.toString();else if(r==="to.have.css")s=this.window.getComputedStyle(t).getPropertyValue(n.expressionArg);else if(r==="to.have.id")s=t.id;else if(r==="to.have.text")s=n.useInnerText?t.innerText:Me(new Map,t).full;else if(r==="to.have.accessible.name")s=Fs(t,!1);else if(r==="to.have.accessible.description")s=Dc(t,!1);else if(r==="to.have.role")s=Ne(t)||"";else if(r==="to.have.title")s=this.document.title;else if(r==="to.have.url")s=this.document.location.href;else if(r==="to.have.value"){if(t=this.retarget(t,"follow-label"),t.nodeName!=="INPUT"&&t.nodeName!=="TEXTAREA"&&t.nodeName!=="SELECT")throw this.createStacklessError("Not an input element");s=t.value}if(s!==void 0&&n.expectedText){const o=new Bo(n.expectedText[0]);return{received:s,matches:o.matches(s)}}}throw this.createStacklessError("Unknown expect matcher: "+r)}expectArray(t,n){const r=n.expression;if(r==="to.have.count"){const s=t.length,o=s===n.expectedNumber;return{received:s,matches:o}}let i;if(r==="to.have.text.array"||r==="to.contain.text.array"?i=t.map(s=>n.useInnerText?s.innerText:Me(new Map,s).full):r==="to.have.class.array"&&(i=t.map(s=>s.classList.toString())),i&&n.expectedText){const s=r!=="to.contain.text.array";if(!(i.length===n.expectedText.length||!s))return{received:i,matches:!1};const l=n.expectedText.map(c=>new Bo(c));let a=0,u=0;for(;a<l.length&&u<i.length;)l[a].matches(i[u])&&++a,++u;return{received:i,matches:a===l.length}}throw this.createStacklessError("Unknown expect matcher: "+r)}getElementAccessibleName(t,n){return Fs(t,!!n)}getElementAccessibleDescription(t,n){return Dc(t,!!n)}getAriaRole(t){return Ne(t)}}const $w=new Set(["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","MENUITEM","META","PARAM","SOURCE","TRACK","WBR"]),Ow=new Set(["checked","selected","disabled","readonly","multiple"]);function Ui(e){return e.replace(/\n/g,"↵").replace(/\t/g,"⇆")}const Dw=new Map([["auxclick","mouse"],["click","mouse"],["dblclick","mouse"],["mousedown","mouse"],["mouseeenter","mouse"],["mouseleave","mouse"],["mousemove","mouse"],["mouseout","mouse"],["mouseover","mouse"],["mouseup","mouse"],["mouseleave","mouse"],["mousewheel","mouse"],["keydown","keyboard"],["keyup","keyboard"],["keypress","keyboard"],["textInput","keyboard"],["touchstart","touch"],["touchmove","touch"],["touchend","touch"],["touchcancel","touch"],["pointerover","pointer"],["pointerout","pointer"],["pointerenter","pointer"],["pointerleave","pointer"],["pointerdown","pointer"],["pointerup","pointer"],["pointermove","pointer"],["pointercancel","pointer"],["gotpointercapture","pointer"],["lostpointercapture","pointer"],["focus","focus"],["blur","focus"],["drag","drag"],["dragstart","drag"],["dragend","drag"],["dragover","drag"],["dragenter","drag"],["dragleave","drag"],["dragexit","drag"],["drop","drag"],["wheel","wheel"],["deviceorientation","deviceorientation"],["deviceorientationabsolute","deviceorientation"],["devicemotion","devicemotion"]]),Bp=new Set(["mousemove"]),Vp=new Set(["pointerdown","pointerup","touchstart","touchend","touchcancel"]),Wp=new Set(["mousedown","mouseup","pointerdown","pointerup","click","auxclick","dblclick","contextmenu"]),zw=new Set([...Bp,...Vp,...Wp]);function Fw(e){if(e=e.substring(1,e.length-1),!e.includes("\\"))return e;const t=[];let n=0;for(;n<e.length;)e[n]==="\\"&&n+1<e.length&&n++,t.push(e[n++]);return t.join("")}function Bi(e,t){if(e[0]==="/"&&e.lastIndexOf("/")>0){const i=e.lastIndexOf("/"),s=new RegExp(e.substring(1,i),e.substring(i+1));return{matcher:o=>s.test(o.full),kind:"regex"}}const n=t?JSON.parse.bind(JSON):Fw;let r=!1;return e.length>1&&e[0]==='"'&&e[e.length-1]==='"'?(e=n(e),r=!0):t&&e.length>1&&e[0]==='"'&&e[e.length-2]==='"'&&e[e.length-1]==="i"?(e=n(e.substring(0,e.length-1)),r=!1):t&&e.length>1&&e[0]==='"'&&e[e.length-2]==='"'&&e[e.length-1]==="s"?(e=n(e.substring(0,e.length-1)),r=!0):e.length>1&&e[0]==="'"&&e[e.length-1]==="'"&&(e=n(e),r=!0),e=Ue(e),r?t?{kind:"strict",matcher:s=>s.normalized===e}:{matcher:s=>!e&&!s.immediate.length?!0:s.immediate.some(o=>Ue(o)===e),kind:"strict"}:(e=e.toLowerCase(),{kind:"lax",matcher:i=>i.normalized.toLowerCase().includes(e)})}class Bo{constructor(t){if(this._normalizeWhiteSpace=t.normalizeWhiteSpace,this._ignoreCase=t.ignoreCase,this._string=t.matchSubstring?void 0:this.normalize(t.string),this._substring=t.matchSubstring?this.normalize(t.string):void 0,t.regexSource){const n=new Set((t.regexFlags||"").split(""));t.ignoreCase===!1&&n.delete("i"),t.ignoreCase===!0&&n.add("i"),this._regex=new RegExp(t.regexSource,[...n].join(""))}}matches(t){return this._regex||(t=this.normalize(t)),this._string!==void 0?t===this._string:this._substring!==void 0?t.includes(this._substring):this._regex?!!this._regex.test(t):!1}normalize(t){return t&&(this._normalizeWhiteSpace&&(t=Ue(t)),this._ignoreCase&&(t=t.toLocaleLowerCase()),t)}}function Ql(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(let r=0;r<e.length;++r)if(!Ql(e[r],t[r]))return!1;return!0}if(e instanceof RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r=0;r<n.length;++r)if(!t.hasOwnProperty(n[r]))return!1;for(const r of n)if(!Ql(e[r],t[r]))return!1;return!0}return typeof e=="number"&&typeof t=="number"?isNaN(e)&&isNaN(t):!1}const Hw={tagName:"svg",children:[{tagName:"defs",children:[{tagName:"clipPath",attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor",id:"icon-gripper"},children:[{tagName:"path",attrs:{d:"M5 3h2v2H5zm0 4h2v2H5zm0 4h2v2H5zm4-8h2v2H9zm0 4h2v2H9zm0 4h2v2H9z"}}]},{tagName:"clipPath",attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor",id:"icon-circle-large-filled"},children:[{tagName:"path",attrs:{d:"M8 1a6.8 6.8 0 0 1 1.86.253 6.899 6.899 0 0 1 3.083 1.805 6.903 6.903 0 0 1 1.804 3.083C14.916 6.738 15 7.357 15 8s-.084 1.262-.253 1.86a6.9 6.9 0 0 1-.704 1.674 7.157 7.157 0 0 1-2.516 2.509 6.966 6.966 0 0 1-1.668.71A6.984 6.984 0 0 1 8 15a6.984 6.984 0 0 1-1.86-.246 7.098 7.098 0 0 1-1.674-.711 7.3 7.3 0 0 1-1.415-1.094 7.295 7.295 0 0 1-1.094-1.415 7.098 7.098 0 0 1-.71-1.675A6.985 6.985 0 0 1 1 8c0-.643.082-1.262.246-1.86a6.968 6.968 0 0 1 .711-1.667 7.156 7.156 0 0 1 2.509-2.516 6.895 6.895 0 0 1 1.675-.704A6.808 6.808 0 0 1 8 1z"}}]},{tagName:"clipPath",attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor",id:"icon-inspect"},children:[{tagName:"path",attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1 3l1-1h12l1 1v6h-1V3H2v8h5v1H2l-1-1V3zm14.707 9.707L9 6v9.414l2.707-2.707h4zM10 13V8.414l3.293 3.293h-2L10 13z"}}]},{tagName:"clipPath",attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor",id:"icon-whole-word"},children:[{tagName:"path",attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M0 11H1V13H15V11H16V14H15H1H0V11Z"}},{tagName:"path",attrs:{d:"M6.84048 11H5.95963V10.1406H5.93814C5.555 10.7995 4.99104 11.1289 4.24625 11.1289C3.69839 11.1289 3.26871 10.9839 2.95718 10.6938C2.64924 10.4038 2.49527 10.0189 2.49527 9.53906C2.49527 8.51139 3.10041 7.91341 4.3107 7.74512L5.95963 7.51416C5.95963 6.57959 5.58186 6.1123 4.82632 6.1123C4.16389 6.1123 3.56591 6.33789 3.03238 6.78906V5.88672C3.57307 5.54297 4.19612 5.37109 4.90152 5.37109C6.19416 5.37109 6.84048 6.05501 6.84048 7.42285V11ZM5.95963 8.21777L4.63297 8.40039C4.22476 8.45768 3.91682 8.55973 3.70914 8.70654C3.50145 8.84977 3.39761 9.10579 3.39761 9.47461C3.39761 9.74316 3.4925 9.96338 3.68228 10.1353C3.87564 10.3035 4.13166 10.3877 4.45035 10.3877C4.8872 10.3877 5.24706 10.2355 5.52994 9.93115C5.8164 9.62321 5.95963 9.2347 5.95963 8.76562V8.21777Z"}},{tagName:"path",attrs:{d:"M9.3475 10.2051H9.32601V11H8.44515V2.85742H9.32601V6.4668H9.3475C9.78076 5.73633 10.4146 5.37109 11.2489 5.37109C11.9543 5.37109 12.5057 5.61816 12.9032 6.1123C13.3042 6.60286 13.5047 7.26172 13.5047 8.08887C13.5047 9.00911 13.2809 9.74674 12.8333 10.3018C12.3857 10.8532 11.7734 11.1289 10.9964 11.1289C10.2695 11.1289 9.71989 10.821 9.3475 10.2051ZM9.32601 7.98682V8.75488C9.32601 9.20964 9.47282 9.59635 9.76644 9.91504C10.0636 10.2301 10.4396 10.3877 10.8944 10.3877C11.4279 10.3877 11.8451 10.1836 12.1458 9.77539C12.4502 9.36719 12.6024 8.79964 12.6024 8.07275C12.6024 7.46045 12.4609 6.98063 12.1781 6.6333C11.8952 6.28597 11.512 6.1123 11.0286 6.1123C10.5166 6.1123 10.1048 6.29134 9.7933 6.64941C9.48177 7.00391 9.32601 7.44971 9.32601 7.98682Z"}}]},{tagName:"clipPath",attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor",id:"icon-eye"},children:[{tagName:"path",attrs:{d:"M7.99993 6.00316C9.47266 6.00316 10.6666 7.19708 10.6666 8.66981C10.6666 10.1426 9.47266 11.3365 7.99993 11.3365C6.52715 11.3365 5.33324 10.1426 5.33324 8.66981C5.33324 7.19708 6.52715 6.00316 7.99993 6.00316ZM7.99993 7.00315C7.07946 7.00315 6.33324 7.74935 6.33324 8.66981C6.33324 9.59028 7.07946 10.3365 7.99993 10.3365C8.9204 10.3365 9.6666 9.59028 9.6666 8.66981C9.6666 7.74935 8.9204 7.00315 7.99993 7.00315ZM7.99993 3.66675C11.0756 3.66675 13.7307 5.76675 14.4673 8.70968C14.5344 8.97755 14.3716 9.24908 14.1037 9.31615C13.8358 9.38315 13.5643 9.22041 13.4973 8.95248C12.8713 6.45205 10.6141 4.66675 7.99993 4.66675C5.38454 4.66675 3.12664 6.45359 2.50182 8.95555C2.43491 9.22341 2.16348 9.38635 1.89557 9.31948C1.62766 9.25255 1.46471 8.98115 1.53162 8.71321C2.26701 5.76856 4.9229 3.66675 7.99993 3.66675Z"}}]},{tagName:"clipPath",attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor",id:"icon-symbol-constant"},children:[{tagName:"path",attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4 6h8v1H4V6zm8 3H4v1h8V9z"}},{tagName:"path",attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1 4l1-1h12l1 1v8l-1 1H2l-1-1V4zm1 0v8h12V4H2z"}}]},{tagName:"clipPath",attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor",id:"icon-check"},children:[{tagName:"path",attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M14.431 3.323l-8.47 10-.79-.036-3.35-4.77.818-.574 2.978 4.24 8.051-9.506.764.646z"}}]},{tagName:"clipPath",attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor",id:"icon-close"},children:[{tagName:"path",attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M8 8.707l3.646 3.647.708-.707L8.707 8l3.647-3.646-.707-.708L8 7.293 4.354 3.646l-.707.708L7.293 8l-3.646 3.646.707.708L8 8.707z"}}]},{tagName:"clipPath",attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor",id:"icon-pass"},children:[{tagName:"path",attrs:{d:"M6.27 10.87h.71l4.56-4.56-.71-.71-4.2 4.21-1.92-1.92L4 8.6l2.27 2.27z"}},{tagName:"path",attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M8.6 1c1.6.1 3.1.9 4.2 2 1.3 1.4 2 3.1 2 5.1 0 1.6-.6 3.1-1.6 4.4-1 1.2-2.4 2.1-4 2.4-1.6.3-3.2.1-4.6-.7-1.4-.8-2.5-2-3.1-3.5C.9 9.2.8 7.5 1.3 6c.5-1.6 1.4-2.9 2.8-3.8C5.4 1.3 7 .9 8.6 1zm.5 12.9c1.3-.3 2.5-1 3.4-2.1.8-1.1 1.3-2.4 1.2-3.8 0-1.6-.6-3.2-1.7-4.3-1-1-2.2-1.6-3.6-1.7-1.3-.1-2.7.2-3.8 1-1.1.8-1.9 1.9-2.3 3.3-.4 1.3-.4 2.7.2 4 .6 1.3 1.5 2.3 2.7 3 1.2.7 2.6.9 3.9.6z"}}]}]}]};class qc{cursor(){return"default"}}class Vo{constructor(t,n){this._hoveredModel=null,this._hoveredElement=null,this._hoveredSelectors=null,this._recorder=t,this._assertVisibility=n}cursor(){return"pointer"}cleanup(){this._hoveredModel=null,this._hoveredElement=null,this._hoveredSelectors=null}onClick(t){var n;q(t),t.button===0&&(n=this._hoveredModel)!=null&&n.selector&&this._commit(this._hoveredModel.selector)}onContextMenu(t){if(this._hoveredModel&&!this._hoveredModel.tooltipListItemSelected&&this._hoveredSelectors&&this._hoveredSelectors.length>1){q(t);const n=this._hoveredSelectors;this._hoveredModel.tooltipFooter=void 0,this._hoveredModel.tooltipList=n.map(r=>this._recorder.injectedScript.utils.asLocator(this._recorder.state.language,r)),this._hoveredModel.tooltipListItemSelected=r=>{r===void 0?this._reset(!0):this._commit(n[r])},this._recorder.updateHighlight(this._hoveredModel,!0)}}onPointerDown(t){q(t)}onPointerUp(t){q(t)}onMouseDown(t){q(t)}onMouseUp(t){q(t)}onMouseMove(t){var s;q(t);let n=this._recorder.deepEventTarget(t);if(n.isConnected||(n=null),this._hoveredElement===n)return;this._hoveredElement=n;let r=null,i=[];if(this._hoveredElement){const o=this._recorder.injectedScript.generateSelector(this._hoveredElement,{testIdAttributeName:this._recorder.state.testIdAttributeName,multiple:!1});i=o.selectors,r={selector:o.selector,elements:o.elements,tooltipText:this._recorder.injectedScript.utils.asLocator(this._recorder.state.language,o.selector),tooltipFooter:i.length>1?"Click to select, right-click for more options":void 0,color:this._assertVisibility?"#8acae480":void 0}}((s=this._hoveredModel)==null?void 0:s.selector)!==(r==null?void 0:r.selector)&&(this._hoveredModel=r,this._hoveredSelectors=i,this._recorder.updateHighlight(r,!0))}onMouseEnter(t){q(t)}onMouseLeave(t){q(t);const n=this._recorder.injectedScript.window;n.top!==n&&this._recorder.deepEventTarget(t).nodeType===Node.DOCUMENT_NODE&&this._reset(!0)}onKeyDown(t){var n,r,i;q(t),t.key==="Escape"&&((n=this._hoveredModel)!=null&&n.tooltipListItemSelected?this._reset(!0):this._assertVisibility&&((i=(r=this._recorder.delegate).setMode)==null||i.call(r,"recording")))}onKeyUp(t){q(t)}onScroll(t){this._reset(!1)}_commit(t){var n,r,i,s,o,l,a;this._assertVisibility?((r=(n=this._recorder.delegate).recordAction)==null||r.call(n,{name:"assertVisible",selector:t,signals:[]}),(s=(i=this._recorder.delegate).setMode)==null||s.call(i,"recording"),(o=this._recorder.overlay)==null||o.flashToolSucceeded("assertingVisibility")):(a=(l=this._recorder.delegate).setSelector)==null||a.call(l,t)}_reset(t){this._hoveredElement=null,this._hoveredModel=null,this._hoveredSelectors=null,this._recorder.updateHighlight(null,t)}}class Uw{constructor(t){this._performingAction=!1,this._hoveredModel=null,this._hoveredElement=null,this._activeModel=null,this._expectProgrammaticKeyUp=!1,this._recorder=t}cursor(){return"pointer"}cleanup(){this._hoveredModel=null,this._hoveredElement=null,this._activeModel=null,this._expectProgrammaticKeyUp=!1}onClick(t){if(Gc(this._hoveredElement)||t.button===2&&t.type==="auxclick"||this._shouldIgnoreMouseEvent(t)||this._actionInProgress(t)||this._consumedDueToNoModel(t,this._hoveredModel))return;const n=Wo(this._recorder.deepEventTarget(t));if(n){this._performAction({name:n.checked?"check":"uncheck",selector:this._hoveredModel.selector,signals:[]});return}this._performAction({name:"click",selector:this._hoveredModel.selector,position:Kc(t),signals:[],button:qw(t),modifiers:Qc(t),clickCount:t.detail})}onContextMenu(t){this._shouldIgnoreMouseEvent(t)||this._actionInProgress(t)||this._consumedDueToNoModel(t,this._hoveredModel)||this._performAction({name:"click",selector:this._hoveredModel.selector,position:Kc(t),signals:[],button:"right",modifiers:0,clickCount:0})}onPointerDown(t){this._shouldIgnoreMouseEvent(t)||this._performingAction||q(t)}onPointerUp(t){this._shouldIgnoreMouseEvent(t)||this._performingAction||q(t)}onMouseDown(t){this._shouldIgnoreMouseEvent(t)||(this._performingAction||q(t),this._activeModel=this._hoveredModel)}onMouseUp(t){this._shouldIgnoreMouseEvent(t)||this._performingAction||q(t)}onMouseMove(t){const n=this._recorder.deepEventTarget(t);this._hoveredElement!==n&&(this._hoveredElement=n,this._updateModelForHoveredElement())}onMouseLeave(t){const n=this._recorder.injectedScript.window;n.top!==n&&this._recorder.deepEventTarget(t).nodeType===Node.DOCUMENT_NODE&&(this._hoveredElement=null,this._updateModelForHoveredElement())}onFocus(t){this._onFocus(!0)}onInput(t){var r,i,s,o,l,a;const n=this._recorder.deepEventTarget(t);if(n.nodeName==="INPUT"&&n.type.toLowerCase()==="file"){(i=(r=this._recorder.delegate).recordAction)==null||i.call(r,{name:"setInputFiles",selector:this._activeModel.selector,signals:[],files:[...n.files||[]].map(u=>u.name)});return}if(Gc(n)){(o=(s=this._recorder.delegate).recordAction)==null||o.call(s,{name:"fill",selector:this._hoveredModel.selector,signals:[],text:n.value});return}if(["INPUT","TEXTAREA"].includes(n.nodeName)||n.isContentEditable){if(n.nodeName==="INPUT"&&["checkbox","radio"].includes(n.type.toLowerCase())||this._consumedDueWrongTarget(t))return;(a=(l=this._recorder.delegate).recordAction)==null||a.call(l,{name:"fill",selector:this._activeModel.selector,signals:[],text:n.isContentEditable?n.innerText:n.value})}if(n.nodeName==="SELECT"){const u=n;if(this._actionInProgress(t))return;this._performAction({name:"select",selector:this._activeModel.selector,options:[...u.selectedOptions].map(c=>c.value),signals:[]})}}onKeyDown(t){if(this._shouldGenerateKeyPressFor(t)){if(this._actionInProgress(t)){this._expectProgrammaticKeyUp=!0;return}if(!this._consumedDueWrongTarget(t)){if(t.key===" "){const n=Wo(this._recorder.deepEventTarget(t));if(n){this._performAction({name:n.checked?"uncheck":"check",selector:this._activeModel.selector,signals:[]});return}}this._performAction({name:"press",selector:this._activeModel.selector,signals:[],key:t.key,modifiers:Qc(t)})}}}onKeyUp(t){if(this._shouldGenerateKeyPressFor(t)){if(!this._expectProgrammaticKeyUp){q(t);return}this._expectProgrammaticKeyUp=!1}}onScroll(t){this._hoveredModel=null,this._hoveredElement=null,this._recorder.updateHighlight(null,!1)}_onFocus(t){const n=Ww(this._recorder.document);if(t&&n===this._recorder.document.body)return;const r=n?this._recorder.injectedScript.generateSelector(n,{testIdAttributeName:this._recorder.state.testIdAttributeName}):null;this._activeModel=r&&r.selector?r:null,t&&(this._hoveredElement=n),this._updateModelForHoveredElement()}_shouldIgnoreMouseEvent(t){const n=this._recorder.deepEventTarget(t),r=n.nodeName;return!!(r==="SELECT"||r==="OPTION"||r==="INPUT"&&["date","range"].includes(n.type))}_actionInProgress(t){return this._performingAction?!0:(q(t),!1)}_consumedDueToNoModel(t,n){return n?!1:(q(t),!0)}_consumedDueWrongTarget(t){return this._activeModel&&this._activeModel.elements[0]===this._recorder.deepEventTarget(t)?!1:(q(t),!0)}async _performAction(t){var n,r;this._hoveredElement=null,this._hoveredModel=null,this._activeModel=null,this._recorder.updateHighlight(null,!1),this._performingAction=!0,await((r=(n=this._recorder.delegate).performAction)==null?void 0:r.call(n,t).catch(()=>{})),this._performingAction=!1,this._onFocus(!1),this._recorder.injectedScript.isUnderTest&&console.error("Action performed for test: "+JSON.stringify({hovered:this._hoveredModel?this._hoveredModel.selector:null,active:this._activeModel?this._activeModel.selector:null}))}_shouldGenerateKeyPressFor(t){if(t.key==="Enter"&&(this._recorder.deepEventTarget(t).nodeName==="TEXTAREA"||this._recorder.deepEventTarget(t).isContentEditable)||["Backspace","Delete","AltGraph"].includes(t.key)||t.key==="@"&&t.code==="KeyL")return!1;if(navigator.platform.includes("Mac")){if(t.key==="v"&&t.metaKey)return!1}else if(t.key==="v"&&t.ctrlKey||t.key==="Insert"&&t.shiftKey)return!1;if(["Shift","Control","Meta","Alt","Process"].includes(t.key))return!1;const n=t.ctrlKey||t.altKey||t.metaKey;return t.key.length===1&&!n?!!Wo(this._recorder.deepEventTarget(t)):!0}_updateModelForHoveredElement(){if(!this._hoveredElement||!this._hoveredElement.isConnected){this._hoveredModel=null,this._hoveredElement=null,this._recorder.updateHighlight(null,!0);return}const{selector:t,elements:n}=this._recorder.injectedScript.generateSelector(this._hoveredElement,{testIdAttributeName:this._recorder.state.testIdAttributeName});this._hoveredModel&&this._hoveredModel.selector===t||(this._hoveredModel=t?{selector:t,elements:n,color:"#dc6f6f7f"}:null,this._recorder.updateHighlight(this._hoveredModel,!0))}}class Xc{constructor(t,n){this._hoverHighlight=null,this._action=null,this._dialogElement=null,this._textCache=new Map,this._recorder=t,this._kind=n,this._acceptButton=this._recorder.document.createElement("x-pw-tool-item"),this._acceptButton.title="Accept",this._acceptButton.classList.add("accept"),this._acceptButton.appendChild(this._recorder.document.createElement("x-div")),this._acceptButton.addEventListener("click",()=>this._commit()),this._cancelButton=this._recorder.document.createElement("x-pw-tool-item"),this._cancelButton.title="Close",this._cancelButton.classList.add("cancel"),this._cancelButton.appendChild(this._recorder.document.createElement("x-div")),this._cancelButton.addEventListener("click",()=>this._closeDialog())}cursor(){return"pointer"}cleanup(){this._closeDialog(),this._hoverHighlight=null}onClick(t){q(t),this._kind==="value"?this._commitAssertValue():this._dialogElement||this._showDialog()}onMouseDown(t){const n=this._recorder.deepEventTarget(t);this._elementHasValue(n)&&t.preventDefault()}onPointerUp(t){var r;const n=(r=this._hoverHighlight)==null?void 0:r.elements[0];this._kind==="value"&&n&&(n.nodeName==="INPUT"||n.nodeName==="SELECT")&&n.disabled&&this._commitAssertValue()}onMouseMove(t){var r;if(this._dialogElement)return;const n=this._recorder.deepEventTarget(t);((r=this._hoverHighlight)==null?void 0:r.elements[0])!==n&&(this._kind==="text"?this._hoverHighlight=this._recorder.injectedScript.utils.elementText(this._textCache,n).full?{elements:[n],selector:""}:null:this._hoverHighlight=this._elementHasValue(n)?this._recorder.injectedScript.generateSelector(n,{testIdAttributeName:this._recorder.state.testIdAttributeName}):null,this._hoverHighlight&&(this._hoverHighlight.color="#8acae480"),this._recorder.updateHighlight(this._hoverHighlight,!0))}onKeyDown(t){var n,r;t.key==="Escape"&&((r=(n=this._recorder.delegate).setMode)==null||r.call(n,"recording")),q(t)}onScroll(t){this._recorder.updateHighlight(this._hoverHighlight,!1)}_elementHasValue(t){return t.nodeName==="TEXTAREA"||t.nodeName==="SELECT"||t.nodeName==="INPUT"&&!["button","image","reset","submit"].includes(t.type)}_generateAction(){var n;this._textCache.clear();const t=(n=this._hoverHighlight)==null?void 0:n.elements[0];if(!t)return null;if(this._kind==="value"){if(!this._elementHasValue(t))return null;const{selector:r}=this._recorder.injectedScript.generateSelector(t,{testIdAttributeName:this._recorder.state.testIdAttributeName});return t.nodeName==="INPUT"&&["checkbox","radio"].includes(t.type.toLowerCase())?{name:"assertChecked",selector:r,signals:[],checked:!t.checked}:{name:"assertValue",selector:r,signals:[],value:t.value}}else return this._hoverHighlight=this._recorder.injectedScript.generateSelector(t,{testIdAttributeName:this._recorder.state.testIdAttributeName,forTextExpect:!0}),this._hoverHighlight.color="#8acae480",this._recorder.updateHighlight(this._hoverHighlight,!0),{name:"assertText",selector:this._hoverHighlight.selector,signals:[],text:this._recorder.injectedScript.utils.elementText(this._textCache,t).normalized,substring:!0}}_renderValue(t){return(t==null?void 0:t.name)==="assertText"?this._recorder.injectedScript.utils.normalizeWhiteSpace(t.text):(t==null?void 0:t.name)==="assertChecked"?String(t.checked):(t==null?void 0:t.name)==="assertValue"?t.value:""}_commit(){var t,n,r,i;!this._action||!this._dialogElement||(this._closeDialog(),(n=(t=this._recorder.delegate).recordAction)==null||n.call(t,this._action),(i=(r=this._recorder.delegate).setMode)==null||i.call(r,"recording"))}_showDialog(){var a;if(!((a=this._hoverHighlight)!=null&&a.elements[0])||(this._action=this._generateAction(),!this._action||this._action.name!=="assertText"))return;this._dialogElement=this._recorder.document.createElement("x-pw-dialog"),this._keyboardListener=u=>{if(u.key==="Escape"){this._closeDialog();return}if(u.key==="Enter"&&(u.ctrlKey||u.metaKey)){this._dialogElement&&this._commit();return}},this._recorder.document.addEventListener("keydown",this._keyboardListener,!0);const t=this._recorder.document.createElement("x-pw-tools-list"),n=this._recorder.document.createElement("label");n.textContent="Assert that element contains text",t.appendChild(n),t.appendChild(this._recorder.document.createElement("x-spacer")),t.appendChild(this._acceptButton),t.appendChild(this._cancelButton),this._dialogElement.appendChild(t);const r=this._recorder.document.createElement("x-pw-dialog-body"),i=this._action,s=this._recorder.document.createElement("textarea");s.setAttribute("spellcheck","false"),s.value=this._renderValue(this._action),s.classList.add("text-editor");const o=()=>{var g;const u=this._recorder.injectedScript.utils.normalizeWhiteSpace(s.value),c=(g=this._hoverHighlight)==null?void 0:g.elements[0];if(!c)return;i.text=u;const h=this._recorder.injectedScript.utils.elementText(this._textCache,c).normalized,f=u&&h.includes(u);s.classList.toggle("does-not-match",!f)};s.addEventListener("input",o),r.appendChild(s),this._dialogElement.appendChild(r),this._recorder.highlight.appendChild(this._dialogElement);const l=this._recorder.highlight.tooltipPosition(this._recorder.highlight.firstBox(),this._dialogElement);this._dialogElement.style.top=l.anchorTop+"px",this._dialogElement.style.left=l.anchorLeft+"px",s.focus()}_closeDialog(){this._dialogElement&&(this._dialogElement.remove(),this._recorder.document.removeEventListener("keydown",this._keyboardListener),this._dialogElement=null)}_commitAssertValue(){var n,r,i,s,o;if(this._kind!=="value")return;const t=this._generateAction();t&&((r=(n=this._recorder.delegate).recordAction)==null||r.call(n,t),(s=(i=this._recorder.delegate).setMode)==null||s.call(i,"recording"),(o=this._recorder.overlay)==null||o.flashToolSucceeded("assertingValue"))}}class Bw{constructor(t){this._listeners=[],this._offsetX=0,this._measure={width:0,height:0},this._recorder=t;const n=this._recorder.document;this._overlayElement=n.createElement("x-pw-overlay"),this._overlayElement.appendChild(Xp(this._recorder.document,Hw));const r=n.createElement("x-pw-tools-list");this._overlayElement.appendChild(r),this._dragHandle=n.createElement("x-pw-tool-gripper"),this._dragHandle.appendChild(n.createElement("x-div")),r.appendChild(this._dragHandle),this._recordToggle=this._recorder.document.createElement("x-pw-tool-item"),this._recordToggle.title="Record",this._recordToggle.classList.add("record"),this._recordToggle.appendChild(this._recorder.document.createElement("x-div")),r.appendChild(this._recordToggle),this._pickLocatorToggle=this._recorder.document.createElement("x-pw-tool-item"),this._pickLocatorToggle.title="Pick locator",this._pickLocatorToggle.classList.add("pick-locator"),this._pickLocatorToggle.appendChild(this._recorder.document.createElement("x-div")),r.appendChild(this._pickLocatorToggle),this._assertVisibilityToggle=this._recorder.document.createElement("x-pw-tool-item"),this._assertVisibilityToggle.title="Assert visibility",this._assertVisibilityToggle.classList.add("visibility"),this._assertVisibilityToggle.appendChild(this._recorder.document.createElement("x-div")),r.appendChild(this._assertVisibilityToggle),this._assertTextToggle=this._recorder.document.createElement("x-pw-tool-item"),this._assertTextToggle.title="Assert text",this._assertTextToggle.classList.add("text"),this._assertTextToggle.appendChild(this._recorder.document.createElement("x-div")),r.appendChild(this._assertTextToggle),this._assertValuesToggle=this._recorder.document.createElement("x-pw-tool-item"),this._assertValuesToggle.title="Assert value",this._assertValuesToggle.classList.add("value"),this._assertValuesToggle.appendChild(this._recorder.document.createElement("x-div")),r.appendChild(this._assertValuesToggle),this._updateVisualPosition(),this._refreshListeners()}_refreshListeners(){qp(this._listeners),this._listeners=[Z(this._dragHandle,"mousedown",t=>{this._dragState={offsetX:this._offsetX,dragStart:{x:t.clientX,y:0}}}),Z(this._recordToggle,"click",()=>{var t,n;(n=(t=this._recorder.delegate).setMode)==null||n.call(t,this._recorder.state.mode==="none"||this._recorder.state.mode==="standby"||this._recorder.state.mode==="inspecting"?"recording":"standby")}),Z(this._pickLocatorToggle,"click",()=>{var n,r;const t={inspecting:"standby",none:"inspecting",standby:"inspecting",recording:"recording-inspecting","recording-inspecting":"recording",assertingText:"recording-inspecting",assertingVisibility:"recording-inspecting",assertingValue:"recording-inspecting"};(r=(n=this._recorder.delegate).setMode)==null||r.call(n,t[this._recorder.state.mode])}),Z(this._assertVisibilityToggle,"click",()=>{var t,n;this._assertVisibilityToggle.classList.contains("disabled")||(n=(t=this._recorder.delegate).setMode)==null||n.call(t,this._recorder.state.mode==="assertingVisibility"?"recording":"assertingVisibility")}),Z(this._assertTextToggle,"click",()=>{var t,n;this._assertTextToggle.classList.contains("disabled")||(n=(t=this._recorder.delegate).setMode)==null||n.call(t,this._recorder.state.mode==="assertingText"?"recording":"assertingText")}),Z(this._assertValuesToggle,"click",()=>{var t,n;this._assertValuesToggle.classList.contains("disabled")||(n=(t=this._recorder.delegate).setMode)==null||n.call(t,this._recorder.state.mode==="assertingValue"?"recording":"assertingValue")})]}install(){this._recorder.highlight.appendChild(this._overlayElement),this._refreshListeners(),this._updateVisualPosition()}contains(t){return this._recorder.injectedScript.utils.isInsideScope(this._overlayElement,t)}setUIState(t){this._recordToggle.classList.toggle("active",t.mode==="recording"||t.mode==="assertingText"||t.mode==="assertingVisibility"||t.mode==="assertingValue"||t.mode==="recording-inspecting"),this._pickLocatorToggle.classList.toggle("active",t.mode==="inspecting"||t.mode==="recording-inspecting"),this._assertVisibilityToggle.classList.toggle("active",t.mode==="assertingVisibility"),this._assertVisibilityToggle.classList.toggle("disabled",t.mode==="none"||t.mode==="standby"||t.mode==="inspecting"),this._assertTextToggle.classList.toggle("active",t.mode==="assertingText"),this._assertTextToggle.classList.toggle("disabled",t.mode==="none"||t.mode==="standby"||t.mode==="inspecting"),this._assertValuesToggle.classList.toggle("active",t.mode==="assertingValue"),this._assertValuesToggle.classList.toggle("disabled",t.mode==="none"||t.mode==="standby"||t.mode==="inspecting"),this._offsetX!==t.overlay.offsetX&&(this._offsetX=t.overlay.offsetX,this._updateVisualPosition()),t.mode==="none"?this._hideOverlay():this._showOverlay()}flashToolSucceeded(t){const n=t==="assertingVisibility"?this._assertVisibilityToggle:this._assertValuesToggle;n.classList.add("succeeded"),this._recorder.injectedScript.builtinSetTimeout(()=>n.classList.remove("succeeded"),2e3)}_hideOverlay(){this._overlayElement.setAttribute("hidden","true")}_showOverlay(){this._overlayElement.hasAttribute("hidden")&&(this._overlayElement.removeAttribute("hidden"),this._updateVisualPosition())}_updateVisualPosition(){this._measure=this._overlayElement.getBoundingClientRect(),this._overlayElement.style.left=(this._recorder.injectedScript.window.innerWidth-this._measure.width)/2+this._offsetX+"px"}onMouseMove(t){var n,r;if(!t.buttons)return this._dragState=void 0,!1;if(this._dragState){this._offsetX=this._dragState.offsetX+t.clientX-this._dragState.dragStart.x;const i=(this._recorder.injectedScript.window.innerWidth-this._measure.width)/2-10;return this._offsetX=Math.max(-i,Math.min(i,this._offsetX)),this._updateVisualPosition(),(r=(n=this._recorder.delegate).setOverlayState)==null||r.call(n,{offsetX:this._offsetX}),q(t),!0}return!1}onMouseUp(t){return this._dragState?(q(t),!0):!1}onClick(t){return this._dragState?(this._dragState=void 0,q(t),!0):!1}}class Vw{constructor(t){this._listeners=[],this._actionSelectorModel=null,this.state={mode:"none",testIdAttributeName:"data-testid",language:"javascript",overlay:{offsetX:0}},this.delegate={},this.document=t.document,this.injectedScript=t,this.highlight=t.createHighlight(),this._tools={none:new qc,standby:new qc,inspecting:new Vo(this,!1),recording:new Uw(this),"recording-inspecting":new Vo(this,!1),assertingText:new Xc(this,"text"),assertingVisibility:new Vo(this,!0),assertingValue:new Xc(this,"value")},this._currentTool=this._tools.none,t.window.top===t.window&&(this.overlay=new Bw(this),this.overlay.setUIState(this.state)),this._stylesheet=new t.window.CSSStyleSheet,this._stylesheet.replaceSync(`
      body[data-pw-cursor=pointer] *, body[data-pw-cursor=pointer] *::after { cursor: pointer !important; }
      body[data-pw-cursor=text] *, body[data-pw-cursor=text] *::after { cursor: text !important; }
    `),this.installListeners(),t.utils.cacheNormalizedWhitespaces(),t.isUnderTest&&console.error("Recorder script ready for test")}installListeners(){var t;qp(this._listeners),this._listeners=[Z(this.document,"click",n=>this._onClick(n),!0),Z(this.document,"auxclick",n=>this._onClick(n),!0),Z(this.document,"contextmenu",n=>this._onContextMenu(n),!0),Z(this.document,"dragstart",n=>this._onDragStart(n),!0),Z(this.document,"input",n=>this._onInput(n),!0),Z(this.document,"keydown",n=>this._onKeyDown(n),!0),Z(this.document,"keyup",n=>this._onKeyUp(n),!0),Z(this.document,"pointerdown",n=>this._onPointerDown(n),!0),Z(this.document,"pointerup",n=>this._onPointerUp(n),!0),Z(this.document,"mousedown",n=>this._onMouseDown(n),!0),Z(this.document,"mouseup",n=>this._onMouseUp(n),!0),Z(this.document,"mousemove",n=>this._onMouseMove(n),!0),Z(this.document,"mouseleave",n=>this._onMouseLeave(n),!0),Z(this.document,"mouseenter",n=>this._onMouseEnter(n),!0),Z(this.document,"focus",n=>this._onFocus(n),!0),Z(this.document,"scroll",n=>this._onScroll(n),!0)],this.highlight.install(),(t=this.overlay)==null||t.install(),this.document.adoptedStyleSheets.push(this._stylesheet)}_switchCurrentTool(){var n,r,i;const t=this._tools[this.state.mode];t!==this._currentTool&&((r=(n=this._currentTool).cleanup)==null||r.call(n),this.clearHighlight(),this._currentTool=t,(i=this.injectedScript.document.body)==null||i.setAttribute("data-pw-cursor",t.cursor()))}setUIState(t,n){var r,i,s,o;this.delegate=n,t.actionPoint&&this.state.actionPoint&&t.actionPoint.x===this.state.actionPoint.x&&t.actionPoint.y===this.state.actionPoint.y||!t.actionPoint&&!this.state.actionPoint||(t.actionPoint?this.highlight.showActionPoint(t.actionPoint.x,t.actionPoint.y):this.highlight.hideActionPoint()),this.state=t,this.highlight.setLanguage(t.language),this._switchCurrentTool(),(r=this.overlay)==null||r.setUIState(t),(i=this._actionSelectorModel)!=null&&i.selector&&!((s=this._actionSelectorModel)!=null&&s.elements.length)&&(this._actionSelectorModel=null),t.actionSelector!==((o=this._actionSelectorModel)==null?void 0:o.selector)&&(this._actionSelectorModel=t.actionSelector?Xw(this.injectedScript,t.actionSelector,this.document):null),(this.state.mode==="none"||this.state.mode==="standby")&&this.updateHighlight(this._actionSelectorModel,!1)}clearHighlight(){var t,n;(n=(t=this._currentTool).cleanup)==null||n.call(t),this.updateHighlight(null,!1)}_onClick(t){var n,r,i;t.isTrusted&&((n=this.overlay)!=null&&n.onClick(t)||this._ignoreOverlayEvent(t)||(i=(r=this._currentTool).onClick)==null||i.call(r,t))}_onContextMenu(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onContextMenu)==null||r.call(n,t))}_onDragStart(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onDragStart)==null||r.call(n,t))}_onPointerDown(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onPointerDown)==null||r.call(n,t))}_onPointerUp(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onPointerUp)==null||r.call(n,t))}_onMouseDown(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onMouseDown)==null||r.call(n,t))}_onMouseUp(t){var n,r,i;t.isTrusted&&((n=this.overlay)!=null&&n.onMouseUp(t)||this._ignoreOverlayEvent(t)||(i=(r=this._currentTool).onMouseUp)==null||i.call(r,t))}_onMouseMove(t){var n,r,i;t.isTrusted&&((n=this.overlay)!=null&&n.onMouseMove(t)||this._ignoreOverlayEvent(t)||(i=(r=this._currentTool).onMouseMove)==null||i.call(r,t))}_onMouseEnter(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onMouseEnter)==null||r.call(n,t))}_onMouseLeave(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onMouseLeave)==null||r.call(n,t))}_onFocus(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onFocus)==null||r.call(n,t))}_onScroll(t){var n,r;t.isTrusted&&(this.highlight.hideActionPoint(),(r=(n=this._currentTool).onScroll)==null||r.call(n,t))}_onInput(t){var n,r;this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onInput)==null||r.call(n,t)}_onKeyDown(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onKeyDown)==null||r.call(n,t))}_onKeyUp(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onKeyUp)==null||r.call(n,t))}updateHighlight(t,n){var i,s;let r=t==null?void 0:t.tooltipText;r===void 0&&!(t!=null&&t.tooltipList)&&(t!=null&&t.selector)&&(r=this.injectedScript.utils.asLocator(this.state.language,t.selector)),this.highlight.updateHighlight((t==null?void 0:t.elements)||[],{...t,tooltipText:r}),n&&((s=(i=this.delegate).highlightUpdated)==null||s.call(i))}_ignoreOverlayEvent(t){return t.composedPath().some(n=>(n.nodeName||"").toLowerCase()==="x-pw-glass")}deepEventTarget(t){var n;for(const r of t.composedPath())if(!((n=this.overlay)!=null&&n.contains(r)))return r;return t.composedPath()[0]}}function Ww(e){let t=e.activeElement;for(;t&&t.shadowRoot&&t.shadowRoot.activeElement;)t=t.shadowRoot.activeElement;return t}function Qc(e){return(e.altKey?1:0)|(e.ctrlKey?2:0)|(e.metaKey?4:0)|(e.shiftKey?8:0)}function qw(e){switch(e.which){case 1:return"left";case 2:return"middle";case 3:return"right"}return"left"}function Kc(e){if(e.target.nodeName==="CANVAS")return{x:e.offsetX,y:e.offsetY}}function q(e){e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation()}function Wo(e){if(!e||e.nodeName!=="INPUT")return null;const t=e;return["checkbox","radio"].includes(t.type)?t:null}function Gc(e){return!e||e.nodeName!=="INPUT"?!1:e.type.toLowerCase()==="range"}function Z(e,t,n,r){return e.addEventListener(t,n,r),()=>{e.removeEventListener(t,n,r)}}function qp(e){for(const t of e)t();e.splice(0,e.length)}function Xw(e,t,n){try{const r=e.parseSelector(t);return{selector:t,elements:e.querySelectorAll(r,n)}}catch{return{selector:t,elements:[]}}}function Xp(e,{tagName:t,attrs:n,children:r}){const i=e.createElementNS("http://www.w3.org/2000/svg",t);if(n)for(const[s,o]of Object.entries(n))i.setAttribute(s,o);if(r)for(const s of r)i.appendChild(Xp(e,s));return i}function tu(e,t,n){return`internal:attr=[${e}=${Ee(t,(n==null?void 0:n.exact)||!1)}]`}function Qw(e,t){return`internal:testid=[${e}=${Ee(t,!0)}]`}function Kw(e,t){return"internal:label="+Ge(e,!!(t!=null&&t.exact))}function Gw(e,t){return tu("alt",e,t)}function Yw(e,t){return tu("title",e,t)}function Jw(e,t){return tu("placeholder",e,t)}function Zw(e,t){return"internal:text="+Ge(e,!!(t!=null&&t.exact))}function e1(e,t={}){const n=[];return t.checked!==void 0&&n.push(["checked",String(t.checked)]),t.disabled!==void 0&&n.push(["disabled",String(t.disabled)]),t.selected!==void 0&&n.push(["selected",String(t.selected)]),t.expanded!==void 0&&n.push(["expanded",String(t.expanded)]),t.includeHidden!==void 0&&n.push(["include-hidden",String(t.includeHidden)]),t.level!==void 0&&n.push(["level",String(t.level)]),t.name!==void 0&&n.push(["name",Ee(t.name,!!t.exact)]),t.pressed!==void 0&&n.push(["pressed",String(t.pressed)]),`internal:role=${e}${n.map(([r,i])=>`[${r}=${i}]`).join("")}`}const Cr=Symbol("selector");class qn{constructor(t,n,r){if(r!=null&&r.hasText&&(n+=` >> internal:has-text=${Ge(r.hasText,!1)}`),r!=null&&r.hasNotText&&(n+=` >> internal:has-not-text=${Ge(r.hasNotText,!1)}`),r!=null&&r.has&&(n+=" >> internal:has="+JSON.stringify(r.has[Cr])),r!=null&&r.hasNot&&(n+=" >> internal:has-not="+JSON.stringify(r.hasNot[Cr])),this[Cr]=n,n){const o=t.parseSelector(n);this.element=t.querySelector(o,t.document,!1),this.elements=t.querySelectorAll(o,t.document)}const i=n,s=this;s.locator=(o,l)=>new qn(t,i?i+" >> "+o:o,l),s.getByTestId=o=>s.locator(Qw(t.testIdAttributeNameForStrictErrorAndConsoleCodegen(),o)),s.getByAltText=(o,l)=>s.locator(Gw(o,l)),s.getByLabel=(o,l)=>s.locator(Kw(o,l)),s.getByPlaceholder=(o,l)=>s.locator(Jw(o,l)),s.getByText=(o,l)=>s.locator(Zw(o,l)),s.getByTitle=(o,l)=>s.locator(Yw(o,l)),s.getByRole=(o,l={})=>s.locator(e1(o,l)),s.filter=o=>new qn(t,n,o),s.first=()=>s.locator("nth=0"),s.last=()=>s.locator("nth=-1"),s.nth=o=>s.locator(`nth=${o}`),s.and=o=>new qn(t,i+" >> internal:and="+JSON.stringify(o[Cr])),s.or=o=>new qn(t,i+" >> internal:or="+JSON.stringify(o[Cr]))}}class t1{constructor(t){this._injectedScript=t,!this._injectedScript.window.playwright&&(this._injectedScript.window.playwright={$:(n,r)=>this._querySelector(n,!!r),$$:n=>this._querySelectorAll(n),inspect:n=>this._inspect(n),selector:n=>this._selector(n),generateLocator:(n,r)=>this._generateLocator(n,r),resume:()=>this._resume(),...new qn(t,"")},delete this._injectedScript.window.playwright.filter,delete this._injectedScript.window.playwright.first,delete this._injectedScript.window.playwright.last,delete this._injectedScript.window.playwright.nth,delete this._injectedScript.window.playwright.and,delete this._injectedScript.window.playwright.or)}_querySelector(t,n){if(typeof t!="string")throw new Error("Usage: playwright.query('Playwright >> selector').");const r=this._injectedScript.parseSelector(t);return this._injectedScript.querySelector(r,this._injectedScript.document,n)}_querySelectorAll(t){if(typeof t!="string")throw new Error("Usage: playwright.$$('Playwright >> selector').");const n=this._injectedScript.parseSelector(t);return this._injectedScript.querySelectorAll(n,this._injectedScript.document)}_inspect(t){if(typeof t!="string")throw new Error("Usage: playwright.inspect('Playwright >> selector').");this._injectedScript.window.inspect(this._querySelector(t,!1))}_selector(t){if(!(t instanceof Element))throw new Error("Usage: playwright.selector(element).");return this._injectedScript.generateSelectorSimple(t)}_generateLocator(t,n){if(!(t instanceof Element))throw new Error("Usage: playwright.locator(element).");const r=this._injectedScript.generateSelectorSimple(t);return Yt(n||"javascript",r)}_resume(){this._injectedScript.window.__pw_resume().catch(()=>{})}}function n1(e,t){e=e.replace(/AriaRole\s*\.\s*([\w]+)/g,(s,o)=>o.toLowerCase()).replace(/(get_by_role|getByRole)\s*\(\s*(?:["'`])([^'"`]+)['"`]/g,(s,o,l)=>`${o}(${l.toLowerCase()}`);const n=[];let r="";for(let s=0;s<e.length;++s){const o=e[s];if(o!=='"'&&o!=="'"&&o!=="`"&&o!=="/"){r+=o;continue}const l=e[s-1]==="r"||e[s]==="/";++s;let a="";for(;s<e.length;){if(e[s]==="\\"){l?(e[s+1]!==o&&(a+=e[s]),++s,a+=e[s]):(++s,e[s]==="n"?a+=`
`:e[s]==="r"?a+="\r":e[s]==="t"?a+="	":a+=e[s]),++s;continue}if(e[s]!==o){a+=e[s++];continue}break}n.push({quote:o,text:a}),r+=(o==="/"?"r":"")+"$"+n.length}r=r.toLowerCase().replace(/get_by_alt_text/g,"getbyalttext").replace(/get_by_test_id/g,"getbytestid").replace(/get_by_([\w]+)/g,"getby$1").replace(/has_not_text/g,"hasnottext").replace(/has_text/g,"hastext").replace(/has_not/g,"hasnot").replace(/frame_locator/g,"framelocator").replace(/[{}\s]/g,"").replace(/new\(\)/g,"").replace(/new[\w]+\.[\w]+options\(\)/g,"").replace(/\.set/g,",set").replace(/\.or_\(/g,"or(").replace(/\.and_\(/g,"and(").replace(/:/g,"=").replace(/,re\.ignorecase/g,"i").replace(/,pattern.case_insensitive/g,"i").replace(/,regexoptions.ignorecase/g,"i").replace(/re.compile\(([^)]+)\)/g,"$1").replace(/pattern.compile\(([^)]+)\)/g,"r$1").replace(/newregex\(([^)]+)\)/g,"r$1").replace(/string=/g,"=").replace(/regex=/g,"=").replace(/,,/g,",");const i=n.map(s=>s.quote).filter(s=>"'\"`".includes(s))[0];return{selector:Qp(r,n,t),preferredQuote:i}}function Yc(e){return[...e.matchAll(/\$\d+/g)].length}function Jc(e,t){return e.replace(/\$(\d+)/g,(n,r)=>`$${r-t}`)}function Qp(e,t,n){for(;;){const i=e.match(/filter\(,?(has=|hasnot=|sethas\(|sethasnot\()/);if(!i)break;const s=i.index+i[0].length;let o=0,l=s;for(;l<e.length&&(e[l]==="("?o++:e[l]===")"&&o--,!(o<0));l++);let a=e.substring(0,s),u=0;["sethas(","sethasnot("].includes(i[1])&&(u=1,a=a.replace(/sethas\($/,"has=").replace(/sethasnot\($/,"hasnot="));const c=Yc(e.substring(0,s)),h=Jc(e.substring(s,l),c),f=Yc(h),g=t.slice(c,c+f),w=JSON.stringify(Qp(h,g,n));e=a.replace(/=$/,"2=")+`$${c+1}`+Jc(e.substring(l+u),f-1);const y=t.slice(0,c),x=t.slice(c+f);t=y.concat([{quote:'"',text:w}]).concat(x)}e=e.replace(/\,set([\w]+)\(([^)]+)\)/g,(i,s,o)=>","+s.toLowerCase()+"="+o.toLowerCase()).replace(/framelocator\(([^)]+)\)/g,"$1.internal:control=enter-frame").replace(/locator\(([^)]+),hastext=([^),]+)\)/g,"locator($1).internal:has-text=$2").replace(/locator\(([^)]+),hasnottext=([^),]+)\)/g,"locator($1).internal:has-not-text=$2").replace(/locator\(([^)]+),hastext=([^),]+)\)/g,"locator($1).internal:has-text=$2").replace(/locator\(([^)]+)\)/g,"$1").replace(/getbyrole\(([^)]+)\)/g,"internal:role=$1").replace(/getbytext\(([^)]+)\)/g,"internal:text=$1").replace(/getbylabel\(([^)]+)\)/g,"internal:label=$1").replace(/getbytestid\(([^)]+)\)/g,`internal:testid=[${n}=$1]`).replace(/getby(placeholder|alt|title)(?:text)?\(([^)]+)\)/g,"internal:attr=[$1=$2]").replace(/first(\(\))?/g,"nth=0").replace(/last(\(\))?/g,"nth=-1").replace(/nth\(([^)]+)\)/g,"nth=$1").replace(/filter\(,?hastext=([^)]+)\)/g,"internal:has-text=$1").replace(/filter\(,?hasnottext=([^)]+)\)/g,"internal:has-not-text=$1").replace(/filter\(,?has2=([^)]+)\)/g,"internal:has=$1").replace(/filter\(,?hasnot2=([^)]+)\)/g,"internal:has-not=$1").replace(/,exact=false/g,"").replace(/,exact=true/g,"s").replace(/\,/g,"][");const r=e.split(".");for(let i=0;i<r.length-1;i++)if(r[i]==="internal:control=enter-frame"&&r[i+1].startsWith("nth=")){const[s]=r.splice(i,1);r.splice(i+1,0,s)}return r.map(i=>!i.startsWith("internal:")||i==="internal:control"?i.replace(/\$(\d+)/g,(s,o)=>t[+o-1].text):(i=i.includes("[")?i.replace(/\]/,"")+"]":i,i=i.replace(/(?:r)\$(\d+)(i)?/g,(s,o,l)=>{const a=t[+o-1];return i.startsWith("internal:attr")||i.startsWith("internal:testid")||i.startsWith("internal:role")?Ee(new RegExp(a.text),!1)+(l||""):Ge(new RegExp(a.text,l),!1)}).replace(/\$(\d+)(i|s)?/g,(s,o,l)=>{const a=t[+o-1];return i.startsWith("internal:has=")||i.startsWith("internal:has-not=")?a.text:i.startsWith("internal:testid")?Ee(a.text,!0):i.startsWith("internal:attr")||i.startsWith("internal:role")?Ee(a.text,l==="s"):Ge(a.text,l==="s")}),i)).join(" >> ")}function r1(e,t,n){try{return so(t),t}catch{}try{const{selector:r,preferredQuote:i}=n1(t,n),s=Bh(e,r,void 0,void 0,i),o=Zc(e,t);if(s.some(l=>Zc(e,l)===o))return r}catch{}return""}function Zc(e,t){return t=t.replace(/\s/g,""),e==="javascript"&&(t=t.replace(/\\?["`]/g,"'")),t}const i1=({url:e})=>d.jsxs("div",{className:"browser-frame-header",children:[d.jsxs("div",{style:{whiteSpace:"nowrap"},children:[d.jsx("span",{className:"browser-frame-dot",style:{backgroundColor:"rgb(242, 95, 88)"}}),d.jsx("span",{className:"browser-frame-dot",style:{backgroundColor:"rgb(251, 190, 60)"}}),d.jsx("span",{className:"browser-frame-dot",style:{backgroundColor:"rgb(88, 203, 66)"}})]}),d.jsx("div",{className:"browser-frame-address-bar",title:e||"about:blank",children:e||"about:blank"}),d.jsx("div",{style:{marginLeft:"auto"},children:d.jsxs("div",{children:[d.jsx("span",{className:"browser-frame-menu-bar"}),d.jsx("span",{className:"browser-frame-menu-bar"}),d.jsx("span",{className:"browser-frame-menu-bar"})]})})]}),s1=({action:e,sdkLanguage:t,testIdAttributeName:n,isInspecting:r,setIsInspecting:i,highlightedLocator:s,setHighlightedLocator:o,openPage:l})=>{const[a,u]=_n(),[c,h]=b.useState("action"),{snapshots:f}=b.useMemo(()=>{if(!e)return{snapshots:{}};let _=e.beforeSnapshot?{action:e,snapshotName:e.beforeSnapshot}:void 0,T=e;for(;!_&&T;)T=Gv(T),_=T!=null&&T.afterSnapshot?{action:T,snapshotName:T==null?void 0:T.afterSnapshot}:void 0;const L=e.afterSnapshot?{action:e,snapshotName:e.afterSnapshot}:_,k=e.inputSnapshot?{action:e,snapshotName:e.inputSnapshot}:L;return k&&(k.point=e.point),{snapshots:{action:k,before:_,after:L}}},[e]),{snapshotInfoUrl:g,snapshotUrl:w,popoutUrl:y}=b.useMemo(()=>{const _=f[c];if(!_)return{snapshotUrl:l1};const T=new URLSearchParams;T.set("trace",Rs(_.action).traceUrl),T.set("name",_.snapshotName),_.point&&(T.set("pointX",String(_.point.x)),T.set("pointY",String(_.point.y)));const L=new URL(`snapshot/${_.action.pageId}?${T.toString()}`,window.location.href).toString(),k=new URL(`snapshotInfo/${_.action.pageId}?${T.toString()}`,window.location.href).toString(),M=new URLSearchParams;M.set("r",L),M.set("trace",Rs(_.action).traceUrl),_.point&&(M.set("pointX",String(_.point.x)),M.set("pointY",String(_.point.y)));const $=new URL(`snapshot.html?${M.toString()}`,window.location.href).toString();return{snapshots:f,snapshotInfoUrl:k,snapshotUrl:L,popoutUrl:$}},[f,c]),x=b.useRef(null),p=b.useRef(null),[m,v]=b.useState({viewport:td,url:""}),S=b.useRef({iteration:0,visibleIframe:0});b.useEffect(()=>{(async()=>{const _=S.current.iteration+1,T=1-S.current.visibleIframe;S.current.iteration=_;const L={url:"",viewport:td};if(g){const $=await(await fetch(g)).json();$.error||(L.url=$.url,L.viewport=$.viewport)}if(S.current.iteration!==_)return;const k=[x,p][T].current;if(k){let M=()=>{};const $=new Promise(Q=>M=Q);try{k.addEventListener("load",M),k.addEventListener("error",M),k.contentWindow?k.contentWindow.location.replace(w):k.src=w,await $}catch{}finally{k.removeEventListener("load",M),k.removeEventListener("error",M)}}S.current.iteration===_&&(S.current.visibleIframe=T,v(L))})()},[w,g]);const E={width:m.viewport.width,height:m.viewport.height+40},N=Math.min(a.width/E.width,a.height/E.height,1),j={x:(a.width-E.width)/2,y:(a.height-E.height)/2};return d.jsxs("div",{className:"snapshot-tab",tabIndex:0,onKeyDown:_=>{_.key==="Escape"&&r&&i(!1)},children:[d.jsx(ed,{isInspecting:r,sdkLanguage:t,testIdAttributeName:n,highlightedLocator:s,setHighlightedLocator:o,iframe:x.current,iteration:S.current.iteration}),d.jsx(ed,{isInspecting:r,sdkLanguage:t,testIdAttributeName:n,highlightedLocator:s,setHighlightedLocator:o,iframe:p.current,iteration:S.current.iteration}),d.jsxs(Xa,{children:[d.jsx(en,{className:"pick-locator",title:"Pick locator",icon:"target",toggled:r,onClick:()=>i(!r)}),["action","before","after"].map(_=>d.jsx(qh,{id:_,title:o1(_),selected:c===_,onSelect:()=>h(_)})),d.jsx("div",{style:{flex:"auto"}}),d.jsx(en,{icon:"link-external",title:"Open snapshot in a new tab",disabled:!y,onClick:()=>{l||(l=window.open);const _=l(y||"","_blank");_==null||_.addEventListener("DOMContentLoaded",()=>{const T=new Up(_,!1,t,n,1,"chromium",[]);new t1(T)})}})]}),d.jsx("div",{ref:u,className:"snapshot-wrapper",children:d.jsxs("div",{className:"snapshot-container",style:{width:E.width+"px",height:E.height+"px",transform:`translate(${j.x}px, ${j.y}px) scale(${N})`},children:[d.jsx(i1,{url:m.url}),d.jsxs("div",{className:"snapshot-switcher",children:[d.jsx("iframe",{ref:x,name:"snapshot",title:"DOM Snapshot",className:S.current.visibleIframe===0?"snapshot-visible":""}),d.jsx("iframe",{ref:p,name:"snapshot",title:"DOM Snapshot",className:S.current.visibleIframe===1?"snapshot-visible":""})]})]})})]})};function o1(e){return e==="before"?"Before":e==="after"?"After":e==="action"?"Action":e}const ed=({iframe:e,isInspecting:t,sdkLanguage:n,testIdAttributeName:r,highlightedLocator:i,setHighlightedLocator:s,iteration:o})=>(b.useEffect(()=>{const l=[],a=new URLSearchParams(window.location.search).get("isUnderTest")==="true";try{Kp(l,n,r,a,"",e==null?void 0:e.contentWindow)}catch{}for(const{recorder:u,frameSelector:c}of l){const h=r1(n,i,r);u.setUIState({mode:t?"inspecting":"none",actionSelector:h.startsWith(c)?h.substring(c.length).trim():void 0,language:n,testIdAttributeName:r,overlay:{offsetX:0}},{async setSelector(f){s(Yt(n,c+f))},highlightUpdated(){for(const f of l)f.recorder!==u&&f.recorder.clearHighlight()}})}},[e,t,i,s,n,r,o]),d.jsx(d.Fragment,{}));function Kp(e,t,n,r,i,s){if(!s)return;const o=s;if(!o._recorder){const l=new Up(s,r,t,n,1,"chromium",[]),a=new Vw(l);o._injectedScript=l,o._recorder={recorder:a,frameSelector:i}}e.push(o._recorder);for(let l=0;l<s.frames.length;++l){const a=s.frames[l],u=a.frameElement?o._injectedScript.generateSelectorSimple(a.frameElement,{omitInternalEngines:!0,testIdAttributeName:n})+" >> internal:control=enter-frame >> ":"";Kp(e,t,n,r,i+u,a)}}const td={width:1280,height:720},l1='data:text/html,<body style="background: #ddd"></body>',a1=vi,u1=({stack:e,setSelectedFrame:t,selectedFrame:n})=>{const r=e||[];return d.jsx(a1,{name:"stack-trace",items:r,selectedItem:r[n],render:i=>{const s=i.file[1]===":"?"\\":"/";return d.jsxs(d.Fragment,{children:[d.jsx("span",{className:"stack-trace-frame-function",children:i.function||"(anonymous)"}),d.jsx("span",{className:"stack-trace-frame-location",children:i.file.split(s).pop()}),d.jsx("span",{className:"stack-trace-frame-line",children:":"+i.line})]})},onSelected:i=>t(r.indexOf(i))})},c1=({stack:e,sources:t,rootDir:n,fallbackLocation:r,stackFrameLocation:i,onOpenExternally:s})=>{const[o,l]=b.useState(),[a,u]=b.useState(0);b.useEffect(()=>{o!==e&&(l(e),u(0))},[e,o,l,u]);const{source:c,highlight:h,targetLine:f,fileName:g,location:w}=Pm(async()=>{var _,T;const p=e==null?void 0:e[a],m=!(p!=null&&p.file);if(m&&!r)return{source:{file:"",errors:[],content:void 0},targetLine:0,highlight:[]};const v=m?r.file:p.file;let S=t.get(v);S||(S={errors:((_=r==null?void 0:r.source)==null?void 0:_.errors)||[],content:void 0},t.set(v,S));const C=m?r:p,E=m?(r==null?void 0:r.line)||((T=S.errors[0])==null?void 0:T.line)||0:p.line,N=n&&v.startsWith(n)?v.substring(n.length+1):v,j=S.errors.map(L=>({type:"error",line:L.line,message:L.message}));if(j.push({line:E,type:"running"}),S.content===void 0||m){const L=await d1(v);try{let k=await fetch(`sha1/src@${L}.txt`);k.status===404&&(k=await fetch(`file?path=${encodeURIComponent(v)}`)),k.status>=400?S.content=`<Unable to read "${v}">`:S.content=await k.text()}catch{S.content=`<Unable to read "${v}">`}}return{source:S,highlight:j,targetLine:E,fileName:N,location:C}},[e,a,n,r],{source:{errors:[],content:"Loading…"},highlight:[]}),y=b.useCallback(()=>{w&&(s?s(w):window.location.href=`vscode://file//${w.file}:${w.line}`)},[s,w]),x=((e==null?void 0:e.length)??0)>1;return d.jsxs($s,{sidebarSize:200,orientation:i==="bottom"?"vertical":"horizontal",sidebarHidden:!x,children:[d.jsxs("div",{className:"vbox","data-testid":"source-code",children:[g&&d.jsxs(Xa,{children:[d.jsx("span",{className:"source-tab-file-name",children:g}),d.jsx(Vh,{description:"Copy filename",value:f1(g)}),w&&d.jsx(en,{icon:"link-external",title:"Open in VS Code",onClick:y})]}),d.jsx(yi,{text:c.content||"",language:"javascript",highlight:h,revealLine:f,readOnly:!0,lineNumbers:!0})]}),d.jsx(u1,{stack:e,selectedFrame:a,setSelectedFrame:u})]})};async function d1(e){const t=new TextEncoder().encode(e),n=await crypto.subtle.digest("SHA-1",t),r=[],i=new DataView(n);for(let s=0;s<i.byteLength;s+=1){const o=i.getUint8(s).toString(16).padStart(2,"0");r.push(o)}return r.join("")}function f1(e){if(!e)return"";const t=e!=null&&e.includes("/")?"/":"\\";return(e==null?void 0:e.split(t).pop())??""}const Gp={width:200,height:45},Mn=2.5,h1=Gp.height+Mn*2,p1=({model:e,boundaries:t,previewPoint:n})=>{var c,h;const[r,i]=_n(),s=b.useRef(null);let o=0;if(s.current&&n){const f=s.current.getBoundingClientRect();o=(n.clientY-f.top+s.current.scrollTop)/h1|0}const l=(h=(c=e==null?void 0:e.pages)==null?void 0:c[o])==null?void 0:h.screencastFrames;let a,u;if(n!==void 0&&l&&l.length){const f=t.minimum+(t.maximum-t.minimum)*n.x/r.width;a=l[fd(l,f,Yp)-1];const g={width:Math.min(800,window.innerWidth/2|0),height:Math.min(800,window.innerHeight/2|0)};u=a?Jp({width:a.width,height:a.height},g):void 0}return d.jsxs("div",{className:"film-strip",ref:i,children:[d.jsx("div",{className:"film-strip-lanes",ref:s,children:e==null?void 0:e.pages.map((f,g)=>f.screencastFrames.length?d.jsx(m1,{boundaries:t,page:f,width:r.width},g):null)}),(n==null?void 0:n.x)!==void 0&&d.jsxs("div",{className:"film-strip-hover",style:{top:r.bottom+5,left:Math.min(n.x,r.width-(u?u.width:0)-10)},children:[n.action&&d.jsx("div",{className:"film-strip-hover-title",children:qa(n.action,n)}),a&&u&&d.jsx("div",{style:{width:u.width,height:u.height},children:d.jsx("img",{src:`sha1/${a.sha1}`,width:u.width,height:u.height})})]})]})},m1=({boundaries:e,page:t,width:n})=>{const r={width:0,height:0},i=t.screencastFrames;for(const y of i)r.width=Math.max(r.width,y.width),r.height=Math.max(r.height,y.height);const s=Jp(r,Gp),o=i[0].timestamp,l=i[i.length-1].timestamp,a=e.maximum-e.minimum,u=(o-e.minimum)/a*n,c=(e.maximum-l)/a*n,f=(l-o)/a*n/(s.width+2*Mn)|0,g=(l-o)/f,w=[];for(let y=0;o&&g&&y<f;++y){const x=o+g*y,p=fd(i,x,Yp)-1;w.push(d.jsx("div",{className:"film-strip-frame",style:{width:s.width,height:s.height,backgroundImage:`url(sha1/${i[p].sha1})`,backgroundSize:`${s.width}px ${s.height}px`,margin:Mn,marginRight:Mn}},y))}return w.push(d.jsx("div",{className:"film-strip-frame",style:{width:s.width,height:s.height,backgroundImage:`url(sha1/${i[i.length-1].sha1})`,backgroundSize:`${s.width}px ${s.height}px`,margin:Mn,marginRight:Mn}},w.length)),d.jsx("div",{className:"film-strip-lane",style:{marginLeft:u+"px",marginRight:c+"px"},children:w})};function Yp(e,t){return e-t.timestamp}function Jp(e,t){const n=Math.max(e.width/t.width,e.height/t.height);return{width:e.width/n|0,height:e.height/n|0}}const g1=({model:e,boundaries:t,consoleEntries:n,onSelected:r,highlightedAction:i,highlightedEntry:s,highlightedConsoleEntry:o,selectedTime:l,setSelectedTime:a,sdkLanguage:u})=>{const[c,h]=_n(),[f,g]=b.useState(),[w,y]=b.useState(),{offsets:x,curtainLeft:p,curtainRight:m}=b.useMemo(()=>{let T=l||t;if(f&&f.startX!==f.endX){const $=ct(c.width,t,f.startX),Q=ct(c.width,t,f.endX);T={minimum:Math.min($,Q),maximum:Math.max($,Q)}}const L=We(c.width,t,T.minimum),M=We(c.width,t,t.maximum)-We(c.width,t,T.maximum);return{offsets:v1(c.width,t),curtainLeft:L,curtainRight:M}},[l,t,f,c]),v=b.useMemo(()=>{const T=[];for(const L of(e==null?void 0:e.actions)||[])L.class!=="Test"&&T.push({action:L,leftTime:L.startTime,rightTime:L.endTime||t.maximum,leftPosition:We(c.width,t,L.startTime),rightPosition:We(c.width,t,L.endTime||t.maximum),active:!1,error:!!L.error});for(const L of(e==null?void 0:e.resources)||[]){const k=L._monotonicTime,M=L._monotonicTime+L.time;T.push({resource:L,leftTime:k,rightTime:M,leftPosition:We(c.width,t,k),rightPosition:We(c.width,t,M),active:!1,error:!1})}for(const L of n||[])T.push({consoleMessage:L,leftTime:L.timestamp,rightTime:L.timestamp,leftPosition:We(c.width,t,L.timestamp),rightPosition:We(c.width,t,L.timestamp),active:!1,error:L.isError});return T},[e,n,t,c]);b.useMemo(()=>{for(const T of v)i?T.active=T.action===i:s?T.active=T.resource===s:o?T.active=T.consoleMessage===o:T.active=!1},[v,i,s,o]);const S=b.useCallback(T=>{if(y(void 0),!h.current)return;const L=T.clientX-h.current.getBoundingClientRect().left,k=ct(c.width,t,L),M=l?We(c.width,t,l.minimum):0,$=l?We(c.width,t,l.maximum):0;l&&Math.abs(L-M)<10?g({startX:$,endX:L,type:"resize"}):l&&Math.abs(L-$)<10?g({startX:M,endX:L,type:"resize"}):l&&k>l.minimum&&k<l.maximum&&T.clientY-h.current.getBoundingClientRect().top<20?g({startX:M,endX:$,pivot:L,type:"move"}):g({startX:L,endX:L,type:"resize"})},[t,c,h,l]),C=b.useCallback(T=>{if(!h.current)return;const L=T.clientX-h.current.getBoundingClientRect().left,k=ct(c.width,t,L),M=e==null?void 0:e.actions.findLast(me=>me.startTime<=k);if(!T.buttons){g(void 0);return}if(M&&r(M),!f)return;let $=f;if(f.type==="resize")$={...f,endX:L};else{const me=L-f.pivot;let I=f.startX+me,R=f.endX+me;I<0&&(I=0,R=I+(f.endX-f.startX)),R>c.width&&(R=c.width,I=R-(f.endX-f.startX)),$={...f,startX:I,endX:R,pivot:L}}g($);const Q=ct(c.width,t,$.startX),Oe=ct(c.width,t,$.endX);Q!==Oe&&a({minimum:Math.min(Q,Oe),maximum:Math.max(Q,Oe)})},[t,f,c,e,r,h,a]),E=b.useCallback(()=>{if(y(void 0),!!f){if(f.startX!==f.endX){const T=ct(c.width,t,f.startX),L=ct(c.width,t,f.endX);a({minimum:Math.min(T,L),maximum:Math.max(T,L)})}else{const T=ct(c.width,t,f.startX),L=e==null?void 0:e.actions.findLast(k=>k.startTime<=T);L&&r(L),a(void 0)}g(void 0)}},[t,f,c,e,a,r]),N=b.useCallback(T=>{if(!h.current)return;const L=T.clientX-h.current.getBoundingClientRect().left,k=ct(c.width,t,L),M=e==null?void 0:e.actions.findLast($=>$.startTime<=k);y({x:L,clientY:T.clientY,action:M,sdkLanguage:u})},[t,c,e,h,u]),j=b.useCallback(()=>{y(void 0)},[]),_=b.useCallback(()=>{a(void 0)},[a]);return d.jsxs("div",{style:{flex:"none",borderBottom:"1px solid var(--vscode-panel-border)"},children:[!!f&&d.jsx(Xh,{cursor:(f==null?void 0:f.type)==="resize"?"ew-resize":"grab",onPaneMouseUp:E,onPaneMouseMove:C,onPaneDoubleClick:_}),d.jsxs("div",{ref:h,className:"timeline-view",onMouseDown:S,onMouseMove:N,onMouseLeave:j,children:[d.jsx("div",{className:"timeline-grid",children:x.map((T,L)=>d.jsx("div",{className:"timeline-divider",style:{left:T.position+"px"},children:d.jsx("div",{className:"timeline-time",children:Ye(T.time-t.minimum)})},L))}),d.jsx("div",{style:{height:8}}),d.jsx(p1,{model:e,boundaries:t,previewPoint:w}),d.jsx("div",{className:"timeline-bars",children:v.map((T,L)=>d.jsx("div",{className:"timeline-bar"+(T.action?" action":"")+(T.resource?" network":"")+(T.consoleMessage?" console-message":"")+(T.active?" active":"")+(T.error?" error":""),style:{left:T.leftPosition,width:Math.max(5,T.rightPosition-T.leftPosition),top:y1(T),bottom:0}},L))}),d.jsx("div",{className:"timeline-marker",style:{display:w!==void 0?"block":"none",left:((w==null?void 0:w.x)||0)+"px"}}),l&&d.jsxs("div",{className:"timeline-window",children:[d.jsx("div",{className:"timeline-window-curtain left",style:{width:p}}),d.jsx("div",{className:"timeline-window-resizer",style:{left:-5}}),d.jsx("div",{className:"timeline-window-center",children:d.jsx("div",{className:"timeline-window-drag"})}),d.jsx("div",{className:"timeline-window-resizer",style:{left:5}}),d.jsx("div",{className:"timeline-window-curtain right",style:{width:m}})]})]})]})};function v1(e,t){let r=e/64;const i=t.maximum-t.minimum,s=e/i;let o=i/r;const l=Math.ceil(Math.log(o)/Math.LN10);o=Math.pow(10,l),o*s>=5*64&&(o=o/5),o*s>=2*64&&(o=o/2);const a=t.minimum;let u=t.maximum;u+=64/s,r=Math.ceil((u-a)/o),o||(r=0);const c=[];for(let h=0;h<r;++h){const f=a+o*h;c.push({position:We(e,t,f),time:f})}return c}function We(e,t,n){return(n-t.minimum)/(t.maximum-t.minimum)*e}function ct(e,t,n){return n/e*(t.maximum-t.minimum)+t.minimum}function y1(e){return e.resource?25:20}const w1=({model:e})=>{var t,n;return e?d.jsxs("div",{"data-testid":"metadata-view",className:"vbox",style:{flexShrink:0},children:[d.jsx("div",{className:"call-section",style:{paddingTop:2},children:"Time"}),!!e.wallTime&&d.jsxs("div",{className:"call-line",children:["start time:",d.jsx("span",{className:"call-value datetime",title:new Date(e.wallTime).toLocaleString(),children:new Date(e.wallTime).toLocaleString()})]}),d.jsxs("div",{className:"call-line",children:["duration:",d.jsx("span",{className:"call-value number",title:Ye(e.endTime-e.startTime),children:Ye(e.endTime-e.startTime)})]}),d.jsx("div",{className:"call-section",children:"Browser"}),d.jsxs("div",{className:"call-line",children:["engine:",d.jsx("span",{className:"call-value string",title:e.browserName,children:e.browserName})]}),e.channel&&d.jsxs("div",{className:"call-line",children:["channel:",d.jsx("span",{className:"call-value string",title:e.channel,children:e.channel})]}),e.platform&&d.jsxs("div",{className:"call-line",children:["platform:",d.jsx("span",{className:"call-value string",title:e.platform,children:e.platform})]}),e.options.userAgent&&d.jsxs("div",{className:"call-line",children:["user agent:",d.jsx("span",{className:"call-value datetime",title:e.options.userAgent,children:e.options.userAgent})]}),e.options.baseURL&&d.jsxs(d.Fragment,{children:[d.jsx("div",{className:"call-section",style:{paddingTop:2},children:"Config"}),d.jsxs("div",{className:"call-line",children:["baseURL:",d.jsx("a",{className:"call-value string",href:e.options.baseURL,title:e.options.baseURL,target:"_blank",rel:"noopener noreferrer",children:e.options.baseURL})]})]}),d.jsx("div",{className:"call-section",children:"Viewport"}),e.options.viewport&&d.jsxs("div",{className:"call-line",children:["width:",d.jsx("span",{className:"call-value number",title:String(!!((t=e.options.viewport)!=null&&t.width)),children:e.options.viewport.width})]}),e.options.viewport&&d.jsxs("div",{className:"call-line",children:["height:",d.jsx("span",{className:"call-value number",title:String(!!((n=e.options.viewport)!=null&&n.height)),children:e.options.viewport.height})]}),d.jsxs("div",{className:"call-line",children:["is mobile:",d.jsx("span",{className:"call-value boolean",title:String(!!e.options.isMobile),children:String(!!e.options.isMobile)})]}),e.options.deviceScaleFactor&&d.jsxs("div",{className:"call-line",children:["device scale:",d.jsx("span",{className:"call-value number",title:String(e.options.deviceScaleFactor),children:String(e.options.deviceScaleFactor)})]}),d.jsx("div",{className:"call-section",children:"Counts"}),d.jsxs("div",{className:"call-line",children:["pages:",d.jsx("span",{className:"call-value number",children:e.pages.length})]}),d.jsxs("div",{className:"call-line",children:["actions:",d.jsx("span",{className:"call-value number",children:e.actions.length})]}),d.jsxs("div",{className:"call-line",children:["events:",d.jsx("span",{className:"call-value number",children:e.events.length})]})]}):d.jsx(d.Fragment,{})};async function qo(e){const t=new Image;return e&&(t.src=e,await new Promise((n,r)=>{t.onload=n,t.onerror=n})),t}const Kl={backgroundImage:`linear-gradient(45deg, #80808020 25%, transparent 25%),
                    linear-gradient(-45deg, #80808020 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #80808020 75%),
                    linear-gradient(-45deg, transparent 75%, #80808020 75%)`,backgroundSize:"20px 20px",backgroundPosition:"0 0, 0 10px, 10px -10px, -10px 0px",boxShadow:`rgb(0 0 0 / 10%) 0px 1.8px 1.9px,
              rgb(0 0 0 / 15%) 0px 6.1px 6.3px,
              rgb(0 0 0 / 10%) 0px -2px 4px,
              rgb(0 0 0 / 15%) 0px -6.1px 12px,
              rgb(0 0 0 / 25%) 0px 6px 12px`},x1=({diff:e,noTargetBlank:t})=>{const[n,r]=b.useState(e.diff?"diff":"actual"),[i,s]=b.useState(!1),[o,l]=b.useState(null),[a,u]=b.useState(null),[c,h]=b.useState(null),[f,g]=_n();b.useEffect(()=>{(async()=>{var E,N,j;l(await qo((E=e.expected)==null?void 0:E.attachment.path)),u(await qo((N=e.actual)==null?void 0:N.attachment.path)),h(await qo((j=e.diff)==null?void 0:j.attachment.path))})()},[e]);const w=o&&a&&c,y=w?Math.max(o.naturalWidth,a.naturalWidth,200):500,x=w?Math.max(o.naturalHeight,a.naturalHeight,200):500,p=Math.min(1,(f.width-30)/y),m=Math.min(1,(f.width-50)/y/2),v=y*p,S=x*p,C={flex:"none",margin:"0 10px",cursor:"pointer",userSelect:"none"};return d.jsx("div",{"data-testid":"test-result-image-mismatch",style:{display:"flex",flexDirection:"column",alignItems:"center",flex:"auto"},ref:g,children:w&&d.jsxs(d.Fragment,{children:[d.jsxs("div",{"data-testid":"test-result-image-mismatch-tabs",style:{display:"flex",margin:"10px 0 20px"},children:[e.diff&&d.jsx("div",{style:{...C,fontWeight:n==="diff"?600:"initial"},onClick:()=>r("diff"),children:"Diff"}),d.jsx("div",{style:{...C,fontWeight:n==="actual"?600:"initial"},onClick:()=>r("actual"),children:"Actual"}),d.jsx("div",{style:{...C,fontWeight:n==="expected"?600:"initial"},onClick:()=>r("expected"),children:"Expected"}),d.jsx("div",{style:{...C,fontWeight:n==="sxs"?600:"initial"},onClick:()=>r("sxs"),children:"Side by side"}),d.jsx("div",{style:{...C,fontWeight:n==="slider"?600:"initial"},onClick:()=>r("slider"),children:"Slider"})]}),d.jsxs("div",{style:{display:"flex",justifyContent:"center",flex:"auto",minHeight:S+60},children:[e.diff&&n==="diff"&&d.jsx(Et,{image:c,alt:"Diff",canvasWidth:v,canvasHeight:S,scale:p}),e.diff&&n==="actual"&&d.jsx(Et,{image:a,alt:"Actual",canvasWidth:v,canvasHeight:S,scale:p}),e.diff&&n==="expected"&&d.jsx(Et,{image:o,alt:"Expected",canvasWidth:v,canvasHeight:S,scale:p}),e.diff&&n==="slider"&&d.jsx(S1,{expectedImage:o,actualImage:a,canvasWidth:v,canvasHeight:S,scale:p}),e.diff&&n==="sxs"&&d.jsxs("div",{style:{display:"flex"},children:[d.jsx(Et,{image:o,title:"Expected",canvasWidth:m*y,canvasHeight:m*x,scale:m}),d.jsx(Et,{image:i?c:a,title:i?"Diff":"Actual",onClick:()=>s(!i),canvasWidth:m*y,canvasHeight:m*x,scale:m})]}),!e.diff&&n==="actual"&&d.jsx(Et,{image:a,title:"Actual",canvasWidth:v,canvasHeight:S,scale:p}),!e.diff&&n==="expected"&&d.jsx(Et,{image:o,title:"Expected",canvasWidth:v,canvasHeight:S,scale:p}),!e.diff&&n==="sxs"&&d.jsxs("div",{style:{display:"flex"},children:[d.jsx(Et,{image:o,title:"Expected",canvasWidth:m*y,canvasHeight:m*x,scale:m}),d.jsx(Et,{image:a,title:"Actual",canvasWidth:m*y,canvasHeight:m*x,scale:m})]})]}),d.jsxs("div",{style:{alignSelf:"start",lineHeight:"18px",marginLeft:"15px"},children:[d.jsx("div",{children:e.diff&&d.jsx("a",{target:"_blank",href:e.diff.attachment.path,children:e.diff.attachment.name})}),d.jsx("div",{children:d.jsx("a",{target:t?"":"_blank",href:e.actual.attachment.path,children:e.actual.attachment.name})}),d.jsx("div",{children:d.jsx("a",{target:t?"":"_blank",href:e.expected.attachment.path,children:e.expected.attachment.name})})]})]})})},S1=({expectedImage:e,actualImage:t,canvasWidth:n,canvasHeight:r,scale:i})=>{const s={position:"absolute",top:0,left:0},[o,l]=b.useState(n/2),a=e.naturalWidth===t.naturalWidth&&e.naturalHeight===t.naturalHeight;return d.jsxs("div",{style:{flex:"none",display:"flex",alignItems:"center",flexDirection:"column",userSelect:"none"},children:[d.jsxs("div",{style:{margin:5},children:[!a&&d.jsx("span",{style:{flex:"none",margin:"0 5px"},children:"Expected "}),d.jsx("span",{children:e.naturalWidth}),d.jsx("span",{style:{flex:"none",margin:"0 5px"},children:"x"}),d.jsx("span",{children:e.naturalHeight}),!a&&d.jsx("span",{style:{flex:"none",margin:"0 5px 0 15px"},children:"Actual "}),!a&&d.jsx("span",{children:t.naturalWidth}),!a&&d.jsx("span",{style:{flex:"none",margin:"0 5px"},children:"x"}),!a&&d.jsx("span",{children:t.naturalHeight})]}),d.jsxs("div",{style:{position:"relative",width:n,height:r,margin:15,...Kl},children:[d.jsx(Qh,{orientation:"horizontal",offsets:[o],setOffsets:u=>l(u[0]),resizerColor:"#57606a80",resizerWidth:6}),d.jsx("img",{alt:"Expected",style:{width:e.naturalWidth*i,height:e.naturalHeight*i},draggable:"false",src:e.src}),d.jsx("div",{style:{...s,bottom:0,overflow:"hidden",width:o,...Kl},children:d.jsx("img",{alt:"Actual",style:{width:t.naturalWidth*i,height:t.naturalHeight*i},draggable:"false",src:t.src})})]})]})},Et=({image:e,title:t,alt:n,canvasWidth:r,canvasHeight:i,scale:s,onClick:o})=>d.jsxs("div",{style:{flex:"none",display:"flex",alignItems:"center",flexDirection:"column"},children:[d.jsxs("div",{style:{margin:5},children:[t&&d.jsx("span",{style:{flex:"none",margin:"0 5px"},children:t}),d.jsx("span",{children:e.naturalWidth}),d.jsx("span",{style:{flex:"none",margin:"0 5px"},children:"x"}),d.jsx("span",{children:e.naturalHeight})]}),d.jsx("div",{style:{display:"flex",flex:"none",width:r,height:i,margin:15,...Kl},children:d.jsx("img",{width:e.naturalWidth*s,height:e.naturalHeight*s,alt:t||n,style:{cursor:o?"pointer":"initial"},draggable:"false",src:e.src,onClick:o})})]});function _1(e){return!!e.match(/^(text\/.*?|application\/(json|(x-)?javascript|xml.*?|ecmascript|graphql|x-www-form-urlencoded)|image\/svg(\+xml)?|application\/.*?(\+json|\+xml))(;\s*charset=.*)?$/)}const E1=({title:e,children:t,setExpanded:n,expanded:r,expandOnTitleClick:i})=>d.jsxs("div",{className:"expandable"+(r?" expanded":""),children:[d.jsxs("div",{className:"expandable-title",onClick:()=>i&&n(!r),children:[d.jsx("div",{className:"codicon codicon-"+(r?"chevron-down":"chevron-right"),style:{cursor:"pointer",color:"var(--vscode-foreground)",marginLeft:"5px"},onClick:()=>!i&&n(!r)}),e]}),r&&d.jsx("div",{style:{marginLeft:25},children:t})]});function Zp(e){const t=[];let n=0,r;for(;(r=hd.exec(e))!==null;){const s=e.substring(n,r.index);s&&t.push(s);const o=r[0];t.push(T1(o)),n=r.index+o.length}const i=e.substring(n);return i&&t.push(i),t}function T1(e){let t=e;return t.startsWith("www.")&&(t="https://"+t),d.jsx("a",{href:t,target:"_blank",rel:"noopener noreferrer",children:e})}const k1=({attachment:e})=>{const[t,n]=b.useState(!1),[r,i]=b.useState(null),[s,o]=b.useState(null),l=_1(e.contentType),a=!!e.sha1||!!e.path;b.useEffect(()=>{t&&r===null&&s===null&&(o("Loading ..."),fetch(nu(e)).then(c=>c.text()).then(c=>{i(c),o(null)}).catch(c=>{o("Failed to load: "+c.message)}))},[t,r,s,e]);const u=d.jsxs("span",{style:{marginLeft:5},children:[Zp(e.name)," ",a&&d.jsx("a",{style:{marginLeft:5},href:cs(e),children:"download"})]});return!l||!a?d.jsx("div",{style:{marginLeft:20},children:u}):d.jsxs(d.Fragment,{children:[d.jsx(E1,{title:u,expanded:t,setExpanded:n,expandOnTitleClick:!0,children:s&&d.jsx("i",{children:s})}),t&&r!==null&&d.jsx(yi,{text:r,readOnly:!0,mimeType:e.contentType,linkify:!0,lineNumbers:!0,wrapLines:!1})]})},N1=({model:e})=>{const{diffMap:t,screenshots:n,attachments:r}=b.useMemo(()=>{const i=new Set,s=new Set;for(const l of(e==null?void 0:e.actions)||[]){const a=l.context.traceUrl;for(const u of l.attachments||[])i.add({...u,traceUrl:a})}const o=new Map;for(const l of i){if(!l.path&&!l.sha1)continue;const a=l.name.match(/^(.*)-(expected|actual|diff)\.png$/);if(a){const u=a[1],c=a[2],h=o.get(u)||{expected:void 0,actual:void 0,diff:void 0};h[c]=l,o.set(u,h),i.delete(l)}else l.contentType.startsWith("image/")&&(s.add(l),i.delete(l))}return{diffMap:o,attachments:i,screenshots:s}},[e]);return!t.size&&!n.size&&!r.size?d.jsx(kn,{text:"No attachments"}):d.jsxs("div",{className:"attachments-tab",children:[[...t.values()].map(({expected:i,actual:s,diff:o})=>d.jsxs(d.Fragment,{children:[i&&s&&d.jsx("div",{className:"attachments-section",children:"Image diff"}),i&&s&&d.jsx(x1,{noTargetBlank:!0,diff:{name:"Image diff",expected:{attachment:{...i,path:cs(i)},title:"Expected"},actual:{attachment:{...s,path:cs(s)}},diff:o?{attachment:{...o,path:cs(o)}}:void 0}})]})),n.size?d.jsx("div",{className:"attachments-section",children:"Screenshots"}):void 0,[...n.values()].map((i,s)=>{const o=nu(i);return d.jsxs("div",{className:"attachment-item",children:[d.jsx("div",{children:d.jsx("img",{draggable:"false",src:o})}),d.jsx("div",{children:d.jsx("a",{target:"_blank",href:o,children:i.name})})]},`screenshot-${s}`)}),r.size?d.jsx("div",{className:"attachments-section",children:"Attachments"}):void 0,[...r.values()].map((i,s)=>d.jsx("div",{className:"attachment-item",children:d.jsx(k1,{attachment:i})},`attachment-${s}`))]})};function nu(e,t={}){const n=new URLSearchParams(t);return e.sha1?(n.set("trace",e.traceUrl),"sha1/"+e.sha1+"?"+n.toString()):(n.set("path",e.path),"file?"+n.toString())}function cs(e){const t={dn:e.name};return e.contentType&&(t.dct=e.contentType),nu(e,t)}const C1=({annotations:e})=>e.length?d.jsx("div",{className:"annotations-tab",children:e.map((t,n)=>d.jsxs("div",{className:"annotation-item",children:[d.jsx("span",{style:{fontWeight:"bold"},children:t.type}),t.description&&d.jsxs("span",{children:[": ",Zp(t.description)]})]},`annotation-${n}`))}):d.jsx(kn,{text:"No annotations"}),b1=({sdkLanguage:e,setIsInspecting:t,highlightedLocator:n,setHighlightedLocator:r})=>d.jsxs("div",{className:"vbox",style:{backgroundColor:"var(--vscode-sideBar-background)"},children:[d.jsx("div",{style:{margin:"10px 0px 10px 10px",color:"var(--vscode-editorCodeLens-foreground)",flex:"none"},children:"Locator"}),d.jsx("div",{style:{margin:"0 10px 10px",flex:"auto"},children:d.jsx(yi,{text:n,language:e,focusOnChange:!0,isFocused:!0,wrapLines:!0,onChange:i=>{r(i),t(!1)}})}),d.jsx("div",{style:{position:"absolute",right:5,top:5},children:d.jsx(en,{icon:"files",title:"Copy locator",onClick:()=>{$m(n)}})})]});function L1(e){return e==="scheduled"?"codicon-clock":e==="running"?"codicon-loading":e==="failed"?"codicon-error":e==="passed"?"codicon-check":e==="skipped"?"codicon-circle-slash":"codicon-circle-outline"}function A1(e){return e==="scheduled"?"Pending":e==="running"?"Running":e==="failed"?"Failed":e==="passed"?"Passed":e==="skipped"?"Skipped":"Did not run"}const j1=({settings:e})=>d.jsx("div",{className:"vbox settings-view",children:e.map(([t,n,r])=>d.jsx("div",{className:"setting",children:d.jsxs("label",{children:[d.jsx("input",{type:"checkbox",checked:t,onClick:()=>n(!t)}),r]})},r))}),H1=({showRouteActionsSetting:e,model:t,showSourcesFirst:n,rootDir:r,fallbackLocation:i,initialSelection:s,onSelectionChanged:o,isLive:l,status:a,annotations:u,inert:c,openPage:h,onOpenExternally:f,revealSource:g})=>{const[w,y]=b.useState(void 0),[x,p]=b.useState(void 0),[m,v]=b.useState(),[S,C]=b.useState(),[E,N]=b.useState(),[j,_]=b.useState("actions"),[T,L]=Rr("propertiesTab",n?"source":"call"),[k,M]=b.useState(!1),[$,Q]=b.useState(""),Oe=t?m||w:void 0,[me,I]=b.useState(),[R,D]=Rr("propertiesSidebarLocation","bottom"),[,,K]=Rr(e?void 0:"show-route-actions",!0,"Show route actions"),re=!e;e||(e=K);const sn=e[0],yt=b.useMemo(()=>((t==null?void 0:t.actions)||[]).filter(O=>sn||!e0(O)),[t,sn]),tt=b.useCallback(O=>{y(O),p(O==null?void 0:O.stack)},[y,p]),wt=b.useMemo(()=>(t==null?void 0:t.sources)||new Map,[t]);b.useEffect(()=>{I(void 0),p(void 0)},[t]),b.useEffect(()=>{if(w&&(t!=null&&t.actions.includes(w)))return;const O=t==null?void 0:t.failedAction();if(s&&(t!=null&&t.actions.includes(s)))tt(s);else if(O)tt(O);else if(t!=null&&t.actions.length){let au=t.actions.length-1;for(let pr=0;pr<t.actions.length;++pr)if(t.actions[pr].apiName==="After Hooks"&&pr){au=pr-1;break}tt(t.actions[au])}},[t,w,tt,s]);const on=b.useCallback(O=>{tt(O),o==null||o(O)},[tt,o]),xt=b.useCallback(O=>{L(O),O!=="inspector"&&M(!1)},[L]),ru=b.useCallback(O=>{!k&&O&&xt("inspector"),M(O)},[M,xt,k]),em=b.useCallback(O=>{Q(O),xt("inspector")},[xt]);b.useEffect(()=>{g&&xt("source")},[g,xt]);const ao=K0(t,me),iu=gy(t,me),su=q0(t),tm=b.useMemo(()=>(t==null?void 0:t.actions.map(O=>O.attachments||[]).flat())||[],[t]),Nn=(t==null?void 0:t.sdkLanguage)||"javascript",nm={id:"inspector",title:"Locator",render:()=>d.jsx(b1,{sdkLanguage:Nn,setIsInspecting:ru,highlightedLocator:$,setHighlightedLocator:Q})},rm={id:"call",title:"Call",render:()=>d.jsx(F0,{action:Oe,sdkLanguage:Nn})},im={id:"log",title:"Log",render:()=>d.jsx(U0,{action:Oe,isLive:l})},sm={id:"errors",title:"Errors",errorCount:su.errors.size,render:()=>d.jsx(X0,{errorsModel:su,sdkLanguage:Nn,revealInSource:O=>{O.action?tt(O.action):p(O.stack),xt("source")}})},uo={id:"source",title:"Source",render:()=>d.jsx(c1,{stack:x,sources:wt,rootDir:r,stackFrameLocation:R==="bottom"?"right":"bottom",fallbackLocation:i,onOpenExternally:f})},om={id:"console",title:"Console",count:ao.entries.length,render:()=>d.jsx(G0,{consoleModel:ao,boundaries:co,selectedTime:me,onAccepted:O=>I({minimum:O.timestamp,maximum:O.timestamp}),onEntryHovered:N})},lm={id:"network",title:"Network",count:iu.resources.length,render:()=>d.jsx(vy,{boundaries:co,networkModel:iu,onEntryHovered:C})},am={id:"attachments",title:"Attachments",count:tm.length,render:()=>d.jsx(N1,{model:t})},hr=[nm,rm,im,sm,om,lm,uo,am];if(u!==void 0){const O={id:"annotations",title:"Annotations",count:u.length,render:()=>d.jsx(C1,{annotations:u})};hr.push(O)}if(n){const O=hr.indexOf(uo);hr.splice(O,1),hr.splice(1,0,uo)}const{boundaries:co}=b.useMemo(()=>{const O={minimum:(t==null?void 0:t.startTime)||0,maximum:(t==null?void 0:t.endTime)||3e4};return O.minimum>O.maximum&&(O.minimum=0,O.maximum=3e4),O.maximum+=(O.maximum-O.minimum)/20,{boundaries:O}},[t]);let xi=0;!l&&t&&t.endTime>=0?xi=t.endTime-t.startTime:t&&t.wallTime&&(xi=Date.now()-t.wallTime);const ou={id:"actions",title:"Actions",component:d.jsxs("div",{className:"vbox",children:[a&&d.jsxs("div",{className:"workbench-run-status",children:[d.jsx("span",{className:`codicon ${L1(a)}`}),d.jsx("div",{children:A1(a)}),d.jsx("div",{className:"spacer"}),d.jsx("div",{className:"workbench-run-duration",children:xi?Ye(xi):""})]}),d.jsx(D0,{sdkLanguage:Nn,actions:yt,selectedAction:t?w:void 0,selectedTime:me,setSelectedTime:I,onSelected:on,onHighlighted:v,revealConsole:()=>xt("console"),isLive:l})]})},lu={id:"metadata",title:"Metadata",component:d.jsx(w1,{model:t})},um={id:"settings",title:"Settings",component:d.jsx(j1,{settings:[e]})};return d.jsxs("div",{className:"vbox workbench",...c?{inert:"true"}:{},children:[d.jsx(g1,{model:t,consoleEntries:ao.entries,boundaries:co,highlightedAction:m,highlightedEntry:S,highlightedConsoleEntry:E,onSelected:on,sdkLanguage:Nn,selectedTime:me,setSelectedTime:I}),d.jsxs($s,{sidebarSize:250,orientation:R==="bottom"?"vertical":"horizontal",settingName:"propertiesSidebar",children:[d.jsxs($s,{sidebarSize:250,orientation:"horizontal",sidebarIsFirst:!0,settingName:"actionListSidebar",children:[d.jsx(s1,{action:Oe,sdkLanguage:Nn,testIdAttributeName:(t==null?void 0:t.testIdAttributeName)||"data-testid",isInspecting:k,setIsInspecting:ru,highlightedLocator:$,setHighlightedLocator:em,openPage:h}),d.jsx(Ul,{tabs:re?[ou,lu,um]:[ou,lu],selectedTab:j,setSelectedTab:_})]}),d.jsx(Ul,{tabs:hr,selectedTab:T,setSelectedTab:xt,rightToolbar:[R==="bottom"?d.jsx(en,{title:"Dock to right",icon:"layout-sidebar-right-off",onClick:()=>{D("right")}}):d.jsx(en,{title:"Dock to bottom",icon:"layout-panel-off",onClick:()=>{D("bottom")}})],mode:R==="bottom"?"default":"select"})]})]})};export{E1 as E,z1 as M,mt as R,$s as S,en as T,H1 as W,ny as _,P1 as a,D1 as b,hu as c,R1 as d,$1 as e,Xa as f,L1 as g,R0 as h,F1 as i,d as j,Rr as k,O1 as l,Ye as m,j1 as n,I1 as o,fm as p,b as r,Wr as s,Dm as t,_n as u};
