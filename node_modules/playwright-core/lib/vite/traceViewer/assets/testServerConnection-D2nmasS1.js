var d;(l=>{function e(s){for(const t of s.splice(0))t.dispose()}l.disposeAll=e})(d||(d={}));class a{constructor(){this._listeners=new Set,this.event=(e,s)=>{this._listeners.add(e);let t=!1;const i=this,n={dispose(){t||(t=!0,i._listeners.delete(e))}};return s&&s.push(n),n}}fire(e){const s=!this._deliveryQueue;this._deliveryQueue||(this._deliveryQueue=[]);for(const t of this._listeners)this._deliveryQueue.push({listener:t,event:e});if(s){for(let t=0;t<this._deliveryQueue.length;t++){const{listener:i,event:n}=this._deliveryQueue[t];i.call(null,n)}this._deliveryQueue=void 0}}dispose(){this._listeners.clear(),this._deliveryQueue&&(this._deliveryQueue=[])}}class u{constructor(e){this._onCloseEmitter=new a,this._onReportEmitter=new a,this._onStdioEmitter=new a,this._onListChangedEmitter=new a,this._onTestFilesChangedEmitter=new a,this._onLoadTraceRequestedEmitter=new a,this._lastId=0,this._callbacks=new Map,this._isClosed=!1,this.onClose=this._onCloseEmitter.event,this.onReport=this._onReportEmitter.event,this.onStdio=this._onStdioEmitter.event,this.onListChanged=this._onListChangedEmitter.event,this.onTestFilesChanged=this._onTestFilesChangedEmitter.event,this.onLoadTraceRequested=this._onLoadTraceRequestedEmitter.event,this._ws=new WebSocket(e),this._ws.addEventListener("message",t=>{const i=JSON.parse(String(t.data)),{id:n,result:o,error:r,method:c,params:_}=i;if(n){const h=this._callbacks.get(n);if(!h)return;this._callbacks.delete(n),r?h.reject(new Error(r)):h.resolve(o)}else this._dispatchEvent(c,_)});const s=setInterval(()=>this._sendMessage("ping").catch(()=>{}),3e4);this._connectedPromise=new Promise((t,i)=>{this._ws.addEventListener("open",()=>t()),this._ws.addEventListener("error",i)}),this._ws.addEventListener("close",()=>{this._isClosed=!0,this._onCloseEmitter.fire(),clearInterval(s)})}isClosed(){return this._isClosed}async _sendMessage(e,s){const t=globalThis.__logForTest;t==null||t({method:e,params:s}),await this._connectedPromise;const i=++this._lastId,n={id:i,method:e,params:s};return this._ws.send(JSON.stringify(n)),new Promise((o,r)=>{this._callbacks.set(i,{resolve:o,reject:r})})}_sendMessageNoReply(e,s){this._sendMessage(e,s).catch(()=>{})}_dispatchEvent(e,s){e==="report"?this._onReportEmitter.fire(s):e==="stdio"?this._onStdioEmitter.fire(s):e==="listChanged"?this._onListChangedEmitter.fire(s):e==="testFilesChanged"?this._onTestFilesChangedEmitter.fire(s):e==="loadTraceRequested"&&this._onLoadTraceRequestedEmitter.fire(s)}async initialize(e){await this._sendMessage("initialize",e)}async ping(e){await this._sendMessage("ping",e)}async pingNoReply(e){this._sendMessageNoReply("ping",e)}async watch(e){await this._sendMessage("watch",e)}watchNoReply(e){this._sendMessageNoReply("watch",e)}async open(e){await this._sendMessage("open",e)}openNoReply(e){this._sendMessageNoReply("open",e)}async resizeTerminal(e){await this._sendMessage("resizeTerminal",e)}resizeTerminalNoReply(e){this._sendMessageNoReply("resizeTerminal",e)}async checkBrowsers(e){return await this._sendMessage("checkBrowsers",e)}async installBrowsers(e){await this._sendMessage("installBrowsers",e)}async runGlobalSetup(e){return await this._sendMessage("runGlobalSetup",e)}async runGlobalTeardown(e){return await this._sendMessage("runGlobalTeardown",e)}async startDevServer(e){return await this._sendMessage("startDevServer",e)}async stopDevServer(e){return await this._sendMessage("stopDevServer",e)}async clearCache(e){return await this._sendMessage("clearCache",e)}async listFiles(e){return await this._sendMessage("listFiles",e)}async listTests(e){return await this._sendMessage("listTests",e)}async runTests(e){return await this._sendMessage("runTests",e)}async findRelatedTestFiles(e){return await this._sendMessage("findRelatedTestFiles",e)}async stopTests(e){await this._sendMessage("stopTests",e)}stopTestsNoReply(e){this._sendMessageNoReply("stopTests",e)}async closeGracefully(e){await this._sendMessage("closeGracefully",e)}close(){try{this._ws.close()}catch{}}}export{u as T};
