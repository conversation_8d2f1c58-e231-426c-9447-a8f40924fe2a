import{M as v,r,c as u,t as E,j as o,W as k,a as x,b as M}from"./assets/workbench-NhP651gz.js";function j(t,e){t&&window.parent.postMessage({command:"openExternal",params:{url:t,target:e}},"*")}const T=()=>{const[t,e]=r.useState([]),[i,w]=r.useState(h),[c,d]=r.useState({done:0,total:0}),[l,g]=r.useState(null);return r.useEffect(()=>{window.addEventListener("message",async({data:{method:s,params:a}})=>{s==="loadTraceRequested"?(e(a.traceUrl?[a.traceUrl]:[]),g(null)):s==="applyTheme"&&u()!==a.theme&&E()}),window.parent.postMessage({type:"loaded"},"*")},[]),r.useEffect(()=>{(async()=>{if(t.length){const s=n=>{n.data.method==="progress"&&d(n.data.params)};navigator.serviceWorker.addEventListener("message",s),d({done:0,total:1});const a=[];for(let n=0;n<t.length;n++){const y=t[n],m=new URLSearchParams;m.set("trace",y);const p=await fetch(`contexts?${m.toString()}`);if(!p.ok){g((await p.json()).error);return}a.push(...await p.json())}navigator.serviceWorker.removeEventListener("message",s);const f=new v(a);d({done:0,total:0}),w(f)}else w(h)})()},[t]),r.useEffect(()=>{var s;l&&((s=window.parent)==null||s.postMessage({method:"showErrorMessage",params:{message:l}},"*"))},[l]),o.jsxs("div",{className:"vbox workbench-loader",children:[o.jsx("div",{className:"progress",children:o.jsx("div",{className:"inner-progress",style:{width:c.total?100*c.done/c.total+"%":0}})}),o.jsx(k,{model:i,openPage:j}),!t.length&&o.jsx("div",{className:"empty-state",children:o.jsx("div",{className:"title",children:"Select test to see the trace"})})]})},h=new v([]);(async()=>{x();const t=e=>{var i;e.isTrusted&&((i=window.parent)==null||i.postMessage({type:e.type,key:e.key,keyCode:e.keyCode,code:e.code,shiftKey:e.shiftKey,altKey:e.altKey,ctrlKey:e.ctrlKey,metaKey:e.metaKey,repeat:e.repeat},"*"))};if(window.addEventListener("keydown",t),window.addEventListener("keyup",t),window.location.protocol!=="file:"){if(!navigator.serviceWorker)throw new Error(`Service workers are not supported.
Make sure to serve the Trace Viewer (${window.location}) via HTTPS or localhost.`);navigator.serviceWorker.register("sw.bundle.js"),navigator.serviceWorker.controller||await new Promise(e=>{navigator.serviceWorker.oncontrollerchange=()=>e()}),setInterval(function(){fetch("ping")},1e4)}M.render(o.jsx(T,{}),document.querySelector("#root"))})();
