function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["./assets/xtermModule-BeNbaIVa.js","./xtermModule.DSXBckUd.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
var Mt=Object.defineProperty;var Ft=(r,t,e)=>t in r?Mt(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var A=(r,t,e)=>(Ft(r,typeof t!="symbol"?t+"":t,e),e);import{u as Ot,r as $,c as At,_ as Wt,d as Ut,e as zt,j as n,R as c,E as Vt,s as xt,m as Kt,f as q,T as P,g as Ht,h as $t,i as gt,W as qt,M as Yt,k as mt,l as Qt,S as Xt,n as Gt,a as Jt,b as Zt}from"./assets/workbench-NhP651gz.js";import{T as te}from"./assets/testServerConnection-D2nmasS1.js";var ee={};class ot{constructor(t,e={}){this.isListing=!1,this._tests=new Map,this._rootSuite=new Y("","root"),this._options=e,this._reporter=t}reset(){this._rootSuite._entries=[],this._tests.clear()}dispatch(t){const{method:e,params:s}=t;if(e==="onConfigure"){this._onConfigure(s.config);return}if(e==="onProject"){this._onProject(s.project);return}if(e==="onBegin"){this._onBegin();return}if(e==="onTestBegin"){this._onTestBegin(s.testId,s.result);return}if(e==="onTestEnd"){this._onTestEnd(s.test,s.result);return}if(e==="onStepBegin"){this._onStepBegin(s.testId,s.resultId,s.step);return}if(e==="onStepEnd"){this._onStepEnd(s.testId,s.resultId,s.step);return}if(e==="onError"){this._onError(s.error);return}if(e==="onStdIO"){this._onStdIO(s.type,s.testId,s.resultId,s.data,s.isBase64);return}if(e==="onEnd")return this._onEnd(s.result);if(e==="onExit")return this._onExit()}_onConfigure(t){var e,s;this._rootDir=t.rootDir,this._config=this._parseConfig(t),(s=(e=this._reporter).onConfigure)==null||s.call(e,this._config)}_onProject(t){let e=this._options.mergeProjects?this._rootSuite.suites.find(s=>s.project().name===t.name):void 0;e||(e=new Y(t.name,"project"),this._rootSuite._addSuite(e)),e._project=this._parseProject(t);for(const s of t.suites)this._mergeSuiteInto(s,e)}_onBegin(){var t,e;(e=(t=this._reporter).onBegin)==null||e.call(t,this._rootSuite)}_onTestBegin(t,e){var l,a;const s=this._tests.get(t);this._options.clearPreviousResultsWhenTestBegins&&(s.results=[]);const i=s._createTestResult(e.id);i.retry=e.retry,i.workerIndex=e.workerIndex,i.parallelIndex=e.parallelIndex,i.setStartTimeNumber(e.startTime),(a=(l=this._reporter).onTestBegin)==null||a.call(l,s,i)}_onTestEnd(t,e){var l,a,_;const s=this._tests.get(t.testId);s.timeout=t.timeout,s.expectedStatus=t.expectedStatus,s.annotations=t.annotations;const i=s.results.find(o=>o._id===e.id);i.duration=e.duration,i.status=e.status,i.errors=e.errors,i.error=(l=i.errors)==null?void 0:l[0],i.attachments=this._parseAttachments(e.attachments),(_=(a=this._reporter).onTestEnd)==null||_.call(a,s,i),i._stepMap=new Map}_onStepBegin(t,e,s){var f,d;const i=this._tests.get(t),l=i.results.find(g=>g._id===e),a=s.parentStepId?l._stepMap.get(s.parentStepId):void 0,_=this._absoluteLocation(s.location),o=new ie(s,a,_);a?a.steps.push(o):l.steps.push(o),l._stepMap.set(s.id,o),(d=(f=this._reporter).onStepBegin)==null||d.call(f,i,l,o)}_onStepEnd(t,e,s){var _,o;const i=this._tests.get(t),l=i.results.find(f=>f._id===e),a=l._stepMap.get(s.id);a.duration=s.duration,a.error=s.error,(o=(_=this._reporter).onStepEnd)==null||o.call(_,i,l,a)}_onError(t){var e,s;(s=(e=this._reporter).onError)==null||s.call(e,t)}_onStdIO(t,e,s,i,l){var f,d,g,T;const a=l?globalThis.Buffer?Buffer.from(i,"base64"):atob(i):i,_=e?this._tests.get(e):void 0,o=_&&s?_.results.find(h=>h._id===s):void 0;t==="stdout"?(o==null||o.stdout.push(a),(d=(f=this._reporter).onStdOut)==null||d.call(f,a,_,o)):(o==null||o.stderr.push(a),(T=(g=this._reporter).onStdErr)==null||T.call(g,a,_,o))}async _onEnd(t){var e,s;await((s=(e=this._reporter).onEnd)==null?void 0:s.call(e,{status:t.status,startTime:new Date(t.startTime),duration:t.duration}))}_onExit(){var t,e;return(e=(t=this._reporter).onExit)==null?void 0:e.call(t)}_parseConfig(t){const e={...oe,...t};return this._options.configOverrides&&(e.configFile=this._options.configOverrides.configFile,e.reportSlowTests=this._options.configOverrides.reportSlowTests,e.quiet=this._options.configOverrides.quiet,e.reporter=[...this._options.configOverrides.reporter]),e}_parseProject(t){return{metadata:t.metadata,name:t.name,outputDir:this._absolutePath(t.outputDir),repeatEach:t.repeatEach,retries:t.retries,testDir:this._absolutePath(t.testDir),testIgnore:et(t.testIgnore),testMatch:et(t.testMatch),timeout:t.timeout,grep:et(t.grep),grepInvert:et(t.grepInvert),dependencies:t.dependencies,teardown:t.teardown,snapshotDir:this._absolutePath(t.snapshotDir),use:{}}}_parseAttachments(t){return t.map(e=>({...e,body:e.base64&&globalThis.Buffer?Buffer.from(e.base64,"base64"):void 0}))}_mergeSuiteInto(t,e){let s=e.suites.find(i=>i.title===t.title);s||(s=new Y(t.title,e.type==="project"?"file":"describe"),e._addSuite(s)),s.location=this._absoluteLocation(t.location),t.entries.forEach(i=>{"testId"in i?this._mergeTestInto(i,s):this._mergeSuiteInto(i,s)})}_mergeTestInto(t,e){let s=this._options.mergeTestCases?e.tests.find(i=>i.title===t.title&&i.repeatEachIndex===t.repeatEachIndex):void 0;s||(s=new se(t.testId,t.title,this._absoluteLocation(t.location),t.repeatEachIndex),e._addTest(s),this._tests.set(s.id,s)),this._updateTest(t,s)}_updateTest(t,e){return e.id=t.testId,e.location=this._absoluteLocation(t.location),e.retries=t.retries,e.tags=t.tags??[],e.annotations=t.annotations??[],e}_absoluteLocation(t){return t&&{...t,file:this._absolutePath(t.file)}}_absolutePath(t){if(t!==void 0)return this._options.resolvePath?this._options.resolvePath(this._rootDir,t):this._rootDir+"/"+t}}class Y{constructor(t,e){this._entries=[],this._requireFile="",this._parallelMode="none",this.title=t,this._type=e}get type(){return this._type}get suites(){return this._entries.filter(t=>t.type!=="test")}get tests(){return this._entries.filter(t=>t.type==="test")}entries(){return this._entries}allTests(){const t=[],e=s=>{for(const i of s.entries())i.type==="test"?t.push(i):e(i)};return e(this),t}titlePath(){const t=this.parent?this.parent.titlePath():[];return(this.title||this._type!=="describe")&&t.push(this.title),t}project(){var t;return this._project??((t=this.parent)==null?void 0:t.project())}_addTest(t){t.parent=this,this._entries.push(t)}_addSuite(t){t.parent=this,this._entries.push(t)}}class se{constructor(t,e,s,i){this.fn=()=>{},this.results=[],this.type="test",this.expectedStatus="passed",this.timeout=0,this.annotations=[],this.retries=0,this.tags=[],this.repeatEachIndex=0,this.id=t,this.title=e,this.location=s,this.repeatEachIndex=i}titlePath(){const t=this.parent?this.parent.titlePath():[];return t.push(this.title),t}outcome(){return ne(this)}ok(){const t=this.outcome();return t==="expected"||t==="flaky"||t==="skipped"}_createTestResult(t){const e=new re(this.results.length,t);return this.results.push(e),e}}class ie{constructor(t,e,s){this.duration=-1,this.steps=[],this._startTime=0,this.title=t.title,this.category=t.category,this.location=s,this.parent=e,this._startTime=t.startTime}titlePath(){var e;return[...((e=this.parent)==null?void 0:e.titlePath())||[],this.title]}get startTime(){return new Date(this._startTime)}set startTime(t){this._startTime=+t}}class re{constructor(t,e){this.parallelIndex=-1,this.workerIndex=-1,this.duration=-1,this.stdout=[],this.stderr=[],this.attachments=[],this.status="skipped",this.steps=[],this.errors=[],this._stepMap=new Map,this._startTime=0,this.retry=t,this._id=e}setStartTimeNumber(t){this._startTime=t}get startTime(){return new Date(this._startTime)}set startTime(t){this._startTime=+t}}const oe={forbidOnly:!1,fullyParallel:!1,globalSetup:null,globalTeardown:null,globalTimeout:0,grep:/.*/,grepInvert:null,maxFailures:0,metadata:{},preserveOutput:"always",projects:[],reporter:[[ee.CI?"dot":"list"]],reportSlowTests:{max:5,threshold:15e3},configFile:"",rootDir:"",quiet:!1,shard:null,updateSnapshots:"missing",version:"",workers:0,webServer:null};function et(r){return r.map(t=>t.s!==void 0?t.s:new RegExp(t.r.source,t.r.flags))}function ne(r){let t=0,e=0,s=0;for(const i of r.results)i.status==="interrupted"||(i.status==="skipped"&&r.expectedStatus==="skipped"?++t:i.status==="skipped"||(i.status===r.expectedStatus?++e:++s));return e===0&&s===0?"skipped":s===0?"expected":e===0&&t===0?"unexpected":"flaky"}class _t{constructor(t,e,s,i,l){this._treeItemById=new Map,this._treeItemByTestId=new Map;const a=i&&[...i.values()].some(Boolean);this.pathSeparator=l,this.rootItem={kind:"group",subKind:"folder",id:t,title:"",location:{file:"",line:0,column:0},duration:0,parent:void 0,children:[],status:"none",hasLoadErrors:!1},this._treeItemById.set(t,this.rootItem);const _=(o,f,d)=>{for(const g of f.suites){const T=g.title||"<anonymous>";let h=d.children.find(m=>m.kind==="group"&&m.title===T);h||(h={kind:"group",subKind:"describe",id:"suite:"+f.titlePath().join("")+""+T,title:T,location:g.location,duration:0,parent:d,children:[],status:"none",hasLoadErrors:!1},this._addChild(d,h)),_(o,g,h)}for(const g of f.tests){const T=g.title;let h=d.children.find(x=>x.kind!=="group"&&x.title===T);h||(h={kind:"case",id:"test:"+g.titlePath().join(""),title:T,parent:d,children:[],tests:[],location:g.location,duration:0,status:"none",project:void 0,test:void 0,tags:g.tags},this._addChild(d,h));const m=g.results[0];let S="none";(m==null?void 0:m[Q])==="scheduled"?S="scheduled":(m==null?void 0:m[Q])==="running"?S="running":(m==null?void 0:m.status)==="skipped"?S="skipped":(m==null?void 0:m.status)==="interrupted"?S="none":m&&g.outcome()!=="expected"?S="failed":m&&g.outcome()==="expected"&&(S="passed"),h.tests.push(g);const C={kind:"test",id:g.id,title:o.name,location:g.location,test:g,parent:h,children:[],status:S,duration:g.results.length?Math.max(0,g.results[0].duration):0,project:o};this._addChild(h,C),this._treeItemByTestId.set(g.id,C),h.duration=h.children.reduce((x,j)=>x+j.duration,0)}};for(const o of(e==null?void 0:e.suites)||[])if(!(a&&!i.get(o.title)))for(const f of o.suites){const d=this._fileItem(f.location.file.split(l),!0);_(o.project(),f,d)}for(const o of s){if(!o.location)continue;const f=this._fileItem(o.location.file.split(l),!0);f.hasLoadErrors=!0}}_addChild(t,e){t.children.push(e),e.parent=t,this._treeItemById.set(e.id,e)}filterTree(t,e,s){const i=t.trim().toLowerCase().split(" "),l=[...e.values()].some(Boolean),a=o=>{const f=[...o.tests[0].titlePath(),...o.tests[0].tags].join(" ").toLowerCase();return!i.every(d=>f.includes(d))&&!o.tests.some(d=>s==null?void 0:s.has(d.id))?!1:(o.children=o.children.filter(d=>!l||(s==null?void 0:s.has(d.test.id))||e.get(d.status)),o.tests=o.children.map(d=>d.test),!!o.children.length)},_=o=>{const f=[];for(const d of o.children)d.kind==="case"?a(d)&&f.push(d):(_(d),(d.children.length||d.hasLoadErrors)&&f.push(d));o.children=f};_(this.rootItem)}_fileItem(t,e){if(t.length===0)return this.rootItem;const s=t.join(this.pathSeparator),i=this._treeItemById.get(s);if(i)return i;const l=this._fileItem(t.slice(0,t.length-1),!1),a={kind:"group",subKind:e?"file":"folder",id:s,title:t[t.length-1],location:{file:s,line:0,column:0},duration:0,parent:l,children:[],status:"none",hasLoadErrors:!1};return this._addChild(l,a),a}sortAndPropagateStatus(){bt(this.rootItem)}flattenForSingleProject(){const t=e=>{e.kind==="case"&&e.children.length===1?(e.project=e.children[0].project,e.test=e.children[0].test,e.children=[],this._treeItemByTestId.set(e.test.id,e)):e.children.forEach(t)};t(this.rootItem)}shortenRoot(){let t=this.rootItem;for(;t.children.length===1&&t.children[0].kind==="group"&&t.children[0].subKind==="folder";)t=t.children[0];t.location=this.rootItem.location,this.rootItem=t}testIds(){const t=new Set,e=s=>{s.kind==="case"&&s.tests.forEach(i=>t.add(i.id)),s.children.forEach(e)};return e(this.rootItem),t}fileNames(){const t=new Set,e=s=>{s.kind==="group"&&s.subKind==="file"?t.add(s.id):s.children.forEach(e)};return e(this.rootItem),[...t]}flatTreeItems(){const t=[],e=s=>{t.push(s),s.children.forEach(e)};return e(this.rootItem),t}treeItemById(t){return this._treeItemById.get(t)}collectTestIds(t){return t?ae(t):new Set}}function bt(r){for(const a of r.children)bt(a);r.kind==="group"&&r.children.sort((a,_)=>a.location.file.localeCompare(_.location.file)||a.location.line-_.location.line);let t=r.children.length>0,e=r.children.length>0,s=!1,i=!1,l=!1;for(const a of r.children)e=e&&a.status==="skipped",t=t&&(a.status==="passed"||a.status==="skipped"),s=s||a.status==="failed",i=i||a.status==="running",l=l||a.status==="scheduled";i?r.status="running":l?r.status="scheduled":s?r.status="failed":e?r.status="skipped":t&&(r.status="passed")}function ae(r){const t=new Set,e=s=>{var i;s.kind==="case"?s.tests.map(l=>l.id).forEach(l=>t.add(l)):s.kind==="test"?t.add(s.id):(i=s.children)==null||i.forEach(e)};return e(r),t}const Q=Symbol("statusEx");class le{constructor(t){A(this,"rootSuite");A(this,"config");A(this,"loadErrors",[]);A(this,"progress",{total:0,passed:0,failed:0,skipped:0});A(this,"_receiver");A(this,"_lastRunReceiver");A(this,"_lastRunTestCount",0);A(this,"_options");A(this,"_testResultsSnapshot");this._receiver=new ot(this._createReporter(),{mergeProjects:!0,mergeTestCases:!0,resolvePath:(e,s)=>e+t.pathSeparator+s,clearPreviousResultsWhenTestBegins:!0}),this._options=t}_createReporter(){return{version:()=>"v2",onConfigure:t=>{this.config=t,this._lastRunReceiver=new ot({onBegin:e=>{this._lastRunTestCount=e.allTests().length,this._lastRunReceiver=void 0}},{mergeProjects:!0,mergeTestCases:!1,resolvePath:(e,s)=>e+this._options.pathSeparator+s})},onBegin:t=>{var e;if(this.rootSuite||(this.rootSuite=t),this._testResultsSnapshot){for(const s of this.rootSuite.allTests())s.results=((e=this._testResultsSnapshot)==null?void 0:e.get(s.id))||s.results;this._testResultsSnapshot=void 0}this.progress.total=this._lastRunTestCount,this.progress.passed=0,this.progress.failed=0,this.progress.skipped=0,this._options.onUpdate(!0)},onEnd:()=>{this._options.onUpdate(!0)},onTestBegin:(t,e)=>{e[Q]="running",this._options.onUpdate()},onTestEnd:(t,e)=>{t.outcome()==="skipped"?++this.progress.skipped:t.outcome()==="unexpected"?++this.progress.failed:++this.progress.passed,e[Q]=e.status,this._options.onUpdate()},onError:t=>this._handleOnError(t),printsToStdio:()=>!1,onStdOut:()=>{},onStdErr:()=>{},onExit:()=>{},onStepBegin:()=>{},onStepEnd:()=>{}}}processGlobalReport(t){const e=new ot({onConfigure:s=>{this.config=s},onError:s=>this._handleOnError(s)});for(const s of t)e.dispatch(s)}processListReport(t){var s;const e=((s=this.rootSuite)==null?void 0:s.allTests())||[];this._testResultsSnapshot=new Map(e.map(i=>[i.id,i.results])),this._receiver.reset();for(const i of t)this._receiver.dispatch(i)}processTestReportEvent(t){var e,s,i;(s=(e=this._lastRunReceiver)==null?void 0:e.dispatch(t))==null||s.catch(()=>{}),(i=this._receiver.dispatch(t))==null||i.catch(()=>{})}_handleOnError(t){var e,s;this.loadErrors.push(t),(s=(e=this._options).onError)==null||s.call(e,t),this._options.onUpdate()}asModel(){return{rootSuite:this.rootSuite||new Y("","root"),config:this.config,loadErrors:this.loadErrors,progress:this.progress}}}const ce=({source:r})=>{const[t,e]=Ot(),[s,i]=$.useState(At()),[l]=$.useState(Wt(()=>import("./assets/xtermModule-BeNbaIVa.js"),__vite__mapDeps([0,1]),import.meta.url).then(_=>_.default)),a=$.useRef(null);return $.useEffect(()=>(Ut(i),()=>zt(i)),[]),$.useEffect(()=>{const _=r.write,o=r.clear;return(async()=>{const{Terminal:f,FitAddon:d}=await l,g=e.current;if(!g)return;const T=s==="dark-mode"?de:ue;if(a.current&&a.current.terminal.options.theme===T)return;a.current&&(g.textContent="");const h=new f({convertEol:!0,fontSize:13,scrollback:1e4,fontFamily:"var(--vscode-editor-font-family)",theme:T}),m=new d;h.loadAddon(m);for(const S of r.pending)h.write(S);r.write=S=>{r.pending.push(S),h.write(S)},r.clear=()=>{r.pending=[],h.clear()},h.open(g),m.fit(),a.current={terminal:h,fitAddon:m}})(),()=>{r.clear=o,r.write=_}},[l,a,e,r,s]),$.useEffect(()=>{setTimeout(()=>{a.current&&(a.current.fitAddon.fit(),r.resize(a.current.terminal.cols,a.current.terminal.rows))},250)},[t,r]),n.jsx("div",{"data-testid":"output",className:"xterm-wrapper",style:{flex:"auto"},ref:e})},ue={foreground:"#383a42",background:"#fafafa",cursor:"#383a42",black:"#000000",red:"#e45649",green:"#50a14f",yellow:"#c18401",blue:"#4078f2",magenta:"#a626a4",cyan:"#0184bc",white:"#a0a0a0",brightBlack:"#000000",brightRed:"#e06c75",brightGreen:"#98c379",brightYellow:"#d19a66",brightBlue:"#4078f2",brightMagenta:"#a626a4",brightCyan:"#0184bc",brightWhite:"#383a42",selectionBackground:"#d7d7d7",selectionForeground:"#383a42"},de={foreground:"#f8f8f2",background:"#1e1e1e",cursor:"#f8f8f0",black:"#000000",red:"#ff5555",green:"#50fa7b",yellow:"#f1fa8c",blue:"#bd93f9",magenta:"#ff79c6",cyan:"#8be9fd",white:"#bfbfbf",brightBlack:"#4d4d4d",brightRed:"#ff6e6e",brightGreen:"#69ff94",brightYellow:"#ffffa5",brightBlue:"#d6acff",brightMagenta:"#ff92df",brightCyan:"#a4ffff",brightWhite:"#e6e6e6",selectionBackground:"#44475a",selectionForeground:"#f8f8f2"},nt=navigator.userAgent.toLowerCase().includes("windows")?"\\":"/",he=({filterText:r,setFilterText:t,statusFilters:e,setStatusFilters:s,projectFilters:i,setProjectFilters:l,testModel:a,runTests:_})=>{const[o,f]=c.useState(!1),d=c.useRef(null);c.useEffect(()=>{var h;(h=d.current)==null||h.focus()},[]);const g=[...e.entries()].filter(([h,m])=>m).map(([h])=>h).join(" ")||"all",T=[...i.entries()].filter(([h,m])=>m).map(([h])=>h).join(" ")||"all";return n.jsxs("div",{className:"filters",children:[n.jsx(Vt,{expanded:o,setExpanded:f,title:n.jsx("input",{ref:d,type:"search",placeholder:"Filter (e.g. text, @tag)",spellCheck:!1,value:r,onChange:h=>{t(h.target.value)},onKeyDown:h=>{h.key==="Enter"&&_()}})}),n.jsxs("div",{className:"filter-summary",title:"Status: "+g+`
Projects: `+T,onClick:()=>f(!o),children:[n.jsx("span",{className:"filter-label",children:"Status:"})," ",g,n.jsx("span",{className:"filter-label",children:"Projects:"})," ",T]}),o&&n.jsxs("div",{className:"hbox",style:{marginLeft:14,maxHeight:200,overflowY:"auto"},children:[n.jsx("div",{className:"filter-list",children:[...e.entries()].map(([h,m])=>n.jsx("div",{className:"filter-entry",children:n.jsxs("label",{children:[n.jsx("input",{type:"checkbox",checked:m,onClick:()=>{const S=new Map(e);S.set(h,!S.get(h)),s(S)}}),n.jsx("div",{children:h})]})}))}),n.jsx("div",{className:"filter-list",children:[...i.entries()].map(([h,m])=>n.jsx("div",{className:"filter-entry",children:n.jsxs("label",{children:[n.jsx("input",{type:"checkbox",checked:m,onClick:()=>{var x;const S=new Map(i);S.set(h,!S.get(h)),l(S);const C=(x=a==null?void 0:a.config)==null?void 0:x.configFile;C&&xt.setObject(C+":projects",[...S.entries()].filter(([j,W])=>W).map(([j])=>j))}}),n.jsx("div",{children:h||"untitled"})]})}))})]})]})},fe=({tag:r,style:t,onClick:e})=>n.jsx("span",{className:`tag tag-color-${pe(r)}`,onClick:e,style:{margin:"6px 0 0 6px",...t},title:`Click to filter by tag: ${r}`,children:r});function pe(r){let t=0;for(let e=0;e<r.length;e++)t=r.charCodeAt(e)+((t<<8)-t);return Math.abs(t%6)}const ge=$t,me=({filterText:r,testModel:t,testServerConnection:e,testTree:s,runTests:i,runningState:l,watchAll:a,watchedTreeIds:_,setWatchedTreeIds:o,isLoading:f,onItemSelected:d,requestedCollapseAllCount:g,setFilterText:T,onRevealSource:h})=>{const[m,S]=c.useState({expandedItems:new Map}),[C,x]=c.useState(),[j,W]=c.useState(g);c.useEffect(()=>{if(j!==g){m.expandedItems.clear();for(const b of s.flatTreeItems())m.expandedItems.set(b.id,!1);W(g),x(void 0),S({...m});return}if(!l||l.itemSelectedByUser)return;let u;const y=b=>{var F;b.children.forEach(y),!u&&b.status==="failed"&&(b.kind==="test"&&l.testIds.has(b.test.id)||b.kind==="case"&&l.testIds.has((F=b.tests[0])==null?void 0:F.id))&&(u=b)};y(s.rootItem),u&&x(u.id)},[l,x,s,j,W,g,m,S]);const{selectedTreeItem:M}=c.useMemo(()=>{if(!t)return{selectedTreeItem:void 0};const u=C?s.treeItemById(C):void 0,y=_e(u,t);let b;return(u==null?void 0:u.kind)==="test"?b=u.test:(u==null?void 0:u.kind)==="case"&&u.tests.length===1&&(b=u.tests[0]),d({treeItem:u,testCase:b,testFile:y}),{selectedTreeItem:u}},[d,C,t,s]);c.useEffect(()=>{if(!f)if(a)e==null||e.watchNoReply({fileNames:s.fileNames()});else{const u=new Set;for(const y of _.value){const b=s.treeItemById(y),F=b==null?void 0:b.location.file;F&&u.add(F)}e==null||e.watchNoReply({fileNames:[...u]})}},[f,s,a,_,e]);const z=u=>{x(u.id),i("bounce-if-busy",s.collectTestIds(u))},D=(u,y)=>{if(u.preventDefault(),u.stopPropagation(),u.metaKey||u.ctrlKey){const b=r.split(" ");b.includes(y)?T(b.filter(F=>F!==y).join(" ").trim()):T((r+" "+y).trim())}else T((r.split(" ").filter(b=>!b.startsWith("@")).join(" ")+" "+y).trim())};return n.jsx(ge,{name:"tests",treeState:m,setTreeState:S,rootItem:s.rootItem,dataTestId:"test-tree",render:u=>n.jsxs("div",{className:"hbox ui-mode-list-item",children:[n.jsxs("div",{className:"ui-mode-list-item-title",children:[n.jsx("span",{title:u.title,children:u.title}),u.kind==="case"?u.tags.map(y=>n.jsx(fe,{tag:y.slice(1),onClick:b=>D(b,y)},y)):null]}),!!u.duration&&u.status!=="skipped"&&n.jsx("div",{className:"ui-mode-list-item-time",children:Kt(u.duration)}),n.jsxs(q,{noMinHeight:!0,noShadow:!0,children:[n.jsx(P,{icon:"play",title:"Run",onClick:()=>z(u),disabled:!!l}),n.jsx(P,{icon:"go-to-file",title:"Show source",onClick:h,style:u.kind==="group"&&u.subKind==="folder"?{visibility:"hidden"}:{}}),!a&&n.jsx(P,{icon:"eye",title:"Watch",onClick:()=>{_.value.has(u.id)?_.value.delete(u.id):_.value.add(u.id),o({..._})},toggled:_.value.has(u.id)})]})]}),icon:u=>Ht(u.status),selectedItem:M,onAccepted:z,onSelected:u=>{l&&(l.itemSelectedByUser=!0),x(u.id)},isError:u=>u.kind==="group"?u.hasLoadErrors:!1,autoExpandDepth:r?5:1,noItemsMessage:f?"Loading…":"No tests"})};function _e(r,t){if(!(!r||!t))return{file:r.location.file,line:r.location.line,column:r.location.column,source:{errors:t.loadErrors.filter(e=>{var s;return((s=e.location)==null?void 0:s.file)===r.location.file}).map(e=>({line:e.location.line,message:e.message})),content:void 0}}}function we(r){return`.playwright-artifacts-${r}`}const ve=({showRouteActionsSetting:r,item:t,rootDir:e,onOpenExternally:s,revealSource:i})=>{var S,C;const[l,a]=c.useState(),[_,o]=c.useState(0),f=c.useRef(null),{outputDir:d}=c.useMemo(()=>({outputDir:t.testCase?Se(t.testCase):void 0}),[t]),[g,T]=c.useState(),h=c.useCallback(x=>T(gt(x)),[T]),m=g?l==null?void 0:l.model.actions.find(x=>gt(x)===g):void 0;return c.useEffect(()=>{var M,z;f.current&&clearTimeout(f.current);const x=(M=t.testCase)==null?void 0:M.results[0];if(!x){a(void 0);return}const j=x&&x.duration>=0&&x.attachments.find(D=>D.name==="trace");if(j&&j.path){wt(j.path).then(D=>a({model:D,isLive:!1}));return}if(!d){a(void 0);return}const W=`${d}/${we(x.workerIndex)}/traces/${(z=t.testCase)==null?void 0:z.id}.json`;return f.current=setTimeout(async()=>{try{const D=await wt(W);a({model:D,isLive:!0})}catch{a(void 0)}finally{o(_+1)}},500),()=>{f.current&&clearTimeout(f.current)}},[d,t,a,_,o]),n.jsx(qt,{showRouteActionsSetting:r,model:l==null?void 0:l.model,showSourcesFirst:!0,rootDir:e,initialSelection:m,onSelectionChanged:h,fallbackLocation:t.testFile,isLive:l==null?void 0:l.isLive,status:(S=t.treeItem)==null?void 0:S.status,annotations:((C=t.testCase)==null?void 0:C.annotations)||[],onOpenExternally:s,revealSource:i},"workbench")},Se=r=>{var t;for(let e=r.parent;e;e=e.parent)if(e.project())return(t=e.project())==null?void 0:t.outputDir};async function wt(r){const t=new URLSearchParams;t.set("trace",r);const s=await(await fetch(`contexts?${t.toString()}`)).json();return new Yt(s)}let vt={cols:80,rows:24};const U={pending:[],clear:()=>{},write:r=>U.pending.push(r),resize:()=>{}},N=new URLSearchParams(window.location.search),xe=N.get("ws"),kt=new URL(`../${xe}`,window.location.toString());kt.protocol=window.location.protocol==="https:"?"wss:":"ws:";const E={args:N.getAll("arg"),grep:N.get("grep")||void 0,grepInvert:N.get("grepInvert")||void 0,projects:N.getAll("project"),workers:N.get("workers")||void 0,timeout:N.has("timeout")?+N.get("timeout"):void 0,headed:N.has("headed"),outputDir:N.get("outputDir")||void 0,updateSnapshots:N.get("updateSnapshots")||void 0,reporters:N.has("reporter")?N.getAll("reporter"):void 0};E.updateSnapshots&&!["all","none","missing"].includes(E.updateSnapshots)&&(E.updateSnapshots=void 0);const St=navigator.platform==="MacIntel",be=({})=>{var pt;const[r,t]=c.useState(""),[e,s]=c.useState(!1),[i,l]=c.useState(new Map([["passed",!1],["failed",!1],["skipped",!1]])),[a,_]=c.useState(new Map),[o,f]=c.useState(),[d,g]=c.useState(),[T,h]=c.useState({}),[m,S]=c.useState(new Set),[C,x]=c.useState(!1),[j,W]=c.useState(),[M,z]=mt("watch-all",!1),[D,u]=c.useState({value:new Set}),y=c.useRef(Promise.resolve()),b=c.useRef(new Set),[F,Tt]=c.useState(0),[jt,Et]=c.useState(!1),[at,lt]=c.useState(!0),[w,yt]=c.useState(),[X,It]=c.useState(!1);c.useState(!1);const[Rt,ct]=c.useState(!1),Bt=c.useCallback(()=>ct(!0),[ct]),Ct=!1,[G,ut]=c.useState(E.workers);c.useMemo(()=>[G==="1",p=>{ut(p?"1":E.workers==="1"?void 0:E.workers)},"Single worker"],[G,ut]);const[J,dt]=c.useState(E.headed);c.useMemo(()=>[J,dt,"Show browser"],[J,dt]);const[Z,ht]=c.useState(E.updateSnapshots);c.useMemo(()=>[Z==="all",p=>ht(p?"all":"missing"),"Update snapshots"],[Z,ht]);const[,,ft]=mt("show-route-actions",!0,"Show route actions"),Nt=Qt(),Pt=c.useRef(null),tt=c.useCallback(()=>{yt(new te(kt.toString()))},[]);c.useEffect(()=>{var p;(p=Pt.current)==null||p.focus(),x(!0),tt()},[tt]),c.useEffect(()=>{if(!w)return;const p=[w.onStdio(v=>{if(v.buffer){const I=atob(v.buffer);U.write(I)}else U.write(v.text)}),w.onClose(()=>Et(!0))];return U.resize=(v,I)=>{vt={cols:v,rows:I},w.resizeTerminalNoReply({cols:v,rows:I})},()=>{for(const v of p)v.dispose()}},[w]),c.useEffect(()=>{if(!w)return;let p;const v=new le({onUpdate:k=>{clearTimeout(p),p=void 0,k?f(v.asModel()):p||(p=setTimeout(()=>{f(v.asModel())},250))},onError:k=>{U.write((k.stack||k.value||"")+`
`)},pathSeparator:nt}),I=async()=>{y.current=y.current.then(async()=>{x(!0);try{const k=await w.listTests({projects:E.projects,locations:E.args,grep:E.grep,grepInvert:E.grepInvert});v.processListReport(k.report)}catch(k){console.log(k)}finally{x(!1)}})};return f(void 0),x(!0),u({value:new Set}),(async()=>{try{await w.initialize({interceptStdio:!0,watchTestDirs:!0});const{status:k,report:B}=await w.runGlobalSetup({});if(v.processGlobalReport(B),k!=="passed")return;const R=await w.listTests({projects:E.projects,locations:E.args,grep:E.grep,grepInvert:E.grepInvert});v.processListReport(R.report),w.onListChanged(I),w.onReport(L=>{v.processTestReportEvent(L)});const{hasBrowsers:O}=await w.checkBrowsers({});lt(O)}finally{x(!1)}})(),()=>{clearTimeout(p)}},[w]),c.useEffect(()=>{if(!o)return;const{config:p,rootSuite:v}=o,I=p.configFile?xt.getObject(p.configFile+":projects",void 0):void 0,k=new Map(a);for(const B of k.keys())v.suites.find(R=>R.title===B)||k.delete(B);for(const B of v.suites)k.has(B.title)||k.set(B.title,!!(I!=null&&I.includes(B.title)));!I&&k.size&&![...k.values()].includes(!0)&&k.set(k.entries().next().value[0],!0),(a.size!==k.size||[...a].some(([B,R])=>k.get(B)!==R))&&_(k)},[a,o]),c.useEffect(()=>{j&&(o!=null&&o.progress)?g(o.progress):o||g(void 0)},[o,j]);const{testTree:K}=c.useMemo(()=>{if(!o)return{testTree:new _t("",new Y("","root"),[],a,nt)};const p=new _t("",o.rootSuite,o.loadErrors,a,nt);return p.filterTree(r,i,j==null?void 0:j.testIds),p.sortAndPropagateStatus(),p.shortenRoot(),p.flattenForSingleProject(),S(p.testIds()),{testTree:p}},[r,o,i,a,S,j]),V=c.useCallback((p,v)=>{!w||!o||p==="bounce-if-busy"&&j||(b.current=new Set([...b.current,...v]),y.current=y.current.then(async()=>{var B,R,O;const I=b.current;if(b.current=new Set,!I.size)return;{for(const L of((B=o.rootSuite)==null?void 0:B.allTests())||[])if(I.has(L.id)){L.results=[];const rt=L._createTestResult("pending");rt[Q]="scheduled"}f({...o})}const k="  ["+new Date().toLocaleTimeString()+"]";U.write("\x1B[2m—".repeat(Math.max(0,vt.cols-k.length))+k+"\x1B[22m"),g({total:0,passed:0,failed:0,skipped:0}),W({testIds:I}),await w.runTests({locations:E.args,grep:E.grep,grepInvert:E.grepInvert,testIds:[...I],projects:[...a].filter(([L,rt])=>rt).map(([L])=>L),workers:G,timeout:E.timeout,headed:J,outputDir:E.outputDir,updateSnapshots:Z,reporters:E.reporters,trace:"on"});for(const L of((R=o.rootSuite)==null?void 0:R.allTests())||[])((O=L.results[0])==null?void 0:O.duration)===-1&&(L.results=[]);f({...o}),W(void 0)}))},[a,j,o,w,G,J,Z]);c.useEffect(()=>{if(!w)return;const p=w.onTestFilesChanged(v=>{const I=[],k=new Set(v.testFiles);if(M){const B=R=>{const O=R.location.file;O&&k.has(O)&&I.push(...K.collectTestIds(R)),R.kind==="group"&&R.subKind==="folder"&&R.children.forEach(B)};B(K.rootItem)}else for(const B of D.value){const R=K.treeItemById(B),O=R==null?void 0:R.location.file;O&&k.has(O)&&I.push(...K.collectTestIds(R))}V("queue-if-busy",new Set(I))});return()=>p.dispose()},[V,w,K,M,D]),c.useEffect(()=>{if(!w)return;const p=v=>{v.code==="Backquote"&&v.ctrlKey?(v.preventDefault(),s(!e)):v.code==="F5"&&v.shiftKey?(v.preventDefault(),w==null||w.stopTestsNoReply({})):v.code==="F5"&&(v.preventDefault(),V("bounce-if-busy",m))};return addEventListener("keydown",p),()=>{removeEventListener("keydown",p)}},[V,tt,w,m,e]);const H=!!j,st=c.useRef(null),Dt=c.useCallback(p=>{var v;p.preventDefault(),p.stopPropagation(),(v=st.current)==null||v.showModal()},[]),it=c.useCallback(p=>{var v;p.preventDefault(),p.stopPropagation(),(v=st.current)==null||v.close()},[]),Lt=c.useCallback(p=>{it(p),s(!0),w==null||w.installBrowsers({}).then(async()=>{s(!1);const{hasBrowsers:v}=await(w==null?void 0:w.checkBrowsers({}));lt(v)})},[it,w]);return n.jsxs("div",{className:"vbox ui-mode",children:[!at&&n.jsxs("dialog",{ref:st,children:[n.jsxs("div",{className:"title",children:[n.jsx("span",{className:"codicon codicon-lightbulb"}),"Install browsers"]}),n.jsxs("div",{className:"body",children:["Playwright did not find installed browsers.",n.jsx("br",{}),"Would you like to run `playwright install`?",n.jsx("br",{}),n.jsx("button",{className:"button",onClick:Lt,children:"Install"}),n.jsx("button",{className:"button secondary",onClick:it,children:"Dismiss"})]})]}),jt&&n.jsxs("div",{className:"disconnected",children:[n.jsx("div",{className:"title",children:"UI Mode disconnected"}),n.jsxs("div",{children:[n.jsx("a",{href:"#",onClick:()=>window.location.href="/",children:"Reload the page"})," to reconnect"]})]}),n.jsxs(Xt,{sidebarSize:250,minSidebarSize:150,orientation:"horizontal",sidebarIsFirst:!0,settingName:"testListSidebar",children:[n.jsxs("div",{className:"vbox",children:[n.jsxs("div",{className:"vbox"+(e?"":" hidden"),children:[n.jsxs(q,{children:[n.jsx("div",{className:"section-title",style:{flex:"none"},children:"Output"}),n.jsx(P,{icon:"circle-slash",title:"Clear output",onClick:()=>U.clear()}),n.jsx("div",{className:"spacer"}),n.jsx(P,{icon:"close",title:"Close",onClick:()=>s(!1)})]}),n.jsx(ce,{source:U})]}),n.jsx("div",{className:"vbox"+(e?" hidden":""),children:n.jsx(ve,{showRouteActionsSetting:ft,item:T,rootDir:(pt=o==null?void 0:o.config)==null?void 0:pt.rootDir,revealSource:Rt,onOpenExternally:p=>w==null?void 0:w.openNoReply({location:{file:p.file,line:p.line,column:p.column}})})})]}),n.jsxs("div",{className:"vbox ui-mode-sidebar",children:[n.jsxs(q,{noShadow:!0,noMinHeight:!0,children:[n.jsx("img",{src:"playwright-logo.svg",alt:"Playwright logo"}),n.jsx("div",{className:"section-title",children:"Playwright"}),n.jsx(P,{icon:"refresh",title:"Reload",onClick:()=>tt(),disabled:H||C}),n.jsx(P,{icon:"terminal",title:"Toggle output — "+(St?"⌃`":"Ctrl + `"),toggled:e,onClick:()=>{s(!e)}}),!at&&n.jsx(P,{icon:"lightbulb-autofix",style:{color:"var(--vscode-list-warningForeground)"},title:"Playwright browsers are missing",onClick:Dt})]}),n.jsx(he,{filterText:r,setFilterText:t,statusFilters:i,setStatusFilters:l,projectFilters:a,setProjectFilters:_,testModel:o,runTests:()=>V("bounce-if-busy",m)}),n.jsxs(q,{noMinHeight:!0,children:[!H&&!d&&n.jsx("div",{className:"section-title",children:"Tests"}),!H&&d&&n.jsx("div",{"data-testid":"status-line",className:"status-line",children:n.jsxs("div",{children:[d.passed,"/",d.total," passed (",d.passed/d.total*100|0,"%)"]})}),H&&d&&n.jsx("div",{"data-testid":"status-line",className:"status-line",children:n.jsxs("div",{children:["Running ",d.passed,"/",j.testIds.size," passed (",d.passed/j.testIds.size*100|0,"%)"]})}),n.jsx(P,{icon:"play",title:"Run all — F5",onClick:()=>V("bounce-if-busy",m),disabled:H||C}),n.jsx(P,{icon:"debug-stop",title:"Stop — "+(St?"⇧F5":"Shift + F5"),onClick:()=>w==null?void 0:w.stopTests({}),disabled:!H||C}),n.jsx(P,{icon:"eye",title:"Watch all",toggled:M,onClick:()=>{u({value:new Set}),z(!M)}}),n.jsx(P,{icon:"collapse-all",title:"Collapse all",onClick:()=>{Tt(F+1)}})]}),n.jsx(me,{filterText:r,testModel:o,testTree:K,testServerConnection:w,runningState:j,runTests:V,onItemSelected:h,watchAll:M,watchedTreeIds:D,setWatchedTreeIds:u,isLoading:C,requestedCollapseAllCount:F,setFilterText:t,onRevealSource:Bt}),Ct,n.jsxs(q,{noShadow:!0,noMinHeight:!0,className:"settings-toolbar",onClick:()=>It(!X),children:[n.jsx("span",{className:`codicon codicon-${X?"chevron-down":"chevron-right"}`,style:{marginLeft:5},title:X?"Hide Settings":"Show Settings"}),n.jsx("div",{className:"section-title",children:"Settings"})]}),X&&n.jsx(Gt,{settings:[Nt,ft]})]})]})]})};(async()=>{if(Jt(),window.location.protocol!=="file:"){if(window.location.href.includes("isUnderTest=true")&&await new Promise(r=>setTimeout(r,1e3)),!navigator.serviceWorker)throw new Error(`Service workers are not supported.
Make sure to serve the website (${window.location}) via HTTPS or localhost.`);navigator.serviceWorker.register("sw.bundle.js"),navigator.serviceWorker.controller||await new Promise(r=>{navigator.serviceWorker.oncontrollerchange=()=>r()}),setInterval(function(){fetch("ping")},1e4)}Zt.render(n.jsx(be,{}),document.querySelector("#root"))})();
