{"name": "fast-uri", "description": "Dependency free RFC 3986 URI toolbox", "version": "3.0.1", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://github.com/zekth)", "repository": {"type": "git", "url": "git+https://github.com/fastify/fast-uri.git"}, "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "homepage": "https://github.com/fastify/fast-uri", "scripts": {"bench": "node benchmark.js", "lint": "standard | snazzy", "lint:fix": "standard --fix", "test": "npm run lint && npm run test:unit && npm run test:typescript", "test:ci": "npm run lint && npm run test:unit -- --coverage-report=lcovonly && npm run test:typescript", "test:unit": "tap", "test:unit:dev": "npm run test:unit -- --coverage-report=html", "test:typescript": "tsd"}, "devDependencies": {"@fastify/pre-commit": "^2.1.0", "ajv": "^8.16.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "snazzy": "^9.0.0", "standard": "^17.1.0", "tap": "^18.7.2", "tsd": "^0.31.0", "uri-js": "^4.4.1"}}