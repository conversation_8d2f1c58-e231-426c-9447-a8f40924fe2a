{"name": "<PERSON><PERSON>s", "version": "0.8.5", "description": "Portable Unix shell commands for Node.js", "keywords": ["<PERSON><PERSON>s", "bash", "unix", "shell", "makefile", "make", "jake", "synchronous"], "contributors": ["<PERSON> <<EMAIL>> (https://github.com/nfischer)", "<PERSON> <<EMAIL>> (https://github.com/freitagbr)"], "repository": {"type": "git", "url": "git://github.com/shelljs/shelljs.git"}, "license": "BSD-3-<PERSON><PERSON>", "homepage": "http://github.com/shelljs/shelljs", "main": "./shell.js", "files": ["commands.js", "global.js", "make.js", "plugin.js", "shell.js", "bin", "src"], "scripts": {"ci-or-install": "node scripts/ci-or-install", "posttest": "npm run lint", "test": "nyc --reporter=text --reporter=lcov ava test/*.js", "test-no-coverage": "ava test/*.js", "gendocs": "node scripts/generate-docs", "lint": "eslint .", "after-travis": "travis-check-changes", "changelog": "shelljs-changelog", "codecov": "codecov", "release:major": "shelljs-release major", "release:minor": "shelljs-release minor", "release:patch": "shelljs-release patch"}, "bin": {"shjs": "./bin/shjs"}, "dependencies": {"glob": "^7.0.0", "interpret": "^1.0.0", "rechoir": "^0.6.2"}, "ava": {"serial": true, "powerAssert": false}, "devDependencies": {"ava": "^0.21.0", "chalk": "^1.1.3", "codecov": "^3.0.2", "coffee-script": "^1.10.0", "eslint": "^2.0.0", "eslint-config-airbnb-base": "^3.0.0", "eslint-plugin-import": "^1.11.1", "nyc": "^11.3.0", "shelljs-changelog": "^0.2.0", "shelljs-release": "^0.3.0", "shx": "^0.2.0", "travis-check-changes": "^0.2.0"}, "optionalDependencies": {}, "engines": {"node": ">=4"}}