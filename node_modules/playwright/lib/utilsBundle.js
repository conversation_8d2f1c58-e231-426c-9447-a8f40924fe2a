"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.stoppable = exports.sourceMapSupport = exports.pirates = exports.json5 = exports.enquirer = exports.chokidar = void 0;
/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

const json5 = exports.json5 = require('./utilsBundleImpl').json5;
const pirates = exports.pirates = require('./utilsBundleImpl').pirates;
const sourceMapSupport = exports.sourceMapSupport = require('./utilsBundleImpl').sourceMapSupport;
const stoppable = exports.stoppable = require('./utilsBundleImpl').stoppable;
const enquirer = exports.enquirer = require('./utilsBundleImpl').enquirer;
const chokidar = exports.chokidar = require('./utilsBundleImpl').chokidar;