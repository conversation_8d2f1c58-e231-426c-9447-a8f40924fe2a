import { addMediaTypePlugin } from "@hyperjump/browser";
import { addKeyword } from "./keywords.js";
import { schemaPlugin } from "./schema.js";

import additionalProperties from "./keywords/additionalProperties.js";
import allOf from "./keywords/allOf.js";
import anchor from "./keywords/anchor.js";
import anyOf from "./keywords/anyOf.js";
import conditional from "./keywords/conditional.js";
import const_ from "./keywords/const.js";
import contains from "./keywords/contains.js";
import comment from "./keywords/comment.js";
import contentEncoding from "./keywords/contentEncoding.js";
import contentMediaType from "./keywords/contentMediaType.js";
import contentSchema from "./keywords/contentSchema.js";
import default_ from "./keywords/default.js";
import definitions from "./keywords/definitions.js";
import dependentRequired from "./keywords/dependentRequired.js";
import dependentSchemas from "./keywords/dependentSchemas.js";
import deprecated from "./keywords/deprecated.js";
import description from "./keywords/description.js";
import dynamicAnchor from "./keywords/dynamicAnchor.js";
import dynamicRef from "./keywords/dynamicRef.js";
import else_ from "./keywords/else.js";
import enum_ from "./keywords/enum.js";
import examples from "./keywords/examples.js";
import exclusiveMaximum from "./keywords/exclusiveMaximum.js";
import exclusiveMinimum from "./keywords/exclusiveMinimum.js";
import format from "./keywords/format.js";
import id from "./keywords/id.js";
import if_ from "./keywords/if.js";
import itemPattern from "./keywords/itemPattern.js";
import items from "./keywords/items.js";
import maxContains from "./keywords/maxContains.js";
import maxItems from "./keywords/maxItems.js";
import maxLength from "./keywords/maxLength.js";
import maxProperties from "./keywords/maxProperties.js";
import maximum from "./keywords/maximum.js";
import minContains from "./keywords/minContains.js";
import minItems from "./keywords/minItems.js";
import minLength from "./keywords/minLength.js";
import minProperties from "./keywords/minProperties.js";
import minimum from "./keywords/minimum.js";
import multipleOf from "./keywords/multipleOf.js";
import not from "./keywords/not.js";
import oneOf from "./keywords/oneOf.js";
import pattern from "./keywords/pattern.js";
import patternProperties from "./keywords/patternProperties.js";
import prefixItems from "./keywords/prefixItems.js";
import properties from "./keywords/properties.js";
import propertyDependencies from "./keywords/propertyDependencies.js";
import propertyNames from "./keywords/propertyNames.js";
import readOnly from "./keywords/readOnly.js";
import ref from "./keywords/ref.js";
import requireAllExcept from "./keywords/requireAllExcept.js";
import required from "./keywords/required.js";
import title from "./keywords/title.js";
import then from "./keywords/then.js";
import type from "./keywords/type.js";
import unevaluatedItems from "./keywords/unevaluatedItems.js";
import unevaluatedProperties from "./keywords/unevaluatedProperties.js";
import uniqueItems from "./keywords/uniqueItems.js";
import unknown from "./keywords/unknown.js";
import vocabulary from "./keywords/vocabulary.js";
import writeOnly from "./keywords/writeOnly.js";


addMediaTypePlugin("application/schema+json", schemaPlugin);

addKeyword(additionalProperties);
addKeyword(allOf);
addKeyword(anchor);
addKeyword(anyOf);
addKeyword(conditional);
addKeyword(const_);
addKeyword(contains);
addKeyword(comment);
addKeyword(contentEncoding);
addKeyword(contentMediaType);
addKeyword(contentSchema);
addKeyword(default_);
addKeyword(definitions);
addKeyword(dependentRequired);
addKeyword(dependentSchemas);
addKeyword(deprecated);
addKeyword(description);
addKeyword(dynamicAnchor);
addKeyword(dynamicRef);
addKeyword(else_);
addKeyword(enum_);
addKeyword(examples);
addKeyword(exclusiveMaximum);
addKeyword(exclusiveMinimum);
addKeyword(format);
addKeyword(id);
addKeyword(if_);
addKeyword(itemPattern);
addKeyword(items);
addKeyword(maxContains);
addKeyword(maxItems);
addKeyword(maxLength);
addKeyword(maxProperties);
addKeyword(maximum);
addKeyword(minContains);
addKeyword(minItems);
addKeyword(minLength);
addKeyword(minProperties);
addKeyword(minimum);
addKeyword(multipleOf);
addKeyword(not);
addKeyword(oneOf);
addKeyword(pattern);
addKeyword(patternProperties);
addKeyword(prefixItems);
addKeyword(properties);
addKeyword(propertyDependencies);
addKeyword(propertyNames);
addKeyword(readOnly);
addKeyword(ref);
addKeyword(requireAllExcept);
addKeyword(required);
addKeyword(title);
addKeyword(then);
addKeyword(type);
addKeyword(unevaluatedItems);
addKeyword(unevaluatedProperties);
addKeyword(uniqueItems);
addKeyword(unknown);
addKeyword(vocabulary);
addKeyword(writeOnly);

export { addSchema, unregisterSchema, validate, FLAG } from "./core.js";
export { registerSchema, hasSchema, getAllRegisteredSchemaUris } from "./schema.js";
export {
  getMetaSchemaOutputFormat,
  setMetaSchemaOutputFormat,
  getShouldValidateSchema,
  setShouldValidateSchema
} from "./configuration.js";
export { InvalidSchemaError } from "./invalid-schema-error.js";
