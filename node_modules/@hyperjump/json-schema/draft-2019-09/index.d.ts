import type { J<PERSON> } from "@hyperjump/json-pointer";
import type { JsonSchemaType } from "../lib/common.js";


export type JsonSchemaDraft201909 = boolean | {
  $schema?: "https://json-schema.org/draft/2019-09/schema";
  $id?: string;
  $anchor?: string;
  $ref?: string;
  $recursiveRef?: "#";
  $recursiveAnchor?: boolean;
  $vocabulary?: Record<string, boolean>;
  $comment?: string;
  $defs?: Record<string, JsonSchemaDraft201909>;
  additionalItems?: JsonSchemaDraft201909;
  unevaluatedItems?: JsonSchemaDraft201909;
  items?: JsonSchemaDraft201909 | JsonSchemaDraft201909[];
  contains?: JsonSchemaDraft201909;
  additionalProperties?: JsonSchemaDraft201909;
  unevaluatedProperties?: JsonSchemaDraft201909;
  properties?: Record<string, JsonSchemaDraft201909>;
  patternProperties?: Record<string, JsonSchemaDraft201909>;
  dependentSchemas?: Record<string, JsonSchemaDraft201909>;
  propertyNames?: JsonSchemaDraft201909;
  if?: JsonSchemaDraft201909;
  then?: JsonSchemaDraft201909;
  else?: JsonSchemaDraft201909;
  allOf?: JsonSchemaDraft201909[];
  anyOf?: JsonSchemaDraft201909[];
  oneOf?: JsonSchemaDraft201909[];
  not?: JsonSchemaDraft201909;
  multipleOf?: number;
  maximum?: number;
  exclusiveMaximum?: number;
  minimum?: number;
  exclusiveMinimum?: number;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  maxItems?: number;
  minItems?: number;
  uniqueItems?: boolean;
  maxContains?: number;
  minContains?: number;
  maxProperties?: number;
  minProperties?: number;
  required?: string[];
  dependentRequired?: Record<string, string[]>;
  const?: Json;
  enum?: Json[];
  type?: JsonSchemaType | JsonSchemaType[];
  title?: string;
  description?: string;
  default?: Json;
  deprecated?: boolean;
  readOnly?: boolean;
  writeOnly?: boolean;
  examples?: Json[];
  format?: "date-time" | "date" | "time" | "duration" | "email" | "idn-email" | "hostname" | "idn-hostname" | "ipv4" | "ipv6" | "uri" | "uri-reference" | "iri" | "iri-reference" | "uuid" | "uri-template" | "json-pointer" | "relative-json-pointer" | "regex";
  contentMediaType?: string;
  contentEncoding?: string;
  contentSchema?: JsonSchemaDraft201909;
};

export * from "../lib/index.js";
