{"name": "@hyperjump/pact", "version": "1.4.0", "description": "Higher order functions for iterators and async iterators", "type": "module", "main": "./src/index.js", "exports": {".": "./src/index.js", "./async": "./src/async.js"}, "scripts": {"lint": "eslint src", "test": "vitest --watch=false", "type-check": "tsc --noEmit", "docs": "typedoc --excludeExternals"}, "repository": "github:hyperjump-io/pact", "keywords": ["Hyperjump", "Promise", "higher order functions", "iterator", "async iterator", "generator", "async generator", "async"], "author": "<PERSON> <j<PERSON><PERSON><EMAIL>>", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/jdesrosiers"}, "devDependencies": {"@stylistic/eslint-plugin": "*", "@types/node": "^22.13.5", "@typescript-eslint/eslint-plugin": "*", "@typescript-eslint/parser": "*", "eslint-import-resolver-typescript": "*", "eslint-plugin-import": "*", "typedoc": "^0.27.9", "typescript-eslint": "*", "vitest": "*"}}