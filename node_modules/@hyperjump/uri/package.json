{"name": "@hyperjump/uri", "version": "1.3.1", "description": "A small and fast library for validating parsing and resolving URIs and IRIs", "type": "module", "main": "./lib/index.js", "exports": "./lib/index.js", "scripts": {"lint": "eslint lib", "test": "vitest --watch=false", "type-check": "tsc --noEmit", "prepack": "tsc --project tsconfig.build.json", "postpack": "node ./bin/postpack.js"}, "repository": "github:hyperjump-io/uri", "author": "<PERSON> <j<PERSON><PERSON><EMAIL>>", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/jdesrosiers"}, "keywords": ["URI", "IRI", "resolve", "relative", "parse", "RFC3986", "RFC-3986", "RFC3987", "RFC-3987"], "devDependencies": {"@stylistic/eslint-plugin": "*", "@types/node": "^22.13.0", "eslint-import-resolver-typescript": "*", "eslint-plugin-import": "*", "typescript-eslint": "*", "vitest": "*"}}