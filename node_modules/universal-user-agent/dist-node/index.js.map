{"version": 3, "file": "index.js", "sources": ["../dist-src/index.js"], "sourcesContent": ["export function getUserAgent() {\n    if (typeof navigator === \"object\" && \"userAgent\" in navigator) {\n        return navigator.userAgent;\n    }\n    if (typeof process === \"object\" && process.version !== undefined) {\n        return `Node.js/${process.version.substr(1)} (${process.platform}; ${process.arch})`;\n    }\n    return \"<environment undetectable>\";\n}\n"], "names": ["getUserAgent", "navigator", "userAgent", "process", "version", "undefined", "substr", "platform", "arch"], "mappings": ";;;;AAAO,SAASA,YAAT,GAAwB;AAC3B,MAAI,OAAOC,SAAP,KAAqB,QAArB,IAAiC,eAAeA,SAApD,EAA+D;AAC3D,WAAOA,SAAS,CAACC,SAAjB;AACH;;AACD,MAAI,OAAOC,OAAP,KAAmB,QAAnB,IAA+BA,OAAO,CAACC,OAAR,KAAoBC,SAAvD,EAAkE;AAC9D,WAAQ,WAAUF,OAAO,CAACC,OAAR,CAAgBE,MAAhB,CAAuB,CAAvB,CAA0B,KAAIH,OAAO,CAACI,QAAS,KAAIJ,OAAO,CAACK,IAAK,GAAlF;AACH;;AACD,SAAO,4BAAP;AACH;;;;"}