{"name": "vue-sonner", "version": "1.3.0", "type": "module", "author": "xiaoluoboding <<EMAIL>>", "scripts": {"dev": "vite", "dev:playground": "nuxi dev playground", "dev:build": "nuxi build playground", "dev:prepare": "nuxt-module-build build --stub && nuxi prepare playground", "build:dev": "vite build --mode lib --watch", "build:docs": "vite build --mode docs", "build:lib": "vite build --mode lib && pnpm run build:types", "build:module": "nuxt-module-build build --outDir lib/nuxt", "build:types": "vue-tsc -p tsconfig.build.json && api-extractor run", "release": "pnpm run build:lib && pnpm run build:module", "preview": "vite preview", "test": "cd ./test && pnpm test:e2e --ui", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0"}, "repository": {"type": "git", "url": "git+https://github.com/xiaoluoboding/vue-sonner.git"}, "homepage": "https://github.com/xiaoluoboding/vue-sonner", "files": ["lib", "volar.d.ts"], "main": "./lib/vue-sonner.cjs", "module": "./lib/vue-sonner.js", "exports": {".": {"types": "./lib/vue-sonner.d.ts", "import": "./lib/vue-sonner.js", "require": "./lib/vue-sonner.cjs"}, "./nuxt": {"import": {"types": "./lib/nuxt/types.d.mts", "default": "./lib/nuxt/module.mjs"}, "require": {"types": "./lib/nuxt/types.d.ts", "default": "./lib/nuxt/module.cjs"}}}, "sideEffects": ["**/*.css"], "types": "./lib/vue-sonner.d.ts", "license": "MIT", "devDependencies": {"@iconify/json": "^2.2.172", "@microsoft/api-extractor": "^7.47.11", "@nuxt/devtools": "^1.6.0", "@nuxt/kit": "^3.13.2", "@nuxt/module-builder": "^0.8.4", "@nuxt/schema": "^3.13.2", "@nuxt/test-utils": "^3.14.4", "@types/node": "^18.19.8", "@unocss/reset": "^0.63.4", "@vitejs/plugin-vue": "^5.1.4", "@vue/tsconfig": "^0.6.0", "@vueuse/core": "^11.1.0", "@vueuse/head": "^2.0.0", "clean-css": "^5.3.3", "highlight.js": "^11.10.0", "nuxt": "^3.13.2", "typescript": "^5.6.3", "unocss": "^0.63.4", "unplugin-icons": "^0.19.3", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.11", "vue": "^3.5.12", "vue-sonner": "^1.2.4", "vue-tsc": "^2.1.10"}}