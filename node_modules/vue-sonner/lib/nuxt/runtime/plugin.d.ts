declare const _default: import("nuxt/app").Plugin<{
    toast: ((message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number) & {
        success: (message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
        info: (message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
        warning: (message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
        error: (message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
        custom: (component: import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
        message: (message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
        promise: <ToastData>(promise: Promise<ToastData> | (() => Promise<ToastData>), data?: (Omit<import("vue-sonner").ToastT<import("vue").Component>, "id" | "type" | "title" | "promise" | "delete"> & {
            id?: number | string;
        } & {
            loading?: string | import("vue").Component;
            success?: (string | import("vue").Component | ((data: ToastData) => import("vue").Component | string | Promise<import("vue").Component | string>)) | undefined;
            error?: string | import("vue").Component | ((data: any) => import("vue").Component | string | Promise<import("vue").Component | string>);
            description?: string | import("vue").Component | ((data: any) => import("vue").Component | string | Promise<import("vue").Component | string>);
            finally?: () => void | Promise<void>;
        }) | undefined) => (string & {
            unwrap: () => Promise<ToastData>;
        }) | (number & {
            unwrap: () => Promise<ToastData>;
        }) | {
            unwrap: () => Promise<ToastData>;
        } | undefined;
        dismiss: (id?: number | string) => string | number | undefined;
        loading: (message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
    } & {
        getHistory: () => (import("vue-sonner").ToastT<import("vue").Component> | import("vue-sonner").ToastToDismiss)[];
    };
}> & import("nuxt/app").ObjectPlugin<{
    toast: ((message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number) & {
        success: (message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
        info: (message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
        warning: (message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
        error: (message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
        custom: (component: import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
        message: (message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
        promise: <ToastData>(promise: Promise<ToastData> | (() => Promise<ToastData>), data?: (Omit<import("vue-sonner").ToastT<import("vue").Component>, "id" | "type" | "title" | "promise" | "delete"> & {
            id?: number | string;
        } & {
            loading?: string | import("vue").Component;
            success?: (string | import("vue").Component | ((data: ToastData) => import("vue").Component | string | Promise<import("vue").Component | string>)) | undefined;
            error?: string | import("vue").Component | ((data: any) => import("vue").Component | string | Promise<import("vue").Component | string>);
            description?: string | import("vue").Component | ((data: any) => import("vue").Component | string | Promise<import("vue").Component | string>);
            finally?: () => void | Promise<void>;
        }) | undefined) => (string & {
            unwrap: () => Promise<ToastData>;
        }) | (number & {
            unwrap: () => Promise<ToastData>;
        }) | {
            unwrap: () => Promise<ToastData>;
        } | undefined;
        dismiss: (id?: number | string) => string | number | undefined;
        loading: (message: string | import("vue").Component, data?: import("vue-sonner").ExternalToast) => string | number;
    } & {
        getHistory: () => (import("vue-sonner").ToastT<import("vue").Component> | import("vue-sonner").ToastToDismiss)[];
    };
}>;
export default _default;
