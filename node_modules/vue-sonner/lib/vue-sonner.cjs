var $e=Object.defineProperty;var ze=(n,a,t)=>a in n?$e(n,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[a]=t;var b=(n,a,t)=>ze(n,typeof a!="symbol"?a+"":a,t);function Pe(n){if(!n||typeof document>"u")return;let a=document.head||document.getElementsByTagName("head")[0],t=document.createElement("style");t.type="text/css",a.appendChild(t),t.styleSheet?t.styleSheet.cssText=n:t.appendChild(document.createTextNode(n))}Pe(":where([data-sonner-toaster][dir=ltr]),:where(html[dir=ltr]){--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}:where([data-sonner-toaster][dir=rtl]),:where(html[dir=rtl]){--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted=true]){transform:translateY(-10px)}@media (hover:none) and (pointer:coarse){:where([data-sonner-toaster][data-lifted=true]){transform:none}}:where([data-sonner-toaster][data-x-position=right]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position=left]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position=center]){left:50%;transform:translateX(-50%)}:where([data-sonner-toaster][data-y-position=top]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position=bottom]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=true]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}:where([data-sonner-toast][data-y-position=top]){top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=bottom]){bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=true]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=dark]) :where([data-cancel]){background:rgba(255,255,255,.3)}[data-sonner-toast] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}:where([data-sonner-toast]) :where([data-disabled=true]){cursor:not-allowed}[data-sonner-toast]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=true])::before{content:'';position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=top][data-swiping=true])::before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=bottom][data-swiping=true])::before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=false][data-removed=true])::before{content:'';position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast])::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=true]){--y:translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=false][data-front=false]){--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=false][data-front=false][data-styled=true])>*{opacity:0}:where([data-sonner-toast][data-visible=false]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=true][data-expanded=true]){--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]){--y:translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]){--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]){--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=true][data-front=false])::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{from{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;--mobile-offset:16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 91%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 91%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 91%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 100%, 12%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 12%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const e=require("vue");let _=0;class Ie{constructor(){b(this,"subscribers");b(this,"toasts");b(this,"subscribe",a=>(this.subscribers.push(a),()=>{const t=this.subscribers.indexOf(a);this.subscribers.splice(t,1)}));b(this,"publish",a=>{this.subscribers.forEach(t=>t(a))});b(this,"addToast",a=>{this.publish(a),this.toasts=[...this.toasts,a]});b(this,"create",a=>{var S;const{message:t,...l}=a,r=typeof a.id=="number"||a.id&&((S=a.id)==null?void 0:S.length)>0?a.id:_++,p=this.toasts.find(f=>f.id===r),w=a.dismissible===void 0?!0:a.dismissible;return p?this.toasts=this.toasts.map(f=>f.id===r?(this.publish({...f,...a,id:r,title:t}),{...f,...a,id:r,dismissible:w,title:t}):f):this.addToast({title:t,...l,dismissible:w,id:r}),r});b(this,"dismiss",a=>(a||this.toasts.forEach(t=>{this.subscribers.forEach(l=>l({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:a,dismiss:!0})),a));b(this,"message",(a,t)=>this.create({...t,message:a,type:"default"}));b(this,"error",(a,t)=>this.create({...t,type:"error",message:a}));b(this,"success",(a,t)=>this.create({...t,type:"success",message:a}));b(this,"info",(a,t)=>this.create({...t,type:"info",message:a}));b(this,"warning",(a,t)=>this.create({...t,type:"warning",message:a}));b(this,"loading",(a,t)=>this.create({...t,type:"loading",message:a}));b(this,"promise",(a,t)=>{if(!t)return;let l;t.loading!==void 0&&(l=this.create({...t,promise:a,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));const r=a instanceof Promise?a:a();let p=l!==void 0,w;const S=r.then(async c=>{if(w=["resolve",c],Ne(c)&&!c.ok){p=!1;const m=typeof t.error=="function"?await t.error(`HTTP error! status: ${c.status}`):t.error,h=typeof t.description=="function"?await t.description(`HTTP error! status: ${c.status}`):t.description;this.create({id:l,type:"error",message:m,description:h})}else if(t.success!==void 0){p=!1;const m=typeof t.success=="function"?await t.success(c):t.success,h=typeof t.description=="function"?await t.description(c):t.description;this.create({id:l,type:"success",message:m,description:h})}}).catch(async c=>{if(w=["reject",c],t.error!==void 0){p=!1;const m=typeof t.error=="function"?await t.error(c):t.error,h=typeof t.description=="function"?await t.description(c):t.description;this.create({id:l,type:"error",message:m,description:h})}}).finally(()=>{var c;p&&(this.dismiss(l),l=void 0),(c=t.finally)==null||c.call(t)}),f=()=>new Promise((c,m)=>S.then(()=>w[0]==="reject"?m(w[1]):c(w[1])).catch(m));return typeof l!="string"&&typeof l!="number"?{unwrap:f}:Object.assign(l,{unwrap:f})});b(this,"custom",(a,t)=>{const l=(t==null?void 0:t.id)||_++;return this.publish({component:a,id:l,...t}),l});this.subscribers=[],this.toasts=[]}}const E=new Ie;function De(n,a){const t=(a==null?void 0:a.id)||_++;return E.create({message:n,id:t,type:"default",...a}),t}const Ne=n=>n&&typeof n=="object"&&"ok"in n&&typeof n.ok=="boolean"&&"status"in n&&typeof n.status=="number",Me=De,Ve=()=>E.toasts,He=Object.assign(Me,{success:E.success,info:E.info,warning:E.warning,error:E.error,custom:E.custom,message:E.message,promise:E.promise,dismiss:E.dismiss,loading:E.loading},{getHistory:Ve});function G(n){return n.label!==void 0}function Oe(){const n=e.ref(!1);return e.watchEffect(()=>{const a=()=>{n.value=document.hidden};return document.addEventListener("visibilitychange",a),()=>window.removeEventListener("visibilitychange",a)}),{isDocumentHidden:n}}function Ae(){const n=e.ref([]);return e.watchEffect(a=>{const t=E.subscribe(l=>{if("dismiss"in l&&l.dismiss)return n.value.filter(p=>p.id!==l.id);const r=n.value.findIndex(p=>p.id===l.id);if(r!==-1){const p=[...n.value];p[r]={...p[r],...l},n.value=p}else n.value=[l,...n.value]});a(()=>{t()})}),{activeToasts:n}}const Le=["aria-live","data-rich-colors","data-styled","data-mounted","data-promise","data-removed","data-visible","data-y-position","data-x-position","data-index","data-front","data-swiping","data-dismissible","data-type","data-invert","data-swipe-out","data-expanded"],Ye=["aria-label","data-disabled"],Be=4e3,Fe=20,Re=200,je=e.defineComponent({__name:"Toast",props:{toast:{},toasts:{},index:{},expanded:{type:Boolean},invert:{type:Boolean},heights:{},gap:{},position:{},visibleToasts:{},expandByDefault:{type:Boolean},closeButton:{type:Boolean},interacting:{type:Boolean},style:{},cancelButtonStyle:{},actionButtonStyle:{},duration:{},class:{},unstyled:{type:Boolean},descriptionClass:{},loadingIcon:{},classes:{},icons:{},closeButtonAriaLabel:{},pauseWhenPageIsHidden:{type:Boolean},cn:{type:Function},defaultRichColors:{type:Boolean}},emits:["update:heights","removeToast"],setup(n,{emit:a}){const t=n,l=a,r=e.ref(!1),p=e.ref(!1),w=e.ref(!1),S=e.ref(!1),f=e.ref(!1),c=e.ref(0),m=e.ref(0),h=e.ref(t.toast.duration||t.duration||Be),$=e.ref(null),k=e.ref(null),Q=e.computed(()=>t.index===0),J=e.computed(()=>t.index+1<=t.visibleToasts),C=e.computed(()=>t.toast.type),D=e.computed(()=>t.toast.dismissible!==!1),Z=e.computed(()=>t.toast.class||""),s=e.computed(()=>t.descriptionClass||""),i=t.toast.style||{},d=e.computed(()=>t.heights.findIndex(o=>o.toastId===t.toast.id)||0),y=e.computed(()=>t.toast.closeButton??t.closeButton);e.computed(()=>t.toast.duration||t.duration||Be);const g=e.ref(0),z=e.ref(0),I=e.ref(null),V=e.computed(()=>t.position.split("-")),H=e.computed(()=>V.value[0]),j=e.computed(()=>V.value[1]),W=e.computed(()=>typeof t.toast.title!="string"),U=e.computed(()=>typeof t.toast.description!="string"),K=e.computed(()=>t.heights.reduce((o,u,B)=>B>=d.value?o:o+u.height,0)),X=Oe(),q=e.computed(()=>t.toast.invert||t.invert),N=e.computed(()=>C.value==="loading"),P=e.computed(()=>d.value*t.gap+K.value||0);e.onMounted(()=>{if(!r.value)return;const o=k.value,u=o==null?void 0:o.style.height;o.style.height="auto";const B=o.getBoundingClientRect().height;o.style.height=u,m.value=B;let x;t.heights.find(v=>v.toastId===t.toast.id)?x=t.heights.map(v=>v.toastId===t.toast.id?{...v,height:B}:v):x=[{toastId:t.toast.id,height:B,position:t.toast.position},...t.heights],l("update:heights",x)});function M(){p.value=!0,c.value=P.value;const o=t.heights.filter(u=>u.toastId!==t.toast.id);l("update:heights",o),setTimeout(()=>{l("removeToast",t.toast)},Re)}function ee(){var o,u;if(N.value||!D.value)return{};M(),(u=(o=t.toast).onDismiss)==null||u.call(o,t.toast)}function Ee(o){N.value||!D.value||($.value=new Date,c.value=P.value,o.target.setPointerCapture(o.pointerId),o.target.tagName!=="BUTTON"&&(w.value=!0,I.value={x:o.clientX,y:o.clientY}))}function Ce(){var x,T,v,O,A;if(S.value||!D)return;I.value=null;const o=Number(((x=k.value)==null?void 0:x.style.getPropertyValue("--swipe-amount").replace("px",""))||0),u=new Date().getTime()-((T=$.value)==null?void 0:T.getTime()),B=Math.abs(o)/u;if(Math.abs(o)>=Fe||B>.11){c.value=P.value,(O=(v=t.toast).onDismiss)==null||O.call(v,t.toast),M(),S.value=!0,f.value=!1;return}(A=k.value)==null||A.style.setProperty("--swipe-amount","0px"),w.value=!1}function Se(o){var T,v;if(!I.value||!D.value)return;const u=o.clientY-I.value.y,B=((T=window.getSelection())==null?void 0:T.toString().length)>0,x=H.value==="top"?Math.min(0,u):Math.max(0,u);Math.abs(x)>0&&(f.value=!0),!B&&((v=k.value)==null||v.style.setProperty("--swipe-amount",`${x}px`))}return e.watchEffect(o=>{if(t.toast.promise&&C.value==="loading"||t.toast.duration===1/0||t.toast.type==="loading")return;let u;const B=()=>{if(z.value<g.value){const T=new Date().getTime()-g.value;h.value=h.value-T}z.value=new Date().getTime()},x=()=>{h.value!==1/0&&(g.value=new Date().getTime(),u=setTimeout(()=>{var T,v;(v=(T=t.toast).onAutoClose)==null||v.call(T,t.toast),M()},h.value))};t.expanded||t.interacting||t.pauseWhenPageIsHidden&&X?B():x(),o(()=>{clearTimeout(u)})}),e.watch(()=>t.toast.delete,()=>{t.toast.delete&&M()},{deep:!0}),e.onMounted(()=>{if(r.value=!0,k.value){const o=k.value.getBoundingClientRect().height;m.value=o;const u=[{toastId:t.toast.id,height:o,position:t.toast.position},...t.heights];l("update:heights",u)}}),e.onBeforeUnmount(()=>{if(k.value){const o=t.heights.filter(u=>u.toastId!==t.toast.id);l("update:heights",o)}}),(o,u)=>{var B,x,T,v,O,A,te,oe,ae,se,ne,re,ie,le,de,ce,ue,fe,pe,me,he,ge,ve,ye,be,we,ke;return e.openBlock(),e.createElementBlock("li",{ref_key:"toastRef",ref:k,"aria-live":o.toast.important?"assertive":"polite","aria-atomic":"true",role:"status",tabindex:"0","data-sonner-toast":"true",class:e.normalizeClass(o.cn(t.class,Z.value,(B=o.classes)==null?void 0:B.toast,(x=o.toast.classes)==null?void 0:x.toast,(T=o.classes)==null?void 0:T[C.value],(O=(v=o.toast)==null?void 0:v.classes)==null?void 0:O[C.value])),"data-rich-colors":o.toast.richColors??o.defaultRichColors,"data-styled":!(o.toast.component||(A=o.toast)!=null&&A.unstyled||o.unstyled),"data-mounted":r.value,"data-promise":!!o.toast.promise,"data-removed":p.value,"data-visible":J.value,"data-y-position":H.value,"data-x-position":j.value,"data-index":o.index,"data-front":Q.value,"data-swiping":w.value,"data-dismissible":D.value,"data-type":C.value,"data-invert":q.value,"data-swipe-out":S.value,"data-expanded":!!(o.expanded||o.expandByDefault&&r.value),style:e.normalizeStyle({"--index":o.index,"--toasts-before":o.index,"--z-index":o.toasts.length-o.index,"--offset":`${p.value?c.value:P.value}px`,"--initial-height":o.expandByDefault?"auto":`${m.value}px`,...o.style,...e.unref(i)}),onPointerdown:Ee,onPointerup:Ce,onPointermove:Se},[y.value&&!o.toast.component?(e.openBlock(),e.createElementBlock("button",{key:0,"aria-label":o.closeButtonAriaLabel||"Close toast","data-disabled":N.value,"data-close-button":"true",class:e.normalizeClass(o.cn((te=o.classes)==null?void 0:te.closeButton,(ae=(oe=o.toast)==null?void 0:oe.classes)==null?void 0:ae.closeButton)),onClick:ee},[(se=o.icons)!=null&&se.close?(e.openBlock(),e.createBlock(e.resolveDynamicComponent((ne=o.icons)==null?void 0:ne.close),{key:0})):e.renderSlot(o.$slots,"close-icon",{key:1})],10,Ye)):e.createCommentVNode("",!0),o.toast.component?(e.openBlock(),e.createBlock(e.resolveDynamicComponent(o.toast.component),e.mergeProps({key:1},o.toast.componentProps,{onCloseToast:ee}),null,16)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:2},[C.value!=="default"||o.toast.icon||o.toast.promise?(e.openBlock(),e.createElementBlock("div",{key:0,"data-icon":"",class:e.normalizeClass(o.cn((re=o.classes)==null?void 0:re.icon,(le=(ie=o.toast)==null?void 0:ie.classes)==null?void 0:le.icon))},[(o.toast.promise||C.value==="loading")&&!o.toast.icon?e.renderSlot(o.$slots,"loading-icon",{key:0}):e.createCommentVNode("",!0),o.toast.icon?(e.openBlock(),e.createBlock(e.resolveDynamicComponent(o.toast.icon),{key:1})):(e.openBlock(),e.createElementBlock(e.Fragment,{key:2},[C.value==="success"?e.renderSlot(o.$slots,"success-icon",{key:0}):C.value==="error"?e.renderSlot(o.$slots,"error-icon",{key:1}):C.value==="warning"?e.renderSlot(o.$slots,"warning-icon",{key:2}):C.value==="info"?e.renderSlot(o.$slots,"info-icon",{key:3}):e.createCommentVNode("",!0)],64))],2)):e.createCommentVNode("",!0),e.createElementVNode("div",{"data-content":"",class:e.normalizeClass(o.cn((de=o.classes)==null?void 0:de.content,(ue=(ce=o.toast)==null?void 0:ce.classes)==null?void 0:ue.content))},[e.createElementVNode("div",{"data-title":"",class:e.normalizeClass(o.cn((fe=o.classes)==null?void 0:fe.title,(pe=o.toast.classes)==null?void 0:pe.title))},[W.value?(e.openBlock(),e.createBlock(e.resolveDynamicComponent(o.toast.title),e.normalizeProps(e.mergeProps({key:0},o.toast.componentProps)),null,16)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(o.toast.title),1)],64))],2),o.toast.description?(e.openBlock(),e.createElementBlock("div",{key:0,"data-description":"",class:e.normalizeClass(o.cn(o.descriptionClass,s.value,(me=o.classes)==null?void 0:me.description,(he=o.toast.classes)==null?void 0:he.description))},[U.value?(e.openBlock(),e.createBlock(e.resolveDynamicComponent(o.toast.description),e.normalizeProps(e.mergeProps({key:0},o.toast.componentProps)),null,16)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(o.toast.description),1)],64))],2)):e.createCommentVNode("",!0)],2),o.toast.cancel?(e.openBlock(),e.createElementBlock("button",{key:1,style:e.normalizeStyle(o.toast.cancelButtonStyle||o.cancelButtonStyle),class:e.normalizeClass(o.cn((ge=o.classes)==null?void 0:ge.cancelButton,(ve=o.toast.classes)==null?void 0:ve.cancelButton)),"data-button":"","data-cancel":"",onClick:u[0]||(u[0]=L=>{var Y,F;e.unref(G)(o.toast.cancel)&&D.value&&((F=(Y=o.toast.cancel).onClick)==null||F.call(Y,L),M())})},e.toDisplayString(e.unref(G)(o.toast.cancel)?(ye=o.toast.cancel)==null?void 0:ye.label:o.toast.cancel),7)):e.createCommentVNode("",!0),o.toast.action?(e.openBlock(),e.createElementBlock("button",{key:2,style:e.normalizeStyle(o.toast.actionButtonStyle||o.actionButtonStyle),class:e.normalizeClass(o.cn((be=o.classes)==null?void 0:be.actionButton,(we=o.toast.classes)==null?void 0:we.actionButton)),"data-button":"","data-action":"",onClick:u[1]||(u[1]=L=>{var Y,F;e.unref(G)(o.toast.action)&&(L.defaultPrevented||((F=(Y=o.toast.action).onClick)==null||F.call(Y,L),!L.defaultPrevented&&M()))})},e.toDisplayString(e.unref(G)(o.toast.action)?(ke=o.toast.action)==null?void 0:ke.label:o.toast.action),7)):e.createCommentVNode("",!0)],64))],46,Le)}}}),R=(n,a)=>{const t=n.__vccOpts||n;for(const[l,r]of a)t[l]=r;return t},We={},Ue={xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stoke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"};function Ke(n,a){return e.openBlock(),e.createElementBlock("svg",Ue,a[0]||(a[0]=[e.createElementVNode("line",{x1:"18",y1:"6",x2:"6",y2:"18"},null,-1),e.createElementVNode("line",{x1:"6",y1:"6",x2:"18",y2:"18"},null,-1)]))}const Xe=R(We,[["render",Ke]]),qe=["data-visible"],Ge={class:"sonner-spinner"},Qe=e.defineComponent({__name:"Loader",props:{visible:{type:Boolean}},setup(n){const a=Array(12).fill(0);return(t,l)=>(e.openBlock(),e.createElementBlock("div",{class:"sonner-loading-wrapper","data-visible":t.visible},[e.createElementVNode("div",Ge,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(a),r=>(e.openBlock(),e.createElementBlock("div",{key:`spinner-bar-${r}`,class:"sonner-loading-bar"}))),128))])],8,qe))}}),Je={},Ze={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"};function _e(n,a){return e.openBlock(),e.createElementBlock("svg",Ze,a[0]||(a[0]=[e.createElementVNode("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z","clip-rule":"evenodd"},null,-1)]))}const et=R(Je,[["render",_e]]),tt={},ot={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"};function at(n,a){return e.openBlock(),e.createElementBlock("svg",ot,a[0]||(a[0]=[e.createElementVNode("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z","clip-rule":"evenodd"},null,-1)]))}const st=R(tt,[["render",at]]),nt={},rt={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"};function it(n,a){return e.openBlock(),e.createElementBlock("svg",rt,a[0]||(a[0]=[e.createElementVNode("path",{"fill-rule":"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z","clip-rule":"evenodd"},null,-1)]))}const lt=R(nt,[["render",it]]),dt={},ct={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"};function ut(n,a){return e.openBlock(),e.createElementBlock("svg",ct,a[0]||(a[0]=[e.createElementVNode("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"},null,-1)]))}const ft=R(dt,[["render",ut]]),pt=["aria-label"],mt=["dir","data-theme","data-rich-colors","data-y-position","data-x-position","data-lifted"],ht=3,xe="32px",gt=356,vt=14,yt=typeof window<"u"&&typeof document<"u";function bt(...n){return n.filter(Boolean).join(" ")}const Te=e.defineComponent({name:"Toaster",inheritAttrs:!1,__name:"Toaster",props:{invert:{type:Boolean,default:!1},theme:{default:"light"},position:{default:"bottom-right"},hotkey:{default:()=>["altKey","KeyT"]},richColors:{type:Boolean,default:!1},expand:{type:Boolean,default:!1},duration:{},gap:{default:vt},visibleToasts:{default:ht},closeButton:{type:Boolean,default:!1},toastOptions:{default:()=>({})},class:{default:""},style:{default:()=>({})},offset:{default:xe},dir:{default:"auto"},icons:{},containerAriaLabel:{default:"Notifications"},pauseWhenPageIsHidden:{type:Boolean,default:!1},cn:{type:Function,default:bt}},setup(n){const a=n;function t(){if(typeof window>"u"||typeof document>"u")return"ltr";const s=document.documentElement.getAttribute("dir");return s==="auto"||!s?window.getComputedStyle(document.documentElement).direction:s}const l=e.useAttrs(),r=e.ref([]),p=e.computed(()=>(s,i)=>r.value.filter(d=>!d.position&&i===0||d.position===s)),w=e.computed(()=>{const s=r.value.filter(i=>i.position).map(i=>i.position);return s.length>0?Array.from(new Set([a.position].concat(s))):[a.position]}),S=e.ref([]),f=e.ref(!1),c=e.ref(!1),m=e.ref(a.theme!=="system"?a.theme:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),h=e.ref(null),$=e.ref(null),k=e.ref(!1),Q=a.hotkey.join("+").replace(/Key/g,"").replace(/Digit/g,"");function J(s){var i;(i=r.value.find(d=>d.id===s.id))!=null&&i.delete||E.dismiss(s.id),r.value=r.value.filter(({id:d})=>d!==s.id)}function C(s){var i,d;k.value&&!((d=(i=s.currentTarget)==null?void 0:i.contains)!=null&&d.call(i,s.relatedTarget))&&(k.value=!1,$.value&&($.value.focus({preventScroll:!0}),$.value=null))}function D(s){s.target instanceof HTMLElement&&s.target.dataset.dismissible==="false"||k.value||(k.value=!0,$.value=s.relatedTarget)}function Z(s){s.target&&s.target instanceof HTMLElement&&s.target.dataset.dismissible==="false"||(c.value=!0)}return e.watchEffect(s=>{const i=E.subscribe(d=>{if(d.dismiss){r.value=r.value.map(y=>y.id===d.id?{...y,delete:!0}:y);return}e.nextTick(()=>{const y=r.value.findIndex(g=>g.id===d.id);y!==-1?r.value=[...r.value.slice(0,y),{...r.value[y],...d},...r.value.slice(y+1)]:r.value=[d,...r.value]})});s(i)}),e.watch(()=>a.theme,s=>{if(s!=="system"){m.value=s;return}if(s==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?m.value="dark":m.value="light"),typeof window>"u")return;const i=window.matchMedia("(prefers-color-scheme: dark)");try{i.addEventListener("change",({matches:d})=>{d?m.value="dark":m.value="light"})}catch{i.addListener(({matches:y})=>{try{y?m.value="dark":m.value="light"}catch(g){console.error(g)}})}}),e.watchEffect(()=>{h.value&&$.value&&($.value.focus({preventScroll:!0}),$.value=null,k.value=!1)}),e.watchEffect(()=>{r.value.length<=1&&(f.value=!1)}),e.watchEffect(s=>{function i(d){const y=a.hotkey.every(I=>d[I]||d.code===I),g=Array.isArray(h.value)?h.value[0]:h.value;y&&(f.value=!0,g==null||g.focus());const z=document.activeElement===h.value||(g==null?void 0:g.contains(document.activeElement));d.code==="Escape"&&z&&(f.value=!1)}yt&&(document.addEventListener("keydown",i),s(()=>{document.removeEventListener("keydown",i)}))}),(s,i)=>(e.openBlock(),e.createElementBlock("section",{"aria-label":`${s.containerAriaLabel} ${e.unref(Q)}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(w.value,(d,y)=>{var g;return e.openBlock(),e.createElementBlock("ol",e.mergeProps({key:d,ref_for:!0,ref_key:"listRef",ref:h,"data-sonner-toaster":"",class:a.class,dir:s.dir==="auto"?t():s.dir,tabIndex:-1,"data-theme":s.theme,"data-rich-colors":s.richColors,"data-y-position":d.split("-")[0],"data-x-position":d.split("-")[1],"data-lifted":f.value&&r.value.length>1&&!s.expand,style:{"--front-toast-height":`${(g=S.value[0])==null?void 0:g.height}px`,"--offset":typeof s.offset=="number"?`${s.offset}px`:s.offset||xe,"--width":`${gt}px`,"--gap":`${s.gap}px`,...s.style,...e.unref(l).style}},s.$attrs,{onBlur:C,onFocus:D,onMouseenter:i[1]||(i[1]=()=>f.value=!0),onMousemove:i[2]||(i[2]=()=>f.value=!0),onMouseleave:i[3]||(i[3]=()=>{c.value||(f.value=!1)}),onPointerdown:Z,onPointerup:i[4]||(i[4]=()=>c.value=!1)}),[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(p.value(d,y),(z,I)=>{var V,H,j,W,U,K,X,q,N;return e.openBlock(),e.createBlock(je,{key:z.id,heights:S.value.filter(P=>P.position===z.position),icons:s.icons,index:I,toast:z,defaultRichColors:s.richColors,duration:((V=s.toastOptions)==null?void 0:V.duration)??s.duration,class:e.normalizeClass(((H=s.toastOptions)==null?void 0:H.class)??""),descriptionClass:(j=s.toastOptions)==null?void 0:j.descriptionClass,invert:s.invert,visibleToasts:s.visibleToasts,closeButton:((W=s.toastOptions)==null?void 0:W.closeButton)??s.closeButton,interacting:c.value,position:d,style:e.normalizeStyle((U=s.toastOptions)==null?void 0:U.style),unstyled:(K=s.toastOptions)==null?void 0:K.unstyled,classes:(X=s.toastOptions)==null?void 0:X.classes,cancelButtonStyle:(q=s.toastOptions)==null?void 0:q.cancelButtonStyle,actionButtonStyle:(N=s.toastOptions)==null?void 0:N.actionButtonStyle,toasts:r.value.filter(P=>P.position===z.position),expandByDefault:s.expand,gap:s.gap,expanded:f.value,pauseWhenPageIsHidden:s.pauseWhenPageIsHidden,cn:s.cn,"onUpdate:heights":i[0]||(i[0]=P=>{S.value=P}),onRemoveToast:J},{"close-icon":e.withCtx(()=>[e.renderSlot(s.$slots,"close-icon",{},()=>[e.createVNode(Xe)])]),"loading-icon":e.withCtx(()=>[e.renderSlot(s.$slots,"loading-icon",{},()=>[e.createVNode(Qe,{visible:z.type==="loading"},null,8,["visible"])])]),"success-icon":e.withCtx(()=>[e.renderSlot(s.$slots,"success-icon",{},()=>[e.createVNode(et)])]),"error-icon":e.withCtx(()=>[e.renderSlot(s.$slots,"error-icon",{},()=>[e.createVNode(ft)])]),"warning-icon":e.withCtx(()=>[e.renderSlot(s.$slots,"warning-icon",{},()=>[e.createVNode(lt)])]),"info-icon":e.withCtx(()=>[e.renderSlot(s.$slots,"info-icon",{},()=>[e.createVNode(st)])]),_:2},1032,["heights","icons","index","toast","defaultRichColors","duration","class","descriptionClass","invert","visibleToasts","closeButton","interacting","position","style","unstyled","classes","cancelButtonStyle","actionButtonStyle","toasts","expandByDefault","gap","expanded","pauseWhenPageIsHidden","cn"])}),128))],16,mt)}),128))],8,pt))}}),wt={install(n){n.component("Toaster",Te)}};exports.Toaster=Te;exports.default=wt;exports.toast=He;exports.useVueSonner=Ae;
//# sourceMappingURL=vue-sonner.cjs.map
