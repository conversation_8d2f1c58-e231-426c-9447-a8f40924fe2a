{"name": "map-visit", "description": "Map `visit` over an array of objects.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/map-visit", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> <<EMAIL>> (https://twitter.com/doowb)", "<PERSON> <<EMAIL>> (http://twitter.com/jonschlinkert)"], "repository": "jonschlinkert/map-visit", "bugs": {"url": "https://github.com/jonschlinkert/map-visit/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"object-visit": "^1.0.0"}, "devDependencies": {"clone-deep": "^0.2.4", "extend-shallow": "^2.0.1", "gulp-format-md": "^0.1.12", "lodash": "^4.17.4", "mocha": "^3.2.0"}, "keywords": ["array", "arrays", "function", "helper", "invoke", "key", "map", "method", "object", "objects", "value", "visit", "visitor"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["collection-visit", "object-visit"]}, "reflinks": ["verb", "verb-generate-readme"]}}