{"name": "postman-collection", "version": "4.5.0", "description": "Enables developers to use a unified Postman Collection format Object across projects", "author": "Postman Inc.", "license": "Apache-2.0", "main": "index.js", "homepage": "https://github.com/postmanlabs/postman-collection#readme", "bugs": {"url": "https://github.com/postmanlabs/postman-collection/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/postmanlabs/postman-collection.git"}, "keywords": ["postman", "collection", "sdk"], "scripts": {"build-docs": "node npm/build-docs.js", "build-types": "node npm/build-types.js", "codecov": "node npm/publish-coverage.js", "release": "node npm/create-release.js", "test": "npm run test-lint && npm run test-system && npm run test-unit && npm run test-browser", "test-benchmark": "node npm/test-benchmark.js", "test-browser": "node npm/test-browser.js", "test-lint": "node npm/test-lint.js", "test-system": "node npm/test-system.js", "test-unit": "nyc --nycrc-path=.nycrc.js node npm/test-unit.js"}, "dependencies": {"@faker-js/faker": "5.5.3", "file-type": "3.9.0", "http-reasons": "0.1.0", "iconv-lite": "0.6.3", "liquid-json": "0.3.1", "lodash": "4.17.21", "mime-format": "2.0.1", "mime-types": "2.1.35", "postman-url-encoder": "3.0.5", "semver": "7.6.3", "uuid": "8.3.2"}, "devDependencies": {"@postman/shipit": "^0.4.0", "async": "^3.2.5", "bipbip": "^0.4.2", "browserify": "^17.0.0", "btoa": "^1.2.1", "chai": "^4.3.10", "chalk": "^4.1.2", "dependency-check": "^4.1.0", "eslint": "^7.32.0", "eslint-plugin-jsdoc": "^36.1.1", "eslint-plugin-lodash": "^7.4.0", "eslint-plugin-mocha": "^10.5.0", "eslint-plugin-security": "^1.7.1", "js-yaml": "^4.1.0", "jsdoc": "^3.6.11", "karma": "^6.4.4", "karma-browserify": "^8.1.0", "karma-chrome-launcher": "^3.2.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "mocha": "^9.2.2", "nyc": "^15.1.0", "parse-gitignore": "^1.0.1", "postman-jsdoc-theme": "^0.0.3", "postman-request": "^2.88.1-postman.31", "recursive-readdir": "^2.2.3", "require-all": "^3.0.0", "shelljs": "^0.8.5", "strip-json-comments": "^3.1.1", "tsd-jsdoc": "^2.5.0"}, "engines": {"node": ">=10"}}