{"name": "npm-packlist", "version": "2.2.2", "description": "Get a list of the files to add from a folder into an npm package", "directories": {"test": "test"}, "main": "index.js", "dependencies": {"glob": "^7.1.6", "ignore-walk": "^3.0.3", "npm-bundled": "^1.1.1", "npm-normalize-package-bin": "^1.0.1"}, "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "files": ["bin/index.js", "index.js"], "devDependencies": {"eslint": "^7.25.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^5.0.0", "mutate-fs": "^2.1.1", "tap": "^15.0.6"}, "scripts": {"test": "tap", "posttest": "npm run lint", "snap": "tap", "postsnap": "npm run lintfix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "eslint": "eslint", "lint": "npm run eslint -- index.js bin/index.js \"test/**/*.js\"", "lintfix": "npm run lint -- --fix"}, "repository": {"type": "git", "url": "git+https://github.com/npm/npm-packlist.git"}, "tap": {"test-env": ["LC_ALL=sk"], "check-coverage": true, "nyc-arg": ["--include=index.js", "--include=bin/index.js"]}, "bin": {"npm-packlist": "bin/index.js"}, "engines": {"node": ">=10"}}