{"requireCurlyBraces": ["if", "else", "for", "while", "do", "try", "catch"], "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "return", "try", "catch"], "requireParenthesesAroundIIFE": true, "requireSpacesInFunctionExpression": {"beforeOpeningCurlyBrace": true}, "requireSpacesInAnonymousFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInNamedFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInFunctionDeclaration": {"beforeOpeningRoundBrace": true}, "disallowMultipleVarDecl": true, "requireSpacesInsideObjectBrackets": "all", "disallowSpaceAfterObjectKeys": true, "requireCommaBeforeLineBreak": true, "disallowSpaceBeforeBinaryOperators": [","], "disallowSpaceAfterPrefixUnaryOperators": ["++", "--", "+", "-", "~", "!"], "disallowSpaceBeforePostfixUnaryOperators": ["++", "--"], "requireSpaceBeforeBinaryOperators": ["+", "-", "/", "*", "=", "==", "===", "!=", "!==", ">", ">=", "<", "<="], "requireSpaceAfterBinaryOperators": [",", "+", "-", "/", "*", "=", "==", "===", "!=", "!==", ">", ">=", "<", "<="], "validateQuoteMarks": true, "validateIndentation": 4, "disallowTrailingWhitespace": true, "disallowKeywordsOnNewLine": ["else"], "requireCapitalizedConstructors": true, "safeContextKeyword": "self", "requireDotNotation": true, "disallowYodaConditions": true}