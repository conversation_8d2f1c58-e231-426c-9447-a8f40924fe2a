{"name": "pofile", "description": "Parse and serialize Gettext PO files.", "version": "1.1.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://savanne.be/"}, "contributors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "homepage": "http://github.com/rubenv/pofile", "license": "MIT", "repository": {"type": "git", "url": "http://github.com/rubenv/pofile.git"}, "main": "./lib/po", "types": "./pofile.d.ts", "keywords": ["i18n", "l10n", "gettext", "mo", "po"], "scripts": {"test": "grunt test", "prepublish": "grunt build"}, "directories": {"test": "test"}, "devDependencies": {"browserify": "~14.0.0", "coffeescript": "^2.6.1", "grunt": "~1.5.2", "grunt-browserify": "~5.0.0", "grunt-bump": "0.8.0", "grunt-contrib-clean": "~1.0.0", "grunt-contrib-jshint": "~1.1.0", "grunt-contrib-uglify": "~2.1.0", "grunt-contrib-watch": "~1.1.0", "grunt-jscs": "~3.0.1", "grunt-mocha-cli": "~7.0.0"}, "dependencies": {}}