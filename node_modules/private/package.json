{"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "private", "description": "Utility for associating truly private state with any JavaScript object", "keywords": ["private", "access control", "access modifiers", "encapsulation", "secret", "state", "privilege", "scope", "es5"], "version": "0.1.8", "homepage": "http://github.com/benjamn/private", "repository": {"type": "git", "url": "git://github.com/benjamn/private.git"}, "license": "MIT", "main": "private.js", "files": ["private.js"], "scripts": {"test": "mocha --reporter spec --full-trace test/run.js"}, "engines": {"node": ">= 0.6"}, "devDependencies": {"mocha": "^4.0.1"}}