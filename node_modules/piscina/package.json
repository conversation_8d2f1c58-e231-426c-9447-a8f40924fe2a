{"name": "piscina", "version": "4.6.1", "description": "A fast, efficient Node.js Worker Thread Pool implementation", "main": "./dist/main.js", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/esm-wrapper.mjs", "require": "./dist/main.js"}, "scripts": {"build": "tsc && gen-esm-wrapper . dist/esm-wrapper.mjs", "lint": "standardx \"**/*.{ts,mjs,js,cjs}\" | snazzy", "test": "c8 tap", "test:ci": "npm run lint && npm run build && npm run test:coverage", "test:coverage": "c8 --reporter=lcov tap --cov", "prepack": "npm run build", "bench": "npm run bench:taskqueue && npm run bench:piscina", "bench:piscina": "npm run benchmark:piscina-default &&npm run benchmark:piscina-fixed-queue && npm run benchmark:piscina-comparison", "bench:taskqueue": "npm run benchmark:queue-comparison", "benchmark:piscina-default": "node benchmark/simple-benchmark.js", "benchmark:piscina-fixed-queue": "node benchmark/simple-benchmark-fixed-queue.js", "benchmark:piscina-comparison": "node benchmark/piscina-queue-comparison.js", "benchmark:queue-comparison": "node benchmark/queue-comparison.js"}, "repository": {"type": "git", "url": "git+https://github.com/piscinajs/piscina.git"}, "keywords": ["fast", "worker threads", "thread pool", "wade wilson"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "devDependencies": {"@types/node": "^20.8.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "abort-controller": "^3.0.0", "c8": "^9.1.0", "concat-stream": "^2.0.0", "gen-esm-wrapper": "^1.1.1", "snazzy": "^9.0.0", "standardx": "^7.0.0", "tap": "^16.3.7", "tinybench": "^2.8.0", "ts-node": "^10.9.2", "typescript": "5.4.5"}, "optionalDependencies": {"nice-napi": "^1.0.2"}, "eslintConfig": {"rules": {"semi": ["error", "always"], "no-unused-vars": "off", "no-use-before-define": "off", "no-unreachable-loop": "off", "no-dupe-class-members": "off", "@typescript-eslint/no-unused-vars": "error"}, "globals": {"SharedArrayBuffer": true, "Atomics": true, "AbortController": true, "MessageChannel": true}}, "standardx": {"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint/eslint-plugin"]}, "bugs": {"url": "https://github.com/piscinajs/piscina/issues"}, "homepage": "https://github.com/piscinajs/piscina#readme", "directories": {"example": "examples", "test": "test"}}