{"name": "micromark-factory-mdx-expression", "version": "1.0.9", "description": "micromark factory to parse MDX expressions (found in JSX attributes, flow, text)", "license": "MIT", "keywords": ["micromark", "factory", "mdx", "expression"], "repository": "https://github.com/micromark/micromark-extension-mdx-expression/tree/main/packages/micromark-factory-mdx-expression", "bugs": "https://github.com/micromark/micromark-extension-mdx-expression/issues", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "dev/index.d.ts", "files": ["dev/", "index.d.ts", "index.js"], "exports": {"development": "./dev/index.js", "default": "./index.js"}, "dependencies": {"@types/estree": "^1.0.0", "micromark-util-character": "^1.0.0", "micromark-util-events-to-acorn": "^1.0.0", "micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.0", "unist-util-position-from-estree": "^1.0.0", "uvu": "^0.5.0", "vfile-message": "^3.0.0"}, "scripts": {"build": "micromark-build"}, "xo": false, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}}