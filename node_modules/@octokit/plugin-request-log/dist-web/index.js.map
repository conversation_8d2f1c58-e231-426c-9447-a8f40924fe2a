{"version": 3, "file": "index.js", "sources": ["../dist-src/version.js", "../dist-src/index.js"], "sourcesContent": ["export const VERSION = \"1.0.4\";\n", "import { VERSION } from \"./version\";\n/**\n * @param octokit Octokit instance\n * @param options Options passed to Octokit constructor\n */\nexport function requestLog(octokit) {\n    octokit.hook.wrap(\"request\", (request, options) => {\n        octokit.log.debug(\"request\", options);\n        const start = Date.now();\n        const requestOptions = octokit.request.endpoint.parse(options);\n        const path = requestOptions.url.replace(options.baseUrl, \"\");\n        return request(options)\n            .then((response) => {\n            octokit.log.info(`${requestOptions.method} ${path} - ${response.status} in ${Date.now() - start}ms`);\n            return response;\n        })\n            .catch((error) => {\n            octokit.log.info(`${requestOptions.method} ${path} - ${error.status} in ${Date.now() - start}ms`);\n            throw error;\n        });\n    });\n}\nrequestLog.VERSION = VERSION;\n"], "names": [], "mappings": "AAAO,MAAM,OAAO,GAAG,mBAAmB;;ACC1C;AACA;AACA;AACA;AACA,AAAO,SAAS,UAAU,CAAC,OAAO,EAAE;AACpC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK;AACvD,QAAQ,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC9C,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACjC,QAAQ,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACvE,QAAQ,MAAM,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AACrE,QAAQ,OAAO,OAAO,CAAC,OAAO,CAAC;AAC/B,aAAa,IAAI,CAAC,CAAC,QAAQ,KAAK;AAChC,YAAY,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AACjH,YAAY,OAAO,QAAQ,CAAC;AAC5B,SAAS,CAAC;AACV,aAAa,KAAK,CAAC,CAAC,KAAK,KAAK;AAC9B,YAAY,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9G,YAAY,MAAM,KAAK,CAAC;AACxB,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP,CAAC;AACD,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;;;;"}