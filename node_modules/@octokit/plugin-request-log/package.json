{"name": "@octokit/plugin-request-log", "description": "Log all requests and request errors", "version": "1.0.4", "license": "MIT", "files": ["dist-*/", "bin/"], "pika": true, "sideEffects": false, "keywords": ["github", "api", "sdk", "toolkit"], "repository": "github:octokit/plugin-request-log.js", "dependencies": {}, "peerDependencies": {"@octokit/core": ">=3"}, "devDependencies": {"@octokit/core": "^3.0.0", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.9.0", "@pika/plugin-build-web": "^0.9.0", "@pika/plugin-ts-standard-pkg": "^0.9.0", "@types/fetch-mock": "^7.3.2", "@types/jest": "^26.0.0", "@types/node": "^14.0.4", "fetch-mock": "^9.0.0", "jest": "^27.0.0", "prettier": "2.3.1", "semantic-release": "^17.0.0", "semantic-release-plugin-update-version-in-files": "^1.0.0", "ts-jest": "^27.0.0-next.12", "typescript": "^4.0.0"}, "publishConfig": {"access": "public"}, "source": "dist-src/index.js", "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js"}