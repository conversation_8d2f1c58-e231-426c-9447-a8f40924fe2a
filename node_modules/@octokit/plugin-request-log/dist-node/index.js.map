{"version": 3, "file": "index.js", "sources": ["../dist-src/version.js", "../dist-src/index.js"], "sourcesContent": ["export const VERSION = \"1.0.4\";\n", "import { VERSION } from \"./version\";\n/**\n * @param octokit Octokit instance\n * @param options Options passed to Octokit constructor\n */\nexport function requestLog(octokit) {\n    octokit.hook.wrap(\"request\", (request, options) => {\n        octokit.log.debug(\"request\", options);\n        const start = Date.now();\n        const requestOptions = octokit.request.endpoint.parse(options);\n        const path = requestOptions.url.replace(options.baseUrl, \"\");\n        return request(options)\n            .then((response) => {\n            octokit.log.info(`${requestOptions.method} ${path} - ${response.status} in ${Date.now() - start}ms`);\n            return response;\n        })\n            .catch((error) => {\n            octokit.log.info(`${requestOptions.method} ${path} - ${error.status} in ${Date.now() - start}ms`);\n            throw error;\n        });\n    });\n}\nrequestLog.VERSION = VERSION;\n"], "names": ["VERSION", "requestLog", "octokit", "hook", "wrap", "request", "options", "log", "debug", "start", "Date", "now", "requestOptions", "endpoint", "parse", "path", "url", "replace", "baseUrl", "then", "response", "info", "method", "status", "catch", "error"], "mappings": ";;;;AAAO,MAAMA,OAAO,GAAG,mBAAhB;;ACCP;AACA;AACA;AACA;;AACA,AAAO,SAASC,UAAT,CAAoBC,OAApB,EAA6B;AAChCA,EAAAA,OAAO,CAACC,IAAR,CAAaC,IAAb,CAAkB,SAAlB,EAA6B,CAACC,OAAD,EAAUC,OAAV,KAAsB;AAC/CJ,IAAAA,OAAO,CAACK,GAAR,CAAYC,KAAZ,CAAkB,SAAlB,EAA6BF,OAA7B;AACA,UAAMG,KAAK,GAAGC,IAAI,CAACC,GAAL,EAAd;AACA,UAAMC,cAAc,GAAGV,OAAO,CAACG,OAAR,CAAgBQ,QAAhB,CAAyBC,KAAzB,CAA+BR,OAA/B,CAAvB;AACA,UAAMS,IAAI,GAAGH,cAAc,CAACI,GAAf,CAAmBC,OAAnB,CAA2BX,OAAO,CAACY,OAAnC,EAA4C,EAA5C,CAAb;AACA,WAAOb,OAAO,CAACC,OAAD,CAAP,CACFa,IADE,CACIC,QAAD,IAAc;AACpBlB,MAAAA,OAAO,CAACK,GAAR,CAAYc,IAAZ,CAAkB,GAAET,cAAc,CAACU,MAAO,IAAGP,IAAK,MAAKK,QAAQ,CAACG,MAAO,OAAMb,IAAI,CAACC,GAAL,KAAaF,KAAM,IAAhG;AACA,aAAOW,QAAP;AACH,KAJM,EAKFI,KALE,CAKKC,KAAD,IAAW;AAClBvB,MAAAA,OAAO,CAACK,GAAR,CAAYc,IAAZ,CAAkB,GAAET,cAAc,CAACU,MAAO,IAAGP,IAAK,MAAKU,KAAK,CAACF,MAAO,OAAMb,IAAI,CAACC,GAAL,KAAaF,KAAM,IAA7F;AACA,YAAMgB,KAAN;AACH,KARM,CAAP;AASH,GAdD;AAeH;AACDxB,UAAU,CAACD,OAAX,GAAqBA,OAArB;;;;"}