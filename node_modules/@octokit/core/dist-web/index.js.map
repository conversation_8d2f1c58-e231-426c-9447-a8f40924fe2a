{"version": 3, "file": "index.js", "sources": ["../dist-src/version.js", "../dist-src/index.js"], "sourcesContent": ["export const VERSION = \"3.6.0\";\n", "import { getUserAgent } from \"universal-user-agent\";\nimport { Collection } from \"before-after-hook\";\nimport { request } from \"@octokit/request\";\nimport { withCustomRequest } from \"@octokit/graphql\";\nimport { createTokenAuth } from \"@octokit/auth-token\";\nimport { VERSION } from \"./version\";\nexport class Octokit {\n    constructor(options = {}) {\n        const hook = new Collection();\n        const requestDefaults = {\n            baseUrl: request.endpoint.DEFAULTS.baseUrl,\n            headers: {},\n            request: Object.assign({}, options.request, {\n                // @ts-ignore internal usage only, no need to type\n                hook: hook.bind(null, \"request\"),\n            }),\n            mediaType: {\n                previews: [],\n                format: \"\",\n            },\n        };\n        // prepend default user agent with `options.userAgent` if set\n        requestDefaults.headers[\"user-agent\"] = [\n            options.userAgent,\n            `octokit-core.js/${VERSION} ${getUserAgent()}`,\n        ]\n            .filter(Boolean)\n            .join(\" \");\n        if (options.baseUrl) {\n            requestDefaults.baseUrl = options.baseUrl;\n        }\n        if (options.previews) {\n            requestDefaults.mediaType.previews = options.previews;\n        }\n        if (options.timeZone) {\n            requestDefaults.headers[\"time-zone\"] = options.timeZone;\n        }\n        this.request = request.defaults(requestDefaults);\n        this.graphql = withCustomRequest(this.request).defaults(requestDefaults);\n        this.log = Object.assign({\n            debug: () => { },\n            info: () => { },\n            warn: console.warn.bind(console),\n            error: console.error.bind(console),\n        }, options.log);\n        this.hook = hook;\n        // (1) If neither `options.authStrategy` nor `options.auth` are set, the `octokit` instance\n        //     is unauthenticated. The `this.auth()` method is a no-op and no request hook is registered.\n        // (2) If only `options.auth` is set, use the default token authentication strategy.\n        // (3) If `options.authStrategy` is set then use it and pass in `options.auth`. Always pass own request as many strategies accept a custom request instance.\n        // TODO: type `options.auth` based on `options.authStrategy`.\n        if (!options.authStrategy) {\n            if (!options.auth) {\n                // (1)\n                this.auth = async () => ({\n                    type: \"unauthenticated\",\n                });\n            }\n            else {\n                // (2)\n                const auth = createTokenAuth(options.auth);\n                // @ts-ignore  ¯\\_(ツ)_/¯\n                hook.wrap(\"request\", auth.hook);\n                this.auth = auth;\n            }\n        }\n        else {\n            const { authStrategy, ...otherOptions } = options;\n            const auth = authStrategy(Object.assign({\n                request: this.request,\n                log: this.log,\n                // we pass the current octokit instance as well as its constructor options\n                // to allow for authentication strategies that return a new octokit instance\n                // that shares the same internal state as the current one. The original\n                // requirement for this was the \"event-octokit\" authentication strategy\n                // of https://github.com/probot/octokit-auth-probot.\n                octokit: this,\n                octokitOptions: otherOptions,\n            }, options.auth));\n            // @ts-ignore  ¯\\_(ツ)_/¯\n            hook.wrap(\"request\", auth.hook);\n            this.auth = auth;\n        }\n        // apply plugins\n        // https://stackoverflow.com/a/16345172\n        const classConstructor = this.constructor;\n        classConstructor.plugins.forEach((plugin) => {\n            Object.assign(this, plugin(this, options));\n        });\n    }\n    static defaults(defaults) {\n        const OctokitWithDefaults = class extends this {\n            constructor(...args) {\n                const options = args[0] || {};\n                if (typeof defaults === \"function\") {\n                    super(defaults(options));\n                    return;\n                }\n                super(Object.assign({}, defaults, options, options.userAgent && defaults.userAgent\n                    ? {\n                        userAgent: `${options.userAgent} ${defaults.userAgent}`,\n                    }\n                    : null));\n            }\n        };\n        return OctokitWithDefaults;\n    }\n    /**\n     * Attach a plugin (or many) to your Octokit instance.\n     *\n     * @example\n     * const API = Octokit.plugin(plugin1, plugin2, plugin3, ...)\n     */\n    static plugin(...newPlugins) {\n        var _a;\n        const currentPlugins = this.plugins;\n        const NewOctokit = (_a = class extends this {\n            },\n            _a.plugins = currentPlugins.concat(newPlugins.filter((plugin) => !currentPlugins.includes(plugin))),\n            _a);\n        return NewOctokit;\n    }\n}\nOctokit.VERSION = VERSION;\nOctokit.plugins = [];\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,OAAO,GAAG,mBAAmB;;ACMnC,MAAM,OAAO,CAAC;AACrB,IAAI,WAAW,CAAC,OAAO,GAAG,EAAE,EAAE;AAC9B,QAAQ,MAAM,IAAI,GAAG,IAAI,UAAU,EAAE,CAAC;AACtC,QAAQ,MAAM,eAAe,GAAG;AAChC,YAAY,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO;AACtD,YAAY,OAAO,EAAE,EAAE;AACvB,YAAY,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,EAAE;AACxD;AACA,gBAAgB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC;AAChD,aAAa,CAAC;AACd,YAAY,SAAS,EAAE;AACvB,gBAAgB,QAAQ,EAAE,EAAE;AAC5B,gBAAgB,MAAM,EAAE,EAAE;AAC1B,aAAa;AACb,SAAS,CAAC;AACV;AACA,QAAQ,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG;AAChD,YAAY,OAAO,CAAC,SAAS;AAC7B,YAAY,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC;AAC1D,SAAS;AACT,aAAa,MAAM,CAAC,OAAO,CAAC;AAC5B,aAAa,IAAI,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,OAAO,CAAC,OAAO,EAAE;AAC7B,YAAY,eAAe,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AACtD,SAAS;AACT,QAAQ,IAAI,OAAO,CAAC,QAAQ,EAAE;AAC9B,YAAY,eAAe,CAAC,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AAClE,SAAS;AACT,QAAQ,IAAI,OAAO,CAAC,QAAQ,EAAE;AAC9B,YAAY,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC;AACpE,SAAS;AACT,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;AACzD,QAAQ,IAAI,CAAC,OAAO,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;AACjF,QAAQ,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;AACjC,YAAY,KAAK,EAAE,MAAM,GAAG;AAC5B,YAAY,IAAI,EAAE,MAAM,GAAG;AAC3B,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5C,YAAY,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;AAC9C,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AACxB,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;AACnC,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AAC/B;AACA,gBAAgB,IAAI,CAAC,IAAI,GAAG,aAAa;AACzC,oBAAoB,IAAI,EAAE,iBAAiB;AAC3C,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,iBAAiB;AACjB;AACA,gBAAgB,MAAM,IAAI,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC3D;AACA,gBAAgB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAChD,gBAAgB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjC,aAAa;AACb,SAAS;AACT,aAAa;AACb,YAAY,MAAM,EAAE,YAAY,EAAE,GAAG,YAAY,EAAE,GAAG,OAAO,CAAC;AAC9D,YAAY,MAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;AACpD,gBAAgB,OAAO,EAAE,IAAI,CAAC,OAAO;AACrC,gBAAgB,GAAG,EAAE,IAAI,CAAC,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA,gBAAgB,OAAO,EAAE,IAAI;AAC7B,gBAAgB,cAAc,EAAE,YAAY;AAC5C,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9B;AACA,YAAY,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5C,YAAY,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AAC7B,SAAS;AACT;AACA;AACA,QAAQ,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC;AAClD,QAAQ,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACrD,YAAY,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;AACvD,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC,QAAQ,EAAE;AAC9B,QAAQ,MAAM,mBAAmB,GAAG,cAAc,IAAI,CAAC;AACvD,YAAY,WAAW,CAAC,GAAG,IAAI,EAAE;AACjC,gBAAgB,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAC9C,gBAAgB,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;AACpD,oBAAoB,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;AAC7C,oBAAoB,OAAO;AAC3B,iBAAiB;AACjB,gBAAgB,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS;AAClG,sBAAsB;AACtB,wBAAwB,SAAS,EAAE,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;AAC/E,qBAAqB;AACrB,sBAAsB,IAAI,CAAC,CAAC,CAAC;AAC7B,aAAa;AACb,SAAS,CAAC;AACV,QAAQ,OAAO,mBAAmB,CAAC;AACnC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,MAAM,CAAC,GAAG,UAAU,EAAE;AACjC,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5C,QAAQ,MAAM,UAAU,IAAI,EAAE,GAAG,cAAc,IAAI,CAAC;AACpD,aAAa;AACb,YAAY,EAAE,CAAC,OAAO,GAAG,cAAc,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAC/G,YAAY,EAAE,CAAC,CAAC;AAChB,QAAQ,OAAO,UAAU,CAAC;AAC1B,KAAK;AACL,CAAC;AACD,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AAC1B,OAAO,CAAC,OAAO,GAAG,EAAE,CAAC;;;;"}