{"version": 3, "file": "index.js", "sources": ["../dist-src/auth.js", "../dist-src/with-authorization-prefix.js", "../dist-src/hook.js", "../dist-src/index.js"], "sourcesContent": ["const REGEX_IS_INSTALLATION_LEGACY = /^v1\\./;\nconst REGEX_IS_INSTALLATION = /^ghs_/;\nconst REGEX_IS_USER_TO_SERVER = /^ghu_/;\nexport async function auth(token) {\n    const isApp = token.split(/\\./).length === 3;\n    const isInstallation = REGEX_IS_INSTALLATION_LEGACY.test(token) ||\n        REGEX_IS_INSTALLATION.test(token);\n    const isUserToServer = REGEX_IS_USER_TO_SERVER.test(token);\n    const tokenType = isApp\n        ? \"app\"\n        : isInstallation\n            ? \"installation\"\n            : isUserToServer\n                ? \"user-to-server\"\n                : \"oauth\";\n    return {\n        type: \"token\",\n        token: token,\n        tokenType,\n    };\n}\n", "/**\n * Prefix token for usage in the Authorization header\n *\n * @param token OAuth token or JSON Web Token\n */\nexport function withAuthorizationPrefix(token) {\n    if (token.split(/\\./).length === 3) {\n        return `bearer ${token}`;\n    }\n    return `token ${token}`;\n}\n", "import { withAuthorizationPrefix } from \"./with-authorization-prefix\";\nexport async function hook(token, request, route, parameters) {\n    const endpoint = request.endpoint.merge(route, parameters);\n    endpoint.headers.authorization = withAuthorizationPrefix(token);\n    return request(endpoint);\n}\n", "import { auth } from \"./auth\";\nimport { hook } from \"./hook\";\nexport const createTokenAuth = function createTokenAuth(token) {\n    if (!token) {\n        throw new Error(\"[@octokit/auth-token] No token passed to createTokenAuth\");\n    }\n    if (typeof token !== \"string\") {\n        throw new Error(\"[@octokit/auth-token] Token passed to createTokenAuth is not a string\");\n    }\n    token = token.replace(/^(token|bearer) +/i, \"\");\n    return Object.assign(auth.bind(null, token), {\n        hook: hook.bind(null, token),\n    });\n};\n"], "names": [], "mappings": "AAAA,MAAM,4BAA4B,GAAG,OAAO,CAAC;AAC7C,MAAM,qBAAqB,GAAG,OAAO,CAAC;AACtC,MAAM,uBAAuB,GAAG,OAAO,CAAC;AACjC,eAAe,IAAI,CAAC,KAAK,EAAE;AAClC,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;AACjD,IAAI,MAAM,cAAc,GAAG,4BAA4B,CAAC,IAAI,CAAC,KAAK,CAAC;AACnE,QAAQ,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1C,IAAI,MAAM,cAAc,GAAG,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/D,IAAI,MAAM,SAAS,GAAG,KAAK;AAC3B,UAAU,KAAK;AACf,UAAU,cAAc;AACxB,cAAc,cAAc;AAC5B,cAAc,cAAc;AAC5B,kBAAkB,gBAAgB;AAClC,kBAAkB,OAAO,CAAC;AAC1B,IAAI,OAAO;AACX,QAAQ,IAAI,EAAE,OAAO;AACrB,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,SAAS;AACjB,KAAK,CAAC;AACN;;ACpBA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,uBAAuB,CAAC,KAAK,EAAE;AAC/C,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AACxC,QAAQ,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AAC5B,CAAC;;ACTM,eAAe,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE;AAC9D,IAAI,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AAC/D,IAAI,QAAQ,CAAC,OAAO,CAAC,aAAa,GAAG,uBAAuB,CAAC,KAAK,CAAC,CAAC;AACpE,IAAI,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC7B,CAAC;;ACHW,MAAC,eAAe,GAAG,SAAS,eAAe,CAAC,KAAK,EAAE;AAC/D,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,QAAQ,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;AACpF,KAAK;AACL,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACnC,QAAQ,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;AACjG,KAAK;AACL,IAAI,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;AACpD,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;AACjD,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;AACpC,KAAK,CAAC,CAAC;AACP,CAAC;;;;"}