{"name": "@octokit/auth-token", "description": "GitHub API token authentication for browsers and Node.js", "version": "2.5.0", "license": "MIT", "files": ["dist-*/", "bin/"], "pika": true, "sideEffects": false, "keywords": ["github", "octokit", "authentication", "api"], "repository": "github:octokit/auth-token.js", "dependencies": {"@octokit/types": "^6.0.3"}, "devDependencies": {"@octokit/core": "^3.0.0", "@octokit/request": "^5.3.0", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.9.0", "@pika/plugin-build-web": "^0.9.0", "@pika/plugin-ts-standard-pkg": "^0.9.0", "@types/fetch-mock": "^7.3.1", "@types/jest": "^27.0.0", "fetch-mock": "^9.0.0", "jest": "^27.0.0", "prettier": "2.4.1", "semantic-release": "^17.0.0", "ts-jest": "^27.0.0-next.12", "typescript": "^4.0.0"}, "publishConfig": {"access": "public"}, "source": "dist-src/index.js", "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js"}