{"version": 3, "file": "index.js", "sources": ["../dist-src/auth.js", "../dist-src/with-authorization-prefix.js", "../dist-src/hook.js", "../dist-src/index.js"], "sourcesContent": ["const REGEX_IS_INSTALLATION_LEGACY = /^v1\\./;\nconst REGEX_IS_INSTALLATION = /^ghs_/;\nconst REGEX_IS_USER_TO_SERVER = /^ghu_/;\nexport async function auth(token) {\n    const isApp = token.split(/\\./).length === 3;\n    const isInstallation = REGEX_IS_INSTALLATION_LEGACY.test(token) ||\n        REGEX_IS_INSTALLATION.test(token);\n    const isUserToServer = REGEX_IS_USER_TO_SERVER.test(token);\n    const tokenType = isApp\n        ? \"app\"\n        : isInstallation\n            ? \"installation\"\n            : isUserToServer\n                ? \"user-to-server\"\n                : \"oauth\";\n    return {\n        type: \"token\",\n        token: token,\n        tokenType,\n    };\n}\n", "/**\n * Prefix token for usage in the Authorization header\n *\n * @param token OAuth token or JSON Web Token\n */\nexport function withAuthorizationPrefix(token) {\n    if (token.split(/\\./).length === 3) {\n        return `bearer ${token}`;\n    }\n    return `token ${token}`;\n}\n", "import { withAuthorizationPrefix } from \"./with-authorization-prefix\";\nexport async function hook(token, request, route, parameters) {\n    const endpoint = request.endpoint.merge(route, parameters);\n    endpoint.headers.authorization = withAuthorizationPrefix(token);\n    return request(endpoint);\n}\n", "import { auth } from \"./auth\";\nimport { hook } from \"./hook\";\nexport const createTokenAuth = function createTokenAuth(token) {\n    if (!token) {\n        throw new Error(\"[@octokit/auth-token] No token passed to createTokenAuth\");\n    }\n    if (typeof token !== \"string\") {\n        throw new Error(\"[@octokit/auth-token] Token passed to createTokenAuth is not a string\");\n    }\n    token = token.replace(/^(token|bearer) +/i, \"\");\n    return Object.assign(auth.bind(null, token), {\n        hook: hook.bind(null, token),\n    });\n};\n"], "names": ["REGEX_IS_INSTALLATION_LEGACY", "REGEX_IS_INSTALLATION", "REGEX_IS_USER_TO_SERVER", "auth", "token", "isApp", "split", "length", "isInstallation", "test", "isUserToServer", "tokenType", "type", "withAuthorizationPrefix", "hook", "request", "route", "parameters", "endpoint", "merge", "headers", "authorization", "createTokenAuth", "Error", "replace", "Object", "assign", "bind"], "mappings": ";;;;AAAA,MAAMA,4BAA4B,GAAG,OAArC;AACA,MAAMC,qBAAqB,GAAG,OAA9B;AACA,MAAMC,uBAAuB,GAAG,OAAhC;AACO,eAAeC,IAAf,CAAoBC,KAApB,EAA2B;AAC9B,QAAMC,KAAK,GAAGD,KAAK,CAACE,KAAN,CAAY,IAAZ,EAAkBC,MAAlB,KAA6B,CAA3C;AACA,QAAMC,cAAc,GAAGR,4BAA4B,CAACS,IAA7B,CAAkCL,KAAlC,KACnBH,qBAAqB,CAACQ,IAAtB,CAA2BL,KAA3B,CADJ;AAEA,QAAMM,cAAc,GAAGR,uBAAuB,CAACO,IAAxB,CAA6BL,KAA7B,CAAvB;AACA,QAAMO,SAAS,GAAGN,KAAK,GACjB,KADiB,GAEjBG,cAAc,GACV,cADU,GAEVE,cAAc,GACV,gBADU,GAEV,OANd;AAOA,SAAO;AACHE,IAAAA,IAAI,EAAE,OADH;AAEHR,IAAAA,KAAK,EAAEA,KAFJ;AAGHO,IAAAA;AAHG,GAAP;AAKH;;ACpBD;AACA;AACA;AACA;AACA;AACA,AAAO,SAASE,uBAAT,CAAiCT,KAAjC,EAAwC;AAC3C,MAAIA,KAAK,CAACE,KAAN,CAAY,IAAZ,EAAkBC,MAAlB,KAA6B,CAAjC,EAAoC;AAChC,WAAQ,UAASH,KAAM,EAAvB;AACH;;AACD,SAAQ,SAAQA,KAAM,EAAtB;AACH;;ACTM,eAAeU,IAAf,CAAoBV,KAApB,EAA2BW,OAA3B,EAAoCC,KAApC,EAA2CC,UAA3C,EAAuD;AAC1D,QAAMC,QAAQ,GAAGH,OAAO,CAACG,QAAR,CAAiBC,KAAjB,CAAuBH,KAAvB,EAA8BC,UAA9B,CAAjB;AACAC,EAAAA,QAAQ,CAACE,OAAT,CAAiBC,aAAjB,GAAiCR,uBAAuB,CAACT,KAAD,CAAxD;AACA,SAAOW,OAAO,CAACG,QAAD,CAAd;AACH;;MCHYI,eAAe,GAAG,SAASA,eAAT,CAAyBlB,KAAzB,EAAgC;AAC3D,MAAI,CAACA,KAAL,EAAY;AACR,UAAM,IAAImB,KAAJ,CAAU,0DAAV,CAAN;AACH;;AACD,MAAI,OAAOnB,KAAP,KAAiB,QAArB,EAA+B;AAC3B,UAAM,IAAImB,KAAJ,CAAU,uEAAV,CAAN;AACH;;AACDnB,EAAAA,KAAK,GAAGA,KAAK,CAACoB,OAAN,CAAc,oBAAd,EAAoC,EAApC,CAAR;AACA,SAAOC,MAAM,CAACC,MAAP,CAAcvB,IAAI,CAACwB,IAAL,CAAU,IAAV,EAAgBvB,KAAhB,CAAd,EAAsC;AACzCU,IAAAA,IAAI,EAAEA,IAAI,CAACa,IAAL,CAAU,IAAV,EAAgBvB,KAAhB;AADmC,GAAtC,CAAP;AAGH,CAXM;;;;"}