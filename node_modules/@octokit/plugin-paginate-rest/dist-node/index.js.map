{"version": 3, "file": "index.js", "sources": ["../dist-src/version.js", "../dist-src/normalize-paginated-list-response.js", "../dist-src/iterator.js", "../dist-src/paginate.js", "../dist-src/compose-paginate.js", "../dist-src/generated/paginating-endpoints.js", "../dist-src/paginating-endpoints.js", "../dist-src/index.js"], "sourcesContent": ["export const VERSION = \"2.21.3\";\n", "/**\n * Some “list” response that can be paginated have a different response structure\n *\n * They have a `total_count` key in the response (search also has `incomplete_results`,\n * /installation/repositories also has `repository_selection`), as well as a key with\n * the list of the items which name varies from endpoint to endpoint.\n *\n * <PERSON><PERSON><PERSON> normalizes these responses so that paginated results are always returned following\n * the same structure. One challenge is that if the list response has only one page, no Link\n * header is provided, so this header alone is not sufficient to check wether a response is\n * paginated or not.\n *\n * We check if a \"total_count\" key is present in the response data, but also make sure that\n * a \"url\" property is not, as the \"Get the combined status for a specific ref\" endpoint would\n * otherwise match: https://developer.github.com/v3/repos/statuses/#get-the-combined-status-for-a-specific-ref\n */\nexport function normalizePaginatedListResponse(response) {\n    // endpoints can respond with 204 if repository is empty\n    if (!response.data) {\n        return {\n            ...response,\n            data: [],\n        };\n    }\n    const responseNeedsNormalization = \"total_count\" in response.data && !(\"url\" in response.data);\n    if (!responseNeedsNormalization)\n        return response;\n    // keep the additional properties intact as there is currently no other way\n    // to retrieve the same information.\n    const incompleteResults = response.data.incomplete_results;\n    const repositorySelection = response.data.repository_selection;\n    const totalCount = response.data.total_count;\n    delete response.data.incomplete_results;\n    delete response.data.repository_selection;\n    delete response.data.total_count;\n    const namespaceKey = Object.keys(response.data)[0];\n    const data = response.data[namespaceKey];\n    response.data = data;\n    if (typeof incompleteResults !== \"undefined\") {\n        response.data.incomplete_results = incompleteResults;\n    }\n    if (typeof repositorySelection !== \"undefined\") {\n        response.data.repository_selection = repositorySelection;\n    }\n    response.data.total_count = totalCount;\n    return response;\n}\n", "import { normalizePaginatedListResponse } from \"./normalize-paginated-list-response\";\nexport function iterator(octokit, route, parameters) {\n    const options = typeof route === \"function\"\n        ? route.endpoint(parameters)\n        : octokit.request.endpoint(route, parameters);\n    const requestMethod = typeof route === \"function\" ? route : octokit.request;\n    const method = options.method;\n    const headers = options.headers;\n    let url = options.url;\n    return {\n        [Symbol.asyncIterator]: () => ({\n            async next() {\n                if (!url)\n                    return { done: true };\n                try {\n                    const response = await requestMethod({ method, url, headers });\n                    const normalizedResponse = normalizePaginatedListResponse(response);\n                    // `response.headers.link` format:\n                    // '<https://api.github.com/users/aseemk/followers?page=2>; rel=\"next\", <https://api.github.com/users/aseemk/followers?page=2>; rel=\"last\"'\n                    // sets `url` to undefined if \"next\" URL is not present or `link` header is not set\n                    url = ((normalizedResponse.headers.link || \"\").match(/<([^>]+)>;\\s*rel=\"next\"/) || [])[1];\n                    return { value: normalizedResponse };\n                }\n                catch (error) {\n                    if (error.status !== 409)\n                        throw error;\n                    url = \"\";\n                    return {\n                        value: {\n                            status: 200,\n                            headers: {},\n                            data: [],\n                        },\n                    };\n                }\n            },\n        }),\n    };\n}\n", "import { iterator } from \"./iterator\";\nexport function paginate(octokit, route, parameters, mapFn) {\n    if (typeof parameters === \"function\") {\n        mapFn = parameters;\n        parameters = undefined;\n    }\n    return gather(octokit, [], iterator(octokit, route, parameters)[Symbol.asyncIterator](), mapFn);\n}\nfunction gather(octokit, results, iterator, mapFn) {\n    return iterator.next().then((result) => {\n        if (result.done) {\n            return results;\n        }\n        let earlyExit = false;\n        function done() {\n            earlyExit = true;\n        }\n        results = results.concat(mapFn ? mapFn(result.value, done) : result.value.data);\n        if (earlyExit) {\n            return results;\n        }\n        return gather(octokit, results, iterator, mapFn);\n    });\n}\n", "import { paginate } from \"./paginate\";\nimport { iterator } from \"./iterator\";\nexport const composePaginateRest = Object.assign(paginate, {\n    iterator,\n});\n", "export const paginatingEndpoints = [\n    \"GET /app/hook/deliveries\",\n    \"GET /app/installations\",\n    \"GET /applications/grants\",\n    \"GET /authorizations\",\n    \"GET /enterprises/{enterprise}/actions/permissions/organizations\",\n    \"GET /enterprises/{enterprise}/actions/runner-groups\",\n    \"GET /enterprises/{enterprise}/actions/runner-groups/{runner_group_id}/organizations\",\n    \"GET /enterprises/{enterprise}/actions/runner-groups/{runner_group_id}/runners\",\n    \"GET /enterprises/{enterprise}/actions/runners\",\n    \"GET /enterprises/{enterprise}/audit-log\",\n    \"GET /enterprises/{enterprise}/secret-scanning/alerts\",\n    \"GET /enterprises/{enterprise}/settings/billing/advanced-security\",\n    \"GET /events\",\n    \"GET /gists\",\n    \"GET /gists/public\",\n    \"GET /gists/starred\",\n    \"GET /gists/{gist_id}/comments\",\n    \"GET /gists/{gist_id}/commits\",\n    \"GET /gists/{gist_id}/forks\",\n    \"GET /installation/repositories\",\n    \"GET /issues\",\n    \"GET /licenses\",\n    \"GET /marketplace_listing/plans\",\n    \"GET /marketplace_listing/plans/{plan_id}/accounts\",\n    \"GET /marketplace_listing/stubbed/plans\",\n    \"GET /marketplace_listing/stubbed/plans/{plan_id}/accounts\",\n    \"GET /networks/{owner}/{repo}/events\",\n    \"GET /notifications\",\n    \"GET /organizations\",\n    \"GET /orgs/{org}/actions/cache/usage-by-repository\",\n    \"GET /orgs/{org}/actions/permissions/repositories\",\n    \"GET /orgs/{org}/actions/runner-groups\",\n    \"GET /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories\",\n    \"GET /orgs/{org}/actions/runner-groups/{runner_group_id}/runners\",\n    \"GET /orgs/{org}/actions/runners\",\n    \"GET /orgs/{org}/actions/secrets\",\n    \"GET /orgs/{org}/actions/secrets/{secret_name}/repositories\",\n    \"GET /orgs/{org}/audit-log\",\n    \"GET /orgs/{org}/blocks\",\n    \"GET /orgs/{org}/code-scanning/alerts\",\n    \"GET /orgs/{org}/codespaces\",\n    \"GET /orgs/{org}/credential-authorizations\",\n    \"GET /orgs/{org}/dependabot/secrets\",\n    \"GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories\",\n    \"GET /orgs/{org}/events\",\n    \"GET /orgs/{org}/external-groups\",\n    \"GET /orgs/{org}/failed_invitations\",\n    \"GET /orgs/{org}/hooks\",\n    \"GET /orgs/{org}/hooks/{hook_id}/deliveries\",\n    \"GET /orgs/{org}/installations\",\n    \"GET /orgs/{org}/invitations\",\n    \"GET /orgs/{org}/invitations/{invitation_id}/teams\",\n    \"GET /orgs/{org}/issues\",\n    \"GET /orgs/{org}/members\",\n    \"GET /orgs/{org}/migrations\",\n    \"GET /orgs/{org}/migrations/{migration_id}/repositories\",\n    \"GET /orgs/{org}/outside_collaborators\",\n    \"GET /orgs/{org}/packages\",\n    \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions\",\n    \"GET /orgs/{org}/projects\",\n    \"GET /orgs/{org}/public_members\",\n    \"GET /orgs/{org}/repos\",\n    \"GET /orgs/{org}/secret-scanning/alerts\",\n    \"GET /orgs/{org}/settings/billing/advanced-security\",\n    \"GET /orgs/{org}/team-sync/groups\",\n    \"GET /orgs/{org}/teams\",\n    \"GET /orgs/{org}/teams/{team_slug}/discussions\",\n    \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments\",\n    \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n    \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions\",\n    \"GET /orgs/{org}/teams/{team_slug}/invitations\",\n    \"GET /orgs/{org}/teams/{team_slug}/members\",\n    \"GET /orgs/{org}/teams/{team_slug}/projects\",\n    \"GET /orgs/{org}/teams/{team_slug}/repos\",\n    \"GET /orgs/{org}/teams/{team_slug}/teams\",\n    \"GET /projects/columns/{column_id}/cards\",\n    \"GET /projects/{project_id}/collaborators\",\n    \"GET /projects/{project_id}/columns\",\n    \"GET /repos/{owner}/{repo}/actions/artifacts\",\n    \"GET /repos/{owner}/{repo}/actions/caches\",\n    \"GET /repos/{owner}/{repo}/actions/runners\",\n    \"GET /repos/{owner}/{repo}/actions/runs\",\n    \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts\",\n    \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs\",\n    \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs\",\n    \"GET /repos/{owner}/{repo}/actions/secrets\",\n    \"GET /repos/{owner}/{repo}/actions/workflows\",\n    \"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs\",\n    \"GET /repos/{owner}/{repo}/assignees\",\n    \"GET /repos/{owner}/{repo}/branches\",\n    \"GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations\",\n    \"GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs\",\n    \"GET /repos/{owner}/{repo}/code-scanning/alerts\",\n    \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances\",\n    \"GET /repos/{owner}/{repo}/code-scanning/analyses\",\n    \"GET /repos/{owner}/{repo}/codespaces\",\n    \"GET /repos/{owner}/{repo}/codespaces/devcontainers\",\n    \"GET /repos/{owner}/{repo}/codespaces/secrets\",\n    \"GET /repos/{owner}/{repo}/collaborators\",\n    \"GET /repos/{owner}/{repo}/comments\",\n    \"GET /repos/{owner}/{repo}/comments/{comment_id}/reactions\",\n    \"GET /repos/{owner}/{repo}/commits\",\n    \"GET /repos/{owner}/{repo}/commits/{commit_sha}/comments\",\n    \"GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls\",\n    \"GET /repos/{owner}/{repo}/commits/{ref}/check-runs\",\n    \"GET /repos/{owner}/{repo}/commits/{ref}/check-suites\",\n    \"GET /repos/{owner}/{repo}/commits/{ref}/status\",\n    \"GET /repos/{owner}/{repo}/commits/{ref}/statuses\",\n    \"GET /repos/{owner}/{repo}/contributors\",\n    \"GET /repos/{owner}/{repo}/dependabot/secrets\",\n    \"GET /repos/{owner}/{repo}/deployments\",\n    \"GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses\",\n    \"GET /repos/{owner}/{repo}/environments\",\n    \"GET /repos/{owner}/{repo}/events\",\n    \"GET /repos/{owner}/{repo}/forks\",\n    \"GET /repos/{owner}/{repo}/git/matching-refs/{ref}\",\n    \"GET /repos/{owner}/{repo}/hooks\",\n    \"GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries\",\n    \"GET /repos/{owner}/{repo}/invitations\",\n    \"GET /repos/{owner}/{repo}/issues\",\n    \"GET /repos/{owner}/{repo}/issues/comments\",\n    \"GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions\",\n    \"GET /repos/{owner}/{repo}/issues/events\",\n    \"GET /repos/{owner}/{repo}/issues/{issue_number}/comments\",\n    \"GET /repos/{owner}/{repo}/issues/{issue_number}/events\",\n    \"GET /repos/{owner}/{repo}/issues/{issue_number}/labels\",\n    \"GET /repos/{owner}/{repo}/issues/{issue_number}/reactions\",\n    \"GET /repos/{owner}/{repo}/issues/{issue_number}/timeline\",\n    \"GET /repos/{owner}/{repo}/keys\",\n    \"GET /repos/{owner}/{repo}/labels\",\n    \"GET /repos/{owner}/{repo}/milestones\",\n    \"GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels\",\n    \"GET /repos/{owner}/{repo}/notifications\",\n    \"GET /repos/{owner}/{repo}/pages/builds\",\n    \"GET /repos/{owner}/{repo}/projects\",\n    \"GET /repos/{owner}/{repo}/pulls\",\n    \"GET /repos/{owner}/{repo}/pulls/comments\",\n    \"GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions\",\n    \"GET /repos/{owner}/{repo}/pulls/{pull_number}/comments\",\n    \"GET /repos/{owner}/{repo}/pulls/{pull_number}/commits\",\n    \"GET /repos/{owner}/{repo}/pulls/{pull_number}/files\",\n    \"GET /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n    \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews\",\n    \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments\",\n    \"GET /repos/{owner}/{repo}/releases\",\n    \"GET /repos/{owner}/{repo}/releases/{release_id}/assets\",\n    \"GET /repos/{owner}/{repo}/releases/{release_id}/reactions\",\n    \"GET /repos/{owner}/{repo}/secret-scanning/alerts\",\n    \"GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations\",\n    \"GET /repos/{owner}/{repo}/stargazers\",\n    \"GET /repos/{owner}/{repo}/subscribers\",\n    \"GET /repos/{owner}/{repo}/tags\",\n    \"GET /repos/{owner}/{repo}/teams\",\n    \"GET /repos/{owner}/{repo}/topics\",\n    \"GET /repositories\",\n    \"GET /repositories/{repository_id}/environments/{environment_name}/secrets\",\n    \"GET /search/code\",\n    \"GET /search/commits\",\n    \"GET /search/issues\",\n    \"GET /search/labels\",\n    \"GET /search/repositories\",\n    \"GET /search/topics\",\n    \"GET /search/users\",\n    \"GET /teams/{team_id}/discussions\",\n    \"GET /teams/{team_id}/discussions/{discussion_number}/comments\",\n    \"GET /teams/{team_id}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n    \"GET /teams/{team_id}/discussions/{discussion_number}/reactions\",\n    \"GET /teams/{team_id}/invitations\",\n    \"GET /teams/{team_id}/members\",\n    \"GET /teams/{team_id}/projects\",\n    \"GET /teams/{team_id}/repos\",\n    \"GET /teams/{team_id}/teams\",\n    \"GET /user/blocks\",\n    \"GET /user/codespaces\",\n    \"GET /user/codespaces/secrets\",\n    \"GET /user/emails\",\n    \"GET /user/followers\",\n    \"GET /user/following\",\n    \"GET /user/gpg_keys\",\n    \"GET /user/installations\",\n    \"GET /user/installations/{installation_id}/repositories\",\n    \"GET /user/issues\",\n    \"GET /user/keys\",\n    \"GET /user/marketplace_purchases\",\n    \"GET /user/marketplace_purchases/stubbed\",\n    \"GET /user/memberships/orgs\",\n    \"GET /user/migrations\",\n    \"GET /user/migrations/{migration_id}/repositories\",\n    \"GET /user/orgs\",\n    \"GET /user/packages\",\n    \"GET /user/packages/{package_type}/{package_name}/versions\",\n    \"GET /user/public_emails\",\n    \"GET /user/repos\",\n    \"GET /user/repository_invitations\",\n    \"GET /user/starred\",\n    \"GET /user/subscriptions\",\n    \"GET /user/teams\",\n    \"GET /users\",\n    \"GET /users/{username}/events\",\n    \"GET /users/{username}/events/orgs/{org}\",\n    \"GET /users/{username}/events/public\",\n    \"GET /users/{username}/followers\",\n    \"GET /users/{username}/following\",\n    \"GET /users/{username}/gists\",\n    \"GET /users/{username}/gpg_keys\",\n    \"GET /users/{username}/keys\",\n    \"GET /users/{username}/orgs\",\n    \"GET /users/{username}/packages\",\n    \"GET /users/{username}/projects\",\n    \"GET /users/{username}/received_events\",\n    \"GET /users/{username}/received_events/public\",\n    \"GET /users/{username}/repos\",\n    \"GET /users/{username}/starred\",\n    \"GET /users/{username}/subscriptions\",\n];\n", "import { paginatingEndpoints, } from \"./generated/paginating-endpoints\";\nexport { paginatingEndpoints } from \"./generated/paginating-endpoints\";\nexport function isPaginatingEndpoint(arg) {\n    if (typeof arg === \"string\") {\n        return paginatingEndpoints.includes(arg);\n    }\n    else {\n        return false;\n    }\n}\n", "import { VERSION } from \"./version\";\nimport { paginate } from \"./paginate\";\nimport { iterator } from \"./iterator\";\nexport { composePaginateRest } from \"./compose-paginate\";\nexport { isPaginatingEndpoint, paginatingEndpoints, } from \"./paginating-endpoints\";\n/**\n * @param octokit Octokit instance\n * @param options Options passed to Octokit constructor\n */\nexport function paginateRest(octokit) {\n    return {\n        paginate: Object.assign(paginate.bind(null, octokit), {\n            iterator: iterator.bind(null, octokit),\n        }),\n    };\n}\npaginateRest.VERSION = VERSION;\n"], "names": ["VERSION", "normalizePaginatedListResponse", "response", "data", "responseNeedsNormalization", "incompleteResults", "incomplete_results", "repositorySelection", "repository_selection", "totalCount", "total_count", "namespaceKey", "Object", "keys", "iterator", "octokit", "route", "parameters", "options", "endpoint", "request", "requestMethod", "method", "headers", "url", "Symbol", "asyncIterator", "next", "done", "normalizedResponse", "link", "match", "value", "error", "status", "paginate", "mapFn", "undefined", "gather", "results", "then", "result", "earlyExit", "concat", "composePaginateRest", "assign", "paginatingEndpoints", "isPaginatingEndpoint", "arg", "includes", "paginateRest", "bind"], "mappings": ";;;;AAAO,MAAMA,OAAO,GAAG,mBAAhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAASC,8BAAT,CAAwCC,QAAxC,EAAkD;;EAErD,IAAI,CAACA,QAAQ,CAACC,IAAd,EAAoB;IAChB,yCACOD,QADP;MAEIC,IAAI,EAAE;;;;EAGd,MAAMC,0BAA0B,GAAG,iBAAiBF,QAAQ,CAACC,IAA1B,IAAkC,EAAE,SAASD,QAAQ,CAACC,IAApB,CAArE;EACA,IAAI,CAACC,0BAAL,EACI,OAAOF,QAAP,CAViD;;;EAarD,MAAMG,iBAAiB,GAAGH,QAAQ,CAACC,IAAT,CAAcG,kBAAxC;EACA,MAAMC,mBAAmB,GAAGL,QAAQ,CAACC,IAAT,CAAcK,oBAA1C;EACA,MAAMC,UAAU,GAAGP,QAAQ,CAACC,IAAT,CAAcO,WAAjC;EACA,OAAOR,QAAQ,CAACC,IAAT,CAAcG,kBAArB;EACA,OAAOJ,QAAQ,CAACC,IAAT,CAAcK,oBAArB;EACA,OAAON,QAAQ,CAACC,IAAT,CAAcO,WAArB;EACA,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAP,CAAYX,QAAQ,CAACC,IAArB,EAA2B,CAA3B,CAArB;EACA,MAAMA,IAAI,GAAGD,QAAQ,CAACC,IAAT,CAAcQ,YAAd,CAAb;EACAT,QAAQ,CAACC,IAAT,GAAgBA,IAAhB;;EACA,IAAI,OAAOE,iBAAP,KAA6B,WAAjC,EAA8C;IAC1CH,QAAQ,CAACC,IAAT,CAAcG,kBAAd,GAAmCD,iBAAnC;;;EAEJ,IAAI,OAAOE,mBAAP,KAA+B,WAAnC,EAAgD;IAC5CL,QAAQ,CAACC,IAAT,CAAcK,oBAAd,GAAqCD,mBAArC;;;EAEJL,QAAQ,CAACC,IAAT,CAAcO,WAAd,GAA4BD,UAA5B;EACA,OAAOP,QAAP;AACH;;AC7CM,SAASY,QAAT,CAAkBC,OAAlB,EAA2BC,KAA3B,EAAkCC,UAAlC,EAA8C;EACjD,MAAMC,OAAO,GAAG,OAAOF,KAAP,KAAiB,UAAjB,GACVA,KAAK,CAACG,QAAN,CAAeF,UAAf,CADU,GAEVF,OAAO,CAACK,OAAR,CAAgBD,QAAhB,CAAyBH,KAAzB,EAAgCC,UAAhC,CAFN;EAGA,MAAMI,aAAa,GAAG,OAAOL,KAAP,KAAiB,UAAjB,GAA8BA,KAA9B,GAAsCD,OAAO,CAACK,OAApE;EACA,MAAME,MAAM,GAAGJ,OAAO,CAACI,MAAvB;EACA,MAAMC,OAAO,GAAGL,OAAO,CAACK,OAAxB;EACA,IAAIC,GAAG,GAAGN,OAAO,CAACM,GAAlB;EACA,OAAO;IACH,CAACC,MAAM,CAACC,aAAR,GAAwB,OAAO;MAC3B,MAAMC,IAAN,GAAa;QACT,IAAI,CAACH,GAAL,EACI,OAAO;UAAEI,IAAI,EAAE;SAAf;;QACJ,IAAI;UACA,MAAM1B,QAAQ,GAAG,MAAMmB,aAAa,CAAC;YAAEC,MAAF;YAAUE,GAAV;YAAeD;WAAhB,CAApC;UACA,MAAMM,kBAAkB,GAAG5B,8BAA8B,CAACC,QAAD,CAAzD,CAFA;;;;UAMAsB,GAAG,GAAG,CAAC,CAACK,kBAAkB,CAACN,OAAnB,CAA2BO,IAA3B,IAAmC,EAApC,EAAwCC,KAAxC,CAA8C,yBAA9C,KAA4E,EAA7E,EAAiF,CAAjF,CAAN;UACA,OAAO;YAAEC,KAAK,EAAEH;WAAhB;SAPJ,CASA,OAAOI,KAAP,EAAc;UACV,IAAIA,KAAK,CAACC,MAAN,KAAiB,GAArB,EACI,MAAMD,KAAN;UACJT,GAAG,GAAG,EAAN;UACA,OAAO;YACHQ,KAAK,EAAE;cACHE,MAAM,EAAE,GADL;cAEHX,OAAO,EAAE,EAFN;cAGHpB,IAAI,EAAE;;WAJd;;;;KAjBY;GAD5B;AA6BH;;ACrCM,SAASgC,QAAT,CAAkBpB,OAAlB,EAA2BC,KAA3B,EAAkCC,UAAlC,EAA8CmB,KAA9C,EAAqD;EACxD,IAAI,OAAOnB,UAAP,KAAsB,UAA1B,EAAsC;IAClCmB,KAAK,GAAGnB,UAAR;IACAA,UAAU,GAAGoB,SAAb;;;EAEJ,OAAOC,MAAM,CAACvB,OAAD,EAAU,EAAV,EAAcD,QAAQ,CAACC,OAAD,EAAUC,KAAV,EAAiBC,UAAjB,CAAR,CAAqCQ,MAAM,CAACC,aAA5C,GAAd,EAA4EU,KAA5E,CAAb;AACH;;AACD,SAASE,MAAT,CAAgBvB,OAAhB,EAAyBwB,OAAzB,EAAkCzB,QAAlC,EAA4CsB,KAA5C,EAAmD;EAC/C,OAAOtB,QAAQ,CAACa,IAAT,GAAgBa,IAAhB,CAAsBC,MAAD,IAAY;IACpC,IAAIA,MAAM,CAACb,IAAX,EAAiB;MACb,OAAOW,OAAP;;;IAEJ,IAAIG,SAAS,GAAG,KAAhB;;IACA,SAASd,IAAT,GAAgB;MACZc,SAAS,GAAG,IAAZ;;;IAEJH,OAAO,GAAGA,OAAO,CAACI,MAAR,CAAeP,KAAK,GAAGA,KAAK,CAACK,MAAM,CAACT,KAAR,EAAeJ,IAAf,CAAR,GAA+Ba,MAAM,CAACT,KAAP,CAAa7B,IAAhE,CAAV;;IACA,IAAIuC,SAAJ,EAAe;MACX,OAAOH,OAAP;;;IAEJ,OAAOD,MAAM,CAACvB,OAAD,EAAUwB,OAAV,EAAmBzB,QAAnB,EAA6BsB,KAA7B,CAAb;GAZG,CAAP;AAcH;;MCrBYQ,mBAAmB,GAAGhC,MAAM,CAACiC,MAAP,CAAcV,QAAd,EAAwB;EACvDrB;AADuD,CAAxB,CAA5B;;MCFMgC,mBAAmB,GAAG,CAC/B,0BAD+B,EAE/B,wBAF+B,EAG/B,0BAH+B,EAI/B,qBAJ+B,EAK/B,iEAL+B,EAM/B,qDAN+B,EAO/B,qFAP+B,EAQ/B,+EAR+B,EAS/B,+CAT+B,EAU/B,yCAV+B,EAW/B,sDAX+B,EAY/B,kEAZ+B,EAa/B,aAb+B,EAc/B,YAd+B,EAe/B,mBAf+B,EAgB/B,oBAhB+B,EAiB/B,+BAjB+B,EAkB/B,8BAlB+B,EAmB/B,4BAnB+B,EAoB/B,gCApB+B,EAqB/B,aArB+B,EAsB/B,eAtB+B,EAuB/B,gCAvB+B,EAwB/B,mDAxB+B,EAyB/B,wCAzB+B,EA0B/B,2DA1B+B,EA2B/B,qCA3B+B,EA4B/B,oBA5B+B,EA6B/B,oBA7B+B,EA8B/B,mDA9B+B,EA+B/B,kDA/B+B,EAgC/B,uCAhC+B,EAiC/B,sEAjC+B,EAkC/B,iEAlC+B,EAmC/B,iCAnC+B,EAoC/B,iCApC+B,EAqC/B,4DArC+B,EAsC/B,2BAtC+B,EAuC/B,wBAvC+B,EAwC/B,sCAxC+B,EAyC/B,4BAzC+B,EA0C/B,2CA1C+B,EA2C/B,oCA3C+B,EA4C/B,+DA5C+B,EA6C/B,wBA7C+B,EA8C/B,iCA9C+B,EA+C/B,oCA/C+B,EAgD/B,uBAhD+B,EAiD/B,4CAjD+B,EAkD/B,+BAlD+B,EAmD/B,6BAnD+B,EAoD/B,mDApD+B,EAqD/B,wBArD+B,EAsD/B,yBAtD+B,EAuD/B,4BAvD+B,EAwD/B,wDAxD+B,EAyD/B,uCAzD+B,EA0D/B,0BA1D+B,EA2D/B,iEA3D+B,EA4D/B,0BA5D+B,EA6D/B,gCA7D+B,EA8D/B,uBA9D+B,EA+D/B,wCA/D+B,EAgE/B,oDAhE+B,EAiE/B,kCAjE+B,EAkE/B,uBAlE+B,EAmE/B,+CAnE+B,EAoE/B,4EApE+B,EAqE/B,uGArE+B,EAsE/B,6EAtE+B,EAuE/B,+CAvE+B,EAwE/B,2CAxE+B,EAyE/B,4CAzE+B,EA0E/B,yCA1E+B,EA2E/B,yCA3E+B,EA4E/B,yCA5E+B,EA6E/B,0CA7E+B,EA8E/B,oCA9E+B,EA+E/B,6CA/E+B,EAgF/B,0CAhF+B,EAiF/B,2CAjF+B,EAkF/B,wCAlF+B,EAmF/B,2DAnF+B,EAoF/B,gFApF+B,EAqF/B,sDArF+B,EAsF/B,2CAtF+B,EAuF/B,6CAvF+B,EAwF/B,gEAxF+B,EAyF/B,qCAzF+B,EA0F/B,oCA1F+B,EA2F/B,iEA3F+B,EA4F/B,oEA5F+B,EA6F/B,gDA7F+B,EA8F/B,yEA9F+B,EA+F/B,kDA/F+B,EAgG/B,sCAhG+B,EAiG/B,oDAjG+B,EAkG/B,8CAlG+B,EAmG/B,yCAnG+B,EAoG/B,oCApG+B,EAqG/B,2DArG+B,EAsG/B,mCAtG+B,EAuG/B,yDAvG+B,EAwG/B,sDAxG+B,EAyG/B,oDAzG+B,EA0G/B,sDA1G+B,EA2G/B,gDA3G+B,EA4G/B,kDA5G+B,EA6G/B,wCA7G+B,EA8G/B,8CA9G+B,EA+G/B,uCA/G+B,EAgH/B,gEAhH+B,EAiH/B,wCAjH+B,EAkH/B,kCAlH+B,EAmH/B,iCAnH+B,EAoH/B,mDApH+B,EAqH/B,iCArH+B,EAsH/B,sDAtH+B,EAuH/B,uCAvH+B,EAwH/B,kCAxH+B,EAyH/B,2CAzH+B,EA0H/B,kEA1H+B,EA2H/B,yCA3H+B,EA4H/B,0DA5H+B,EA6H/B,wDA7H+B,EA8H/B,wDA9H+B,EA+H/B,2DA/H+B,EAgI/B,0DAhI+B,EAiI/B,gCAjI+B,EAkI/B,kCAlI+B,EAmI/B,sCAnI+B,EAoI/B,gEApI+B,EAqI/B,yCArI+B,EAsI/B,wCAtI+B,EAuI/B,oCAvI+B,EAwI/B,iCAxI+B,EAyI/B,0CAzI+B,EA0I/B,iEA1I+B,EA2I/B,wDA3I+B,EA4I/B,uDA5I+B,EA6I/B,qDA7I+B,EA8I/B,mEA9I+B,EA+I/B,uDA/I+B,EAgJ/B,4EAhJ+B,EAiJ/B,oCAjJ+B,EAkJ/B,wDAlJ+B,EAmJ/B,2DAnJ+B,EAoJ/B,kDApJ+B,EAqJ/B,2EArJ+B,EAsJ/B,sCAtJ+B,EAuJ/B,uCAvJ+B,EAwJ/B,gCAxJ+B,EAyJ/B,iCAzJ+B,EA0J/B,kCA1J+B,EA2J/B,mBA3J+B,EA4J/B,2EA5J+B,EA6J/B,kBA7J+B,EA8J/B,qBA9J+B,EA+J/B,oBA/J+B,EAgK/B,oBAhK+B,EAiK/B,0BAjK+B,EAkK/B,oBAlK+B,EAmK/B,mBAnK+B,EAoK/B,kCApK+B,EAqK/B,+DArK+B,EAsK/B,0FAtK+B,EAuK/B,gEAvK+B,EAwK/B,kCAxK+B,EAyK/B,8BAzK+B,EA0K/B,+BA1K+B,EA2K/B,4BA3K+B,EA4K/B,4BA5K+B,EA6K/B,kBA7K+B,EA8K/B,sBA9K+B,EA+K/B,8BA/K+B,EAgL/B,kBAhL+B,EAiL/B,qBAjL+B,EAkL/B,qBAlL+B,EAmL/B,oBAnL+B,EAoL/B,yBApL+B,EAqL/B,wDArL+B,EAsL/B,kBAtL+B,EAuL/B,gBAvL+B,EAwL/B,iCAxL+B,EAyL/B,yCAzL+B,EA0L/B,4BA1L+B,EA2L/B,sBA3L+B,EA4L/B,kDA5L+B,EA6L/B,gBA7L+B,EA8L/B,oBA9L+B,EA+L/B,2DA/L+B,EAgM/B,yBAhM+B,EAiM/B,iBAjM+B,EAkM/B,kCAlM+B,EAmM/B,mBAnM+B,EAoM/B,yBApM+B,EAqM/B,iBArM+B,EAsM/B,YAtM+B,EAuM/B,8BAvM+B,EAwM/B,yCAxM+B,EAyM/B,qCAzM+B,EA0M/B,iCA1M+B,EA2M/B,iCA3M+B,EA4M/B,6BA5M+B,EA6M/B,gCA7M+B,EA8M/B,4BA9M+B,EA+M/B,4BA/M+B,EAgN/B,gCAhN+B,EAiN/B,gCAjN+B,EAkN/B,uCAlN+B,EAmN/B,8CAnN+B,EAoN/B,6BApN+B,EAqN/B,+BArN+B,EAsN/B,qCAtN+B,CAA5B;;ACEA,SAASC,oBAAT,CAA8BC,GAA9B,EAAmC;EACtC,IAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;IACzB,OAAOF,mBAAmB,CAACG,QAApB,CAA6BD,GAA7B,CAAP;GADJ,MAGK;IACD,OAAO,KAAP;;AAEP;;ACJD;AACA;AACA;AACA;;AACA,AAAO,SAASE,YAAT,CAAsBnC,OAAtB,EAA+B;EAClC,OAAO;IACHoB,QAAQ,EAAEvB,MAAM,CAACiC,MAAP,CAAcV,QAAQ,CAACgB,IAAT,CAAc,IAAd,EAAoBpC,OAApB,CAAd,EAA4C;MAClDD,QAAQ,EAAEA,QAAQ,CAACqC,IAAT,CAAc,IAAd,EAAoBpC,OAApB;KADJ;GADd;AAKH;AACDmC,YAAY,CAAClD,OAAb,GAAuBA,OAAvB;;;;;;;"}