{"name": "@octokit/rest", "description": "GitHub REST API client for Node.js", "version": "18.12.0", "license": "MIT", "files": ["dist-*/", "bin/"], "pika": true, "sideEffects": false, "keywords": ["octokit", "github", "rest", "api-client"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://github.com/gr2m"}], "repository": "github:octokit/rest.js", "dependencies": {"@octokit/core": "^3.5.1", "@octokit/plugin-paginate-rest": "^2.16.8", "@octokit/plugin-request-log": "^1.0.4", "@octokit/plugin-rest-endpoint-methods": "^5.12.0"}, "devDependencies": {"@octokit/auth": "^3.0.3", "@octokit/fixtures-server": "^7.0.0", "@octokit/request": "^5.6.1", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.9.2", "@pika/plugin-build-web": "^0.9.2", "@pika/plugin-ts-standard-pkg": "^0.9.2", "@types/jest": "^27.0.0", "@types/node": "^14.0.1", "fetch-mock": "^9.0.0", "jest": "^27.0.1", "prettier": "2.4.1", "semantic-release": "^18.0.0", "semantic-release-plugin-update-version-in-files": "^1.0.0", "ts-jest": "^27.0.1", "typescript": "^4.0.0"}, "publishConfig": {"access": "public"}, "source": "dist-src/index.js", "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js"}