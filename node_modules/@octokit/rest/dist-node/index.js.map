{"version": 3, "file": "index.js", "sources": ["../dist-src/version.js", "../dist-src/index.js"], "sourcesContent": ["export const VERSION = \"18.12.0\";\n", "import { Octokit as Core } from \"@octokit/core\";\nimport { requestLog } from \"@octokit/plugin-request-log\";\nimport { paginateRest } from \"@octokit/plugin-paginate-rest\";\nimport { legacyRestEndpointMethods } from \"@octokit/plugin-rest-endpoint-methods\";\nimport { VERSION } from \"./version\";\nexport const Octokit = Core.plugin(requestLog, legacyRestEndpointMethods, paginateRest).defaults({\n    userAgent: `octokit-rest.js/${VERSION}`,\n});\n"], "names": ["VERSION", "Octokit", "Core", "plugin", "requestLog", "legacyRestEndpointMethods", "paginateRest", "defaults", "userAgent"], "mappings": ";;;;;;;;;AAAO,MAAMA,OAAO,GAAG,mBAAhB;;MCKMC,OAAO,GAAGC,YAAI,CAACC,MAAL,CAAYC,2BAAZ,EAAwBC,mDAAxB,EAAmDC,+BAAnD,EAAiEC,QAAjE,CAA0E;AAC7FC,EAAAA,SAAS,EAAG,mBAAkBR,OAAQ;AADuD,CAA1E,CAAhB;;;;"}