{"version": 3, "file": "index.js", "sources": ["../dist-src/version.js", "../dist-src/index.js"], "sourcesContent": ["export const VERSION = \"18.12.0\";\n", "import { Octokit as Core } from \"@octokit/core\";\nimport { requestLog } from \"@octokit/plugin-request-log\";\nimport { paginateRest } from \"@octokit/plugin-paginate-rest\";\nimport { legacyRestEndpointMethods } from \"@octokit/plugin-rest-endpoint-methods\";\nimport { VERSION } from \"./version\";\nexport const Octokit = Core.plugin(requestLog, legacyRestEndpointMethods, paginateRest).defaults({\n    userAgent: `octokit-rest.js/${VERSION}`,\n});\n"], "names": ["Core"], "mappings": ";;;;;AAAO,MAAM,OAAO,GAAG,mBAAmB;;ACK9B,MAAC,OAAO,GAAGA,SAAI,CAAC,MAAM,CAAC,UAAU,EAAE,yBAAyB,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC;AACjG,IAAI,SAAS,EAAE,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AAC3C,CAAC,CAAC;;;;"}