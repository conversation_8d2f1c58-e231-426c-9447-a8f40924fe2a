{"name": "untildify", "version": "4.0.0", "description": "Convert a tilde path to an absolute path: `~/dev` → `/Users/<USER>/dev`", "license": "MIT", "repository": "sindresorhus/untildify", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "contributors": ["silverwind <<EMAIL>> (https://silverwind.io)"], "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["tilde", "expansion", "expand", "untildify", "path", "home", "directory", "user", "shell", "bash"], "devDependencies": {"ava": "^1.4.1", "rewire": "^4.0.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}