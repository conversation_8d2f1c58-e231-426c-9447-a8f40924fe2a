{"version": 3, "file": "equiv.js", "sourceRoot": "", "sources": ["../src/equiv.ts"], "names": [], "mappings": ";;;AAAA,mCAAiD;AACjD,0DAA4C;AAE5C,mBAAyB,IAAU;IAC/B,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,eAAW,CAAC,CAAC;IAClC,IAAI,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;IACxC,IAAI,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;IACxC,IAAI,OAAO,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC;IACvC,IAAI,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;IACzC,IAAI,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC;IACrC,IAAI,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;IACzC,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;IAE7C,SAAS,qBAAqB,CAAC,CAAM,EAAE,CAAM,EAAE,WAAiB;QAC5D,IAAI,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;YAC5B,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;SAC1B;aAAM;YACH,WAAW,GAAG,IAAI,CAAC;SACtB;QAED,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;IAC5C,CAAC;IAED,qBAAqB,CAAC,MAAM,GAAG,UAAU,CAAM,EAAE,CAAM;QACnD,IAAI,WAAW,GAAU,EAAE,CAAC;QAC5B,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,EAAE;YAC3C,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1B,IAAI,CAAC,KAAK,CAAC,EAAE;oBACT,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;iBAC1C;aACJ;iBAAM;gBACH,MAAM,IAAI,KAAK,CACb,sCAAsC;oBACtC,WAAW,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAC/C,CAAC;aACL;SACJ;IACL,CAAC,CAAC;IAEF,SAAS,oBAAoB,CAAC,QAAa;QACvC,IAAI,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACtC,OAAO,GAAG,GAAG,QAAQ,CAAC;SACzB;QACD,OAAO,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;IAChD,CAAC;IAED,SAAS,aAAa,CAAC,CAAM,EAAE,CAAM,EAAE,WAAgB;QACnD,IAAI,CAAC,KAAK,CAAC,EAAE;YACT,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YAClB,OAAO,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;SACjD;QAED,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YACnB,OAAO,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;SAClD;QAED,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YACjB,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACzC;QAED,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YACnB,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CACxB,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM;gBACrB,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM;gBACrB,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS;gBAC3B,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU,CAC9B,CAAC;SACP;QAED,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAED,SAAS,mBAAmB,CAAC,CAAM,EAAE,CAAM,EAAE,WAAgB;QACzD,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAClB,IAAI,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC;QAEvB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,OAAO,EAAE;YAC3C,IAAI,WAAW,EAAE;gBACb,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC9B;YACD,OAAO,KAAK,CAAC;SAChB;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,EAAE,CAAC,EAAE;YAC9B,IAAI,WAAW,EAAE;gBACb,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACvB;YAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBACnB,OAAO,KAAK,CAAC;aAChB;YAED,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE;gBACzC,OAAO,KAAK,CAAC;aAChB;YAED,IAAI,WAAW,EAAE;gBACb,IAAI,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;gBACxC,IAAI,eAAe,KAAK,CAAC,EAAE;oBACvB,MAAM,IAAI,KAAK,CAAC,EAAE,GAAG,eAAe,CAAC,CAAC;iBACzC;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,SAAS,oBAAoB,CAAC,CAAM,EAAE,CAAM,EAAE,WAAgB;QAC1D,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YACpB,OAAO,KAAK,CAAC;SAChB;QAED,gDAAgD;QAChD,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE;YACnB,IAAI,WAAW,EAAE;gBACb,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC5B;YACD,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;QAE/B,IAAI,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;QAE/B,IAAI,UAAU,KAAK,UAAU,EAAE;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,EAAE,CAAC,EAAE;gBACjC,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrB,IAAI,MAAM,GAAG,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBACpC,IAAI,MAAM,GAAG,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBAEpC,IAAI,WAAW,EAAE;oBACb,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAC1B;gBAED,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE;oBAC7C,OAAO,KAAK,CAAC;iBAChB;gBAED,IAAI,WAAW,EAAE;oBACb,IAAI,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;oBACxC,IAAI,eAAe,KAAK,IAAI,EAAE;wBAC1B,MAAM,IAAI,KAAK,CAAC,EAAE,GAAG,eAAe,CAAC,CAAC;qBACzC;iBACJ;aACJ;YAED,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,WAAW,EAAE;YACd,OAAO,KAAK,CAAC;SAChB;QAED,oEAAoE;QACpE,0DAA0D;QAE1D,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEpC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,EAAE,CAAC,EAAE;YAC7B,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;SAC/B;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,EAAE,CAAC,EAAE;YAC7B,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAEjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE;gBAC/B,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvB,OAAO,KAAK,CAAC;aAChB;YAED,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;SAC1B;QAED,KAAK,IAAI,IAAI,SAAS,EAAE;YACpB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,MAAM;SACT;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,OAAO,qBAAqB,CAAC;AACjC,CAAC;AAzLD,4BAyLC;AAAA,CAAC;AAEF,IAAA,8BAAqB,EAAC,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC,CAAC"}