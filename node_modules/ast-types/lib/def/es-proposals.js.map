{"version": 3, "file": "es-proposals.js", "sourceRoot": "", "sources": ["../../src/def/es-proposals.ts"], "names": [], "mappings": ";;;AACA,2DAAmC;AACnC,0DAAgE;AAChE,4DAAiC;AAEjC,mBAAyB,IAAU;IACjC,IAAI,CAAC,GAAG,CAAC,gBAAS,CAAC,CAAC;IAEpB,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,eAAW,CAAC,CAAC;IACpC,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;IACxB,IAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;IAC3B,IAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IAEnB,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAY,CAAC,CAAC;IACtC,IAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;IAEjC,GAAG,CAAC,iBAAiB,CAAC;SACnB,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC;SACxB,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;SAC9C,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAE5C,aAAa;IACb,GAAG,CAAC,WAAW,CAAC;SACb,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;IAE1C,GAAG,CAAC,UAAU,CAAC;SACZ,KAAK,CAAC,YAAY,EACZ,EAAE,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,EAC5B,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAE3B,GAAG,CAAC,kBAAkB,CAAC;SACpB,KAAK,CAAC,YAAY,EACZ,EAAE,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,EAC5B,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAE3B,gBAAgB;IAChB,GAAG,CAAC,aAAa,CAAC;SACf,KAAK,CAAC,YAAY,EAAE,SAAS,CAAC;SAC9B,KAAK,CAAC,IAAI,CAAC;SACX,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;IAElC,GAAG,CAAC,sBAAsB,CAAC;SACxB,KAAK,CAAC,eAAe,CAAC;SACtB,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC;SACrB,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC;SAChC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAEjE,qDAAqD;IACrD,GAAG,CAAC,iBAAiB,CAAC;SACnB,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC;SACrB,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;SACnD,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;IAErC,CAAE,mBAAmB;QACnB,sBAAsB;QACtB,wBAAwB;KACzB,CAAC,OAAO,CAAC,UAAA,IAAI;QACZ,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,CACb,YAAY,EACZ,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,EACxB,QAAQ,CAAC,UAAU,CACpB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,gDAAgD;IAChD,4CAA4C;IAC5C,GAAG,CAAC,kBAAkB,CAAC;SACpB,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,YAAY,EAAE,CAAC,EAAE,CACtB,GAAG,CAAC,gBAAgB,CAAC,EACrB,GAAG,CAAC,cAAc,CAAC,EACnB,GAAG,CAAC,eAAe,CAAC,CACrB,CAAC,CAAC,CAAC;IACN,GAAG,CAAC,iBAAiB,CAAC;SACnB,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,UAAU,CAAC;SACjB,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,CACpB,GAAG,CAAC,YAAY,CAAC,EACjB,GAAG,CAAC,eAAe,CAAC,EACpB,IAAI,CACL,CAAC,CAAC,CAAC;IAEN,oDAAoD;IACpD,4CAA4C;IAC5C,GAAG,CAAC,kBAAkB,CAAC;SACpB,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;AACnC,CAAC;AAvFD,4BAuFC;AAAA,CAAC;AAEF,IAAA,8BAAqB,EAAC,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC,CAAC"}