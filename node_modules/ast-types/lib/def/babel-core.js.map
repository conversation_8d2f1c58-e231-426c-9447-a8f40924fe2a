{"version": 3, "file": "babel-core.js", "sourceRoot": "", "sources": ["../../src/def/babel-core.ts"], "names": [], "mappings": ";;;AACA,wEAA4C;AAC5C,2DAAmC;AACnC,0DAAgE;AAGhE,mBAAyB,IAAU;;IACjC,IAAI,CAAC,GAAG,CAAC,sBAAc,CAAC,CAAC;IAEzB,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,eAAW,CAAC,CAAC;IACpC,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAY,CAAC,CAAC,QAAQ,CAAC;IACjD,IAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;IAC3B,IAAM,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;IAEvB,IAAW,WAAW,GACpB,KAAK,CAAC,YAAY,UADE,CACD;IAEvB,GAAG,CAAC,MAAM,CAAC;SACR,KAAK,CAAC,WAAW,CAAC;SAClB,KAAK,EAAE,CAAC;IAEX,GAAG,CAAC,cAAc,CAAC;SAChB,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAErC,GAAG,CAAC,gBAAgB,CAAC;SAClB,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC;SACzB,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;SAC5C,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;IAEtC,GAAG,CAAC,yBAAyB,CAAC;SAC3B,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;IAE1C,GAAG,CAAC,0BAA0B,CAAC;SAC5B,KAAK,CAAC,WAAW,CAAC;SAClB,KAAK,CAAC,UAAU,CAAC;SACjB,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;IAExC,GAAG,CAAC,wBAAwB,CAAC;SAC1B,KAAK,CAAC,WAAW,CAAC;SAClB,KAAK,CAAC,UAAU,CAAC;SACjB,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;IAExC,GAAG,CAAC,cAAc,CAAC;SAChB,KAAK,CAAC,SAAS,CAAC;SAChB,KAAK,CAAC,OAAO,EAAE,aAAa,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IAEvD,GAAG,CAAC,aAAa,CAAC;SACf,KAAK,CAAC,SAAS,CAAC;SAChB,KAAK,CAAC,OAAO,EAAE,aAAa,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IAEvD,GAAG,CAAC,WAAW,CAAC;SACb,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,OAAO,CAAC;SACd,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC;IAE3C,GAAG,CAAC,kBAAkB,CAAC;SACpB,KAAK,CAAC,MAAM,EAAE,YAAY,CAAC;SAC3B,KAAK,CAAC,OAAO,CAAC;SACd,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;IAElD,GAAG,CAAC,sBAAsB,CAAC;SACxB,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,OAAO,CAAC;SACd,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAE1B,GAAG,CAAC,gBAAgB,CAAC;SAClB,KAAK,CAAC,WAAW,CAAC;SAClB,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;SACjC,KAAK,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;IAEhE,GAAG,CAAC,SAAS,CAAC;SACX,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;SACjC,KAAK,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,UAAU,CAAC;SAC5D,KAAK,CAAC,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAEjF,SAAS,gBAAgB,CAMvB,YAA0B,EAC1B,KAA8B;QAD9B,6BAAA,EAAA,qBAA0B;QAG1B,OAAO;YACL,OAAO;YACP;gBACE,QAAQ,EAAE,YAAY;gBACtB,GAAG,EAAE,MAAM;aACZ;YACD,SAAS,UAAU;gBACjB,IAAM,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACjD,OAAO;oBACL,QAAQ,EAAE,KAAK;oBACf,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC1C,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC;IAED,gBAAgB;IAChB,CAAA,KAAA,GAAG,CAAC,eAAe,CAAC;SACjB,KAAK,CAAC,SAAS,CAAC;SAChB,KAAK,CAAC,OAAO,CAAC;SACd,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;SACtB,KAAK,WAAI,gBAAgB,CAAkB,MAAM,EAAE,UAAA,GAAG,IAAI,OAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAnB,CAAmB,CAAC,EAAE;IAEnF,CAAA,KAAA,GAAG,CAAC,gBAAgB,CAAC;SAClB,KAAK,CAAC,SAAS,CAAC;SAChB,KAAK,CAAC,OAAO,CAAC;SACd,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC;SACtB,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAA;SAChD,KAAK,WAAI,gBAAgB,CAAmB,MAAM,CAAC,EAAE;IAExD,CAAA,KAAA,GAAG,CAAC,eAAe,CAAC;SACjB,KAAK,CAAC,SAAS,CAAC;SAChB,KAAK,CAAC,OAAO,CAAC;QACf,iEAAiE;QACjE,yCAAyC;SACxC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAA;SAClC,KAAK,WAAI,gBAAgB,CAAkB,MAAM,EAAE,UAAA,GAAG,IAAI,OAAA,GAAG,GAAG,GAAG,EAAT,CAAS,CAAC,EAAE;IAEzE,2CAA2C;IAC3C,4CAA4C;IAC5C,CAAA,KAAA,GAAG,CAAC,gBAAgB,CAAC;SAClB,KAAK,CAAC,SAAS,CAAC;SAChB,KAAK,CAAC,OAAO,CAAC;SACd,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;SACtB,KAAK,WAAI,gBAAgB,CAAmB,MAAM,EAAE,UAAA,GAAG,IAAI,OAAA,GAAG,GAAG,GAAG,EAAT,CAAS,CAAC,EAAE;IAE1E,GAAG,CAAC,aAAa,CAAC;SACf,KAAK,CAAC,SAAS,CAAC;SAChB,KAAK,EAAE;SACP,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1C,GAAG,CAAC,gBAAgB,CAAC;SAClB,KAAK,CAAC,SAAS,CAAC;SAChB,KAAK,CAAC,OAAO,CAAC;SACd,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAE3B,CAAA,KAAA,GAAG,CAAC,eAAe,CAAC;SACjB,KAAK,CAAC,SAAS,CAAC;SAChB,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC;SACzB,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC;SACxB,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC;SACtB,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE;QACtB,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAA;SACD,KAAK,WAAI,gBAAgB,CACxB,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EACvB,UAAA,GAAG,IAAI,OAAA,WAAI,GAAG,CAAC,OAAO,cAAI,GAAG,CAAC,KAAK,IAAI,EAAE,CAAE,EAApC,CAAoC,CAC5C,EAGA,KAAK,CAAC,OAAO,EAAE;QACd,OAAO,EAAE,MAAM;QACf,KAAK,EAAE,MAAM;KACd,EAAE;QACD,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEL,IAAI,wBAAwB,GAAG,EAAE,CAC/B,GAAG,CAAC,UAAU,CAAC,EACf,GAAG,CAAC,cAAc,CAAC,EACnB,GAAG,CAAC,gBAAgB,CAAC,EACrB,GAAG,CAAC,gBAAgB,CAAC,EACrB,GAAG,CAAC,eAAe,CAAC,CACrB,CAAC;IAEF,oDAAoD;IACpD,GAAG,CAAC,kBAAkB,CAAC;SACpB,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,YAAY,EAAE,CAAC,wBAAwB,CAAC,CAAC,CAAC;IAEnD,yDAAyD;IACzD,GAAG,CAAC,cAAc,CAAC;SAChB,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC;SACzB,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;SAClD,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SACzC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;SACtE,KAAK,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;SACjC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,gBAAgB,CAAC,CAAC;SACpC,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;SAC7C,KAAK,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;SAC9C,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;SAC1C,KAAK,CAAC,eAAe,EAAE,aAAa;IAC9B,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,EACxB,QAAQ,CAAC,MAAM,CAAC,CAAC;SACvB,KAAK,CAAC,YAAY,EACZ,EAAE,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,EAC5B,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAE3B,GAAG,CAAC,gBAAgB,CAAC;SAClB,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC;SACrB,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;SACtE,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;SACrD,KAAK,CAAC,eAAe,EAAE,aAAa;IAC9B,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,EACxB,QAAQ,CAAC,MAAM,CAAC,CAAC;SACvB,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAEjD,IAAI,gBAAgB,GAAG,EAAE,CACvB,GAAG,CAAC,kBAAkB,CAAC,EACvB,GAAG,CAAC,oBAAoB,CAAC,EACzB,GAAG,CAAC,yBAAyB,CAAC,EAC9B,GAAG,CAAC,eAAe,CAAC,EACpB,GAAG,CAAC,sBAAsB,CAAC,EAC3B,GAAG,CAAC,aAAa,CAAC,EAClB,GAAG,CAAC,oBAAoB,CAAC,EACzB,GAAG,CAAC,uBAAuB,CAAC,EAC5B,GAAG,CAAC,aAAa,CAAC,CACnB,CAAC;IAEF,kCAAkC;IAClC,GAAG,CAAC,WAAW,CAAC;SACb,KAAK,CAAC,aAAa,CAAC;SACpB,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,MAAM,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAErC,GAAG,CAAC,aAAa,CAAC;SACf,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC;SAChC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC;SAC5D,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAE1E,GAAG,CAAC,oBAAoB,CAAC;SACtB,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC;SAChC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC;SAC5D,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;IAEpC,GAAG,CAAC,uBAAuB,CAAC;SACzB,KAAK,CAAC,aAAa,CAAC;SACpB,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,CAAC;SACzD,KAAK,CAAC,KAAK,EAAE,EAAE,CACd,GAAG,CAAC,SAAS,CAAC,EACd,GAAG,CAAC,YAAY,CAAC,EACjB,GAAG,CAAC,aAAa,CAAC;IAClB,kDAAkD;IAClD,GAAG,CAAC,YAAY,CAAC,CAClB,CAAC;SACD,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAEjE,CAAC,aAAa;QACb,oBAAoB;KACpB,CAAC,OAAO,CAAC,UAAA,QAAQ;QAChB,GAAG,CAAC,QAAQ,CAAC;aACV,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,CAAC,EAAE,cAAM,OAAA,QAAQ,EAAR,CAAQ,CAAC;aACxE,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,gBAAgB,CAAC,CAAC;YACrC,8EAA8E;aAC7E,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAA;IAClF,CAAC,CAAC,CAAC;IAEH,CAAC,aAAa;QACb,oBAAoB;QACpB,uBAAuB;KACvB,CAAC,OAAO,CAAC,UAAA,QAAQ;QAChB,GAAG,CAAC,QAAQ,CAAC;aACV,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;aAC7C,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;aAC3C,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;aAC7C,KAAK,CAAC,eAAe,EAAE,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;aACpF,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;aACnE,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;aAC7C,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;aAC7C,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;aAC7C,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,IAAI,qBAAqB,GAAG,EAAE,CAC5B,GAAG,CAAC,UAAU,CAAC,EACf,GAAG,CAAC,iBAAiB,CAAC,EACtB,GAAG,CAAC,uBAAuB,CAAC,EAC5B,GAAG,CAAC,gBAAgB,CAAC,EAAE,kBAAkB;IACzC,GAAG,CAAC,gBAAgB,CAAC,EAAE,UAAU;IACjC,GAAG,CAAC,cAAc,CAAC,EAAE,UAAU;IAC/B,GAAG,CAAC,aAAa,CAAC,CACnB,CAAC;IAEF,6CAA6C;IAC7C,GAAG,CAAC,eAAe,CAAC;SACjB,KAAK,CAAC,SAAS,CAAC;SAChB,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,YAAY,EAAE,CAAC,qBAAqB,CAAC,CAAC;SAC5C,KAAK,CAAC,YAAY,EACZ,EAAE,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,EAC5B,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAE3B,GAAG,CAAC,gBAAgB,CAAC;SAClB,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,UAAU,CAAC;SACjB,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;IAExC,GAAG,CAAC,cAAc,CAAC;SAChB,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,UAAU,CAAC;SACjB,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;IAExC,GAAG,CAAC,mBAAmB,CAAC;SACrB,KAAK,CAAC,WAAW,CAAC;SAClB,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;SAC9B,KAAK,CAAC,MAAM,EAAE,EAAE,CACf,GAAG,CAAC,qBAAqB,CAAC,EAC1B,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;SACpB,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC;SACjC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;IAEnC,uDAAuD;IACvD,GAAG,CAAC,QAAQ,CAAC;SACV,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,EAAE,CAAC;AACb,CAAC;AA5TD,4BA4TC;AAAA,CAAC;AAEF,IAAA,8BAAqB,EAAC,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC,CAAC"}