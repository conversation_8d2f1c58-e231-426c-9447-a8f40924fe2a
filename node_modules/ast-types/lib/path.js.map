{"version": 3, "file": "path.js", "sourceRoot": "", "sources": ["../src/path.ts"], "names": [], "mappings": ";;;AAAA,mCAAiD;AACjD,0DAAqD;AAErD,IAAI,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC;AAC1B,IAAI,MAAM,GAAG,EAAE,CAAC,cAAc,CAAC;AA0B/B,SAAwB,UAAU,CAAC,IAAU;IAC3C,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,eAAW,CAAC,CAAC;IAClC,IAAI,OAAO,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC;IACvC,IAAI,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;IAEzC,IAAM,IAAI,GAAG,SAAS,IAAI,CAAa,KAAU,EAAE,UAAgB,EAAE,IAAU;QAC7E,IAAI,CAAC,CAAC,IAAI,YAAY,IAAI,CAAC,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;SACrE;QAED,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,CAAC,UAAU,YAAY,IAAI,CAAC,EAAE;gBACjC,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;aACrB;SACF;aAAM;YACL,UAAU,GAAG,IAAI,CAAC;YAClB,IAAI,GAAG,IAAI,CAAC;SACb;QAED,0DAA0D;QAC1D,kDAAkD;QAClD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,0CAA0C;QAC1C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,kEAAkE;QAClE,4BAA4B;QAC5B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,mEAAmE;QACnE,mEAAmE;QACnE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC3B,CAA2B,CAAC;IAE5B,IAAI,EAAE,GAAS,IAAI,CAAC,SAAS,CAAC;IAE9B,SAAS,aAAa,CAAC,IAAS;QAC9B,0DAA0D;QAC1D,oEAAoE;QACpE,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IAED,SAAS,YAAY,CAAC,IAAS,EAAE,IAAS;QACxC,IAAI,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC;YAC3B,gDAAgD;YAChD,SAAS,CAAC,KAAK,KAAK,gBAAgB,EAAE;YACtC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,WAAW,CAC5C,gBAAgB,EAAE,IAAI,EAAE,IAAI,CAC7B,CAAC;SACH;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEH,sEAAsE;IACtE,kCAAkC;IAChC,EAAE,CAAC,gBAAgB,GAAG,SAAS,gBAAgB,CAAC,IAAI;QAClD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC;IAEF,EAAE,CAAC,GAAG,GAAG,SAAS,GAAG;QAAC,eAAQ;aAAR,UAAQ,EAAR,qBAAQ,EAAR,IAAQ;YAAR,0BAAQ;;QAC5B,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;QAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;YAC9B,IAAI,GAAG,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACrC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IAEF,EAAE,CAAC,IAAI,GAAG,SAAS,IAAI,CAAC,QAAQ,EAAE,OAAO;QACvC,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QAC5B,IAAI,CAAC,GAAG,CAAC,CAAC;QAEV,qEAAqE;QACrE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;YAC5B,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE;gBAC9B,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAC7B;SACF;QAED,sEAAsE;QACtE,qEAAqE;QACrE,wEAAwE;QACxE,2CAA2C;QAC3C,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC;QAC1B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;YACxB,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE;gBAC9B,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aACvC;SACF;IACH,CAAC,CAAC;IAEF,EAAE,CAAC,GAAG,GAAG,SAAS,GAAG,CAAC,QAAQ,EAAE,OAAO;QACrC,IAAI,MAAM,GAAU,EAAE,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,UAAqB,SAAc;YAC3C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;QAC9C,CAAC,EAAE,OAAO,CAAC,CAAC;QAEZ,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;IAEF,EAAE,CAAC,MAAM,GAAG,SAAS,MAAM,CAAC,QAAQ,EAAE,OAAO;QAC3C,IAAI,MAAM,GAAU,EAAE,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,UAAqB,SAAc;YAC3C,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE;gBAClC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACxB;QACH,CAAC,EAAE,OAAO,CAAC,CAAC;QAEZ,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;IAEF,SAAS,UAAU,KAAI,CAAC;IACxB,SAAS,QAAQ,CAAC,IAAS,EAAE,MAAc,EAAE,KAAW,EAAE,GAAS;QACjE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE3B,IAAI,MAAM,KAAK,CAAC,EAAE;YAChB,OAAO,UAAU,CAAC;SACnB;QAED,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QAC/B,IAAI,MAAM,GAAG,CAAC,EAAE;YACd,OAAO,UAAU,CAAC;SACnB;QAED,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC;QAC5B,IAAI,IAAI,KAAK,CAAC,EAAE;YACd,KAAK,GAAG,CAAC,CAAC;YACV,GAAG,GAAG,MAAM,CAAC;SACd;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE;YACrB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC3B,GAAG,GAAG,MAAM,CAAC;SACd;aAAM;YACL,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC3B,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;SAC7B;QAED,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACvB,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAErB,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAEhC,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;YAChC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE;gBAC9B,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC5B,IAAI,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE;oBACxB,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;iBACrB;gBACD,IAAI,QAAQ,GAAG,CAAC,GAAG,MAAM,CAAC;gBAC1B,SAAS,CAAC,IAAI,GAAG,QAAQ,CAAC;gBAC1B,KAAK,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;gBAC5B,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;aACjB;SACF;QAED,OAAO,KAAK,CAAC,MAAM,CAAC;QAEpB,OAAO;YACL,KAAK,IAAI,QAAQ,IAAI,KAAK,EAAE;gBAC1B,IAAI,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAChC,IAAI,SAAS,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE;oBAChC,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;iBACrB;gBACD,KAAK,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;gBAC5B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;aACxC;QACH,CAAC,CAAC;IACJ,CAAC;IAED,EAAE,CAAC,KAAK,GAAG,SAAS,KAAK;QACvB,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QAChC,IAAI,EAAE,CAAC;QACP,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;IAEF,EAAE,CAAC,OAAO,GAAG,SAAS,OAAO;QAAC,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACnC,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACxD,IAAI,EAAE,CAAC;QACP,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;IAEF,EAAE,CAAC,IAAI,GAAG,SAAS,IAAI;QAAC,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QAC7B,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,CAAA;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC,CAAC;IAEF,EAAE,CAAC,GAAG,GAAG,SAAS,GAAG;QACnB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAI,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAChC,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpC,OAAO,KAAK,CAAC,MAAM,CAAC;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IAC1B,CAAC,CAAC;IAEF,EAAE,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,KAAK;QACnC,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC;QAC5B,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QAC3C,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,IAAI,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC;SACb;QAED,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAE3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;SAC1C;QAED,IAAI,EAAE,CAAC;QAEP,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IAEF,EAAE,CAAC,YAAY,GAAG,SAAS,YAAY;QAAC,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QAC7C,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;QACzB,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;QACvB,IAAI,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE;YAC7B,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5B;QACD,OAAO,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEF,EAAE,CAAC,WAAW,GAAG,SAAS,WAAW;QAAC,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QAC3C,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;QACzB,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;QACvB,IAAI,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE;YAC7B,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5B;QACD,OAAO,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEF,SAAS,4BAA4B,CAAC,IAAS;QAC7C,IAAI,CAAC,CAAC,IAAI,YAAY,IAAI,CAAC,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;SACrB;QAED,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;QACzB,IAAI,CAAC,EAAE,EAAE;YACP,+CAA+C;YAC/C,OAAO,IAAI,CAAC;SACb;QAED,IAAI,WAAW,GAAG,EAAE,CAAC,KAAK,CAAC;QAC3B,IAAI,WAAW,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;QAEpC,iDAAiD;QACjD,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;YACzC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;SAC/B;aAAM,IAAI,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;YACrC,kEAAkE;YAClE,sDAAsD;YACtD,IAAI,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACV,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;aACnC;SACF;aAAM;YACL,2DAA2D;YAC3D,iEAAiE;YACjE,6DAA6D;YAC7D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;YACpC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;SAC/B;QAED,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;YACzC,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;SACrB;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;SACrB;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,EAAE,CAAC,OAAO,GAAG,SAAS,OAAO,CAAC,WAAW;QACvC,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;QACxC,IAAI,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC;QAE7B,4BAA4B,CAAC,IAAI,CAAC,CAAC;QAEnC,IAAI,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;YAC9B,IAAI,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC;YACxC,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAE/D,IAAI,UAAU,GAA+B,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;gBAC9B,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;aAC/B;YAED,IAAI,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAEnE,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;gBAChC,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;aACrB;YACD,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,cAAc,GAAG,CAAC,GAAG,KAAK,CAAC,EAAE;gBACvD,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;aACrB;YAED,IAAI,EAAE,CAAC;YAEP,IAAI,KAAK,KAAK,CAAC,EAAE;gBACf,OAAO,IAAI,CAAC,KAAK,CAAC;gBAClB,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;aAE1B;iBAAM;gBACL,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE;oBAC1C,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;iBACrB;gBAED,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE;oBAC9B,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;oBACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;iBAC1B;gBAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;oBAC1B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;iBAClD;gBAED,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;oBACvB,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;iBACrB;aACF;SAEF;aAAM,IAAI,KAAK,KAAK,CAAC,EAAE;YACtB,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE;gBAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;aAC1B;YACD,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAEpB;aAAM,IAAI,KAAK,KAAK,CAAC,EAAE;YACtB,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,OAAO,IAAI,CAAC,KAAK,CAAC;YAClB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAEzB,gEAAgE;YAChE,oCAAoC;SAErC;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;SAC3C;QAED,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;IAEF,OAAO,IAAI,CAAC;AACd,CAAC;AAzWD,6BAyWC;AAAA,CAAC;AAEF,IAAA,8BAAqB,EAAC,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC,CAAC"}