{"version": 3, "file": "scope.js", "sourceRoot": "", "sources": ["../src/scope.ts"], "names": [], "mappings": ";;;AACA,mCAAiD;AACjD,0DAA4C;AAE5C,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AA4B7C,SAAwB,WAAW,CAAC,IAAU;IAC5C,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,eAAW,CAAC,CAAC;IAClC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;IACtB,IAAI,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;IAClC,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;IAC3B,IAAI,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;IACvC,IAAI,OAAO,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC;IACvC,IAAI,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC;IAEvB,IAAM,KAAK,GAAG,SAAS,KAAK,CAAc,IAAc,EAAE,WAAoB;QAC5E,IAAI,CAAC,CAAC,IAAI,YAAY,KAAK,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;SACtE;QACD,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC7C,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC9B;QAED,IAAI,KAAa,CAAC;QAElB,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,CAAC,WAAW,YAAY,KAAK,CAAC,EAAE;gBACnC,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;aACrB;YACD,KAAK,GAAI,WAAqB,CAAC,KAAK,GAAG,CAAC,CAAC;SAC1C;aAAM;YACL,WAAW,GAAG,IAAI,CAAC;YACnB,KAAK,GAAG,CAAC,CAAC;SACX;QAED,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;YACrB,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;YAC3B,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,IAAI,EAAE;YACnD,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;YACvB,MAAM,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE;YAC9B,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;YACvB,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;SACrB,CAAC,CAAC;IACL,CAA4B,CAAC;IAE7B,IAAI,SAAS,GAAG,IAAI,CAAC,EAAE;IACrB,yCAAyC;IACzC,UAAU,CAAC,OAAO;IAElB,mDAAmD;IACnD,6CAA6C;IAC7C,UAAU,CAAC,QAAQ;IAEnB,qEAAqE;IACrE,sCAAsC;IACtC,UAAU,CAAC,WAAW,CACvB,CAAC;IAEF,yEAAyE;IACzE,2CAA2C;IAC3C,IAAI,sBAAsB,GAAG,IAAI,CAAC,EAAE,CAClC,UAAU,CAAC,QAAQ,EACnB,UAAU,CAAC,gBAAgB,EAC3B,UAAU,CAAC,eAAe,EAC1B,UAAU,CAAC,oBAAoB,EAC/B,UAAU,CAAC,sBAAsB,EACjC,UAAU,CAAC,SAAS,EACpB,UAAU,CAAC,sBAAsB,CAClC,CAAC;IAEF,IAAI,yBAAyB,GAAG,IAAI,CAAC,EAAE,CACrC,UAAU,CAAC,aAAa,EACxB,UAAU,CAAC,eAAe,CAC3B,CAAC;IAEF,KAAK,CAAC,eAAe,GAAG,UAAS,IAAI;QACnC,OAAO,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACrE,CAAC,CAAC;IAEF,IAAI,EAAE,GAAU,KAAK,CAAC,SAAS,CAAC;IAElC,+DAA+D;IAC7D,EAAE,CAAC,OAAO,GAAG,KAAK,CAAC;IAEnB,EAAE,CAAC,QAAQ,GAAG,UAAS,IAAI;QACzB,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,EAAE,CAAC,YAAY,GAAG,UAAS,IAAI;QAC7B,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,EAAE,CAAC,gBAAgB,GAAG,UAAS,MAAM;QACnC,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC7B,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;aACrB;SACF;aAAM;YACL,MAAM,GAAG,IAAI,CAAC;SACf;QAED,gEAAgE;QAChE,yDAAyD;QACzD,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;QAExC,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE;YACpC,EAAE,KAAK,CAAC;SACT;QAED,IAAI,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC,CAAC;IAEF,EAAE,CAAC,eAAe,GAAG,UAAS,UAAU,EAAE,IAAI;QAC5C,UAAU,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;QAErD,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACnD,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SACjC;QAED,QAAQ,CAAC,OAAO,CACd,CAAC,CAAC,mBAAmB,CACrB,KAAK,EACL,CAAC,CAAC,CAAC,kBAAkB,CAAC,UAAU,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC,CAC/C,CACF,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;IAEF,EAAE,CAAC,IAAI,GAAG,UAAS,KAAK;QACtB,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YAC1B,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAC9B,0CAA0C;gBAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;aAC5B;YACD,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;gBAC3B,uCAAuC;gBACvC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aACzB;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACrB;IACH,CAAC,CAAC;IAEF,EAAE,CAAC,WAAW,GAAG;QACf,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC,CAAC;IAEF,EAAE,CAAC,QAAQ,GAAG;QACZ,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC,CAAC;IAEF,SAAS,SAAS,CAAC,IAAc,EAAE,QAAa,EAAE,UAAe;QAC/D,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACtB,IAAI,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YACpD,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAC/B,MAAM,CAAC,IAAI,CAAC,UAAC,SAAmB;oBAC9B,gBAAgB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAC1C,CAAC,CAAC,CAAC;aACJ;SACF;QACD,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtC,+DAA+D;gBAC/D,wDAAwD;gBACxD,mDAAmD;gBACnD,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC;aACzC;iBAAM;gBACL,kBAAkB,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;aAChD;SACF;IACH,CAAC;IAED,SAAS,kBAAkB,CAAC,IAAc,EAAE,QAAa,EAAE,UAAe;QACxE,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QAEtB,IAAI,IAAI,CAAC,MAAM;YACb,UAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACrD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE;YACrB,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,IAAI,EAAE;YACT,uDAAuD;SAExD;aAAM,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC9B,IAAI,CAAC,IAAI,CAAC,UAAC,SAAmB;gBAC5B,kBAAkB,CAAC,SAAS,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;SAEJ;aAAM,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1C,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAC,SAAmB;gBAC1C,UAAU,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC3D,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;SAEtE;aAAM,IACL,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1D,CAAC,UAAU,CAAC,oBAAoB,IAAI,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChF,CAAC,UAAU,CAAC,sBAAsB,IAAI,UAAU,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACpF,CAAC,UAAU,CAAC,sBAAsB,IAAI,UAAU,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EACpF;YACA,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,CAAC;SAE5C;aAAM,IAAI,UAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACpD,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;YACrC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;SAE5D;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAiB;YACxC,IAAI,CAAC,IAAI,KAAK,0BAA0B;YACxC,IAAI,CAAC,IAAI,KAAK,wBAAwB,EAAE;YACxC,UAAU;YACR,4DAA4D;YAC5D,4DAA4D;YAC5D,6DAA6D;YAC7D,2DAA2D;YAC3D,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;gBAC/B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAC1B,QAAQ,CACT,CAAC;SAEH;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACtD,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,UAAS,IAAS,EAAE,KAAU;gBAClD,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC/B,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE;oBACnC,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;iBACrB;gBACD,kBAAkB,CAAC,SAAS,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;SACJ;IACH,CAAC;IAED,SAAS,YAAY,CAAC,IAAc,EAAE,KAAU;QAC9C,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;YACxB,OAAO,IAAI,CAAC;SACb;QAED,sEAAsE;QACtE,gEAAgE;QAChE,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;YACvB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;YACpB,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,OAAO,IAAI,CAAC;SACb;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,SAAS,kBAAkB,CAAC,IAAc,EAAE,QAAa,EAAE,UAAe;QACxE,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QAEtB,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACnC,uCAAuC;SAExC;aAAM,IAAI,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC;YAC9C,IAAI,CAAC,EAAE,KAAK,IAAI,EAAE;YACvB,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;SAEtC;aAAM,IAAI,UAAU,CAAC,gBAAgB;YACpC,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC;YACvC,IAAI,CAAC,EAAE,KAAK,IAAI,EAAE;YAClB,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;YACrC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;SAEtE;aAAM,IACL,CAAC,UAAU,CAAC,oBAAoB;YAC/B,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC7C,CAAC,UAAU,CAAC,sBAAsB;gBACjC,UAAU,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAC/C;YACA,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,CAAC;SAE5C;aAAM,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAChC,IACE,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;gBAClC,2CAA2C;gBAC3C,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EACvC;gBACA,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBACrC,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;gBAEvD,4DAA4D;gBAC5D,6DAA6D;gBAC7D,kCAAkC;gBAClC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;gBAE3D,yDAAyD;gBACzD,8DAA8D;gBAC9D,2DAA2D;gBAC3D,uCAAuC;gBACvC,IAAI,CAAC,UAAU,EAAE;oBACf,OAAO,QAAQ,CAAC,cAAc,CAAC,CAAC;iBACjC;aACF;SAEF;aAAM;YACL,kBAAkB,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;SAChD;IACH,CAAC;IAED,SAAS,UAAU,CAAC,WAAqB,EAAE,QAAa;QACtD,IAAI,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC;QAChC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEnC,IAAI,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;YACxC,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;gBACvC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAC1C;iBAAM;gBACL,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;aACxC;SAEF;aAAM,IAAI,UAAU,CAAC,iBAAiB;YACrC,UAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;YAC7C,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAC;SAE/C;aAAM,IACL,UAAU,CAAC,aAAa;YACxB,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,EACvC;YACA,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,UAAS,YAAiB;gBAC3D,IAAI,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC;gBAClC,IAAI,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;oBACtC,UAAU,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;iBACpC;qBAAM,IACL,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;oBACnC,CAAC,UAAU,CAAC,cAAc;wBACzB,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAC3C;oBACA,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC;iBACjD;qBAAM,IACL,UAAU,CAAC,cAAc;oBACzB,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,EACzC;oBACA,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,CAAC;iBACpD;YACH,CAAC,CAAC,CAAC;SAEJ;aAAM,IACL,UAAU,CAAC,YAAY;YACvB,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,EACtC;YACA,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAS,WAAgB;gBACxD,IAAI,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC;gBAChC,IAAI,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;oBACrC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;iBACnC;qBAAM,IACL,UAAU,CAAC,aAAa;oBACxB,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,EACvC;oBACA,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,CAAC;iBACnD;YACH,CAAC,CAAC,CAAC;SAEJ;aAAM,IACL,UAAU,CAAC,eAAe;YAC1B,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,EACzC;YACA,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,CAAC;SAElD;aAAM,IACL,CAAC,UAAU,CAAC,oBAAoB;YAC/B,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAChD,CAAC,UAAU,CAAC,WAAW;gBACtB,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACvC,CAAC,UAAU,CAAC,qBAAqB;gBAChC,UAAU,CAAC,qBAAqB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EACjD;YACA,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,CAAC;SACnD;IACH,CAAC;IAED,SAAS,cAAc,CAAC,WAAqB,EAAE,KAAU;QACvD,IAAI,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC;QAChC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEnC,IAAI,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;YACxC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;gBACpC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aACvC;iBAAM;gBACL,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;aACrC;SACF;IACH,CAAC;IAED,SAAS,gBAAgB,CAAC,aAAuB,EAAE,KAAU;QAC3D,IAAI,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC;QACpC,yBAAyB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAE5C,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE;YACtC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAC3C;aAAM;YACL,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;SACzC;IACH,CAAC;IAED,EAAE,CAAC,MAAM,GAAG,UAAS,IAAI;QACvB,KAAK,IAAI,KAAK,GAAG,IAAI,EAAE,KAAK,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM;YAChD,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACtB,MAAM;QACV,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,EAAE,CAAC,UAAU,GAAG,UAAS,IAAI;QAC3B,KAAK,IAAI,KAAK,GAAG,IAAI,EAAE,KAAK,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM;YAChD,IAAI,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC;gBAC1B,MAAM;QACV,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,EAAE,CAAC,cAAc,GAAG;QAClB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,OAAO,CAAC,KAAK,CAAC,QAAQ;YACpB,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;QACvB,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,OAAO,KAAK,CAAC;AACf,CAAC;AAzaD,8BAyaC;AAAA,CAAC;AAEF,IAAA,8BAAqB,EAAC,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC,CAAC"}