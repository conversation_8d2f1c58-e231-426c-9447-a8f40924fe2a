{"version": 3, "file": "node-path.js", "sourceRoot": "", "sources": ["../src/node-path.ts"], "names": [], "mappings": ";;;AAAA,0DAAqD;AACrD,wDAA0C;AAC1C,0DAA6C;AAE7C,mCAAiD;AAqBjD,SAAwB,cAAc,CAAC,IAAU;IAC/C,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,eAAW,CAAC,CAAC;IAClC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC;IACzB,IAAI,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC;IACvB,IAAI,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;IACzC,IAAI,OAAO,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC;IACvC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,cAAU,CAAC,CAAC;IAChC,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,eAAW,CAAC,CAAC;IAElC,IAAM,QAAQ,GAAG,SAAS,QAAQ,CAAiB,KAAU,EAAE,UAAgB,EAAE,IAAU;QACzF,IAAI,CAAC,CAAC,IAAI,YAAY,QAAQ,CAAC,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;SACzE;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IAC3C,CAA+B,CAAC;IAEhC,IAAI,GAAG,GAAa,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE;QACrE,WAAW,EAAE;YACX,KAAK,EAAE,QAAQ;YACf,UAAU,EAAE,KAAK;YACjB,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI;SACnB;KACF,CAAC,CAAC;IAEH,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE;QAC3B,IAAI,EAAE;YACJ,GAAG,EAAE;gBACH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;oBAClC,YAAY,EAAE,IAAI;oBAClB,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE;iBAC3B,CAAC,CAAC;gBAEH,OAAO,IAAI,CAAC,IAAI,CAAC;YACnB,CAAC;SACF;QAED,MAAM,EAAE;YACN,GAAG,EAAE;gBACH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE;oBACpC,YAAY,EAAE,IAAI;oBAClB,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE;iBAC7B,CAAC,CAAC;gBAEH,OAAO,IAAI,CAAC,MAAM,CAAC;YACrB,CAAC;SACF;QAED,KAAK,EAAE;YACL,GAAG,EAAE;gBACH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;oBACnC,YAAY,EAAE,IAAI;oBAClB,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE;iBAC5B,CAAC,CAAC;gBAEH,OAAO,IAAI,CAAC,KAAK,CAAC;YACpB,CAAC;SACF;KACF,CAAC,CAAC;IAEH,GAAG,CAAC,OAAO,GAAG;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACvD,CAAC,CAAC;IAEF,GAAG,CAAC,KAAK,GAAG;QACV,IAAI,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC;QAEpC,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,OAAO,sBAAsB,CAAC,iBAAiB,CAAC,CAAC;IACnD,CAAC,CAAC;IAEF,8DAA8D;IAC9D,GAAG,CAAC,YAAY,GAAG;QACjB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvB,OAAO,KAAK,CAAC;SACd;QAED,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;QACzB,OAAO,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,CAAC;IAC/B,CAAC,CAAC;IAEF,yEAAyE;IACzE,GAAG,CAAC,cAAc,GAAG;QACnB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;QAEzB,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACxB,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;gBACpC,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC;aACpB;YAED,IAAI,EAAE,EAAE;gBACN,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC;aACpB;SACF;QAED,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;YACpC,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC;SACpB;QAED,OAAO,EAAE,IAAI,IAAI,CAAC;IACpB,CAAC,CAAC;IAEF,sDAAsD;IACtD,GAAG,CAAC,aAAa,GAAG;QAClB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;QACzB,IAAI,KAAK,GAAG,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC;QAE3B,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;YACrB,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;YAC9B,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SAChC;QAED,OAAO,KAAK,IAAI,IAAI,CAAC;IACvB,CAAC,CAAC;IAEF,GAAG,CAAC,gBAAgB,GAAG,UAAU,IAAI;QACnC,OAAO,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/C,CAAC,CAAC;IAEF;;;;;;;;;;;;OAYG;IACH,GAAG,CAAC,WAAW,GAAG,UAAU,uBAAuB;QACjD,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;QACzB,IAAI,CAAC,EAAE,EAAE;YACP,OAAO,KAAK,CAAC;SACd;QAED,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QAEtB,qCAAqC;QACrC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC7B,OAAO,KAAK,CAAC;SACd;QAED,sCAAsC;QACtC,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE;YAC9B,OAAO,KAAK,CAAC;SACd;QAED,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;YAC9B,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC;YACnB,IAAI,CAAC,EAAE,EAAE;gBACP,OAAO,KAAK,CAAC;aACd;SACF;QAED,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC;QAEtB,QAAQ,IAAI,CAAC,IAAI,EAAE;YACjB,KAAK,iBAAiB,CAAC;YACvB,KAAK,eAAe,CAAC;YACrB,KAAK,gBAAgB;gBACnB,OAAO,MAAM,CAAC,IAAI,KAAK,kBAAkB;uBACpC,IAAI,CAAC,IAAI,KAAK,QAAQ;uBACtB,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;YAE9B,KAAK,kBAAkB,CAAC;YACxB,KAAK,mBAAmB;gBACtB,QAAQ,MAAM,CAAC,IAAI,EAAE;oBACnB,KAAK,gBAAgB;wBACnB,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ;+BACxB,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;oBAE9B,KAAK,iBAAiB,CAAC;oBACvB,KAAK,eAAe,CAAC;oBACrB,KAAK,gBAAgB;wBACnB,OAAO,IAAI,CAAC;oBAEd,KAAK,kBAAkB;wBACrB,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ;+BACxB,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;oBAE9B,KAAK,kBAAkB,CAAC;oBACxB,KAAK,mBAAmB,CAAC,CAAC;wBACxB,IAAM,GAAC,GAAG,IAAkE,CAAC;wBAC7E,IAAM,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC;wBAC3B,IAAM,IAAE,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;wBAC1B,IAAM,EAAE,GAAG,GAAC,CAAC,QAAQ,CAAC;wBACtB,IAAM,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;wBAE1B,IAAI,IAAE,GAAG,EAAE,EAAE;4BACX,OAAO,IAAI,CAAC;yBACb;wBAED,IAAI,IAAE,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;4BACtC,IAAI,MAAM,CAAC,KAAK,KAAK,GAAC,EAAE;gCACtB,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;6BACxC;4BACD,OAAO,IAAI,CAAC;yBACb;qBACF;oBAED;wBACE,OAAO,KAAK,CAAC;iBAChB;YAEH,KAAK,oBAAoB;gBACvB,QAAQ,MAAM,CAAC,IAAI,EAAE;oBACnB,KAAK,cAAc;wBACjB,qDAAqD;wBACrD,0DAA0D;wBAC1D,yDAAyD;wBACzD,eAAe;wBACf,OAAO,KAAK,CAAC;oBAEf,KAAK,qBAAqB;wBACxB,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC;oBAEpC;wBACE,4DAA4D;wBAC5D,wDAAwD;wBACxD,OAAO,IAAI,CAAC;iBACf;YAEH,KAAK,iBAAiB;gBACpB,QAAQ,MAAM,CAAC,IAAI,EAAE;oBACnB,KAAK,kBAAkB,CAAC;oBACxB,KAAK,mBAAmB,CAAC;oBACzB,KAAK,iBAAiB,CAAC;oBACvB,KAAK,eAAe,CAAC;oBACrB,KAAK,gBAAgB,CAAC;oBACtB,KAAK,gBAAgB,CAAC;oBACtB,KAAK,kBAAkB,CAAC;oBACxB,KAAK,eAAe,CAAC;oBACrB,KAAK,uBAAuB,CAAC;oBAC7B,KAAK,iBAAiB;wBACpB,OAAO,IAAI,CAAC;oBAEd;wBACE,OAAO,KAAK,CAAC;iBAChB;YAEH,KAAK,SAAS;gBACZ,OAAO,MAAM,CAAC,IAAI,KAAK,kBAAkB;uBACpC,QAAQ,CAAC,KAAK,CAAE,IAA2B,CAAC,KAAK,CAAC;uBAClD,IAAI,CAAC,IAAI,KAAK,QAAQ;uBACtB,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;YAE9B,KAAK,sBAAsB,CAAC;YAC5B,KAAK,uBAAuB;gBAC1B,QAAQ,MAAM,CAAC,IAAI,EAAE;oBACnB,KAAK,iBAAiB,CAAC;oBACvB,KAAK,eAAe,CAAC;oBACrB,KAAK,gBAAgB,CAAC;oBACtB,KAAK,kBAAkB,CAAC;oBACxB,KAAK,mBAAmB;wBACtB,OAAO,IAAI,CAAC;oBAEd,KAAK,gBAAgB;wBACnB,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ;+BACxB,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;oBAE9B,KAAK,uBAAuB;wBAC1B,OAAO,IAAI,CAAC,IAAI,KAAK,MAAM;+BACtB,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC;oBAE5B,KAAK,kBAAkB;wBACrB,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ;+BACxB,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;oBAE9B;wBACE,OAAO,KAAK,CAAC;iBAChB;YAEH;gBACE,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe;oBACjC,IAAI,CAAC,IAAI,KAAK,QAAQ;oBACtB,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;oBACxB,OAAO,sBAAsB,CAAC,IAAI,CAAC,CAAC;iBACrC;SACJ;QAED,IAAI,uBAAuB,KAAK,IAAI;YAClC,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO,IAAI,CAAC;QAEd,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,SAAS,QAAQ,CAAC,IAAS;QACzB,OAAO,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC;eAChC,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED,2EAA2E;IAC3E,SAAS,WAAW,CAAC,IAAS;QAC5B,OAAO,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC;YAClC,gEAAgE;YAChE,+DAA+D;eAC5D,CAAC,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;eAChD,CAAC,CAAC,CAAC,cAAc,IAAI,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED,IAAI,UAAU,GAAQ,EAAE,CAAC;IACzB,CAAC,CAAC,IAAI,CAAC;QACL,CAAC,IAAI,CAAC;QACN,CAAC,GAAG,CAAC;QACL,CAAC,GAAG,CAAC;QACL,CAAC,GAAG,CAAC;QACL,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;QAC1B,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC;QAC1C,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;QACnB,CAAC,GAAG,EAAE,GAAG,CAAC;QACV,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;KAChB,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QACzB,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YACvB,UAAU,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,SAAS,sBAAsB,CAAC,IAAS;QACvC,IAAI,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC;SACb;QAED,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACvB,OAAO,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACtB,OAAO,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,KAAU,EAAE,KAAU;gBAC3D,OAAO,sBAAsB,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,GAAG,CAAC,qBAAqB,GAAG;QAC1B,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrB,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC;eACnC,CAAC,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,GAAG,CAAC,gBAAgB,GAAG;QACrB,OAAO,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC,CAAC;IAEF,SAAS,gBAAgB,CAAC,IAAS;QACjC,KAAK,IAAI,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE;YACtD,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACjB,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YAE1B,IAAI,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM;gBAC3B,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;gBACjB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;oBAC3B,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;iBACxC;gBACD,OAAO,IAAI,CAAC;aACb;YAED,IAAI,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC;gBACrC,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE;gBAC5B,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,EAAE;oBAC9B,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;iBACxC;gBACD,OAAO,IAAI,CAAC;aACb;YAED,IAAI,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC;gBACpC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,aAAa;gBAClC,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;gBACjB,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;oBAClC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;iBACxC;gBACD,SAAS;aACV;YAED,IAAI,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC;gBAChC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACxB,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;oBAC1B,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;iBACxC;gBACD,SAAS;aACV;YAED,IAAI,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACxB,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;oBAC1B,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;iBACxC;gBACD,SAAS;aACV;YAED,IAAI,CAAC,CAAC,qBAAqB,CAAC,KAAK,CAAC,MAAM,CAAC;gBACvC,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;gBACtB,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;oBACxB,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;iBACxC;gBACD,SAAS;aACV;YAED,IAAI,QAAQ,CAAC,MAAM,CAAC;gBAClB,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;gBACtB,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;oBACxB,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;iBACxC;gBACD,SAAS;aACV;YAED,IAAI,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC;gBACjC,CAAC,MAAM,CAAC,MAAM;gBACd,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;gBAC1B,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,EAAE;oBAC5B,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;iBACxC;gBACD,SAAS;aACV;YAED,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,SAAS,sBAAsB,CAAC,iBAAsB;QACpD,IAAI,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;YACvD,IAAI,YAAY,GAAG,iBAAiB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC;YAC/D,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC9C,OAAO,iBAAiB,CAAC,KAAK,EAAE,CAAC;aAClC;SACF;aAAM,IAAI,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;YAC9D,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE;gBAC9C,OAAO,iBAAiB,CAAC,KAAK,EAAE,CAAC;aAClC;SACF;aAAM,IAAI,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;YACtD,4BAA4B,CAAC,iBAAiB,CAAC,CAAC;SACjD;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,SAAS,4BAA4B,CAAC,WAAgB;QACpD,IAAI,cAAc,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;QACnD,IAAI,SAAS,GAAG,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC;QACnD,IAAI,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC;QAErD,IAAI,CAAC,UAAU,IAAI,CAAC,SAAS,EAAE;YAC7B,IAAI,uBAAuB,GAAG,CAAC,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;YAEpE,WAAW,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;SAC9C;aAAM,IAAI,CAAC,UAAU,IAAI,SAAS,EAAE;YACnC,IAAI,qBAAqB,GACvB,CAAC,CAAC,eAAe,CAAC,GAAG,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;YAE/C,IAAI,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,cAAc,CAAC,QAAQ,KAAK,GAAG,EAAE;gBAC9E,qBAAqB,GAAG,cAAc,CAAC,QAAQ,CAAC;aACjD;YAED,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YACvD,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACjD,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,CAAC;SACxC;IACH,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AA/dD,iCA+dC;AAAA,CAAC;AAEF,IAAA,8BAAqB,EAAC,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC,CAAC"}