{"name": "babel-plugin-transform-exponentiation-operator", "version": "6.24.1", "description": "Compile exponentiation operator to ES5", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-exponentiation-operator", "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"babel-plugin-syntax-exponentiation-operator": "^6.8.0", "babel-helper-builder-binary-assignment-operator-visitor": "^6.24.1", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}}