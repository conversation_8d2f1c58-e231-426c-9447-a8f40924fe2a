var util = require('util');
var PassThrough = require('readable-stream/passthrough');

module.exports = {
  Readable: Readable,
  Writable: Writable
};

util.inherits(Readable, PassThrough);
util.inherits(Writable, PassThrough);

// Patch the given method of instance so that the callback
// is executed once, before the actual method is called the
// first time.
function beforeFirstCall(instance, method, callback) {
  instance[method] = function() {
    delete instance[method];
    callback.apply(this, arguments);
    return this[method].apply(this, arguments);
  };
}

function Readable(fn, options) {
  if (!(this instanceof Readable))
    return new Readable(fn, options);

  PassThrough.call(this, options);

  beforeFirstCall(this, '_read', function() {
    var source = fn.call(this, options);
    var emit = this.emit.bind(this, 'error');
    source.on('error', emit);
    source.pipe(this);
  });

  this.emit('readable');
}

function Writable(fn, options) {
  if (!(this instanceof Writable))
    return new Writable(fn, options);

  PassThrough.call(this, options);

  beforeFirstCall(this, '_write', function() {
    var destination = fn.call(this, options);
    var emit = this.emit.bind(this, 'error');
    destination.on('error', emit);
    this.pipe(destination);
  });

  this.emit('writable');
}

