{"name": "@npmcli/agent", "version": "2.2.2", "description": "the http/https agent used by the npm cli", "main": "lib/index.js", "scripts": {"gencerts": "bash scripts/create-cert.sh", "test": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "author": "GitHub Inc.", "license": "ISC", "bugs": {"url": "https://github.com/npm/agent/issues"}, "homepage": "https://github.com/npm/agent#readme", "files": ["bin/", "lib/"], "engines": {"node": "^16.14.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.21.3", "publish": "true"}, "dependencies": {"agent-base": "^7.1.0", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.1", "lru-cache": "^10.0.1", "socks-proxy-agent": "^8.0.3"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.21.3", "minipass-fetch": "^3.0.3", "nock": "^13.2.7", "semver": "^7.5.4", "simple-socks": "^3.1.0", "tap": "^16.3.0"}, "repository": {"type": "git", "url": "https://github.com/npm/agent.git"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}