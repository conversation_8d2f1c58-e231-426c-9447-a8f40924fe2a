{"name": "fastest-stable-stringify", "version": "2.0.2", "description": "Fastest stable deterministic JSON.stringify()", "main": "index.js", "dependencies": {}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.0.0", "eslint": "^4.9.0", "fast-stable-stringify": "latest", "faster-stable-stringify": "latest", "json-stable-stringify": "latest", "fast-json-stable-stringify": "latest", "nyc": "^11.2.1", "pre-commit": "^1.2.2", "tape": "~1.0.4", "mol-conventional-changelog": "1.2.0", "chai": "^4.1.2"}, "scripts": {"eslint": "eslint index.js test", "test-spec": "tape test/*.js", "test": "npm run eslint && nyc npm run test-spec"}, "repository": {"type": "git", "url": "https://github.com/streamich/fastest-stable-stringify"}, "homepage": "https://github.com/streamich/fastest-stable-stringify", "keywords": ["json", "stringify", "deterministic", "hash", "stable"], "license": "MIT", "nyc": {"exclude": ["test", "node_modules"], "reporter": ["lcov", "text-summary"]}, "config": {"commitizen": {"path": "./node_modules/mol-conventional-changelog"}}}