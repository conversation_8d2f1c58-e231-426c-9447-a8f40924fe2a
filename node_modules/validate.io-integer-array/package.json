{"name": "validate.io-integer-array", "version": "1.0.0", "description": "Validates if a value is an integer array.", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "./node_modules/.bin/mocha", "test-cov": "./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --dir ./reports/coverage -- -R spec", "coveralls": "./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --dir ./reports/coveralls/coverage --report lcovonly -- -R spec && cat ./reports/coveralls/coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js && rm -rf ./reports/coveralls"}, "main": "./lib", "repository": {"type": "git", "url": "git://github.com/validate-io/integer-array.git"}, "keywords": ["validate.io", "validate", "validation", "validator", "valid", "integer", "array", "integerarray", "is", "isintegerarray"], "bugs": {"url": "https://github.com/validate-io/integer-array/issues"}, "dependencies": {"validate.io-array": "^1.0.3", "validate.io-integer": "^1.0.4"}, "devDependencies": {"chai": "1.x.x", "mocha": "1.x.x", "coveralls": "^2.11.1", "istanbul": "^0.3.0", "jshint": "2.x.x", "jshint-stylish": "^1.0.0"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}]}