{"name": "hookable", "version": "5.5.3", "description": "Awaitable hook system", "keywords": ["hook", "hookable", "plugin", "tapable", "tappable"], "repository": "unjs/hookable", "license": "MIT", "exports": {"import": "./dist/index.mjs", "types": "./dist/index.d.ts", "require": "./dist/index.cjs"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "devDependencies": {"@types/node": "^18.15.11", "@vitest/coverage-c8": "^0.29.8", "changelogen": "^0.5.2", "eslint": "^8.37.0", "eslint-config-unjs": "^0.1.0", "expect-type": "^0.15.0", "prettier": "^2.8.7", "typescript": "^5.0.2", "unbuild": "^1.1.2", "vite": "^4.2.1", "vitest": "^0.29.8"}, "packageManager": "pnpm@8.0.0", "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint --cache --ext .ts,.js,.mjs,.cjs . && prettier -c src test", "lint:fix": "eslint --cache --ext .ts,.js,.mjs,.cjs . --fix && prettier -c src test -w", "prepublish": "pnpm build", "release": "pnpm test && pnpm build && changelogen --release --push && pnpm publish", "test": "pnpm lint && vitest run --coverage", "test:types": "tsc --noEmit"}}