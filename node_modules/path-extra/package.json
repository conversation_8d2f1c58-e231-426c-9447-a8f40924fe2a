{"name": "path-extra", "version": "1.0.3", "description": "path-extra contains methods that aren't included in the vanilla Node.js path package.", "homepage": "https://github.com/jprichardson/node-path-extra", "repository": {"type": "git", "url": "https://github.com/jprichardson/node-path-extra"}, "keywords": ["fs", "file", "file system", "path"], "author": "<PERSON> <jp<PERSON><PERSON><EMAIL>>", "license": "MIT", "devDependencies": {"mocha": "*", "standard": "^2.10.0"}, "main": "./lib/path.js", "scripts": {"test": "standard && mocha"}}