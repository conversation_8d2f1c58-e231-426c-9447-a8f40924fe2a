/*!
  Theme: Isotope
  Author: Jan <PERSON>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#d0d0d0;background:#000}.hljs ::selection,.hljs::selection{background-color:#606060;color:#d0d0d0}.hljs-comment{color:grey}.hljs-tag{color:silver}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#d0d0d0}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:red}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#f90}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#f09}.hljs-strong{font-weight:700;color:#f09}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#3f0}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#0ff}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#06f}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#c0f}.hljs-emphasis{color:#c0f;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#30f}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}