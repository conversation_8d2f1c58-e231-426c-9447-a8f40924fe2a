pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: darkmoss
  Author: <PERSON> (https://github.com/a<PERSON>)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme darkmoss
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #171e1f  Default Background
base01  #252c2d  Lighter Background (Used for status bars, line number and folding marks)
base02  #373c3d  Selection Background
base03  #555e5f  Comments, Invisibles, Line Highlighting
base04  #818f80  Dark Foreground (Used for status bars)
base05  #c7c7a5  Default Foreground, Caret, Delimiters, Operators
base06  #e3e3c8  Light Foreground (Not often used)
base07  #e1eaef  Light Background (Not often used)
base08  #ff4658  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #e6db74  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #fdb11f  Classes, Markup Bold, Search Text Background
base0B  #499180  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #66d9ef  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #498091  Functions, Methods, Attribute IDs, Headings
base0E  #9bc0c8  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #d27b53  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #c7c7a5;
  background: #171e1f
}
.hljs::selection,
.hljs ::selection {
  background-color: #373c3d;
  color: #c7c7a5
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #555e5f -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #555e5f
}
/* base04 - #818f80 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #818f80
}
/* base05 - #c7c7a5 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #c7c7a5
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ff4658
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #e6db74
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #fdb11f
}
.hljs-strong {
  font-weight: bold;
  color: #fdb11f
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #499180
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #66d9ef
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #498091
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #9bc0c8
}
.hljs-emphasis {
  color: #9bc0c8;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #d27b53
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}