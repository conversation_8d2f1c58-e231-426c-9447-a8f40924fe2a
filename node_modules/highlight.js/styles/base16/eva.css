pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Eva
  Author: kjakapat (https://github.com/kjakapat)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme eva
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #2a3b4d  Default Background
base01  #3d566f  Lighter Background (Used for status bars, line number and folding marks)
base02  #4b6988  Selection Background
base03  #55799c  Comments, Invisibles, Line Highlighting
base04  #7e90a3  Dark Foreground (Used for status bars)
base05  #9fa2a6  Default Foreground, Caret, Delimiters, Operators
base06  #d6d7d9  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #c4676c  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #ff9966  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #ffff66  Classes, Markup Bold, Search Text Background
base0B  #66ff66  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #4b8f77  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #15f4ee  Functions, Methods, Attribute IDs, Headings
base0E  #9c6cd3  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #bb64a9  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #9fa2a6;
  background: #2a3b4d
}
.hljs::selection,
.hljs ::selection {
  background-color: #4b6988;
  color: #9fa2a6
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #55799c -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #55799c
}
/* base04 - #7e90a3 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #7e90a3
}
/* base05 - #9fa2a6 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #9fa2a6
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #c4676c
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #ff9966
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #ffff66
}
.hljs-strong {
  font-weight: bold;
  color: #ffff66
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #66ff66
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #4b8f77
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #15f4ee
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #9c6cd3
}
.hljs-emphasis {
  color: #9c6cd3;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #bb64a9
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}