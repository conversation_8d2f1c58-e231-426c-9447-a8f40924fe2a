pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Horizon Light
  Author: <PERSON><PERSON><PERSON> (http://github.com/michael-ball/)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme horizon-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #FDF0ED  Default Background
base01  #FADAD1  Lighter Background (Used for status bars, line number and folding marks)
base02  #F9CBBE  Selection Background
base03  #BDB3B1  Comments, Invisibles, Line Highlighting
base04  #948C8A  Dark Foreground (Used for status bars)
base05  #403C3D  Default Foreground, Caret, Delimiters, Operators
base06  #302C2D  Light Foreground (Not often used)
base07  #201C1D  Light Background (Not often used)
base08  #E95678  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #F9CEC3  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #FADAD1  Classes, Markup Bold, Search Text Background
base0B  #29D398  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #59E1E3  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #26BBD9  Functions, Methods, Attribute IDs, Headings
base0E  #EE64AC  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #F9CBBE  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #403C3D;
  background: #FDF0ED
}
.hljs::selection,
.hljs ::selection {
  background-color: #F9CBBE;
  color: #403C3D
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #BDB3B1 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #BDB3B1
}
/* base04 - #948C8A -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #948C8A
}
/* base05 - #403C3D -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #403C3D
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #E95678
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #F9CEC3
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #FADAD1
}
.hljs-strong {
  font-weight: bold;
  color: #FADAD1
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #29D398
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #59E1E3
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #26BBD9
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #EE64AC
}
.hljs-emphasis {
  color: #EE64AC;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #F9CBBE
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}