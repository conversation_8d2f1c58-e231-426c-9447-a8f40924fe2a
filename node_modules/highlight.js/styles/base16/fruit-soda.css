pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Fruit Soda
  Author: jozip
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme fruit-soda
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #f1ecf1  Default Background
base01  #e0dee0  Lighter Background (Used for status bars, line number and folding marks)
base02  #d8d5d5  Selection Background
base03  #b5b4b6  Comments, Invisibles, Line Highlighting
base04  #979598  Dark Foreground (Used for status bars)
base05  #515151  Default Foreground, Caret, Delimiters, Operators
base06  #474545  Light Foreground (Not often used)
base07  #2d2c2c  Light Background (Not often used)
base08  #fe3e31  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #fe6d08  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #f7e203  Classes, Markup Bold, Search Text Background
base0B  #47f74c  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #0f9cfd  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #2931df  Functions, Methods, Attribute IDs, Headings
base0E  #611fce  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b16f40  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #515151;
  background: #f1ecf1
}
.hljs::selection,
.hljs ::selection {
  background-color: #d8d5d5;
  color: #515151
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #b5b4b6 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #b5b4b6
}
/* base04 - #979598 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #979598
}
/* base05 - #515151 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #515151
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #fe3e31
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #fe6d08
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #f7e203
}
.hljs-strong {
  font-weight: bold;
  color: #f7e203
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #47f74c
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #0f9cfd
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #2931df
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #611fce
}
.hljs-emphasis {
  color: #611fce;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #b16f40
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}