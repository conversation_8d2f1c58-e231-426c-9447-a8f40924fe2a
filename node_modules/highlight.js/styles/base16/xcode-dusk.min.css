/*!
  Theme: XCode Dusk
  Author: <PERSON> (https://github.com/gonsie)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#939599;background:#282b35}.hljs ::selection,.hljs::selection{background-color:#53555d;color:#939599}.hljs-comment{color:#686a71}.hljs-tag{color:#7e8086}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#939599}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#b21889}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#786dc5}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#438288}.hljs-strong{font-weight:700;color:#438288}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#df0002}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#00a0be}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#790ead}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#b21889}.hljs-emphasis{color:#b21889;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#c77c48}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}