pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*

Brown Paper style from goldblog.com.ua (c) Zaripov Yura <<EMAIL>>

*/
.hljs {
  color: #363c69;
  background: #b7a68e url(./brown-papersq.png)
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal {
  color: #005599;
  font-weight: bold
}
.hljs-subst {
  /* default */
  
}
.hljs-string,
.hljs-title,
.hljs-section,
.hljs-type,
.hljs-attribute,
.hljs-symbol,
.hljs-bullet,
.hljs-built_in,
.hljs-addition,
.hljs-variable,
.hljs-template-tag,
.hljs-template-variable,
.hljs-link,
.hljs-name {
  color: #2c009f
}
.hljs-comment,
.hljs-quote,
.hljs-meta,
.hljs-deletion {
  color: #802022
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-doctag,
.hljs-title,
.hljs-section,
.hljs-type,
.hljs-name,
.hljs-strong {
  font-weight: bold
}
.hljs-emphasis {
  font-style: italic
}