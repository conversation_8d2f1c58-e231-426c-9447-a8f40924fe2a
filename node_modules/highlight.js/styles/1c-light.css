pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: 1c-light
  Description: Style IDE 1C:Enterprise 8
  Author: (c) <PERSON><PERSON><PERSON> <<EMAIL>>
  Maintainer: @Diversus23
  Website: https://softonit.ru/
  License: see project LICENSE
  Touched: 2023
*/
/* end baseline CSS */
.hljs {
  color: #0000ff;
  background: #ffffff
}
/* Base color: saturation 0; */
.hljs-subst {
  /* default */
  
}
/* purposely ignored */
.hljs-formula,
.hljs-attr,
.hljs-property {
  
}
.hljs-comment {
  color: #008000
}
.hljs-tag {
  color: #444a
}
.hljs-tag .hljs-name,
.hljs-tag .hljs-attr {
  color: #444
}
.hljs-punctuation,
.hljs-function,
.hljs-keyword,
.hljs-attribute,
.hljs-selector-tag,
.hljs-doctag,
.hljs-name {
  color: #ff0000
}
.hljs-type,
.hljs-params {
  color: #0000ff
}
/* User color: hue: 0 */
.hljs-string,
.hljs-number,
.hljs-selector-id,
.hljs-selector-class,
.hljs-quote,
.hljs-template-tag,
.hljs-symbol,
.hljs-deletion {
  color: #000000
}
.hljs-title,
.hljs-section {
  color: #0000ff
}
.hljs-regexp,
.hljs-variable,
.hljs-template-variable,
.hljs-link,
.hljs-selector-attr,
.hljs-operator,
.hljs-selector-pseudo {
  color: #ab5656
}
/* Language color: hue: 90; */
.hljs-literal {
  color: #ff0000
}
.hljs-built_in,
.hljs-bullet,
.hljs-code,
.hljs-addition {
  color: #0000ff
}
/* Meta color: hue: 200 */
.hljs-meta {
  color: #963200
}
.hljs-meta .hljs-string {
  color: #963200
}
.hljs-meta .hljs-keyword {
  color: #963200
}
/* Misc effects */
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}