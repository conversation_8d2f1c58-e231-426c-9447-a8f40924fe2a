sudo: false
language: node_js
node_js:
  - "0.10"
  - "0.12"
env:
  global:
    - REMOVE_DEPS=""
  matrix:
    - "CUSTOM_DEPS=coffee-script@~1.3"
    - "CUSTOM_DEPS=coffee-script@~1.5"
    - "CUSTOM_DEPS=coffee-script@~1.7"
    - "CUSTOM_DEPS=coffee-script@latest"
    - "CUSTOM_DEPS=iced-coffee-script@1.6.3-j"
    - "CUSTOM_DEPS=iced-coffee-script@latest"
    - "CUSTOM_DEPS=LiveScript@1.3.1 REMOVE_DEPS=livescript"
    - "CUSTOM_DEPS=typescript-require REMOVE_DEPS=typescript-register"
matrix:
  fast_finish: true
before_install:
  - "npm install -g npm" # needs the newest version of npm
before_script:
  - "[ \"${REMOVE_DEPS}\" == \"\" ] || npm rm $REMOVE_DEPS"
  - "npm install $CUSTOM_DEPS" # install a specific version of dependencies
