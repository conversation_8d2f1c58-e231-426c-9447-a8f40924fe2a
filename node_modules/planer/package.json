{"name": "planer", "version": "1.2.0", "description": "Remove reply quotations from emails", "main": "lib/planer.js", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "scripts": {"test": "mocha test/", "compile": "coffee -o lib -c src"}, "repository": {"type": "git", "url": "git+https://github.com/lever/planer.git"}, "keywords": ["email", "quotations", "reply", "talon"], "author": "<PERSON><PERSON> (https://github.com/l8on)", "license": "MIT", "bugs": {"url": "https://github.com/lever/planer/issues"}, "homepage": "https://github.com/lever/planer#readme", "devDependencies": {"chai": "^3.4.1", "coffee-script": "^1.10.0", "jsdom": "^11.6.0", "mocha": "^2.3.4"}}