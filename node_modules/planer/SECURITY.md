# Vulnerability Disclosure Policy

## Reporting Security Issues

At Employ Inc. (Lever, Jobvite, JazzHR and NXTThing RPO), we take security issues seriously. We appreciate your efforts to responsibly disclose your findings, and we will make every effort to quickly address the issue.

To report a security issue, please email [<EMAIL>](mailto:<EMAIL>) and include a detailed description of the vulnerability. 

* The website, IP or page where the vulnerability can be observed.

* A brief description of the type of vulnerability, for example; "XSS vulnerability".

* Steps to reproduce. These should be a benign, non-destructive, proof of concept. This helps to ensure that the report can be triaged quickly and accurately. It also reduces the likelihood of duplicate reports, or malicious exploitation of some vulnerabilities, such as sub-domain takeovers.

Our team will review and respond to your email as soon as possible. We kindly request that you refrain from publicly disclosing the issue until we have had an opportunity to address it.

## Vulnerability Disclosure Process

1. Contact us via the email address provided above to report the security issue.
2. Our security team will acknowledge receipt of your report and work with you to understand and validate the vulnerability.
3. Once the vulnerability is confirmed, we will develop and test a fix.
4. We will notify you when the fix has been implemented and deployed.
5. We will acknowledge your contribution in our release notes or security acknowledgments, unless you request to remain anonymous.

## Bug Bounty Program

At this time, we do not offer a bug bounty program for security researchers. However, we value your contributions to the security of our projects and products.

## Responsible Disclosure Guidelines

- Do not break any applicable law or regulations.
- Do not exploit any security vulnerabilities you discover.
- Do not access, delete, or modify data without explicit permission from the account holder.
- Do not publicly disclose the issue until we have addressed it.
- Provide us with a reasonable amount of time to address the issue before any public disclosure.
- Do not demand financial compensation in order to disclose any vulnerabilities.
- Do not submit reports detailing non-exploitable vulnerabilities, or reports indicating that the services do not fully align with "best practice", for example missing security headers.
- Do not submit reports detailing TLS configuration weaknesses, for example "weak" cipher suite support or the presence of TLS1.0 support.

## Contact

If you have any questions, concerns, or suggestions regarding the security of our projects, please feel free to reach out to our security team at [<EMAIL>](mailto:<EMAIL>).

Thank you for your contributions to the security of our projects!
