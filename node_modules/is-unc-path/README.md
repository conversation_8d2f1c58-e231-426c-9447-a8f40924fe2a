# is-unc-path [![NPM version](https://img.shields.io/npm/v/is-unc-path.svg?style=flat)](https://www.npmjs.com/package/is-unc-path) [![NPM monthly downloads](https://img.shields.io/npm/dm/is-unc-path.svg?style=flat)](https://npmjs.org/package/is-unc-path) [![NPM total downloads](https://img.shields.io/npm/dt/is-unc-path.svg?style=flat)](https://npmjs.org/package/is-unc-path) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/is-unc-path.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/is-unc-path)

> Returns true if a filepath is a windows UNC file path.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save is-unc-path
```

## Usage

```js
var isUncPath = require('is-unc-path');
```

**true**

Returns true for windows UNC paths:

```js
isUncPath('\\/foo/bar');
isUncPath('\\\\foo/bar');
isUncPath('\\\\foo\\admin$');
isUncPath('\\\\foo\\admin$\\system32');
isUncPath('\\\\foo\\temp');
isUncPath('\\\\/foo/bar');
isUncPath('\\\\\\/foo/bar');
```

**false**

Returns false for non-UNC paths:

```js
isUncPath('/foo/bar');
isUncPath('/');
isUncPath('/foo');
isUncPath('/foo/');
isUncPath('c:');
isUncPath('c:.');
isUncPath('c:./');
isUncPath('c:./file');
isUncPath('c:/');
isUncPath('c:/file');
```

**Customization**

Use `.source` to use the regex as a component of another regex:

```js
var myRegex = new RegExp(isUncPath.source + 'foo');
```

**[Rules for UNC paths](http://resources.esri.com/help/9.3/ArcGISDesktop/com/Gp_ToolRef/sharing_tools_and_toolboxes/pathnames_explained_colon_absolute_relative_unc_and_url.htm)**

* The computer name is always preceded by a double backward-slash (`\\`).
* UNC paths cannot contain a drive letter (such as `D:`)

## Release history

### v1.0.0 - 2017-07-12

**Changes**

* now throws a `TypeError` if value is not a string

## About

### Related projects

* [is-absolute](https://www.npmjs.com/package/is-absolute): Polyfill for node.js `path.isAbolute`. Returns true if a file path is absolute. | [homepage](https://github.com/jonschlinkert/is-absolute "Polyfill for node.js `path.isAbolute`. Returns true if a file path is absolute.")
* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern… [more](https://github.com/jonschlinkert/is-glob) | [homepage](https://github.com/jonschlinkert/is-glob "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet")
* [is-relative](https://www.npmjs.com/package/is-relative): Returns `true` if the path appears to be relative. | [homepage](https://github.com/jonschlinkert/is-relative "Returns `true` if the path appears to be relative.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on July 13, 2017._