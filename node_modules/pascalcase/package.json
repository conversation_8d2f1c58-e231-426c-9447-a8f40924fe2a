{"name": "pascalcase", "description": "Convert a string to pascal-case.", "version": "0.1.1", "homepage": "https://github.com/jonschlinkert/pascalcase", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/pascalcase", "bugs": {"url": "https://github.com/jonschlinkert/pascalcase/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["camelcase", "case", "casing", "pascal", "pascal-case", "pascalcase", "string"], "verb": {"related": {"list": ["pad-left", "pad-right", "word-wrap", "repeat-string", "justified"]}}}