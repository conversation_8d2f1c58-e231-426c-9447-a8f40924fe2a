{"name": "hast-util-minify-whitespace", "version": "1.0.1", "description": "hast utility to get the plain-text value of a node", "license": "MIT", "keywords": ["hast", "hast-util", "html", "minify", "unist", "util", "utility", "whitespace"], "repository": "https://github.com/rehypejs/rehype-minify/tree/main/packages/hast-util-minify-whitespace", "bugs": "https://github.com/rehypejs/rehype-minify/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["index.d.ts", "index.d.ts.map", "index.js", "lib/"], "dependencies": {"@types/hast": "^3.0.0", "hast-util-embedded": "^3.0.0", "hast-util-is-element": "^3.0.0", "hast-util-whitespace": "^3.0.0", "unist-util-is": "^6.0.0"}, "scripts": {}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"prettier": true, "rules": {"capitalized-comments": "off", "unicorn/prefer-string-replace-all": "off"}}}