// packages/twenty-front/vite.config.ts
import { lingui } from "file:///opt/apps/twenty/node_modules/@lingui/vite-plugin/dist/index.cjs";
import { isNonEmptyString } from "file:///opt/apps/twenty/node_modules/@sniptt/guards/build/index.js";
import react from "file:///opt/apps/twenty/node_modules/@vitejs/plugin-react-swc/index.mjs";
import wyw from "file:///opt/apps/twenty/node_modules/@wyw-in-js/vite/esm/index.mjs";
import fs from "fs";
import path from "path";
import { visualizer } from "file:///opt/apps/twenty/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import { defineConfig, loadEnv, searchForWorkspaceRoot } from "file:///opt/apps/twenty/node_modules/vite/dist/node/index.js";
import checker from "file:///opt/apps/twenty/node_modules/vite-plugin-checker/dist/esm/main.js";
import svgr from "file:///opt/apps/twenty/node_modules/vite-plugin-svgr/dist/index.js";
import tsconfigPaths from "file:///opt/apps/twenty/node_modules/vite-tsconfig-paths/dist/index.mjs";
var __vite_injected_original_dirname = "/opt/apps/twenty/packages/twenty-front";
var vite_config_default = defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, __vite_injected_original_dirname, "");
  const {
    REACT_APP_SERVER_BASE_URL,
    VITE_BUILD_SOURCEMAP,
    VITE_DISABLE_TYPESCRIPT_CHECKER,
    VITE_DISABLE_ESLINT_CHECKER,
    VITE_HOST,
    SSL_CERT_PATH,
    SSL_KEY_PATH,
    REACT_APP_PORT,
    IS_DEBUG_MODE
  } = env;
  const port = isNonEmptyString(REACT_APP_PORT) ? parseInt(REACT_APP_PORT) : 3001;
  const isBuildCommand = command === "build";
  const tsConfigPath = isBuildCommand ? path.resolve(__vite_injected_original_dirname, "./tsconfig.build.json") : path.resolve(__vite_injected_original_dirname, "./tsconfig.dev.json");
  const CHUNK_SIZE_WARNING_LIMIT = 1024 * 1024;
  const MAIN_CHUNK_SIZE_LIMIT = 4.7 * 1024 * 1024;
  const OTHER_CHUNK_SIZE_LIMIT = 5 * 1024 * 1024;
  const checkers = {
    overlay: false
  };
  if (VITE_DISABLE_TYPESCRIPT_CHECKER === "true") {
    console.log(
      `VITE_DISABLE_TYPESCRIPT_CHECKER: ${VITE_DISABLE_TYPESCRIPT_CHECKER}`
    );
  }
  if (VITE_DISABLE_ESLINT_CHECKER === "true") {
    console.log(`VITE_DISABLE_ESLINT_CHECKER: ${VITE_DISABLE_ESLINT_CHECKER}`);
  }
  if (VITE_BUILD_SOURCEMAP === "true") {
    console.log(`VITE_BUILD_SOURCEMAP: ${VITE_BUILD_SOURCEMAP}`);
  }
  if (VITE_DISABLE_TYPESCRIPT_CHECKER !== "true") {
    checkers["typescript"] = {
      tsconfigPath: tsConfigPath
    };
  }
  if (VITE_DISABLE_ESLINT_CHECKER !== "true") {
    checkers["eslint"] = {
      lintCommand: (
        // Appended to packages/twenty-front/.eslintrc.cjs
        "eslint ../../packages/twenty-front --report-unused-disable-directives --max-warnings 0 --config .eslintrc.cjs"
      )
    };
  }
  return {
    root: __vite_injected_original_dirname,
    cacheDir: "../../node_modules/.vite/packages/twenty-front",
    server: {
      port,
      ...VITE_HOST ? { host: VITE_HOST } : {},
      ...SSL_KEY_PATH && SSL_CERT_PATH ? {
        protocol: "https",
        https: {
          key: fs.readFileSync(env.SSL_KEY_PATH),
          cert: fs.readFileSync(env.SSL_CERT_PATH)
        }
      } : {
        protocol: "http"
      },
      fs: {
        allow: [
          searchForWorkspaceRoot(process.cwd()),
          "**/@blocknote/core/src/fonts/**"
        ]
      }
    },
    plugins: [
      react({
        jsxImportSource: "@emotion/react",
        plugins: [["@lingui/swc-plugin", {}]]
      }),
      tsconfigPaths({
        projects: ["tsconfig.json"]
      }),
      svgr(),
      lingui({
        configPath: path.resolve(__vite_injected_original_dirname, "./lingui.config.ts")
      }),
      checker(checkers),
      // TODO: fix this, we have to restrict the include to only the components that are using linaria
      // Otherwise the build will fail because wyw tries to include emotion styled components
      wyw({
        include: [
          "**/CurrencyDisplay.tsx",
          "**/EllipsisDisplay.tsx",
          "**/ContactLink.tsx",
          "**/BooleanDisplay.tsx",
          "**/LinksDisplay.tsx",
          "**/RoundedLink.tsx",
          "**/OverflowingTextWithTooltip.tsx",
          "**/Chip.tsx",
          "**/Tag.tsx",
          "**/MultiSelectFieldDisplay.tsx",
          "**/RatingInput.tsx",
          "**/RecordTableCellContainer.tsx",
          "**/RecordTableCellDisplayContainer.tsx",
          "**/Avatar.tsx",
          "**/RecordTableBodyDroppable.tsx",
          "**/RecordTableCellBaseContainer.tsx",
          "**/RecordTableCellTd.tsx",
          "**/RecordTableTd.tsx",
          "**/RecordTableHeaderDragDropColumn.tsx",
          "**/ActorDisplay.tsx",
          "**/BooleanDisplay.tsx",
          "**/CurrencyDisplay.tsx",
          "**/TextDisplay.tsx",
          "**/EllipsisDisplay.tsx",
          "**/AvatarChip.tsx",
          "**/URLDisplay.tsx",
          "**/EmailsDisplay.tsx",
          "**/PhonesDisplay.tsx",
          "**/MultiSelectDisplay.tsx"
        ],
        babelOptions: {
          presets: ["@babel/preset-typescript", "@babel/preset-react"]
        }
      }),
      visualizer({
        open: true,
        gzipSize: true,
        brotliSize: true,
        filename: "dist/stats.html"
      })
      // https://github.com/btd/rollup-plugin-visualizer/issues/162#issuecomment-1538265997,
    ],
    optimizeDeps: {
      exclude: [
        "../../node_modules/.vite",
        "../../node_modules/.cache",
        "../../node_modules/twenty-ui"
      ]
    },
    build: {
      minify: "esbuild",
      outDir: "build",
      sourcemap: VITE_BUILD_SOURCEMAP === "true",
      rollupOptions: {
        //  Don't use manual chunks as it causes many issue
        // including this one we wasted a lot of time on:
        // https://github.com/rollup/rollup/issues/2793
        output: {
          // Set chunk size warning limit (in bytes) - warns at 1MB
          chunkSizeWarningLimit: CHUNK_SIZE_WARNING_LIMIT,
          // Custom plugin to fail build if chunks exceed max size
          plugins: [
            {
              name: "chunk-size-limit",
              generateBundle(_options, bundle) {
                const oversizedChunks = [];
                Object.entries(bundle).forEach(([fileName, chunk]) => {
                  if (chunk.type === "chunk" && chunk.code) {
                    const size = Buffer.byteLength(chunk.code, "utf8");
                    const isMainChunk = fileName.includes("index") && chunk.isEntry;
                    const sizeLimit = isMainChunk ? MAIN_CHUNK_SIZE_LIMIT : OTHER_CHUNK_SIZE_LIMIT;
                    const limitType = isMainChunk ? "main" : "other";
                    if (size > sizeLimit) {
                      oversizedChunks.push(`${fileName} (${limitType}): ${(size / 1024 / 1024).toFixed(2)}MB (limit: ${(sizeLimit / 1024 / 1024).toFixed(2)}MB)`);
                    }
                  }
                });
                if (oversizedChunks.length > 0) {
                  const errorMessage = `Build failed: The following chunks exceed their size limits:
${oversizedChunks.map((chunk) => `  - ${chunk}`).join("\n")}`;
                  this.error(errorMessage);
                }
              }
            }
            // TODO; later - think about prefetching modules such 
            // as date time picker, phone input etc...
            /*
                        {
                          name: 'add-prefetched-modules',
                          transformIndexHtml(html: string, 
                            ctx: {
                              path: string;
                              filename: string;
                              server?: ViteDevServer;
                              bundle?: import('rollup').OutputBundle;
                              chunk?: import('rollup').OutputChunk;
                            }) {
            
                              const bundles = Object.keys(ctx.bundle ?? {});
            
                              let modernBundles = bundles.filter(
                                (bundle) => bundle.endsWith('.map') === false
                              );
            
                              
                              // Remove existing files and concatenate them into link tags
                              const prefechBundlesString = modernBundles
                                .filter((bundle) => html.includes(bundle) === false)
                                .map((bundle) => `<link rel="prefetch" href="${ctx.server?.config.base}${bundle}">`)
                                .join('');
                        
                              // Use regular expression to get the content within <head> </head>
                              const headContent = html.match(/<head>([\s\S]*)<\/head>/)?.[1] ?? '';
                              // Insert the content of prefetch into the head
                              const newHeadContent = `${headContent}${prefechBundlesString}`;
                              // Replace the original head
                              html = html.replace(
                                /<head>([\s\S]*)<\/head>/,
                                `<head>${newHeadContent}</head>`
                              );
                        
                              return html;
                        
                         
                          },
                        }*/
          ]
        }
      }
    },
    envPrefix: "REACT_APP_",
    define: {
      _env_: {
        REACT_APP_SERVER_BASE_URL
      },
      "process.env": {
        REACT_APP_SERVER_BASE_URL,
        IS_DEBUG_MODE
      }
    },
    css: {
      modules: {
        localsConvention: "camelCaseOnly"
      }
    },
    resolve: {
      alias: {
        path: "rollup-plugin-node-polyfills/polyfills/path",
        // https://github.com/twentyhq/twenty/pull/10782/files
        // This will likely be migrated to twenty-ui package when built separately
        "@tabler/icons-react": "@tabler/icons-react/dist/esm/icons/index.mjs"
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
