{"name": "babel-plugin-transform-object-rest-spread", "version": "6.26.0", "description": "Compile object rest and spread to ES5", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-rest-spread", "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"babel-plugin-syntax-object-rest-spread": "^6.8.0", "babel-runtime": "^6.26.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}}