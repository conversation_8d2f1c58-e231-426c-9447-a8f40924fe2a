{"version": 3, "names": ["_cache", "require", "_hoister", "_index", "_context", "_removal", "_t", "arrowFunctionExpression", "assertExpression", "assignmentExpression", "blockStatement", "callExpression", "cloneNode", "expressionStatement", "isAssignmentExpression", "isCallExpression", "isExportNamedDeclaration", "isExpression", "isIdentifier", "isSequenceExpression", "is<PERSON><PERSON><PERSON>", "thisExpression", "insertBefore", "nodes_", "_assertUnremoved", "call", "nodes", "_verifyNodeList", "parentPath", "parent", "isExpressionStatement", "isLabeledStatement", "isExportDefaultDeclaration", "isDeclaration", "isNodeType", "isJSXElement", "isForStatement", "key", "node", "push", "replaceExpressionWithStatements", "Array", "isArray", "container", "_containerInsertBefore", "isStatementOrBlock", "shouldInsertCurrentNode", "expression", "replaceWith", "unshiftContainer", "Error", "_containerInsert", "from", "updateSiblingKeys", "length", "paths", "splice", "i", "_this$context", "to", "path", "getSibling", "context", "queue", "pushContext", "contexts", "_getQueueContexts", "setScope", "debug", "maybeQueue", "_containerInsertAfter", "last", "arr", "isHiddenInSequenceExpression", "expressions", "isAlmostConstantAssignment", "scope", "left", "blockScope", "getBlockParent", "hasOwnBinding", "name", "getOwnBinding", "constantViolations", "insertAfter", "get", "map", "self", "isPattern", "unshift", "callee", "isPure", "isMethod", "computed", "temp", "generateDeclaredUidIdentifier", "pushContainer", "fromIndex", "incrementBy", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hub", "msg", "type", "NodePath", "<PERSON><PERSON><PERSON>", "setContext", "verifiedNodes", "replaceWithMultiple", "hoist", "hoister", "PathHoister", "run"], "sources": ["../../src/path/modification.ts"], "sourcesContent": ["// This file contains methods that modify the path/node in some ways.\n\nimport { getCachedPaths } from \"../cache.ts\";\nimport PathHoister from \"./lib/hoister.ts\";\nimport NodePath from \"./index.ts\";\nimport { _getQueueContexts } from \"./context.ts\";\nimport { _assertUnremoved } from \"./removal.ts\";\nimport {\n  arrowFunctionExpression,\n  assertExpression,\n  assignmentExpression,\n  blockStatement,\n  callExpression,\n  cloneNode,\n  expressionStatement,\n  isAssignmentExpression,\n  isCallExpression,\n  isExportNamedDeclaration,\n  isExpression,\n  isIdentifier,\n  isSequenceExpression,\n  isSuper,\n  thisExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type Scope from \"../scope/index.ts\";\n\n/**\n * Insert the provided nodes before the current one.\n */\n\nexport function insertBefore(\n  this: NodePath,\n  nodes_: t.Node | t.Node[],\n): NodePath[] {\n  _assertUnremoved.call(this);\n\n  const nodes = _verifyNodeList.call(this, nodes_);\n\n  const { parentPath, parent } = this;\n\n  if (\n    parentPath.isExpressionStatement() ||\n    parentPath.isLabeledStatement() ||\n    // https://github.com/babel/babel/issues/15293\n    // When Babel transforms `export class String { field }`, the class properties plugin will inject the defineProperty\n    // helper, which depends on the builtins e.g. String, Number, Symbol, etc. To prevent them from being shadowed by local\n    // exports, the helper injector replaces the named export into `class _String { field }; export { _String as String }`,\n    // with `parentPath` here changed to the moved ClassDeclaration, causing rare inconsistency between `parent` and `parentPath`.\n    // Here we retrieve the parent type from the `parent` property. This is a temporary fix and we should revisit when\n    // helpers should get injected.\n    isExportNamedDeclaration(parent) ||\n    (parentPath.isExportDefaultDeclaration() && this.isDeclaration())\n  ) {\n    return parentPath.insertBefore(nodes);\n  } else if (\n    (this.isNodeType(\"Expression\") && !this.isJSXElement()) ||\n    (parentPath.isForStatement() && this.key === \"init\")\n  ) {\n    if (this.node) nodes.push(this.node);\n    // @ts-expect-error todo(flow->ts): check that nodes is an array of statements\n    return this.replaceExpressionWithStatements(nodes);\n  } else if (Array.isArray(this.container)) {\n    return _containerInsertBefore.call(this, nodes);\n  } else if (this.isStatementOrBlock()) {\n    const node = this.node as t.Statement;\n    const shouldInsertCurrentNode =\n      node &&\n      (!this.isExpressionStatement() ||\n        (node as t.ExpressionStatement).expression != null);\n\n    this.replaceWith(blockStatement(shouldInsertCurrentNode ? [node] : []));\n    return (this as NodePath<t.BlockStatement>).unshiftContainer(\n      \"body\",\n      // @ts-expect-error Fixme: refine nodes to t.BlockStatement[\"body\"] when this is a BlockStatement path\n      nodes,\n    );\n  } else {\n    throw new Error(\n      \"We don't know what to do with this node type. \" +\n        \"We were previously a Statement but we can't fit in here?\",\n    );\n  }\n}\n\nexport function _containerInsert<N extends t.Node>(\n  this: NodePath,\n  from: number,\n  nodes: N[],\n): NodePath<N>[] {\n  this.updateSiblingKeys(from, nodes.length);\n\n  const paths: NodePath<N>[] = [];\n\n  // @ts-expect-error todo(flow->ts): this.container could be a NodePath\n  this.container.splice(from, 0, ...nodes);\n  for (let i = 0; i < nodes.length; i++) {\n    const to = from + i;\n    const path = this.getSibling(to) as NodePath<N>;\n    paths.push(path);\n\n    if (this.context?.queue) {\n      path.pushContext(this.context);\n    }\n  }\n\n  const contexts = _getQueueContexts.call(this);\n\n  for (const path of paths) {\n    path.setScope();\n    path.debug(\"Inserted.\");\n\n    for (const context of contexts) {\n      context.maybeQueue(path, true);\n    }\n  }\n\n  return paths;\n}\n\nexport function _containerInsertBefore<N extends t.Node>(\n  this: NodePath,\n  nodes: N[],\n) {\n  return _containerInsert.call(this, this.key as number, nodes);\n}\n\nexport function _containerInsertAfter<N extends t.Node>(\n  this: NodePath,\n  nodes: N[],\n) {\n  return _containerInsert.call(this, (this.key as number) + 1, nodes);\n}\n\nconst last = <T>(arr: T[]) => arr[arr.length - 1];\n\nfunction isHiddenInSequenceExpression(path: NodePath): boolean {\n  return (\n    isSequenceExpression(path.parent) &&\n    (last(path.parent.expressions) !== path.node ||\n      isHiddenInSequenceExpression(path.parentPath))\n  );\n}\n\nfunction isAlmostConstantAssignment(\n  node: t.Node,\n  scope: Scope,\n): node is t.AssignmentExpression & { left: t.Identifier } {\n  if (!isAssignmentExpression(node) || !isIdentifier(node.left)) {\n    return false;\n  }\n\n  // Not every scope can contain variables. For example, we might be in\n  // a ClassScope either in the ClassHeritage or in a computed key.\n  const blockScope = scope.getBlockParent();\n\n  // If the variable is defined in the current scope and only assigned here,\n  // we can be sure that its value won't change.\n  return (\n    blockScope.hasOwnBinding(node.left.name) &&\n    blockScope.getOwnBinding(node.left.name).constantViolations.length <= 1\n  );\n}\n\n/**\n * Insert the provided nodes after the current one. When inserting nodes after an\n * expression, ensure that the completion record is correct by pushing the current node.\n */\n\nexport function insertAfter(\n  this: NodePath,\n  nodes_: t.Node | t.Node[],\n): NodePath[] {\n  _assertUnremoved.call(this);\n\n  if (this.isSequenceExpression()) {\n    return last(this.get(\"expressions\")).insertAfter(nodes_);\n  }\n\n  const nodes = _verifyNodeList.call(this, nodes_);\n\n  const { parentPath, parent } = this;\n  if (\n    parentPath.isExpressionStatement() ||\n    parentPath.isLabeledStatement() ||\n    // see insertBefore\n    isExportNamedDeclaration(parent) ||\n    (parentPath.isExportDefaultDeclaration() && this.isDeclaration())\n  ) {\n    return parentPath.insertAfter(\n      nodes.map(node => {\n        // Usually after an expression we can safely insert another expression:\n        //   A.insertAfter(B)\n        //     foo = A;  -> foo = (A, B);\n        // If A is an expression statement, it isn't safe anymore so we need to\n        // convert B to an expression statement\n        //     A;        -> A; B // No semicolon! It could break if followed by [!\n        return isExpression(node) ? expressionStatement(node) : node;\n      }),\n    );\n  } else if (\n    (this.isNodeType(\"Expression\") &&\n      !this.isJSXElement() &&\n      !parentPath.isJSXElement()) ||\n    (parentPath.isForStatement() && this.key === \"init\")\n  ) {\n    const self = this as NodePath<t.Expression | t.VariableDeclaration>;\n    if (self.node) {\n      const node = self.node;\n      let { scope } = this;\n\n      if (scope.path.isPattern()) {\n        assertExpression(node);\n\n        self.replaceWith(callExpression(arrowFunctionExpression([], node), []));\n        (self.get(\"callee.body\") as NodePath<t.Expression>).insertAfter(nodes);\n        return [self];\n      }\n\n      if (isHiddenInSequenceExpression(self)) {\n        nodes.unshift(node);\n      }\n      // We need to preserve the value of this expression.\n      else if (isCallExpression(node) && isSuper(node.callee)) {\n        nodes.unshift(node);\n        // `super(...)` always evaluates to `this`.\n        nodes.push(thisExpression());\n      } else if (isAlmostConstantAssignment(node, scope)) {\n        nodes.unshift(node);\n        nodes.push(cloneNode(node.left));\n      } else if (scope.isPure(node, true)) {\n        // Insert the nodes before rather than after; it's not observable.\n        nodes.push(node);\n      } else {\n        // Inserting after the computed key of a method should insert the\n        // temporary binding in the method's parent's scope.\n        if (parentPath.isMethod({ computed: true, key: node })) {\n          scope = scope.parent;\n        }\n        const temp = scope.generateDeclaredUidIdentifier();\n        nodes.unshift(\n          expressionStatement(\n            // @ts-expect-error todo(flow->ts): This can be a variable\n            // declaration in the \"init\" of a for statement, but that's\n            // invalid here.\n            assignmentExpression(\"=\", cloneNode(temp), node),\n          ),\n        );\n        nodes.push(expressionStatement(cloneNode(temp)));\n      }\n    }\n    // @ts-expect-error todo(flow->ts): check that nodes is an array of statements\n    return this.replaceExpressionWithStatements(nodes);\n  } else if (Array.isArray(this.container)) {\n    return _containerInsertAfter.call(this, nodes);\n  } else if (this.isStatementOrBlock()) {\n    const node = this.node as t.Statement;\n    const shouldInsertCurrentNode =\n      node &&\n      (!this.isExpressionStatement() ||\n        (node as t.ExpressionStatement).expression != null);\n\n    this.replaceWith(blockStatement(shouldInsertCurrentNode ? [node] : []));\n    // @ts-expect-error Fixme: refine nodes to t.BlockStatement[\"body\"] when this is a BlockStatement path\n    return this.pushContainer(\"body\", nodes);\n  } else {\n    throw new Error(\n      \"We don't know what to do with this node type. \" +\n        \"We were previously a Statement but we can't fit in here?\",\n    );\n  }\n}\n\n/**\n * Update all sibling node paths after `fromIndex` by `incrementBy`.\n */\n\nexport function updateSiblingKeys(\n  this: NodePath,\n  fromIndex: number,\n  incrementBy: number,\n) {\n  if (!this.parent) return;\n\n  const paths = getCachedPaths(this.hub, this.parent) || ([] as never[]);\n\n  for (const [, path] of paths) {\n    if (typeof path.key === \"number\" && path.key >= fromIndex) {\n      path.key += incrementBy;\n    }\n  }\n}\n\nexport function _verifyNodeList<N extends t.Node>(\n  this: NodePath,\n  nodes: N | N[],\n): N[] {\n  if (!nodes) {\n    return [];\n  }\n\n  if (!Array.isArray(nodes)) {\n    nodes = [nodes];\n  }\n\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    let msg;\n\n    if (!node) {\n      msg = \"has falsy node\";\n    } else if (typeof node !== \"object\") {\n      msg = \"contains a non-object node\";\n    } else if (!node.type) {\n      msg = \"without a type\";\n    } else if (node instanceof NodePath) {\n      msg = \"has a NodePath when it expected a raw object\";\n    }\n\n    if (msg) {\n      const type = Array.isArray(node) ? \"array\" : typeof node;\n      throw new Error(\n        `Node list ${msg} with the index of ${i} and type of ${type}`,\n      );\n    }\n  }\n\n  return nodes;\n}\n\nexport function unshiftContainer<N extends t.Node, K extends keyof N & string>(\n  this: NodePath<N>,\n  listKey: K,\n  nodes: N[K] extends (infer E)[]\n    ? E | E[]\n    : // todo: refine to t.Node[]\n      //  ? E extends t.Node\n      //    ? E | E[]\n      //    : never\n      never,\n) {\n  // todo: NodePaths<Nodes>\n  _assertUnremoved.call(this);\n\n  // @ts-expect-error fixme\n  nodes = _verifyNodeList.call(this, nodes);\n\n  // get the first path and insert our nodes before it, if it doesn't exist then it\n  // doesn't matter, our nodes will be inserted anyway\n  const path = NodePath.get({\n    parentPath: this,\n    parent: this.node,\n    container: (this.node as N)[listKey] as unknown as t.Node | t.Node[],\n    listKey,\n    key: 0,\n  }).setContext(this.context);\n\n  return _containerInsertBefore.call(\n    path,\n    // @ts-expect-error typings needed to narrow down nodes as t.Node[]\n    nodes,\n  );\n}\n\nexport function pushContainer<\n  P extends NodePath,\n  K extends string & keyof P[\"node\"],\n>(\n  this: P,\n  listKey: K,\n  nodes: P[\"node\"][K] extends (infer E)[]\n    ? E | E[]\n    : // todo: refine to t.Node[]\n      //  ? E extends t.Node\n      //    ? E | E[]\n      //    : never\n      never,\n) {\n  _assertUnremoved.call(this);\n\n  const verifiedNodes = _verifyNodeList.call(\n    this,\n    // @ts-expect-error refine typings\n    nodes,\n  );\n\n  // get an invisible path that represents the last node + 1 and replace it with our\n  // nodes, effectively inlining it\n\n  const container = (this.node as P[\"node\"])[listKey] as t.Node[];\n  const path = NodePath.get({\n    parentPath: this,\n    parent: this.node,\n    container: container as unknown as t.Node | t.Node[],\n    listKey,\n    key: container.length,\n  }).setContext(this.context);\n\n  return path.replaceWithMultiple(verifiedNodes);\n}\n\n/**\n * Hoist the current node to the highest scope possible and return a UID\n * referencing it.\n */\nexport function hoist<T extends t.Node>(\n  this: NodePath<T>,\n  scope: Scope = this.scope,\n) {\n  const hoister = new PathHoister<T>(this, scope);\n  return hoister.run();\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAEA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,EAAA,GAAAL,OAAA;AAgBsB;EAfpBM,uBAAuB;EACvBC,gBAAgB;EAChBC,oBAAoB;EACpBC,cAAc;EACdC,cAAc;EACdC,SAAS;EACTC,mBAAmB;EACnBC,sBAAsB;EACtBC,gBAAgB;EAChBC,wBAAwB;EACxBC,YAAY;EACZC,YAAY;EACZC,oBAAoB;EACpBC,OAAO;EACPC;AAAc,IAAAf,EAAA;AAST,SAASgB,YAAYA,CAE1BC,MAAyB,EACb;EACZC,yBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC;EAE3B,MAAMC,KAAK,GAAGC,eAAe,CAACF,IAAI,CAAC,IAAI,EAAEF,MAAM,CAAC;EAEhD,MAAM;IAAEK,UAAU;IAAEC;EAAO,CAAC,GAAG,IAAI;EAEnC,IACED,UAAU,CAACE,qBAAqB,CAAC,CAAC,IAClCF,UAAU,CAACG,kBAAkB,CAAC,CAAC,IAQ/Bf,wBAAwB,CAACa,MAAM,CAAC,IAC/BD,UAAU,CAACI,0BAA0B,CAAC,CAAC,IAAI,IAAI,CAACC,aAAa,CAAC,CAAE,EACjE;IACA,OAAOL,UAAU,CAACN,YAAY,CAACI,KAAK,CAAC;EACvC,CAAC,MAAM,IACJ,IAAI,CAACQ,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC,CAAC,IACrDP,UAAU,CAACQ,cAAc,CAAC,CAAC,IAAI,IAAI,CAACC,GAAG,KAAK,MAAO,EACpD;IACA,IAAI,IAAI,CAACC,IAAI,EAAEZ,KAAK,CAACa,IAAI,CAAC,IAAI,CAACD,IAAI,CAAC;IAEpC,OAAO,IAAI,CAACE,+BAA+B,CAACd,KAAK,CAAC;EACpD,CAAC,MAAM,IAAIe,KAAK,CAACC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;IACxC,OAAOC,sBAAsB,CAACnB,IAAI,CAAC,IAAI,EAAEC,KAAK,CAAC;EACjD,CAAC,MAAM,IAAI,IAAI,CAACmB,kBAAkB,CAAC,CAAC,EAAE;IACpC,MAAMP,IAAI,GAAG,IAAI,CAACA,IAAmB;IACrC,MAAMQ,uBAAuB,GAC3BR,IAAI,KACH,CAAC,IAAI,CAACR,qBAAqB,CAAC,CAAC,IAC3BQ,IAAI,CAA2BS,UAAU,IAAI,IAAI,CAAC;IAEvD,IAAI,CAACC,WAAW,CAACtC,cAAc,CAACoC,uBAAuB,GAAG,CAACR,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IACvE,OAAQ,IAAI,CAAgCW,gBAAgB,CAC1D,MAAM,EAENvB,KACF,CAAC;EACH,CAAC,MAAM;IACL,MAAM,IAAIwB,KAAK,CACb,gDAAgD,GAC9C,0DACJ,CAAC;EACH;AACF;AAEO,SAASC,gBAAgBA,CAE9BC,IAAY,EACZ1B,KAAU,EACK;EACf,IAAI,CAAC2B,iBAAiB,CAACD,IAAI,EAAE1B,KAAK,CAAC4B,MAAM,CAAC;EAE1C,MAAMC,KAAoB,GAAG,EAAE;EAG/B,IAAI,CAACZ,SAAS,CAACa,MAAM,CAACJ,IAAI,EAAE,CAAC,EAAE,GAAG1B,KAAK,CAAC;EACxC,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,KAAK,CAAC4B,MAAM,EAAEG,CAAC,EAAE,EAAE;IAAA,IAAAC,aAAA;IACrC,MAAMC,EAAE,GAAGP,IAAI,GAAGK,CAAC;IACnB,MAAMG,IAAI,GAAG,IAAI,CAACC,UAAU,CAACF,EAAE,CAAgB;IAC/CJ,KAAK,CAAChB,IAAI,CAACqB,IAAI,CAAC;IAEhB,KAAAF,aAAA,GAAI,IAAI,CAACI,OAAO,aAAZJ,aAAA,CAAcK,KAAK,EAAE;MACvBH,IAAI,CAACI,WAAW,CAAC,IAAI,CAACF,OAAO,CAAC;IAChC;EACF;EAEA,MAAMG,QAAQ,GAAGC,0BAAiB,CAACzC,IAAI,CAAC,IAAI,CAAC;EAE7C,KAAK,MAAMmC,IAAI,IAAIL,KAAK,EAAE;IACxBK,IAAI,CAACO,QAAQ,CAAC,CAAC;IACfP,IAAI,CAACQ,KAAK,CAAC,WAAW,CAAC;IAEvB,KAAK,MAAMN,OAAO,IAAIG,QAAQ,EAAE;MAC9BH,OAAO,CAACO,UAAU,CAACT,IAAI,EAAE,IAAI,CAAC;IAChC;EACF;EAEA,OAAOL,KAAK;AACd;AAEO,SAASX,sBAAsBA,CAEpClB,KAAU,EACV;EACA,OAAOyB,gBAAgB,CAAC1B,IAAI,CAAC,IAAI,EAAE,IAAI,CAACY,GAAG,EAAYX,KAAK,CAAC;AAC/D;AAEO,SAAS4C,qBAAqBA,CAEnC5C,KAAU,EACV;EACA,OAAOyB,gBAAgB,CAAC1B,IAAI,CAAC,IAAI,EAAG,IAAI,CAACY,GAAG,GAAc,CAAC,EAAEX,KAAK,CAAC;AACrE;AAEA,MAAM6C,IAAI,GAAOC,GAAQ,IAAKA,GAAG,CAACA,GAAG,CAAClB,MAAM,GAAG,CAAC,CAAC;AAEjD,SAASmB,4BAA4BA,CAACb,IAAc,EAAW;EAC7D,OACEzC,oBAAoB,CAACyC,IAAI,CAAC/B,MAAM,CAAC,KAChC0C,IAAI,CAACX,IAAI,CAAC/B,MAAM,CAAC6C,WAAW,CAAC,KAAKd,IAAI,CAACtB,IAAI,IAC1CmC,4BAA4B,CAACb,IAAI,CAAChC,UAAU,CAAC,CAAC;AAEpD;AAEA,SAAS+C,0BAA0BA,CACjCrC,IAAY,EACZsC,KAAY,EAC6C;EACzD,IAAI,CAAC9D,sBAAsB,CAACwB,IAAI,CAAC,IAAI,CAACpB,YAAY,CAACoB,IAAI,CAACuC,IAAI,CAAC,EAAE;IAC7D,OAAO,KAAK;EACd;EAIA,MAAMC,UAAU,GAAGF,KAAK,CAACG,cAAc,CAAC,CAAC;EAIzC,OACED,UAAU,CAACE,aAAa,CAAC1C,IAAI,CAACuC,IAAI,CAACI,IAAI,CAAC,IACxCH,UAAU,CAACI,aAAa,CAAC5C,IAAI,CAACuC,IAAI,CAACI,IAAI,CAAC,CAACE,kBAAkB,CAAC7B,MAAM,IAAI,CAAC;AAE3E;AAOO,SAAS8B,WAAWA,CAEzB7D,MAAyB,EACb;EACZC,yBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC;EAE3B,IAAI,IAAI,CAACN,oBAAoB,CAAC,CAAC,EAAE;IAC/B,OAAOoD,IAAI,CAAC,IAAI,CAACc,GAAG,CAAC,aAAa,CAAC,CAAC,CAACD,WAAW,CAAC7D,MAAM,CAAC;EAC1D;EAEA,MAAMG,KAAK,GAAGC,eAAe,CAACF,IAAI,CAAC,IAAI,EAAEF,MAAM,CAAC;EAEhD,MAAM;IAAEK,UAAU;IAAEC;EAAO,CAAC,GAAG,IAAI;EACnC,IACED,UAAU,CAACE,qBAAqB,CAAC,CAAC,IAClCF,UAAU,CAACG,kBAAkB,CAAC,CAAC,IAE/Bf,wBAAwB,CAACa,MAAM,CAAC,IAC/BD,UAAU,CAACI,0BAA0B,CAAC,CAAC,IAAI,IAAI,CAACC,aAAa,CAAC,CAAE,EACjE;IACA,OAAOL,UAAU,CAACwD,WAAW,CAC3B1D,KAAK,CAAC4D,GAAG,CAAChD,IAAI,IAAI;MAOhB,OAAOrB,YAAY,CAACqB,IAAI,CAAC,GAAGzB,mBAAmB,CAACyB,IAAI,CAAC,GAAGA,IAAI;IAC9D,CAAC,CACH,CAAC;EACH,CAAC,MAAM,IACJ,IAAI,CAACJ,UAAU,CAAC,YAAY,CAAC,IAC5B,CAAC,IAAI,CAACC,YAAY,CAAC,CAAC,IACpB,CAACP,UAAU,CAACO,YAAY,CAAC,CAAC,IAC3BP,UAAU,CAACQ,cAAc,CAAC,CAAC,IAAI,IAAI,CAACC,GAAG,KAAK,MAAO,EACpD;IACA,MAAMkD,IAAI,GAAG,IAAsD;IACnE,IAAIA,IAAI,CAACjD,IAAI,EAAE;MACb,MAAMA,IAAI,GAAGiD,IAAI,CAACjD,IAAI;MACtB,IAAI;QAAEsC;MAAM,CAAC,GAAG,IAAI;MAEpB,IAAIA,KAAK,CAAChB,IAAI,CAAC4B,SAAS,CAAC,CAAC,EAAE;QAC1BhF,gBAAgB,CAAC8B,IAAI,CAAC;QAEtBiD,IAAI,CAACvC,WAAW,CAACrC,cAAc,CAACJ,uBAAuB,CAAC,EAAE,EAAE+B,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACtEiD,IAAI,CAACF,GAAG,CAAC,aAAa,CAAC,CAA4BD,WAAW,CAAC1D,KAAK,CAAC;QACtE,OAAO,CAAC6D,IAAI,CAAC;MACf;MAEA,IAAId,4BAA4B,CAACc,IAAI,CAAC,EAAE;QACtC7D,KAAK,CAAC+D,OAAO,CAACnD,IAAI,CAAC;MACrB,CAAC,MAEI,IAAIvB,gBAAgB,CAACuB,IAAI,CAAC,IAAIlB,OAAO,CAACkB,IAAI,CAACoD,MAAM,CAAC,EAAE;QACvDhE,KAAK,CAAC+D,OAAO,CAACnD,IAAI,CAAC;QAEnBZ,KAAK,CAACa,IAAI,CAAClB,cAAc,CAAC,CAAC,CAAC;MAC9B,CAAC,MAAM,IAAIsD,0BAA0B,CAACrC,IAAI,EAAEsC,KAAK,CAAC,EAAE;QAClDlD,KAAK,CAAC+D,OAAO,CAACnD,IAAI,CAAC;QACnBZ,KAAK,CAACa,IAAI,CAAC3B,SAAS,CAAC0B,IAAI,CAACuC,IAAI,CAAC,CAAC;MAClC,CAAC,MAAM,IAAID,KAAK,CAACe,MAAM,CAACrD,IAAI,EAAE,IAAI,CAAC,EAAE;QAEnCZ,KAAK,CAACa,IAAI,CAACD,IAAI,CAAC;MAClB,CAAC,MAAM;QAGL,IAAIV,UAAU,CAACgE,QAAQ,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAExD,GAAG,EAAEC;QAAK,CAAC,CAAC,EAAE;UACtDsC,KAAK,GAAGA,KAAK,CAAC/C,MAAM;QACtB;QACA,MAAMiE,IAAI,GAAGlB,KAAK,CAACmB,6BAA6B,CAAC,CAAC;QAClDrE,KAAK,CAAC+D,OAAO,CACX5E,mBAAmB,CAIjBJ,oBAAoB,CAAC,GAAG,EAAEG,SAAS,CAACkF,IAAI,CAAC,EAAExD,IAAI,CACjD,CACF,CAAC;QACDZ,KAAK,CAACa,IAAI,CAAC1B,mBAAmB,CAACD,SAAS,CAACkF,IAAI,CAAC,CAAC,CAAC;MAClD;IACF;IAEA,OAAO,IAAI,CAACtD,+BAA+B,CAACd,KAAK,CAAC;EACpD,CAAC,MAAM,IAAIe,KAAK,CAACC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;IACxC,OAAO2B,qBAAqB,CAAC7C,IAAI,CAAC,IAAI,EAAEC,KAAK,CAAC;EAChD,CAAC,MAAM,IAAI,IAAI,CAACmB,kBAAkB,CAAC,CAAC,EAAE;IACpC,MAAMP,IAAI,GAAG,IAAI,CAACA,IAAmB;IACrC,MAAMQ,uBAAuB,GAC3BR,IAAI,KACH,CAAC,IAAI,CAACR,qBAAqB,CAAC,CAAC,IAC3BQ,IAAI,CAA2BS,UAAU,IAAI,IAAI,CAAC;IAEvD,IAAI,CAACC,WAAW,CAACtC,cAAc,CAACoC,uBAAuB,GAAG,CAACR,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IAEvE,OAAO,IAAI,CAAC0D,aAAa,CAAC,MAAM,EAAEtE,KAAK,CAAC;EAC1C,CAAC,MAAM;IACL,MAAM,IAAIwB,KAAK,CACb,gDAAgD,GAC9C,0DACJ,CAAC;EACH;AACF;AAMO,SAASG,iBAAiBA,CAE/B4C,SAAiB,EACjBC,WAAmB,EACnB;EACA,IAAI,CAAC,IAAI,CAACrE,MAAM,EAAE;EAElB,MAAM0B,KAAK,GAAG,IAAA4C,qBAAc,EAAC,IAAI,CAACC,GAAG,EAAE,IAAI,CAACvE,MAAM,CAAC,IAAK,EAAc;EAEtE,KAAK,MAAM,GAAG+B,IAAI,CAAC,IAAIL,KAAK,EAAE;IAC5B,IAAI,OAAOK,IAAI,CAACvB,GAAG,KAAK,QAAQ,IAAIuB,IAAI,CAACvB,GAAG,IAAI4D,SAAS,EAAE;MACzDrC,IAAI,CAACvB,GAAG,IAAI6D,WAAW;IACzB;EACF;AACF;AAEO,SAASvE,eAAeA,CAE7BD,KAAc,EACT;EACL,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,EAAE;EACX;EAEA,IAAI,CAACe,KAAK,CAACC,OAAO,CAAChB,KAAK,CAAC,EAAE;IACzBA,KAAK,GAAG,CAACA,KAAK,CAAC;EACjB;EAEA,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,KAAK,CAAC4B,MAAM,EAAEG,CAAC,EAAE,EAAE;IACrC,MAAMnB,IAAI,GAAGZ,KAAK,CAAC+B,CAAC,CAAC;IACrB,IAAI4C,GAAG;IAEP,IAAI,CAAC/D,IAAI,EAAE;MACT+D,GAAG,GAAG,gBAAgB;IACxB,CAAC,MAAM,IAAI,OAAO/D,IAAI,KAAK,QAAQ,EAAE;MACnC+D,GAAG,GAAG,4BAA4B;IACpC,CAAC,MAAM,IAAI,CAAC/D,IAAI,CAACgE,IAAI,EAAE;MACrBD,GAAG,GAAG,gBAAgB;IACxB,CAAC,MAAM,IAAI/D,IAAI,YAAYiE,cAAQ,EAAE;MACnCF,GAAG,GAAG,8CAA8C;IACtD;IAEA,IAAIA,GAAG,EAAE;MACP,MAAMC,IAAI,GAAG7D,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,GAAG,OAAO,GAAG,OAAOA,IAAI;MACxD,MAAM,IAAIY,KAAK,CACb,aAAamD,GAAG,sBAAsB5C,CAAC,gBAAgB6C,IAAI,EAC7D,CAAC;IACH;EACF;EAEA,OAAO5E,KAAK;AACd;AAEO,SAASuB,gBAAgBA,CAE9BuD,OAAU,EACV9E,KAMS,EACT;EAEAF,yBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC;EAG3BC,KAAK,GAAGC,eAAe,CAACF,IAAI,CAAC,IAAI,EAAEC,KAAK,CAAC;EAIzC,MAAMkC,IAAI,GAAG2C,cAAQ,CAAClB,GAAG,CAAC;IACxBzD,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,IAAI,CAACS,IAAI;IACjBK,SAAS,EAAG,IAAI,CAACL,IAAI,CAAOkE,OAAO,CAAiC;IACpEA,OAAO;IACPnE,GAAG,EAAE;EACP,CAAC,CAAC,CAACoE,UAAU,CAAC,IAAI,CAAC3C,OAAO,CAAC;EAE3B,OAAOlB,sBAAsB,CAACnB,IAAI,CAChCmC,IAAI,EAEJlC,KACF,CAAC;AACH;AAEO,SAASsE,aAAaA,CAK3BQ,OAAU,EACV9E,KAMS,EACT;EACAF,yBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC;EAE3B,MAAMiF,aAAa,GAAG/E,eAAe,CAACF,IAAI,CACxC,IAAI,EAEJC,KACF,CAAC;EAKD,MAAMiB,SAAS,GAAI,IAAI,CAACL,IAAI,CAAekE,OAAO,CAAa;EAC/D,MAAM5C,IAAI,GAAG2C,cAAQ,CAAClB,GAAG,CAAC;IACxBzD,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,IAAI,CAACS,IAAI;IACjBK,SAAS,EAAEA,SAAyC;IACpD6D,OAAO;IACPnE,GAAG,EAAEM,SAAS,CAACW;EACjB,CAAC,CAAC,CAACmD,UAAU,CAAC,IAAI,CAAC3C,OAAO,CAAC;EAE3B,OAAOF,IAAI,CAAC+C,mBAAmB,CAACD,aAAa,CAAC;AAChD;AAMO,SAASE,KAAKA,CAEnBhC,KAAY,GAAG,IAAI,CAACA,KAAK,EACzB;EACA,MAAMiC,OAAO,GAAG,IAAIC,gBAAW,CAAI,IAAI,EAAElC,KAAK,CAAC;EAC/C,OAAOiC,OAAO,CAACE,GAAG,CAAC,CAAC;AACtB", "ignoreList": []}