{"version": 3, "names": ["virtualTypes", "require", "_debug", "_index", "_index2", "_t", "t", "cache", "_generator", "NodePath_ancestry", "NodePath_inference", "NodePath_replacement", "NodePath_evaluation", "NodePath_conversion", "NodePath_introspection", "NodePath_context", "NodePath_removal", "NodePath_modification", "NodePath_family", "NodePath_comments", "NodePath_virtual_types_validator", "validate", "debug", "buildDebug", "REMOVED", "exports", "SHOULD_STOP", "SHOULD_SKIP", "NodePath_Final", "default", "NodePath", "constructor", "hub", "parent", "contexts", "state", "opts", "_traverseFlags", "<PERSON><PERSON><PERSON><PERSON>", "parentPath", "container", "<PERSON><PERSON><PERSON>", "key", "node", "type", "data", "context", "scope", "removed", "v", "shouldStop", "shouldSkip", "get", "Error", "targetNode", "paths", "getOrCreateCachedPaths", "path", "set", "setup", "getScope", "isScope", "<PERSON><PERSON>", "setData", "val", "Object", "create", "getData", "def", "undefined", "hasNode", "buildCodeFrameError", "msg", "SyntaxError", "buildError", "traverse", "visitor", "getPathLocation", "parts", "inList", "unshift", "join", "message", "enabled", "toString", "generator", "code", "parent<PERSON><PERSON>", "methods", "findParent", "find", "getFunctionParent", "getStatementParent", "getEarliestCommonAncestorFrom", "getDeepestCommonAncestorFrom", "getAncestry", "isAncestor", "isDescendant", "inType", "getTypeAnnotation", "isBaseType", "couldBeBaseType", "baseTypeStrictlyMatches", "isGenericType", "replaceWithMultiple", "replaceWithSourceString", "replaceWith", "replaceExpressionWithStatements", "replaceInline", "<PERSON><PERSON><PERSON><PERSON>", "evaluate", "toCom<PERSON><PERSON>ey", "ensureBlock", "unwrapFunctionEnvironment", "arrowFunctionToExpression", "splitExportDeclaration", "ensureFunctionName", "matchesPattern", "has", "isStatic", "is", "isnt", "equals", "isNodeType", "canHaveVariableDeclarationOrExpression", "canSwapBetweenExpressionAndStatement", "isCompletionRecord", "isStatementOrBlock", "referencesImport", "getSource", "willIMaybeExecuteBefore", "_guessExecutionStatusRelativeTo", "resolve", "isConstantExpression", "isInStrictMode", "call", "isDenylisted", "isBlacklisted", "visit", "skip", "<PERSON><PERSON><PERSON>", "stop", "setScope", "setContext", "resync", "popContext", "pushContext", "<PERSON><PERSON><PERSON>", "requeue", "requeueComputedKeyAndDecorators", "remove", "insertBefore", "insertAfter", "updateSiblingKeys", "unshiftContainer", "pushContainer", "hoist", "getOpposite", "getCompletionRecords", "getSibling", "getPrevSibling", "getNextSibling", "getAllNextSiblings", "getAllPrevSiblings", "getAssignmentIdentifiers", "getBindingIdentifiers", "getOuterBindingIdentifiers", "getBindingIdentifierPaths", "getOuterBindingIdentifierPaths", "shareCommentsWithSiblings", "addComment", "addComments", "assign", "prototype", "arrowFunctionToShadowed", "String", "_guessExecutionStatusRelativeToDifferentFunctions", "_getTypeAnnotation", "_replaceWith", "_resolve", "_call", "_resyncParent", "_resync<PERSON>ey", "_resyncList", "_resyncRemoved", "_getQueueContexts", "_removeFromScope", "_callRemovalHooks", "_remove", "_mark<PERSON><PERSON>oved", "_assertUnremoved", "_containerInsert", "_containerInsertBefore", "_containerInsertAfter", "_verifyNodeList", "_get<PERSON>ey", "_get<PERSON>attern", "TYPES", "typeKey", "fn", "TypeError", "keys", "includes", "push"], "sources": ["../../src/path/index.ts"], "sourcesContent": ["import type { HubInterface } from \"../hub.ts\";\nimport type TraversalContext from \"../context.ts\";\nimport type { ExplodedTraverseOptions } from \"../index.ts\";\nimport * as virtualTypes from \"./lib/virtual-types.ts\";\nimport buildDebug from \"debug\";\nimport traverse from \"../index.ts\";\nimport type { Visitor } from \"../types.ts\";\nimport Scope from \"../scope/index.ts\";\nimport { validate } from \"@babel/types\";\nimport * as t from \"@babel/types\";\nimport * as cache from \"../cache.ts\";\nimport generator from \"@babel/generator\";\n\n// NodePath is split across many files.\nimport * as NodePath_ancestry from \"./ancestry.ts\";\nimport * as NodePath_inference from \"./inference/index.ts\";\nimport * as NodePath_replacement from \"./replacement.ts\";\nimport * as NodePath_evaluation from \"./evaluation.ts\";\nimport * as NodePath_conversion from \"./conversion.ts\";\nimport * as NodePath_introspection from \"./introspection.ts\";\nimport * as NodePath_context from \"./context.ts\";\nimport * as NodePath_removal from \"./removal.ts\";\nimport * as NodePath_modification from \"./modification.ts\";\nimport * as NodePath_family from \"./family.ts\";\nimport * as NodePath_comments from \"./comments.ts\";\nimport * as NodePath_virtual_types_validator from \"./lib/virtual-types-validator.ts\";\nimport type { NodePathAssertions } from \"./generated/asserts.ts\";\nimport type { NodePathValidators } from \"./generated/validators.ts\";\n\nconst debug = buildDebug(\"babel\");\n\nexport const REMOVED = 1 << 0;\nexport const SHOULD_STOP = 1 << 1;\nexport const SHOULD_SKIP = 1 << 2;\n\ndeclare const bit: import(\"../../../../scripts/babel-plugin-bit-decorator/types.d.ts\").BitDecorator<any>;\n\nconst NodePath_Final = class NodePath {\n  constructor(hub: HubInterface, parent: t.Node | null) {\n    this.parent = parent;\n    this.hub = hub;\n    this.data = null;\n\n    this.context = null;\n    this.scope = null;\n  }\n\n  declare parent: t.Node;\n  declare hub: HubInterface;\n  declare data: Record<string | symbol, unknown>;\n  // TraversalContext is configured by setContext\n  declare context: TraversalContext;\n  declare scope: Scope;\n\n  contexts: Array<TraversalContext> = [];\n  state: any = null;\n  opts: ExplodedTraverseOptions | null = null;\n\n  @bit.storage _traverseFlags: number;\n  @bit(REMOVED) accessor removed = false;\n  @bit(SHOULD_STOP) accessor shouldStop = false;\n  @bit(SHOULD_SKIP) accessor shouldSkip = false;\n\n  skipKeys: Record<string, boolean> | null = null;\n  parentPath: NodePath_Final | null = null;\n  container: t.Node | Array<t.Node> | null = null;\n  listKey: string | null = null;\n  key: string | number | null = null;\n  node: t.Node | null = null;\n  type: t.Node[\"type\"] | null = null;\n\n  static get({\n    hub,\n    parentPath,\n    parent,\n    container,\n    listKey,\n    key,\n  }: {\n    hub?: HubInterface;\n    parentPath: NodePath_Final | null;\n    parent: t.Node;\n    container: t.Node | t.Node[];\n    listKey?: string;\n    key: string | number;\n  }): NodePath_Final {\n    if (!hub && parentPath) {\n      hub = parentPath.hub;\n    }\n\n    if (!parent) {\n      throw new Error(\"To get a node path the parent needs to exist\");\n    }\n\n    const targetNode =\n      // @ts-expect-error key must present in container\n      container[key];\n\n    const paths = cache.getOrCreateCachedPaths(hub, parent);\n\n    let path = paths.get(targetNode);\n    if (!path) {\n      path = new NodePath(hub, parent) as NodePath_Final;\n      if (targetNode) paths.set(targetNode, path);\n    }\n\n    path.setup(parentPath, container, listKey, key);\n\n    return path;\n  }\n\n  getScope(this: NodePath_Final, scope: Scope): Scope {\n    return this.isScope() ? new Scope(this) : scope;\n  }\n\n  setData(key: string | symbol, val: any): any {\n    if (this.data == null) {\n      this.data = Object.create(null);\n    }\n    return (this.data[key] = val);\n  }\n\n  getData(key: string | symbol, def?: any): any {\n    if (this.data == null) {\n      this.data = Object.create(null);\n    }\n    let val = this.data[key];\n    if (val === undefined && def !== undefined) val = this.data[key] = def;\n    return val;\n  }\n\n  hasNode(): boolean {\n    return this.node != null;\n  }\n\n  buildCodeFrameError(\n    msg: string,\n    Error: new () => Error = SyntaxError,\n  ): Error {\n    return this.hub.buildError(this.node, msg, Error);\n  }\n\n  traverse<T>(this: NodePath_Final, visitor: Visitor<T>, state: T): void;\n  traverse(this: NodePath_Final, visitor: Visitor): void;\n  traverse(this: NodePath_Final, visitor: any, state?: any) {\n    traverse(this.node, visitor, this.scope, state, this);\n  }\n\n  set(key: string, node: any) {\n    validate(this.node, key, node);\n    // @ts-expect-error key must present in this.node\n    this.node[key] = node;\n  }\n\n  getPathLocation(this: NodePath_Final): string {\n    const parts = [];\n    let path: NodePath_Final = this;\n    do {\n      let key = path.key;\n      if (path.inList) key = `${path.listKey}[${key}]`;\n      parts.unshift(key);\n    } while ((path = path.parentPath));\n    return parts.join(\".\");\n  }\n\n  debug(this: NodePath_Final, message: string) {\n    if (!debug.enabled) return;\n    debug(`${this.getPathLocation()} ${this.type}: ${message}`);\n  }\n\n  toString() {\n    return generator(this.node).code;\n  }\n\n  get inList() {\n    return !!this.listKey;\n  }\n\n  set inList(inList) {\n    if (!inList) {\n      this.listKey = null;\n    }\n    // ignore inList = true as it should depend on `listKey`\n  }\n\n  get parentKey(): string {\n    return (this.listKey || this.key) as string;\n  }\n};\n\nconst methods = {\n  // NodePath_ancestry\n  findParent: NodePath_ancestry.findParent,\n  find: NodePath_ancestry.find,\n  getFunctionParent: NodePath_ancestry.getFunctionParent,\n  getStatementParent: NodePath_ancestry.getStatementParent,\n  getEarliestCommonAncestorFrom:\n    NodePath_ancestry.getEarliestCommonAncestorFrom,\n  getDeepestCommonAncestorFrom: NodePath_ancestry.getDeepestCommonAncestorFrom,\n  getAncestry: NodePath_ancestry.getAncestry,\n  isAncestor: NodePath_ancestry.isAncestor,\n  isDescendant: NodePath_ancestry.isDescendant,\n  inType: NodePath_ancestry.inType,\n\n  // NodePath_inference\n  getTypeAnnotation: NodePath_inference.getTypeAnnotation,\n  isBaseType: NodePath_inference.isBaseType,\n  couldBeBaseType: NodePath_inference.couldBeBaseType,\n  baseTypeStrictlyMatches: NodePath_inference.baseTypeStrictlyMatches,\n  isGenericType: NodePath_inference.isGenericType,\n\n  // NodePath_replacement\n  replaceWithMultiple: NodePath_replacement.replaceWithMultiple,\n  replaceWithSourceString: NodePath_replacement.replaceWithSourceString,\n  replaceWith: NodePath_replacement.replaceWith,\n  replaceExpressionWithStatements:\n    NodePath_replacement.replaceExpressionWithStatements,\n  replaceInline: NodePath_replacement.replaceInline,\n\n  // NodePath_evaluation\n  evaluateTruthy: NodePath_evaluation.evaluateTruthy,\n  evaluate: NodePath_evaluation.evaluate,\n\n  // NodePath_conversion\n  toComputedKey: NodePath_conversion.toComputedKey,\n  ensureBlock: NodePath_conversion.ensureBlock,\n  unwrapFunctionEnvironment: NodePath_conversion.unwrapFunctionEnvironment,\n  arrowFunctionToExpression: NodePath_conversion.arrowFunctionToExpression,\n  splitExportDeclaration: NodePath_conversion.splitExportDeclaration,\n  ensureFunctionName: NodePath_conversion.ensureFunctionName,\n\n  // NodePath_introspection\n  matchesPattern: NodePath_introspection.matchesPattern,\n  has: NodePath_introspection.has,\n  isStatic: NodePath_introspection.isStatic,\n  is: NodePath_introspection.is,\n  isnt: NodePath_introspection.isnt,\n  equals: NodePath_introspection.equals,\n  isNodeType: NodePath_introspection.isNodeType,\n  canHaveVariableDeclarationOrExpression:\n    NodePath_introspection.canHaveVariableDeclarationOrExpression,\n  canSwapBetweenExpressionAndStatement:\n    NodePath_introspection.canSwapBetweenExpressionAndStatement,\n  isCompletionRecord: NodePath_introspection.isCompletionRecord,\n  isStatementOrBlock: NodePath_introspection.isStatementOrBlock,\n  referencesImport: NodePath_introspection.referencesImport,\n  getSource: NodePath_introspection.getSource,\n  willIMaybeExecuteBefore: NodePath_introspection.willIMaybeExecuteBefore,\n  _guessExecutionStatusRelativeTo:\n    NodePath_introspection._guessExecutionStatusRelativeTo,\n  resolve: NodePath_introspection.resolve,\n  isConstantExpression: NodePath_introspection.isConstantExpression,\n  isInStrictMode: NodePath_introspection.isInStrictMode,\n\n  // NodePath_context\n  call: NodePath_context.call,\n  isDenylisted: NodePath_context.isDenylisted,\n  isBlacklisted: NodePath_context.isBlacklisted,\n  visit: NodePath_context.visit,\n  skip: NodePath_context.skip,\n  skipKey: NodePath_context.skipKey,\n  stop: NodePath_context.stop,\n  setScope: NodePath_context.setScope,\n  setContext: NodePath_context.setContext,\n  resync: NodePath_context.resync,\n  popContext: NodePath_context.popContext,\n  pushContext: NodePath_context.pushContext,\n  setup: NodePath_context.setup,\n  setKey: NodePath_context.setKey,\n  requeue: NodePath_context.requeue,\n  requeueComputedKeyAndDecorators:\n    NodePath_context.requeueComputedKeyAndDecorators,\n\n  // NodePath_removal\n  remove: NodePath_removal.remove,\n\n  // NodePath_modification\n  insertBefore: NodePath_modification.insertBefore,\n  insertAfter: NodePath_modification.insertAfter,\n  updateSiblingKeys: NodePath_modification.updateSiblingKeys,\n  unshiftContainer: NodePath_modification.unshiftContainer,\n  pushContainer: NodePath_modification.pushContainer,\n  hoist: NodePath_modification.hoist,\n\n  // NodePath_family\n  getOpposite: NodePath_family.getOpposite,\n  getCompletionRecords: NodePath_family.getCompletionRecords,\n  getSibling: NodePath_family.getSibling,\n  getPrevSibling: NodePath_family.getPrevSibling,\n  getNextSibling: NodePath_family.getNextSibling,\n  getAllNextSiblings: NodePath_family.getAllNextSiblings,\n  getAllPrevSiblings: NodePath_family.getAllPrevSiblings,\n  get: NodePath_family.get,\n  getAssignmentIdentifiers: NodePath_family.getAssignmentIdentifiers,\n  getBindingIdentifiers: NodePath_family.getBindingIdentifiers,\n  getOuterBindingIdentifiers: NodePath_family.getOuterBindingIdentifiers,\n  getBindingIdentifierPaths: NodePath_family.getBindingIdentifierPaths,\n  getOuterBindingIdentifierPaths:\n    NodePath_family.getOuterBindingIdentifierPaths,\n\n  // NodePath_comments\n  shareCommentsWithSiblings: NodePath_comments.shareCommentsWithSiblings,\n  addComment: NodePath_comments.addComment,\n  addComments: NodePath_comments.addComments,\n};\n\nObject.assign(NodePath_Final.prototype, methods);\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  // @ts-expect-error babel 7 only\n  NodePath_Final.prototype.arrowFunctionToShadowed =\n    // workaround for rollup\n    // @ts-expect-error babel 7 only\n    NodePath_conversion[String(\"arrowFunctionToShadowed\")];\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-expect-error The original _guessExecutionStatusRelativeToDifferentFunctions only worked for paths in\n  // different functions, but _guessExecutionStatusRelativeTo works as a replacement in those cases.\n  NodePath_Final.prototype._guessExecutionStatusRelativeToDifferentFunctions =\n    NodePath_introspection._guessExecutionStatusRelativeTo;\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-expect-error The original _guessExecutionStatusRelativeToDifferentFunctions only worked for paths in\n  // different functions, but _guessExecutionStatusRelativeTo works as a replacement in those cases.\n  NodePath_Final.prototype._guessExecutionStatusRelativeToDifferentFunctions =\n    NodePath_introspection._guessExecutionStatusRelativeTo;\n\n  Object.assign(NodePath_Final.prototype, {\n    // NodePath_inference\n    _getTypeAnnotation: NodePath_inference._getTypeAnnotation,\n\n    // NodePath_replacement\n    _replaceWith: NodePath_replacement._replaceWith,\n\n    // NodePath_introspection\n    _resolve: NodePath_introspection._resolve,\n\n    // NodePath_context\n    _call: NodePath_context._call,\n    _resyncParent: NodePath_context._resyncParent,\n    _resyncKey: NodePath_context._resyncKey,\n    _resyncList: NodePath_context._resyncList,\n    _resyncRemoved: NodePath_context._resyncRemoved,\n    _getQueueContexts: NodePath_context._getQueueContexts,\n\n    // NodePath_removal\n    _removeFromScope: NodePath_removal._removeFromScope,\n    _callRemovalHooks: NodePath_removal._callRemovalHooks,\n    _remove: NodePath_removal._remove,\n    _markRemoved: NodePath_removal._markRemoved,\n    _assertUnremoved: NodePath_removal._assertUnremoved,\n\n    // NodePath_modification\n    _containerInsert: NodePath_modification._containerInsert,\n    _containerInsertBefore: NodePath_modification._containerInsertBefore,\n    _containerInsertAfter: NodePath_modification._containerInsertAfter,\n    _verifyNodeList: NodePath_modification._verifyNodeList,\n\n    // NodePath_family\n    _getKey: NodePath_family._getKey,\n    _getPattern: NodePath_family._getPattern,\n  });\n}\n\n// we can not use `import { TYPES } from \"@babel/types\"` here\n// because the transformNamedBabelTypesImportToDestructuring plugin in babel.config.js\n// does not offer live bindings for `TYPES`\n// we can change to `import { TYPES }` when we are publishing ES modules only\nfor (const type of t.TYPES) {\n  const typeKey = `is${type}`;\n  // @ts-expect-error typeKey must present in t\n  const fn = t[typeKey];\n  // @ts-expect-error augmenting NodePath prototype\n  NodePath_Final.prototype[typeKey] = function (opts: any) {\n    return fn(this.node, opts);\n  };\n\n  // @ts-expect-error augmenting NodePath prototype\n  NodePath_Final.prototype[`assert${type}`] = function (opts: any) {\n    if (!fn(this.node, opts)) {\n      throw new TypeError(`Expected node path of type ${type}`);\n    }\n  };\n}\n\n// Register virtual types validators after base types validators\nObject.assign(NodePath_Final.prototype, NodePath_virtual_types_validator);\n\nfor (const type of Object.keys(virtualTypes) as (keyof typeof virtualTypes)[]) {\n  if (type[0] === \"_\") continue;\n  if (!t.TYPES.includes(type)) t.TYPES.push(type);\n}\n\ninterface NodePathOverwrites {\n  // We need to re-define these predicate and assertion\n  // methods here, because we cannot refine `this` in\n  // a function declaration.\n  // See https://github.com/microsoft/TypeScript/issues/38150\n\n  /**\n   * NOTE: This assertion doesn't narrow the type on unions of\n   * NodePaths, due to https://github.com/microsoft/TypeScript/issues/44212\n   *\n   * @see ./conversion.ts for implementation.\n   */\n  ensureBlock(\n    this: NodePath_Final,\n  ): asserts this is NodePath_Final<\n    (\n      | t.Loop\n      | t.WithStatement\n      | t.Function\n      | t.LabeledStatement\n      | t.CatchClause\n    ) & { body: t.BlockStatement }\n  >;\n  /**\n   * @see ./introspection.ts for implementation.\n   */\n  isStatementOrBlock(\n    this: NodePath_Final,\n  ): this is NodePath_Final<t.Statement | t.Block>;\n}\n\ntype NodePathMixins = Omit<typeof methods, keyof NodePathOverwrites>;\n\ninterface NodePath<T extends t.Node>\n  extends InstanceType<typeof NodePath_Final>,\n    NodePathAssertions,\n    NodePathValidators,\n    NodePathMixins,\n    NodePathOverwrites {\n  type: T[\"type\"] | null;\n  node: T;\n  parent: t.ParentMaps[T[\"type\"]];\n  parentPath: t.ParentMaps[T[\"type\"]] extends null\n    ? null\n    : NodePath_Final<t.ParentMaps[T[\"type\"]]> | null;\n}\n\n// This trick is necessary so that\n// NodePath_Final<A | B> is the same as NodePath_Final<A> | NodePath_Final<B>\ntype NodePath_Final<T extends t.Node = t.Node> = T extends any\n  ? NodePath<T>\n  : never;\n\nexport { NodePath_Final as default, type NodePath as NodePath_Internal };\n"], "mappings": ";;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAEA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,EAAA,GAAAJ,OAAA;AAAwC,IAAAK,CAAA,GAAAD,EAAA;AAExC,IAAAE,KAAA,GAAAN,OAAA;AACA,IAAAO,UAAA,GAAAP,OAAA;AAGA,IAAAQ,iBAAA,GAAAR,OAAA;AACA,IAAAS,kBAAA,GAAAT,OAAA;AACA,IAAAU,oBAAA,GAAAV,OAAA;AACA,IAAAW,mBAAA,GAAAX,OAAA;AACA,IAAAY,mBAAA,GAAAZ,OAAA;AACA,IAAAa,sBAAA,GAAAb,OAAA;AACA,IAAAc,gBAAA,GAAAd,OAAA;AACA,IAAAe,gBAAA,GAAAf,OAAA;AACA,IAAAgB,qBAAA,GAAAhB,OAAA;AACA,IAAAiB,eAAA,GAAAjB,OAAA;AACA,IAAAkB,iBAAA,GAAAlB,OAAA;AACA,IAAAmB,gCAAA,GAAAnB,OAAA;AAAqF;EAjB5EoB;AAAQ,IAAAhB,EAAA;AAqBjB,MAAMiB,KAAK,GAAGC,MAAU,CAAC,OAAO,CAAC;AAE1B,MAAMC,OAAO,GAAAC,OAAA,CAAAD,OAAA,GAAG,CAAC,IAAI,CAAC;AACtB,MAAME,WAAW,GAAAD,OAAA,CAAAC,WAAA,GAAG,CAAC,IAAI,CAAC;AAC1B,MAAMC,WAAW,GAAAF,OAAA,CAAAE,WAAA,GAAG,CAAC,IAAI,CAAC;AAIjC,MAAMC,cAAc,GAAAH,OAAA,CAAAI,OAAA,GAAG,MAAMC,QAAQ,CAAC;EACpCC,WAAWA,CAACC,GAAiB,EAAEC,MAAqB,EAAE;IAAA,KAgBtDC,QAAQ,GAA4B,EAAE;IAAA,KACtCC,KAAK,GAAQ,IAAI;IAAA,KACjBC,IAAI,GAAmC,IAAI;IAAA,KAE9BC,cAAc;IAAA,KAK3BC,QAAQ,GAAmC,IAAI;IAAA,KAC/CC,UAAU,GAA0B,IAAI;IAAA,KACxCC,SAAS,GAAkC,IAAI;IAAA,KAC/CC,OAAO,GAAkB,IAAI;IAAA,KAC7BC,GAAG,GAA2B,IAAI;IAAA,KAClCC,IAAI,GAAkB,IAAI;IAAA,KAC1BC,IAAI,GAA0B,IAAI;IA9BhC,IAAI,CAACX,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACa,IAAI,GAAG,IAAI;IAEhB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;EACnB;EAAC,IAcsBC,OAAOA,CAAA;IAAA,aADjBX,cAAc;EAAA;EAAA,IACJW,OAAOA,CAAAC,CAAA;IAAA,IAAAA,CAAA,OADjBZ,cAAc,gBAAdA,cAAc;EAAA;EAAA,IAEAa,UAAUA,CAAA;IAAA,aAFxBb,cAAc;EAAA;EAAA,IAEAa,UAAUA,CAAAD,CAAA;IAAA,IAAAA,CAAA,OAFxBZ,cAAc,gBAAdA,cAAc;EAAA;EAAA,IAGAc,UAAUA,CAAA;IAAA,aAHxBd,cAAc;EAAA;EAAA,IAGAc,UAAUA,CAAAF,CAAA;IAAA,IAAAA,CAAA,OAHxBZ,cAAc,gBAAdA,cAAc;EAAA;EAa3B,OAAOe,GAAGA,CAAC;IACTpB,GAAG;IACHO,UAAU;IACVN,MAAM;IACNO,SAAS;IACTC,OAAO;IACPC;EAQF,CAAC,EAAkB;IACjB,IAAI,CAACV,GAAG,IAAIO,UAAU,EAAE;MACtBP,GAAG,GAAGO,UAAU,CAACP,GAAG;IACtB;IAEA,IAAI,CAACC,MAAM,EAAE;MACX,MAAM,IAAIoB,KAAK,CAAC,8CAA8C,CAAC;IACjE;IAEA,MAAMC,UAAU,GAEdd,SAAS,CAACE,GAAG,CAAC;IAEhB,MAAMa,KAAK,GAAGhD,KAAK,CAACiD,sBAAsB,CAACxB,GAAG,EAAEC,MAAM,CAAC;IAEvD,IAAIwB,IAAI,GAAGF,KAAK,CAACH,GAAG,CAACE,UAAU,CAAC;IAChC,IAAI,CAACG,IAAI,EAAE;MACTA,IAAI,GAAG,IAAI3B,QAAQ,CAACE,GAAG,EAAEC,MAAM,CAAmB;MAClD,IAAIqB,UAAU,EAAEC,KAAK,CAACG,GAAG,CAACJ,UAAU,EAAEG,IAAI,CAAC;IAC7C;IAEAA,IAAI,CAACE,KAAK,CAACpB,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAEC,GAAG,CAAC;IAE/C,OAAOe,IAAI;EACb;EAEAG,QAAQA,CAAuBb,KAAY,EAAS;IAClD,OAAO,IAAI,CAACc,OAAO,CAAC,CAAC,GAAG,IAAIC,eAAK,CAAC,IAAI,CAAC,GAAGf,KAAK;EACjD;EAEAgB,OAAOA,CAACrB,GAAoB,EAAEsB,GAAQ,EAAO;IAC3C,IAAI,IAAI,CAACnB,IAAI,IAAI,IAAI,EAAE;MACrB,IAAI,CAACA,IAAI,GAAGoB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACjC;IACA,OAAQ,IAAI,CAACrB,IAAI,CAACH,GAAG,CAAC,GAAGsB,GAAG;EAC9B;EAEAG,OAAOA,CAACzB,GAAoB,EAAE0B,GAAS,EAAO;IAC5C,IAAI,IAAI,CAACvB,IAAI,IAAI,IAAI,EAAE;MACrB,IAAI,CAACA,IAAI,GAAGoB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACjC;IACA,IAAIF,GAAG,GAAG,IAAI,CAACnB,IAAI,CAACH,GAAG,CAAC;IACxB,IAAIsB,GAAG,KAAKK,SAAS,IAAID,GAAG,KAAKC,SAAS,EAAEL,GAAG,GAAG,IAAI,CAACnB,IAAI,CAACH,GAAG,CAAC,GAAG0B,GAAG;IACtE,OAAOJ,GAAG;EACZ;EAEAM,OAAOA,CAAA,EAAY;IACjB,OAAO,IAAI,CAAC3B,IAAI,IAAI,IAAI;EAC1B;EAEA4B,mBAAmBA,CACjBC,GAAW,EACXnB,KAAsB,GAAGoB,WAAW,EAC7B;IACP,OAAO,IAAI,CAACzC,GAAG,CAAC0C,UAAU,CAAC,IAAI,CAAC/B,IAAI,EAAE6B,GAAG,EAAEnB,KAAK,CAAC;EACnD;EAIAsB,QAAQA,CAAuBC,OAAY,EAAEzC,KAAW,EAAE;IACxD,IAAAwC,cAAQ,EAAC,IAAI,CAAChC,IAAI,EAAEiC,OAAO,EAAE,IAAI,CAAC7B,KAAK,EAAEZ,KAAK,EAAE,IAAI,CAAC;EACvD;EAEAuB,GAAGA,CAAChB,GAAW,EAAEC,IAAS,EAAE;IAC1BtB,QAAQ,CAAC,IAAI,CAACsB,IAAI,EAAED,GAAG,EAAEC,IAAI,CAAC;IAE9B,IAAI,CAACA,IAAI,CAACD,GAAG,CAAC,GAAGC,IAAI;EACvB;EAEAkC,eAAeA,CAAA,EAA+B;IAC5C,MAAMC,KAAK,GAAG,EAAE;IAChB,IAAIrB,IAAoB,GAAG,IAAI;IAC/B,GAAG;MACD,IAAIf,GAAG,GAAGe,IAAI,CAACf,GAAG;MAClB,IAAIe,IAAI,CAACsB,MAAM,EAAErC,GAAG,GAAG,GAAGe,IAAI,CAAChB,OAAO,IAAIC,GAAG,GAAG;MAChDoC,KAAK,CAACE,OAAO,CAACtC,GAAG,CAAC;IACpB,CAAC,QAASe,IAAI,GAAGA,IAAI,CAAClB,UAAU;IAChC,OAAOuC,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC;EACxB;EAEA3D,KAAKA,CAAuB4D,OAAe,EAAE;IAC3C,IAAI,CAAC5D,KAAK,CAAC6D,OAAO,EAAE;IACpB7D,KAAK,CAAC,GAAG,IAAI,CAACuD,eAAe,CAAC,CAAC,IAAI,IAAI,CAACjC,IAAI,KAAKsC,OAAO,EAAE,CAAC;EAC7D;EAEAE,QAAQA,CAAA,EAAG;IACT,OAAO,IAAAC,kBAAS,EAAC,IAAI,CAAC1C,IAAI,CAAC,CAAC2C,IAAI;EAClC;EAEA,IAAIP,MAAMA,CAAA,EAAG;IACX,OAAO,CAAC,CAAC,IAAI,CAACtC,OAAO;EACvB;EAEA,IAAIsC,MAAMA,CAACA,MAAM,EAAE;IACjB,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAACtC,OAAO,GAAG,IAAI;IACrB;EAEF;EAEA,IAAI8C,SAASA,CAAA,EAAW;IACtB,OAAQ,IAAI,CAAC9C,OAAO,IAAI,IAAI,CAACC,GAAG;EAClC;AACF,CAAC;AAED,MAAM8C,OAAO,GAAG;EAEdC,UAAU,EAAEhF,iBAAiB,CAACgF,UAAU;EACxCC,IAAI,EAAEjF,iBAAiB,CAACiF,IAAI;EAC5BC,iBAAiB,EAAElF,iBAAiB,CAACkF,iBAAiB;EACtDC,kBAAkB,EAAEnF,iBAAiB,CAACmF,kBAAkB;EACxDC,6BAA6B,EAC3BpF,iBAAiB,CAACoF,6BAA6B;EACjDC,4BAA4B,EAAErF,iBAAiB,CAACqF,4BAA4B;EAC5EC,WAAW,EAAEtF,iBAAiB,CAACsF,WAAW;EAC1CC,UAAU,EAAEvF,iBAAiB,CAACuF,UAAU;EACxCC,YAAY,EAAExF,iBAAiB,CAACwF,YAAY;EAC5CC,MAAM,EAAEzF,iBAAiB,CAACyF,MAAM;EAGhCC,iBAAiB,EAAEzF,kBAAkB,CAACyF,iBAAiB;EACvDC,UAAU,EAAE1F,kBAAkB,CAAC0F,UAAU;EACzCC,eAAe,EAAE3F,kBAAkB,CAAC2F,eAAe;EACnDC,uBAAuB,EAAE5F,kBAAkB,CAAC4F,uBAAuB;EACnEC,aAAa,EAAE7F,kBAAkB,CAAC6F,aAAa;EAG/CC,mBAAmB,EAAE7F,oBAAoB,CAAC6F,mBAAmB;EAC7DC,uBAAuB,EAAE9F,oBAAoB,CAAC8F,uBAAuB;EACrEC,WAAW,EAAE/F,oBAAoB,CAAC+F,WAAW;EAC7CC,+BAA+B,EAC7BhG,oBAAoB,CAACgG,+BAA+B;EACtDC,aAAa,EAAEjG,oBAAoB,CAACiG,aAAa;EAGjDC,cAAc,EAAEjG,mBAAmB,CAACiG,cAAc;EAClDC,QAAQ,EAAElG,mBAAmB,CAACkG,QAAQ;EAGtCC,aAAa,EAAElG,mBAAmB,CAACkG,aAAa;EAChDC,WAAW,EAAEnG,mBAAmB,CAACmG,WAAW;EAC5CC,yBAAyB,EAAEpG,mBAAmB,CAACoG,yBAAyB;EACxEC,yBAAyB,EAAErG,mBAAmB,CAACqG,yBAAyB;EACxEC,sBAAsB,EAAEtG,mBAAmB,CAACsG,sBAAsB;EAClEC,kBAAkB,EAAEvG,mBAAmB,CAACuG,kBAAkB;EAG1DC,cAAc,EAAEvG,sBAAsB,CAACuG,cAAc;EACrDC,GAAG,EAAExG,sBAAsB,CAACwG,GAAG;EAC/BC,QAAQ,EAAEzG,sBAAsB,CAACyG,QAAQ;EACzCC,EAAE,EAAE1G,sBAAsB,CAAC0G,EAAE;EAC7BC,IAAI,EAAE3G,sBAAsB,CAAC2G,IAAI;EACjCC,MAAM,EAAE5G,sBAAsB,CAAC4G,MAAM;EACrCC,UAAU,EAAE7G,sBAAsB,CAAC6G,UAAU;EAC7CC,sCAAsC,EACpC9G,sBAAsB,CAAC8G,sCAAsC;EAC/DC,oCAAoC,EAClC/G,sBAAsB,CAAC+G,oCAAoC;EAC7DC,kBAAkB,EAAEhH,sBAAsB,CAACgH,kBAAkB;EAC7DC,kBAAkB,EAAEjH,sBAAsB,CAACiH,kBAAkB;EAC7DC,gBAAgB,EAAElH,sBAAsB,CAACkH,gBAAgB;EACzDC,SAAS,EAAEnH,sBAAsB,CAACmH,SAAS;EAC3CC,uBAAuB,EAAEpH,sBAAsB,CAACoH,uBAAuB;EACvEC,+BAA+B,EAC7BrH,sBAAsB,CAACqH,+BAA+B;EACxDC,OAAO,EAAEtH,sBAAsB,CAACsH,OAAO;EACvCC,oBAAoB,EAAEvH,sBAAsB,CAACuH,oBAAoB;EACjEC,cAAc,EAAExH,sBAAsB,CAACwH,cAAc;EAGrDC,IAAI,EAAExH,gBAAgB,CAACwH,IAAI;EAC3BC,YAAY,EAAEzH,gBAAgB,CAACyH,YAAY;EAC3CC,aAAa,EAAE1H,gBAAgB,CAAC0H,aAAa;EAC7CC,KAAK,EAAE3H,gBAAgB,CAAC2H,KAAK;EAC7BC,IAAI,EAAE5H,gBAAgB,CAAC4H,IAAI;EAC3BC,OAAO,EAAE7H,gBAAgB,CAAC6H,OAAO;EACjCC,IAAI,EAAE9H,gBAAgB,CAAC8H,IAAI;EAC3BC,QAAQ,EAAE/H,gBAAgB,CAAC+H,QAAQ;EACnCC,UAAU,EAAEhI,gBAAgB,CAACgI,UAAU;EACvCC,MAAM,EAAEjI,gBAAgB,CAACiI,MAAM;EAC/BC,UAAU,EAAElI,gBAAgB,CAACkI,UAAU;EACvCC,WAAW,EAAEnI,gBAAgB,CAACmI,WAAW;EACzCvF,KAAK,EAAE5C,gBAAgB,CAAC4C,KAAK;EAC7BwF,MAAM,EAAEpI,gBAAgB,CAACoI,MAAM;EAC/BC,OAAO,EAAErI,gBAAgB,CAACqI,OAAO;EACjCC,+BAA+B,EAC7BtI,gBAAgB,CAACsI,+BAA+B;EAGlDC,MAAM,EAAEtI,gBAAgB,CAACsI,MAAM;EAG/BC,YAAY,EAAEtI,qBAAqB,CAACsI,YAAY;EAChDC,WAAW,EAAEvI,qBAAqB,CAACuI,WAAW;EAC9CC,iBAAiB,EAAExI,qBAAqB,CAACwI,iBAAiB;EAC1DC,gBAAgB,EAAEzI,qBAAqB,CAACyI,gBAAgB;EACxDC,aAAa,EAAE1I,qBAAqB,CAAC0I,aAAa;EAClDC,KAAK,EAAE3I,qBAAqB,CAAC2I,KAAK;EAGlCC,WAAW,EAAE3I,eAAe,CAAC2I,WAAW;EACxCC,oBAAoB,EAAE5I,eAAe,CAAC4I,oBAAoB;EAC1DC,UAAU,EAAE7I,eAAe,CAAC6I,UAAU;EACtCC,cAAc,EAAE9I,eAAe,CAAC8I,cAAc;EAC9CC,cAAc,EAAE/I,eAAe,CAAC+I,cAAc;EAC9CC,kBAAkB,EAAEhJ,eAAe,CAACgJ,kBAAkB;EACtDC,kBAAkB,EAAEjJ,eAAe,CAACiJ,kBAAkB;EACtD/G,GAAG,EAAElC,eAAe,CAACkC,GAAG;EACxBgH,wBAAwB,EAAElJ,eAAe,CAACkJ,wBAAwB;EAClEC,qBAAqB,EAAEnJ,eAAe,CAACmJ,qBAAqB;EAC5DC,0BAA0B,EAAEpJ,eAAe,CAACoJ,0BAA0B;EACtEC,yBAAyB,EAAErJ,eAAe,CAACqJ,yBAAyB;EACpEC,8BAA8B,EAC5BtJ,eAAe,CAACsJ,8BAA8B;EAGhDC,yBAAyB,EAAEtJ,iBAAiB,CAACsJ,yBAAyB;EACtEC,UAAU,EAAEvJ,iBAAiB,CAACuJ,UAAU;EACxCC,WAAW,EAAExJ,iBAAiB,CAACwJ;AACjC,CAAC;AAED1G,MAAM,CAAC2G,MAAM,CAAChJ,cAAc,CAACiJ,SAAS,EAAErF,OAAO,CAAC;AAED;EAE7C5D,cAAc,CAACiJ,SAAS,CAACC,uBAAuB,GAG9CjK,mBAAmB,CAACkK,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAC1D;AAEmC;EAGjCnJ,cAAc,CAACiJ,SAAS,CAACG,iDAAiD,GACxElK,sBAAsB,CAACqH,+BAA+B;AAC1D;AAEmC;EAGjCvG,cAAc,CAACiJ,SAAS,CAACG,iDAAiD,GACxElK,sBAAsB,CAACqH,+BAA+B;EAExDlE,MAAM,CAAC2G,MAAM,CAAChJ,cAAc,CAACiJ,SAAS,EAAE;IAEtCI,kBAAkB,EAAEvK,kBAAkB,CAACuK,kBAAkB;IAGzDC,YAAY,EAAEvK,oBAAoB,CAACuK,YAAY;IAG/CC,QAAQ,EAAErK,sBAAsB,CAACqK,QAAQ;IAGzCC,KAAK,EAAErK,gBAAgB,CAACqK,KAAK;IAC7BC,aAAa,EAAEtK,gBAAgB,CAACsK,aAAa;IAC7CC,UAAU,EAAEvK,gBAAgB,CAACuK,UAAU;IACvCC,WAAW,EAAExK,gBAAgB,CAACwK,WAAW;IACzCC,cAAc,EAAEzK,gBAAgB,CAACyK,cAAc;IAC/CC,iBAAiB,EAAE1K,gBAAgB,CAAC0K,iBAAiB;IAGrDC,gBAAgB,EAAE1K,gBAAgB,CAAC0K,gBAAgB;IACnDC,iBAAiB,EAAE3K,gBAAgB,CAAC2K,iBAAiB;IACrDC,OAAO,EAAE5K,gBAAgB,CAAC4K,OAAO;IACjCC,YAAY,EAAE7K,gBAAgB,CAAC6K,YAAY;IAC3CC,gBAAgB,EAAE9K,gBAAgB,CAAC8K,gBAAgB;IAGnDC,gBAAgB,EAAE9K,qBAAqB,CAAC8K,gBAAgB;IACxDC,sBAAsB,EAAE/K,qBAAqB,CAAC+K,sBAAsB;IACpEC,qBAAqB,EAAEhL,qBAAqB,CAACgL,qBAAqB;IAClEC,eAAe,EAAEjL,qBAAqB,CAACiL,eAAe;IAGtDC,OAAO,EAAEjL,eAAe,CAACiL,OAAO;IAChCC,WAAW,EAAElL,eAAe,CAACkL;EAC/B,CAAC,CAAC;AACJ;AAMA,KAAK,MAAMxJ,IAAI,IAAItC,CAAC,CAAC+L,KAAK,EAAE;EAC1B,MAAMC,OAAO,GAAG,KAAK1J,IAAI,EAAE;EAE3B,MAAM2J,EAAE,GAAGjM,CAAC,CAACgM,OAAO,CAAC;EAErB1K,cAAc,CAACiJ,SAAS,CAACyB,OAAO,CAAC,GAAG,UAAUlK,IAAS,EAAE;IACvD,OAAOmK,EAAE,CAAC,IAAI,CAAC5J,IAAI,EAAEP,IAAI,CAAC;EAC5B,CAAC;EAGDR,cAAc,CAACiJ,SAAS,CAAC,SAASjI,IAAI,EAAE,CAAC,GAAG,UAAUR,IAAS,EAAE;IAC/D,IAAI,CAACmK,EAAE,CAAC,IAAI,CAAC5J,IAAI,EAAEP,IAAI,CAAC,EAAE;MACxB,MAAM,IAAIoK,SAAS,CAAC,8BAA8B5J,IAAI,EAAE,CAAC;IAC3D;EACF,CAAC;AACH;AAGAqB,MAAM,CAAC2G,MAAM,CAAChJ,cAAc,CAACiJ,SAAS,EAAEzJ,gCAAgC,CAAC;AAEzE,KAAK,MAAMwB,IAAI,IAAIqB,MAAM,CAACwI,IAAI,CAACzM,YAAY,CAAC,EAAmC;EAC7E,IAAI4C,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;EACrB,IAAI,CAACtC,CAAC,CAAC+L,KAAK,CAACK,QAAQ,CAAC9J,IAAI,CAAC,EAAEtC,CAAC,CAAC+L,KAAK,CAACM,IAAI,CAAC/J,IAAI,CAAC;AACjD", "ignoreList": []}