{"version": 3, "names": ["_t", "require", "_jsesc", "isAssignmentPattern", "isIdentifier", "Identifier", "node", "_node$loc", "sourceIdentifierName", "loc", "identifierName", "name", "word", "ArgumentPlaceholder", "token", "RestElement", "print", "argument", "ObjectExpression", "props", "properties", "length", "exit", "enterForStatementInit", "space", "printList", "indent", "statement", "sourceWithOffset", "ObjectMethod", "printJoin", "decorators", "_methodHead", "body", "ObjectProperty", "computed", "key", "value", "left", "shorthand", "ArrayExpression", "elems", "elements", "len", "i", "elem", "RecordExpression", "startToken", "endToken", "format", "recordAndTupleSyntaxType", "Error", "JSON", "stringify", "TupleExpression", "RegExpLiteral", "pattern", "flags", "<PERSON>olean<PERSON>iter<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NumericLiteral", "raw", "getPossibleRaw", "opts", "jsescOption", "str", "numbers", "number", "jsesc", "minified", "StringLiteral", "undefined", "val", "BigIntLiteral", "DecimalLiteral", "validTopicTokenSet", "Set", "TopicReference", "topicToken", "has", "givenTopicTokenJSON", "validTopics", "Array", "from", "v", "join", "PipelineTopicExpression", "expression", "PipelineBareFunction", "callee", "PipelinePrimaryTopicReference"], "sources": ["../../src/generators/types.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport { isAssignmentPattern, isIdentifier } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport jsesc from \"jsesc\";\n\nexport function Identifier(this: Printer, node: t.Identifier) {\n  this.sourceIdentifierName(node.loc?.identifierName || node.name);\n  this.word(node.name);\n}\n\nexport function ArgumentPlaceholder(this: Printer) {\n  this.token(\"?\");\n}\n\nexport function RestElement(this: Printer, node: t.RestElement) {\n  this.token(\"...\");\n  this.print(node.argument, node);\n}\n\nexport { RestElement as SpreadElement };\n\nexport function ObjectExpression(this: Printer, node: t.ObjectExpression) {\n  const props = node.properties;\n\n  this.token(\"{\");\n\n  if (props.length) {\n    const exit = this.enterForStatementInit(false);\n    this.space();\n    this.printList(props, node, { indent: true, statement: true });\n    this.space();\n    exit();\n  }\n\n  this.sourceWithOffset(\"end\", node.loc, -1);\n\n  this.token(\"}\");\n}\n\nexport { ObjectExpression as ObjectPattern };\n\nexport function ObjectMethod(this: Printer, node: t.ObjectMethod) {\n  this.printJoin(node.decorators, node);\n  this._methodHead(node);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function ObjectProperty(this: Printer, node: t.ObjectProperty) {\n  this.printJoin(node.decorators, node);\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key, node);\n    this.token(\"]\");\n  } else {\n    // print `({ foo: foo = 5 } = {})` as `({ foo = 5 } = {});`\n    if (\n      isAssignmentPattern(node.value) &&\n      isIdentifier(node.key) &&\n      // @ts-expect-error todo(flow->ts) `.name` does not exist on some types in union\n      node.key.name === node.value.left.name\n    ) {\n      this.print(node.value, node);\n      return;\n    }\n\n    this.print(node.key, node);\n\n    // shorthand!\n    if (\n      node.shorthand &&\n      isIdentifier(node.key) &&\n      isIdentifier(node.value) &&\n      node.key.name === node.value.name\n    ) {\n      return;\n    }\n  }\n\n  this.token(\":\");\n  this.space();\n  this.print(node.value, node);\n}\n\nexport function ArrayExpression(this: Printer, node: t.ArrayExpression) {\n  const elems = node.elements;\n  const len = elems.length;\n\n  this.token(\"[\");\n\n  const exit = this.enterForStatementInit(false);\n\n  for (let i = 0; i < elems.length; i++) {\n    const elem = elems[i];\n    if (elem) {\n      if (i > 0) this.space();\n      this.print(elem, node);\n      if (i < len - 1) this.token(\",\");\n    } else {\n      // If the array expression ends with a hole, that hole\n      // will be ignored by the interpreter, but if it ends with\n      // two (or more) holes, we need to write out two (or more)\n      // commas so that the resulting code is interpreted with\n      // both (all) of the holes.\n      this.token(\",\");\n    }\n  }\n\n  exit();\n\n  this.token(\"]\");\n}\n\nexport { ArrayExpression as ArrayPattern };\n\nexport function RecordExpression(this: Printer, node: t.RecordExpression) {\n  const props = node.properties;\n\n  let startToken;\n  let endToken;\n  if (process.env.BABEL_8_BREAKING) {\n    startToken = \"#{\";\n    endToken = \"}\";\n  } else {\n    if (this.format.recordAndTupleSyntaxType === \"bar\") {\n      startToken = \"{|\";\n      endToken = \"|}\";\n    } else if (\n      this.format.recordAndTupleSyntaxType !== \"hash\" &&\n      this.format.recordAndTupleSyntaxType != null\n    ) {\n      throw new Error(\n        `The \"recordAndTupleSyntaxType\" generator option must be \"bar\" or \"hash\" (${JSON.stringify(\n          this.format.recordAndTupleSyntaxType,\n        )} received).`,\n      );\n    } else {\n      startToken = \"#{\";\n      endToken = \"}\";\n    }\n  }\n\n  this.token(startToken);\n\n  if (props.length) {\n    this.space();\n    this.printList(props, node, { indent: true, statement: true });\n    this.space();\n  }\n  this.token(endToken);\n}\n\nexport function TupleExpression(this: Printer, node: t.TupleExpression) {\n  const elems = node.elements;\n  const len = elems.length;\n\n  let startToken;\n  let endToken;\n  if (process.env.BABEL_8_BREAKING) {\n    startToken = \"#[\";\n    endToken = \"]\";\n  } else {\n    if (this.format.recordAndTupleSyntaxType === \"bar\") {\n      startToken = \"[|\";\n      endToken = \"|]\";\n    } else if (this.format.recordAndTupleSyntaxType === \"hash\") {\n      startToken = \"#[\";\n      endToken = \"]\";\n    } else {\n      throw new Error(\n        `${this.format.recordAndTupleSyntaxType} is not a valid recordAndTuple syntax type`,\n      );\n    }\n  }\n\n  this.token(startToken);\n\n  for (let i = 0; i < elems.length; i++) {\n    const elem = elems[i];\n    if (elem) {\n      if (i > 0) this.space();\n      this.print(elem, node);\n      if (i < len - 1) this.token(\",\");\n    }\n  }\n\n  this.token(endToken);\n}\n\nexport function RegExpLiteral(this: Printer, node: t.RegExpLiteral) {\n  this.word(`/${node.pattern}/${node.flags}`);\n}\n\nexport function BooleanLiteral(this: Printer, node: t.BooleanLiteral) {\n  this.word(node.value ? \"true\" : \"false\");\n}\n\nexport function NullLiteral(this: Printer) {\n  this.word(\"null\");\n}\n\nexport function NumericLiteral(this: Printer, node: t.NumericLiteral) {\n  const raw = this.getPossibleRaw(node);\n  const opts = this.format.jsescOption;\n  const value = node.value;\n  const str = value + \"\";\n  if (opts.numbers) {\n    this.number(jsesc(value, opts), value);\n  } else if (raw == null) {\n    this.number(str, value); // normalize\n  } else if (this.format.minified) {\n    this.number(raw.length < str.length ? raw : str, value);\n  } else {\n    this.number(raw, value);\n  }\n}\n\nexport function StringLiteral(this: Printer, node: t.StringLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.token(raw);\n    return;\n  }\n\n  const val = jsesc(node.value, this.format.jsescOption);\n\n  this.token(val);\n}\n\nexport function BigIntLiteral(this: Printer, node: t.BigIntLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.word(raw);\n    return;\n  }\n  this.word(node.value + \"n\");\n}\n\nexport function DecimalLiteral(this: Printer, node: t.DecimalLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.word(raw);\n    return;\n  }\n  this.word(node.value + \"m\");\n}\n\n// Hack pipe operator\nconst validTopicTokenSet = new Set([\"^^\", \"@@\", \"^\", \"%\", \"#\"]);\nexport function TopicReference(this: Printer) {\n  const { topicToken } = this.format;\n\n  if (validTopicTokenSet.has(topicToken)) {\n    this.token(topicToken);\n  } else {\n    const givenTopicTokenJSON = JSON.stringify(topicToken);\n    const validTopics = Array.from(validTopicTokenSet, v => JSON.stringify(v));\n    throw new Error(\n      `The \"topicToken\" generator option must be one of ` +\n        `${validTopics.join(\", \")} (${givenTopicTokenJSON} received instead).`,\n    );\n  }\n}\n\n// Smart-mix pipe operator\nexport function PipelineTopicExpression(\n  this: Printer,\n  node: t.PipelineTopicExpression,\n) {\n  this.print(node.expression, node);\n}\n\nexport function PipelineBareFunction(\n  this: Printer,\n  node: t.PipelineBareFunction,\n) {\n  this.print(node.callee, node);\n}\n\nexport function PipelinePrimaryTopicReference(this: Printer) {\n  this.token(\"#\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAA0B;EAFjBE,mBAAmB;EAAEC;AAAY,IAAAJ,EAAA;AAInC,SAASK,UAAUA,CAAgBC,IAAkB,EAAE;EAAA,IAAAC,SAAA;EAC5D,IAAI,CAACC,oBAAoB,CAAC,EAAAD,SAAA,GAAAD,IAAI,CAACG,GAAG,qBAARF,SAAA,CAAUG,cAAc,KAAIJ,IAAI,CAACK,IAAI,CAAC;EAChE,IAAI,CAACC,IAAI,CAACN,IAAI,CAACK,IAAI,CAAC;AACtB;AAEO,SAASE,mBAAmBA,CAAA,EAAgB;EACjD,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAASC,WAAWA,CAAgBT,IAAmB,EAAE;EAC9D,IAAI,CAACQ,KAAK,CAAC,KAAK,CAAC;EACjB,IAAI,CAACE,KAAK,CAACV,IAAI,CAACW,QAAQ,EAAEX,IAAI,CAAC;AACjC;AAIO,SAASY,gBAAgBA,CAAgBZ,IAAwB,EAAE;EACxE,MAAMa,KAAK,GAAGb,IAAI,CAACc,UAAU;EAE7B,IAAI,CAACN,SAAK,IAAI,CAAC;EAEf,IAAIK,KAAK,CAACE,MAAM,EAAE;IAChB,MAAMC,IAAI,GAAG,IAAI,CAACC,qBAAqB,CAAC,KAAK,CAAC;IAC9C,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,SAAS,CAACN,KAAK,EAAEb,IAAI,EAAE;MAAEoB,MAAM,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC9D,IAAI,CAACH,KAAK,CAAC,CAAC;IACZF,IAAI,CAAC,CAAC;EACR;EAEA,IAAI,CAACM,gBAAgB,CAAC,KAAK,EAAEtB,IAAI,CAACG,GAAG,EAAE,CAAC,CAAC,CAAC;EAE1C,IAAI,CAACK,SAAK,IAAI,CAAC;AACjB;AAIO,SAASe,YAAYA,CAAgBvB,IAAoB,EAAE;EAChE,IAAI,CAACwB,SAAS,CAACxB,IAAI,CAACyB,UAAU,EAAEzB,IAAI,CAAC;EACrC,IAAI,CAAC0B,WAAW,CAAC1B,IAAI,CAAC;EACtB,IAAI,CAACkB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACR,KAAK,CAACV,IAAI,CAAC2B,IAAI,EAAE3B,IAAI,CAAC;AAC7B;AAEO,SAAS4B,cAAcA,CAAgB5B,IAAsB,EAAE;EACpE,IAAI,CAACwB,SAAS,CAACxB,IAAI,CAACyB,UAAU,EAAEzB,IAAI,CAAC;EAErC,IAAIA,IAAI,CAAC6B,QAAQ,EAAE;IACjB,IAAI,CAACrB,SAAK,GAAI,CAAC;IACf,IAAI,CAACE,KAAK,CAACV,IAAI,CAAC8B,GAAG,EAAE9B,IAAI,CAAC;IAC1B,IAAI,CAACQ,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IAEL,IACEX,mBAAmB,CAACG,IAAI,CAAC+B,KAAK,CAAC,IAC/BjC,YAAY,CAACE,IAAI,CAAC8B,GAAG,CAAC,IAEtB9B,IAAI,CAAC8B,GAAG,CAACzB,IAAI,KAAKL,IAAI,CAAC+B,KAAK,CAACC,IAAI,CAAC3B,IAAI,EACtC;MACA,IAAI,CAACK,KAAK,CAACV,IAAI,CAAC+B,KAAK,EAAE/B,IAAI,CAAC;MAC5B;IACF;IAEA,IAAI,CAACU,KAAK,CAACV,IAAI,CAAC8B,GAAG,EAAE9B,IAAI,CAAC;IAG1B,IACEA,IAAI,CAACiC,SAAS,IACdnC,YAAY,CAACE,IAAI,CAAC8B,GAAG,CAAC,IACtBhC,YAAY,CAACE,IAAI,CAAC+B,KAAK,CAAC,IACxB/B,IAAI,CAAC8B,GAAG,CAACzB,IAAI,KAAKL,IAAI,CAAC+B,KAAK,CAAC1B,IAAI,EACjC;MACA;IACF;EACF;EAEA,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAACU,KAAK,CAAC,CAAC;EACZ,IAAI,CAACR,KAAK,CAACV,IAAI,CAAC+B,KAAK,EAAE/B,IAAI,CAAC;AAC9B;AAEO,SAASkC,eAAeA,CAAgBlC,IAAuB,EAAE;EACtE,MAAMmC,KAAK,GAAGnC,IAAI,CAACoC,QAAQ;EAC3B,MAAMC,GAAG,GAAGF,KAAK,CAACpB,MAAM;EAExB,IAAI,CAACP,SAAK,GAAI,CAAC;EAEf,MAAMQ,IAAI,GAAG,IAAI,CAACC,qBAAqB,CAAC,KAAK,CAAC;EAE9C,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACpB,MAAM,EAAEuB,CAAC,EAAE,EAAE;IACrC,MAAMC,IAAI,GAAGJ,KAAK,CAACG,CAAC,CAAC;IACrB,IAAIC,IAAI,EAAE;MACR,IAAID,CAAC,GAAG,CAAC,EAAE,IAAI,CAACpB,KAAK,CAAC,CAAC;MACvB,IAAI,CAACR,KAAK,CAAC6B,IAAI,EAAEvC,IAAI,CAAC;MACtB,IAAIsC,CAAC,GAAGD,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC7B,SAAK,GAAI,CAAC;IAClC,CAAC,MAAM;MAML,IAAI,CAACA,SAAK,GAAI,CAAC;IACjB;EACF;EAEAQ,IAAI,CAAC,CAAC;EAEN,IAAI,CAACR,SAAK,GAAI,CAAC;AACjB;AAIO,SAASgC,gBAAgBA,CAAgBxC,IAAwB,EAAE;EACxE,MAAMa,KAAK,GAAGb,IAAI,CAACc,UAAU;EAE7B,IAAI2B,UAAU;EACd,IAAIC,QAAQ;EAIL;IACL,IAAI,IAAI,CAACC,MAAM,CAACC,wBAAwB,KAAK,KAAK,EAAE;MAClDH,UAAU,GAAG,IAAI;MACjBC,QAAQ,GAAG,IAAI;IACjB,CAAC,MAAM,IACL,IAAI,CAACC,MAAM,CAACC,wBAAwB,KAAK,MAAM,IAC/C,IAAI,CAACD,MAAM,CAACC,wBAAwB,IAAI,IAAI,EAC5C;MACA,MAAM,IAAIC,KAAK,CACb,4EAA4EC,IAAI,CAACC,SAAS,CACxF,IAAI,CAACJ,MAAM,CAACC,wBACd,CAAC,aACH,CAAC;IACH,CAAC,MAAM;MACLH,UAAU,GAAG,IAAI;MACjBC,QAAQ,GAAG,GAAG;IAChB;EACF;EAEA,IAAI,CAAClC,KAAK,CAACiC,UAAU,CAAC;EAEtB,IAAI5B,KAAK,CAACE,MAAM,EAAE;IAChB,IAAI,CAACG,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,SAAS,CAACN,KAAK,EAAEb,IAAI,EAAE;MAAEoB,MAAM,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC9D,IAAI,CAACH,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACV,KAAK,CAACkC,QAAQ,CAAC;AACtB;AAEO,SAASM,eAAeA,CAAgBhD,IAAuB,EAAE;EACtE,MAAMmC,KAAK,GAAGnC,IAAI,CAACoC,QAAQ;EAC3B,MAAMC,GAAG,GAAGF,KAAK,CAACpB,MAAM;EAExB,IAAI0B,UAAU;EACd,IAAIC,QAAQ;EAIL;IACL,IAAI,IAAI,CAACC,MAAM,CAACC,wBAAwB,KAAK,KAAK,EAAE;MAClDH,UAAU,GAAG,IAAI;MACjBC,QAAQ,GAAG,IAAI;IACjB,CAAC,MAAM,IAAI,IAAI,CAACC,MAAM,CAACC,wBAAwB,KAAK,MAAM,EAAE;MAC1DH,UAAU,GAAG,IAAI;MACjBC,QAAQ,GAAG,GAAG;IAChB,CAAC,MAAM;MACL,MAAM,IAAIG,KAAK,CACb,GAAG,IAAI,CAACF,MAAM,CAACC,wBAAwB,4CACzC,CAAC;IACH;EACF;EAEA,IAAI,CAACpC,KAAK,CAACiC,UAAU,CAAC;EAEtB,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACpB,MAAM,EAAEuB,CAAC,EAAE,EAAE;IACrC,MAAMC,IAAI,GAAGJ,KAAK,CAACG,CAAC,CAAC;IACrB,IAAIC,IAAI,EAAE;MACR,IAAID,CAAC,GAAG,CAAC,EAAE,IAAI,CAACpB,KAAK,CAAC,CAAC;MACvB,IAAI,CAACR,KAAK,CAAC6B,IAAI,EAAEvC,IAAI,CAAC;MACtB,IAAIsC,CAAC,GAAGD,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC7B,SAAK,GAAI,CAAC;IAClC;EACF;EAEA,IAAI,CAACA,KAAK,CAACkC,QAAQ,CAAC;AACtB;AAEO,SAASO,aAAaA,CAAgBjD,IAAqB,EAAE;EAClE,IAAI,CAACM,IAAI,CAAC,IAAIN,IAAI,CAACkD,OAAO,IAAIlD,IAAI,CAACmD,KAAK,EAAE,CAAC;AAC7C;AAEO,SAASC,cAAcA,CAAgBpD,IAAsB,EAAE;EACpE,IAAI,CAACM,IAAI,CAACN,IAAI,CAAC+B,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;AAC1C;AAEO,SAASsB,WAAWA,CAAA,EAAgB;EACzC,IAAI,CAAC/C,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAASgD,cAAcA,CAAgBtD,IAAsB,EAAE;EACpE,MAAMuD,GAAG,GAAG,IAAI,CAACC,cAAc,CAACxD,IAAI,CAAC;EACrC,MAAMyD,IAAI,GAAG,IAAI,CAACd,MAAM,CAACe,WAAW;EACpC,MAAM3B,KAAK,GAAG/B,IAAI,CAAC+B,KAAK;EACxB,MAAM4B,GAAG,GAAG5B,KAAK,GAAG,EAAE;EACtB,IAAI0B,IAAI,CAACG,OAAO,EAAE;IAChB,IAAI,CAACC,MAAM,CAACC,MAAK,CAAC/B,KAAK,EAAE0B,IAAI,CAAC,EAAE1B,KAAK,CAAC;EACxC,CAAC,MAAM,IAAIwB,GAAG,IAAI,IAAI,EAAE;IACtB,IAAI,CAACM,MAAM,CAACF,GAAG,EAAE5B,KAAK,CAAC;EACzB,CAAC,MAAM,IAAI,IAAI,CAACY,MAAM,CAACoB,QAAQ,EAAE;IAC/B,IAAI,CAACF,MAAM,CAACN,GAAG,CAACxC,MAAM,GAAG4C,GAAG,CAAC5C,MAAM,GAAGwC,GAAG,GAAGI,GAAG,EAAE5B,KAAK,CAAC;EACzD,CAAC,MAAM;IACL,IAAI,CAAC8B,MAAM,CAACN,GAAG,EAAExB,KAAK,CAAC;EACzB;AACF;AAEO,SAASiC,aAAaA,CAAgBhE,IAAqB,EAAE;EAClE,MAAMuD,GAAG,GAAG,IAAI,CAACC,cAAc,CAACxD,IAAI,CAAC;EACrC,IAAI,CAAC,IAAI,CAAC2C,MAAM,CAACoB,QAAQ,IAAIR,GAAG,KAAKU,SAAS,EAAE;IAC9C,IAAI,CAACzD,KAAK,CAAC+C,GAAG,CAAC;IACf;EACF;EAEA,MAAMW,GAAG,GAAGJ,MAAK,CAAC9D,IAAI,CAAC+B,KAAK,EAAE,IAAI,CAACY,MAAM,CAACe,WAAW,CAAC;EAEtD,IAAI,CAAClD,KAAK,CAAC0D,GAAG,CAAC;AACjB;AAEO,SAASC,aAAaA,CAAgBnE,IAAqB,EAAE;EAClE,MAAMuD,GAAG,GAAG,IAAI,CAACC,cAAc,CAACxD,IAAI,CAAC;EACrC,IAAI,CAAC,IAAI,CAAC2C,MAAM,CAACoB,QAAQ,IAAIR,GAAG,KAAKU,SAAS,EAAE;IAC9C,IAAI,CAAC3D,IAAI,CAACiD,GAAG,CAAC;IACd;EACF;EACA,IAAI,CAACjD,IAAI,CAACN,IAAI,CAAC+B,KAAK,GAAG,GAAG,CAAC;AAC7B;AAEO,SAASqC,cAAcA,CAAgBpE,IAAsB,EAAE;EACpE,MAAMuD,GAAG,GAAG,IAAI,CAACC,cAAc,CAACxD,IAAI,CAAC;EACrC,IAAI,CAAC,IAAI,CAAC2C,MAAM,CAACoB,QAAQ,IAAIR,GAAG,KAAKU,SAAS,EAAE;IAC9C,IAAI,CAAC3D,IAAI,CAACiD,GAAG,CAAC;IACd;EACF;EACA,IAAI,CAACjD,IAAI,CAACN,IAAI,CAAC+B,KAAK,GAAG,GAAG,CAAC;AAC7B;AAGA,MAAMsC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxD,SAASC,cAAcA,CAAA,EAAgB;EAC5C,MAAM;IAAEC;EAAW,CAAC,GAAG,IAAI,CAAC7B,MAAM;EAElC,IAAI0B,kBAAkB,CAACI,GAAG,CAACD,UAAU,CAAC,EAAE;IACtC,IAAI,CAAChE,KAAK,CAACgE,UAAU,CAAC;EACxB,CAAC,MAAM;IACL,MAAME,mBAAmB,GAAG5B,IAAI,CAACC,SAAS,CAACyB,UAAU,CAAC;IACtD,MAAMG,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACR,kBAAkB,EAAES,CAAC,IAAIhC,IAAI,CAACC,SAAS,CAAC+B,CAAC,CAAC,CAAC;IAC1E,MAAM,IAAIjC,KAAK,CACb,mDAAmD,GACjD,GAAG8B,WAAW,CAACI,IAAI,CAAC,IAAI,CAAC,KAAKL,mBAAmB,qBACrD,CAAC;EACH;AACF;AAGO,SAASM,uBAAuBA,CAErChF,IAA+B,EAC/B;EACA,IAAI,CAACU,KAAK,CAACV,IAAI,CAACiF,UAAU,EAAEjF,IAAI,CAAC;AACnC;AAEO,SAASkF,oBAAoBA,CAElClF,IAA4B,EAC5B;EACA,IAAI,CAACU,KAAK,CAACV,IAAI,CAACmF,MAAM,EAAEnF,IAAI,CAAC;AAC/B;AAEO,SAASoF,6BAA6BA,CAAA,EAAgB;EAC3D,IAAI,CAAC5E,SAAK,GAAI,CAAC;AACjB", "ignoreList": []}