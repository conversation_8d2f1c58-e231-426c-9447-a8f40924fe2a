{"version": 3, "names": ["_t", "require", "_index", "isCallExpression", "isLiteral", "isMemberExpression", "isNewExpression", "UnaryExpression", "node", "operator", "word", "space", "token", "print", "argument", "DoExpression", "async", "body", "ParenthesizedExpression", "expression", "rightParens", "UpdateExpression", "prefix", "printTerminatorless", "ConditionalExpression", "test", "consequent", "alternate", "NewExpression", "parent", "callee", "format", "minified", "arguments", "length", "optional", "typeArguments", "typeParameters", "exit", "enterForStatementInit", "printList", "SequenceExpression", "expressions", "ThisExpression", "Super", "_shouldPrintDecoratorsBeforeExport", "decoratorsBeforeExport", "start", "declaration", "Decorator", "newline", "OptionalMemberExpression", "computed", "property", "object", "TypeError", "value", "OptionalCallExpression", "CallExpression", "Import", "AwaitExpression", "YieldExpression", "delegate", "EmptyStatement", "semicolon", "ExpressionStatement", "tokenContext", "TokenContext", "expressionStatement", "AssignmentPattern", "left", "type", "typeAnnotation", "right", "AssignmentExpression", "BindExpression", "MemberExpression", "MetaProperty", "meta", "PrivateName", "id", "V8IntrinsicIdentifier", "name", "ModuleExpression", "indent", "directives", "dedent", "rightBrace"], "sources": ["../../src/generators/expressions.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport {\n  isCallExpression,\n  isLiteral,\n  isMemberExpression,\n  isNewExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport { TokenContext } from \"../node/index.ts\";\n\nexport function UnaryExpression(this: Printer, node: t.UnaryExpression) {\n  const { operator } = node;\n  if (\n    operator === \"void\" ||\n    operator === \"delete\" ||\n    operator === \"typeof\" ||\n    // throwExpressions\n    operator === \"throw\"\n  ) {\n    this.word(operator);\n    this.space();\n  } else {\n    this.token(operator);\n  }\n\n  this.print(node.argument, node);\n}\n\nexport function DoExpression(this: Printer, node: t.DoExpression) {\n  if (node.async) {\n    this.word(\"async\", true);\n    this.space();\n  }\n  this.word(\"do\");\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function ParenthesizedExpression(\n  this: Printer,\n  node: t.ParenthesizedExpression,\n) {\n  this.token(\"(\");\n  this.print(node.expression, node);\n  this.rightParens(node);\n}\n\nexport function UpdateExpression(this: Printer, node: t.UpdateExpression) {\n  if (node.prefix) {\n    this.token(node.operator);\n    this.print(node.argument, node);\n  } else {\n    this.printTerminatorless(node.argument, node, true);\n    this.token(node.operator);\n  }\n}\n\nexport function ConditionalExpression(\n  this: Printer,\n  node: t.ConditionalExpression,\n) {\n  this.print(node.test, node);\n  this.space();\n  this.token(\"?\");\n  this.space();\n  this.print(node.consequent, node);\n  this.space();\n  this.token(\":\");\n  this.space();\n  this.print(node.alternate, node);\n}\n\nexport function NewExpression(\n  this: Printer,\n  node: t.NewExpression,\n  parent: t.Node,\n) {\n  this.word(\"new\");\n  this.space();\n  this.print(node.callee, node);\n  if (\n    this.format.minified &&\n    node.arguments.length === 0 &&\n    !node.optional &&\n    !isCallExpression(parent, { callee: node }) &&\n    !isMemberExpression(parent) &&\n    !isNewExpression(parent)\n  ) {\n    return;\n  }\n\n  this.print(node.typeArguments, node); // Flow\n  this.print(node.typeParameters, node); // TS\n\n  if (node.optional) {\n    // TODO: This can never happen\n    this.token(\"?.\");\n  }\n  this.token(\"(\");\n  const exit = this.enterForStatementInit(false);\n  this.printList(node.arguments, node);\n  exit();\n  this.rightParens(node);\n}\n\nexport function SequenceExpression(this: Printer, node: t.SequenceExpression) {\n  this.printList(node.expressions, node);\n}\n\nexport function ThisExpression(this: Printer) {\n  this.word(\"this\");\n}\n\nexport function Super(this: Printer) {\n  this.word(\"super\");\n}\n\nexport function _shouldPrintDecoratorsBeforeExport(\n  this: Printer,\n  node: t.ExportDeclaration & { declaration: t.ClassDeclaration },\n) {\n  if (typeof this.format.decoratorsBeforeExport === \"boolean\") {\n    return this.format.decoratorsBeforeExport;\n  }\n  return (\n    typeof node.start === \"number\" && node.start === node.declaration.start\n  );\n}\n\nexport function Decorator(this: Printer, node: t.Decorator) {\n  this.token(\"@\");\n  this.print(node.expression, node);\n  this.newline();\n}\n\nexport function OptionalMemberExpression(\n  this: Printer,\n  node: t.OptionalMemberExpression,\n) {\n  let { computed } = node;\n  const { optional, property } = node;\n\n  this.print(node.object, node);\n\n  if (!computed && isMemberExpression(property)) {\n    throw new TypeError(\"Got a MemberExpression for MemberExpression property\");\n  }\n\n  // @ts-expect-error todo(flow->ts) maybe instead of typeof check specific literal types?\n  if (isLiteral(property) && typeof property.value === \"number\") {\n    computed = true;\n  }\n  if (optional) {\n    this.token(\"?.\");\n  }\n\n  if (computed) {\n    this.token(\"[\");\n    this.print(property, node);\n    this.token(\"]\");\n  } else {\n    if (!optional) {\n      this.token(\".\");\n    }\n    this.print(property, node);\n  }\n}\n\nexport function OptionalCallExpression(\n  this: Printer,\n  node: t.OptionalCallExpression,\n) {\n  this.print(node.callee, node);\n\n  this.print(node.typeParameters, node); // TS\n\n  if (node.optional) {\n    this.token(\"?.\");\n  }\n\n  this.print(node.typeArguments, node); // Flow\n\n  this.token(\"(\");\n  const exit = this.enterForStatementInit(false);\n  this.printList(node.arguments, node);\n  exit();\n  this.rightParens(node);\n}\n\nexport function CallExpression(this: Printer, node: t.CallExpression) {\n  this.print(node.callee, node);\n\n  this.print(node.typeArguments, node); // Flow\n  this.print(node.typeParameters, node); // TS\n  this.token(\"(\");\n  const exit = this.enterForStatementInit(false);\n  this.printList(node.arguments, node);\n  exit();\n  this.rightParens(node);\n}\n\nexport function Import(this: Printer) {\n  this.word(\"import\");\n}\n\nexport function AwaitExpression(this: Printer, node: t.AwaitExpression) {\n  this.word(\"await\");\n\n  if (node.argument) {\n    this.space();\n    this.printTerminatorless(node.argument, node, false);\n  }\n}\n\nexport function YieldExpression(this: Printer, node: t.YieldExpression) {\n  this.word(\"yield\", true);\n\n  if (node.delegate) {\n    this.token(\"*\");\n    if (node.argument) {\n      this.space();\n      // line terminators are allowed after yield*\n      this.print(node.argument, node);\n    }\n  } else {\n    if (node.argument) {\n      this.space();\n      this.printTerminatorless(node.argument, node, false);\n    }\n  }\n}\n\nexport function EmptyStatement(this: Printer) {\n  this.semicolon(true /* force */);\n}\n\nexport function ExpressionStatement(\n  this: Printer,\n  node: t.ExpressionStatement,\n) {\n  this.tokenContext |= TokenContext.expressionStatement;\n  this.print(node.expression, node);\n  this.semicolon();\n}\n\nexport function AssignmentPattern(this: Printer, node: t.AssignmentPattern) {\n  this.print(node.left, node);\n  if (node.left.type === \"Identifier\") {\n    if (node.left.optional) this.token(\"?\");\n    this.print(node.left.typeAnnotation, node);\n  }\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(node.right, node);\n}\n\nexport function AssignmentExpression(\n  this: Printer,\n  node: t.AssignmentExpression,\n) {\n  this.print(node.left, node);\n\n  this.space();\n  if (node.operator === \"in\" || node.operator === \"instanceof\") {\n    this.word(node.operator);\n  } else {\n    this.token(node.operator);\n  }\n  this.space();\n\n  this.print(node.right, node);\n}\n\nexport function BindExpression(this: Printer, node: t.BindExpression) {\n  this.print(node.object, node);\n  this.token(\"::\");\n  this.print(node.callee, node);\n}\n\nexport {\n  AssignmentExpression as BinaryExpression,\n  AssignmentExpression as LogicalExpression,\n};\n\nexport function MemberExpression(this: Printer, node: t.MemberExpression) {\n  this.print(node.object, node);\n\n  if (!node.computed && isMemberExpression(node.property)) {\n    throw new TypeError(\"Got a MemberExpression for MemberExpression property\");\n  }\n\n  let computed = node.computed;\n  // @ts-expect-error todo(flow->ts) maybe use specific literal types\n  if (isLiteral(node.property) && typeof node.property.value === \"number\") {\n    computed = true;\n  }\n\n  if (computed) {\n    const exit = this.enterForStatementInit(false);\n    this.token(\"[\");\n    this.print(node.property, node);\n    this.token(\"]\");\n    exit();\n  } else {\n    this.token(\".\");\n    this.print(node.property, node);\n  }\n}\n\nexport function MetaProperty(this: Printer, node: t.MetaProperty) {\n  this.print(node.meta, node);\n  this.token(\".\");\n  this.print(node.property, node);\n}\n\nexport function PrivateName(this: Printer, node: t.PrivateName) {\n  this.token(\"#\");\n  this.print(node.id, node);\n}\n\nexport function V8IntrinsicIdentifier(\n  this: Printer,\n  node: t.V8IntrinsicIdentifier,\n) {\n  this.token(\"%\");\n  this.word(node.name);\n}\n\nexport function ModuleExpression(this: Printer, node: t.ModuleExpression) {\n  this.word(\"module\", true);\n  this.space();\n  this.token(\"{\");\n  this.indent();\n  const { body } = node;\n  if (body.body.length || body.directives.length) {\n    this.newline();\n  }\n  this.print(body, node);\n  this.dedent();\n  this.rightBrace(node);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAOA,IAAAC,MAAA,GAAAD,OAAA;AAAgD;EAN9CE,gBAAgB;EAChBC,SAAS;EACTC,kBAAkB;EAClBC;AAAe,IAAAN,EAAA;AAKV,SAASO,eAAeA,CAAgBC,IAAuB,EAAE;EACtE,MAAM;IAAEC;EAAS,CAAC,GAAGD,IAAI;EACzB,IACEC,QAAQ,KAAK,MAAM,IACnBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,QAAQ,IAErBA,QAAQ,KAAK,OAAO,EACpB;IACA,IAAI,CAACC,IAAI,CAACD,QAAQ,CAAC;IACnB,IAAI,CAACE,KAAK,CAAC,CAAC;EACd,CAAC,MAAM;IACL,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;EACtB;EAEA,IAAI,CAACI,KAAK,CAACL,IAAI,CAACM,QAAQ,EAAEN,IAAI,CAAC;AACjC;AAEO,SAASO,YAAYA,CAAgBP,IAAoB,EAAE;EAChE,IAAIA,IAAI,CAACQ,KAAK,EAAE;IACd,IAAI,CAACN,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACS,IAAI,EAAET,IAAI,CAAC;AAC7B;AAEO,SAASU,uBAAuBA,CAErCV,IAA+B,EAC/B;EACA,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACW,UAAU,EAAEX,IAAI,CAAC;EACjC,IAAI,CAACY,WAAW,CAACZ,IAAI,CAAC;AACxB;AAEO,SAASa,gBAAgBA,CAAgBb,IAAwB,EAAE;EACxE,IAAIA,IAAI,CAACc,MAAM,EAAE;IACf,IAAI,CAACV,KAAK,CAACJ,IAAI,CAACC,QAAQ,CAAC;IACzB,IAAI,CAACI,KAAK,CAACL,IAAI,CAACM,QAAQ,EAAEN,IAAI,CAAC;EACjC,CAAC,MAAM;IACL,IAAI,CAACe,mBAAmB,CAACf,IAAI,CAACM,QAAQ,EAAEN,IAAI,EAAE,IAAI,CAAC;IACnD,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACC,QAAQ,CAAC;EAC3B;AACF;AAEO,SAASe,qBAAqBA,CAEnChB,IAA6B,EAC7B;EACA,IAAI,CAACK,KAAK,CAACL,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAAC;EAC3B,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACkB,UAAU,EAAElB,IAAI,CAAC;EACjC,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACmB,SAAS,EAAEnB,IAAI,CAAC;AAClC;AAEO,SAASoB,aAAaA,CAE3BpB,IAAqB,EACrBqB,MAAc,EACd;EACA,IAAI,CAACnB,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACsB,MAAM,EAAEtB,IAAI,CAAC;EAC7B,IACE,IAAI,CAACuB,MAAM,CAACC,QAAQ,IACpBxB,IAAI,CAACyB,SAAS,CAACC,MAAM,KAAK,CAAC,IAC3B,CAAC1B,IAAI,CAAC2B,QAAQ,IACd,CAAChC,gBAAgB,CAAC0B,MAAM,EAAE;IAAEC,MAAM,EAAEtB;EAAK,CAAC,CAAC,IAC3C,CAACH,kBAAkB,CAACwB,MAAM,CAAC,IAC3B,CAACvB,eAAe,CAACuB,MAAM,CAAC,EACxB;IACA;EACF;EAEA,IAAI,CAAChB,KAAK,CAACL,IAAI,CAAC4B,aAAa,EAAE5B,IAAI,CAAC;EACpC,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC6B,cAAc,EAAE7B,IAAI,CAAC;EAErC,IAAIA,IAAI,CAAC2B,QAAQ,EAAE;IAEjB,IAAI,CAACvB,KAAK,CAAC,IAAI,CAAC;EAClB;EACA,IAAI,CAACA,SAAK,GAAI,CAAC;EACf,MAAM0B,IAAI,GAAG,IAAI,CAACC,qBAAqB,CAAC,KAAK,CAAC;EAC9C,IAAI,CAACC,SAAS,CAAChC,IAAI,CAACyB,SAAS,EAAEzB,IAAI,CAAC;EACpC8B,IAAI,CAAC,CAAC;EACN,IAAI,CAAClB,WAAW,CAACZ,IAAI,CAAC;AACxB;AAEO,SAASiC,kBAAkBA,CAAgBjC,IAA0B,EAAE;EAC5E,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAACkC,WAAW,EAAElC,IAAI,CAAC;AACxC;AAEO,SAASmC,cAAcA,CAAA,EAAgB;EAC5C,IAAI,CAACjC,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAASkC,KAAKA,CAAA,EAAgB;EACnC,IAAI,CAAClC,IAAI,CAAC,OAAO,CAAC;AACpB;AAEO,SAASmC,kCAAkCA,CAEhDrC,IAA+D,EAC/D;EACA,IAAI,OAAO,IAAI,CAACuB,MAAM,CAACe,sBAAsB,KAAK,SAAS,EAAE;IAC3D,OAAO,IAAI,CAACf,MAAM,CAACe,sBAAsB;EAC3C;EACA,OACE,OAAOtC,IAAI,CAACuC,KAAK,KAAK,QAAQ,IAAIvC,IAAI,CAACuC,KAAK,KAAKvC,IAAI,CAACwC,WAAW,CAACD,KAAK;AAE3E;AAEO,SAASE,SAASA,CAAgBzC,IAAiB,EAAE;EAC1D,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACW,UAAU,EAAEX,IAAI,CAAC;EACjC,IAAI,CAAC0C,OAAO,CAAC,CAAC;AAChB;AAEO,SAASC,wBAAwBA,CAEtC3C,IAAgC,EAChC;EACA,IAAI;IAAE4C;EAAS,CAAC,GAAG5C,IAAI;EACvB,MAAM;IAAE2B,QAAQ;IAAEkB;EAAS,CAAC,GAAG7C,IAAI;EAEnC,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC8C,MAAM,EAAE9C,IAAI,CAAC;EAE7B,IAAI,CAAC4C,QAAQ,IAAI/C,kBAAkB,CAACgD,QAAQ,CAAC,EAAE;IAC7C,MAAM,IAAIE,SAAS,CAAC,sDAAsD,CAAC;EAC7E;EAGA,IAAInD,SAAS,CAACiD,QAAQ,CAAC,IAAI,OAAOA,QAAQ,CAACG,KAAK,KAAK,QAAQ,EAAE;IAC7DJ,QAAQ,GAAG,IAAI;EACjB;EACA,IAAIjB,QAAQ,EAAE;IACZ,IAAI,CAACvB,KAAK,CAAC,IAAI,CAAC;EAClB;EAEA,IAAIwC,QAAQ,EAAE;IACZ,IAAI,CAACxC,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAACwC,QAAQ,EAAE7C,IAAI,CAAC;IAC1B,IAAI,CAACI,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACuB,QAAQ,EAAE;MACb,IAAI,CAACvB,SAAK,GAAI,CAAC;IACjB;IACA,IAAI,CAACC,KAAK,CAACwC,QAAQ,EAAE7C,IAAI,CAAC;EAC5B;AACF;AAEO,SAASiD,sBAAsBA,CAEpCjD,IAA8B,EAC9B;EACA,IAAI,CAACK,KAAK,CAACL,IAAI,CAACsB,MAAM,EAAEtB,IAAI,CAAC;EAE7B,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC6B,cAAc,EAAE7B,IAAI,CAAC;EAErC,IAAIA,IAAI,CAAC2B,QAAQ,EAAE;IACjB,IAAI,CAACvB,KAAK,CAAC,IAAI,CAAC;EAClB;EAEA,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC4B,aAAa,EAAE5B,IAAI,CAAC;EAEpC,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,MAAM0B,IAAI,GAAG,IAAI,CAACC,qBAAqB,CAAC,KAAK,CAAC;EAC9C,IAAI,CAACC,SAAS,CAAChC,IAAI,CAACyB,SAAS,EAAEzB,IAAI,CAAC;EACpC8B,IAAI,CAAC,CAAC;EACN,IAAI,CAAClB,WAAW,CAACZ,IAAI,CAAC;AACxB;AAEO,SAASkD,cAAcA,CAAgBlD,IAAsB,EAAE;EACpE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACsB,MAAM,EAAEtB,IAAI,CAAC;EAE7B,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC4B,aAAa,EAAE5B,IAAI,CAAC;EACpC,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC6B,cAAc,EAAE7B,IAAI,CAAC;EACrC,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,MAAM0B,IAAI,GAAG,IAAI,CAACC,qBAAqB,CAAC,KAAK,CAAC;EAC9C,IAAI,CAACC,SAAS,CAAChC,IAAI,CAACyB,SAAS,EAAEzB,IAAI,CAAC;EACpC8B,IAAI,CAAC,CAAC;EACN,IAAI,CAAClB,WAAW,CAACZ,IAAI,CAAC;AACxB;AAEO,SAASmD,MAAMA,CAAA,EAAgB;EACpC,IAAI,CAACjD,IAAI,CAAC,QAAQ,CAAC;AACrB;AAEO,SAASkD,eAAeA,CAAgBpD,IAAuB,EAAE;EACtE,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC;EAElB,IAAIF,IAAI,CAACM,QAAQ,EAAE;IACjB,IAAI,CAACH,KAAK,CAAC,CAAC;IACZ,IAAI,CAACY,mBAAmB,CAACf,IAAI,CAACM,QAAQ,EAAEN,IAAI,EAAE,KAAK,CAAC;EACtD;AACF;AAEO,SAASqD,eAAeA,CAAgBrD,IAAuB,EAAE;EACtE,IAAI,CAACE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;EAExB,IAAIF,IAAI,CAACsD,QAAQ,EAAE;IACjB,IAAI,CAAClD,SAAK,GAAI,CAAC;IACf,IAAIJ,IAAI,CAACM,QAAQ,EAAE;MACjB,IAAI,CAACH,KAAK,CAAC,CAAC;MAEZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACM,QAAQ,EAAEN,IAAI,CAAC;IACjC;EACF,CAAC,MAAM;IACL,IAAIA,IAAI,CAACM,QAAQ,EAAE;MACjB,IAAI,CAACH,KAAK,CAAC,CAAC;MACZ,IAAI,CAACY,mBAAmB,CAACf,IAAI,CAACM,QAAQ,EAAEN,IAAI,EAAE,KAAK,CAAC;IACtD;EACF;AACF;AAEO,SAASuD,cAAcA,CAAA,EAAgB;EAC5C,IAAI,CAACC,SAAS,CAAC,IAAgB,CAAC;AAClC;AAEO,SAASC,mBAAmBA,CAEjCzD,IAA2B,EAC3B;EACA,IAAI,CAAC0D,YAAY,IAAIC,mBAAY,CAACC,mBAAmB;EACrD,IAAI,CAACvD,KAAK,CAACL,IAAI,CAACW,UAAU,EAAEX,IAAI,CAAC;EACjC,IAAI,CAACwD,SAAS,CAAC,CAAC;AAClB;AAEO,SAASK,iBAAiBA,CAAgB7D,IAAyB,EAAE;EAC1E,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC8D,IAAI,EAAE9D,IAAI,CAAC;EAC3B,IAAIA,IAAI,CAAC8D,IAAI,CAACC,IAAI,KAAK,YAAY,EAAE;IACnC,IAAI/D,IAAI,CAAC8D,IAAI,CAACnC,QAAQ,EAAE,IAAI,CAACvB,SAAK,GAAI,CAAC;IACvC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC8D,IAAI,CAACE,cAAc,EAAEhE,IAAI,CAAC;EAC5C;EACA,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACiE,KAAK,EAAEjE,IAAI,CAAC;AAC9B;AAEO,SAASkE,oBAAoBA,CAElClE,IAA4B,EAC5B;EACA,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC8D,IAAI,EAAE9D,IAAI,CAAC;EAE3B,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAIH,IAAI,CAACC,QAAQ,KAAK,IAAI,IAAID,IAAI,CAACC,QAAQ,KAAK,YAAY,EAAE;IAC5D,IAAI,CAACC,IAAI,CAACF,IAAI,CAACC,QAAQ,CAAC;EAC1B,CAAC,MAAM;IACL,IAAI,CAACG,KAAK,CAACJ,IAAI,CAACC,QAAQ,CAAC;EAC3B;EACA,IAAI,CAACE,KAAK,CAAC,CAAC;EAEZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACiE,KAAK,EAAEjE,IAAI,CAAC;AAC9B;AAEO,SAASmE,cAAcA,CAAgBnE,IAAsB,EAAE;EACpE,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC8C,MAAM,EAAE9C,IAAI,CAAC;EAC7B,IAAI,CAACI,KAAK,CAAC,IAAI,CAAC;EAChB,IAAI,CAACC,KAAK,CAACL,IAAI,CAACsB,MAAM,EAAEtB,IAAI,CAAC;AAC/B;AAOO,SAASoE,gBAAgBA,CAAgBpE,IAAwB,EAAE;EACxE,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC8C,MAAM,EAAE9C,IAAI,CAAC;EAE7B,IAAI,CAACA,IAAI,CAAC4C,QAAQ,IAAI/C,kBAAkB,CAACG,IAAI,CAAC6C,QAAQ,CAAC,EAAE;IACvD,MAAM,IAAIE,SAAS,CAAC,sDAAsD,CAAC;EAC7E;EAEA,IAAIH,QAAQ,GAAG5C,IAAI,CAAC4C,QAAQ;EAE5B,IAAIhD,SAAS,CAACI,IAAI,CAAC6C,QAAQ,CAAC,IAAI,OAAO7C,IAAI,CAAC6C,QAAQ,CAACG,KAAK,KAAK,QAAQ,EAAE;IACvEJ,QAAQ,GAAG,IAAI;EACjB;EAEA,IAAIA,QAAQ,EAAE;IACZ,MAAMd,IAAI,GAAG,IAAI,CAACC,qBAAqB,CAAC,KAAK,CAAC;IAC9C,IAAI,CAAC3B,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC6C,QAAQ,EAAE7C,IAAI,CAAC;IAC/B,IAAI,CAACI,SAAK,GAAI,CAAC;IACf0B,IAAI,CAAC,CAAC;EACR,CAAC,MAAM;IACL,IAAI,CAAC1B,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC6C,QAAQ,EAAE7C,IAAI,CAAC;EACjC;AACF;AAEO,SAASqE,YAAYA,CAAgBrE,IAAoB,EAAE;EAChE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACsE,IAAI,EAAEtE,IAAI,CAAC;EAC3B,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC6C,QAAQ,EAAE7C,IAAI,CAAC;AACjC;AAEO,SAASuE,WAAWA,CAAgBvE,IAAmB,EAAE;EAC9D,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACwE,EAAE,EAAExE,IAAI,CAAC;AAC3B;AAEO,SAASyE,qBAAqBA,CAEnCzE,IAA6B,EAC7B;EACA,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACF,IAAI,CAACF,IAAI,CAAC0E,IAAI,CAAC;AACtB;AAEO,SAASC,gBAAgBA,CAAgB3E,IAAwB,EAAE;EACxE,IAAI,CAACE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;EACzB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,IAAI,CAAC;EACf,IAAI,CAACwE,MAAM,CAAC,CAAC;EACb,MAAM;IAAEnE;EAAK,CAAC,GAAGT,IAAI;EACrB,IAAIS,IAAI,CAACA,IAAI,CAACiB,MAAM,IAAIjB,IAAI,CAACoE,UAAU,CAACnD,MAAM,EAAE;IAC9C,IAAI,CAACgB,OAAO,CAAC,CAAC;EAChB;EACA,IAAI,CAACrC,KAAK,CAACI,IAAI,EAAET,IAAI,CAAC;EACtB,IAAI,CAAC8E,MAAM,CAAC,CAAC;EACb,IAAI,CAACC,UAAU,CAAC/E,IAAI,CAAC;AACvB", "ignoreList": []}