/* This file is automatically generated by scripts/generators/tsconfig.js */
{
  "extends": [
    "../../tsconfig.base.json",
    "../../tsconfig.paths.json"
  ],
  "include": [
    "../../packages/babel-preset-typescript/src/**/*.ts",
    "../../lib/globals.d.ts",
    "../../scripts/repo-utils/*.d.ts"
  ],
  "references": [
    {
      "path": "../../packages/babel-helper-plugin-utils"
    },
    {
      "path": "../../packages/babel-plugin-syntax-jsx"
    },
    {
      "path": "../../packages/babel-plugin-transform-modules-commonjs"
    },
    {
      "path": "../../packages/babel-plugin-transform-typescript"
    }
  ]
}