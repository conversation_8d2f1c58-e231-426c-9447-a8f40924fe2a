{"version": 3, "names": ["_core", "require", "_globalTypes", "transpileNamespace", "path", "allowNamespaces", "node", "declare", "id", "type", "remove", "get", "buildCodeFrameError", "name", "value", "handleNested", "t", "cloneNode", "program", "findParent", "p", "isProgram", "registerGlobalType", "scope", "hasOwnBinding", "replaceWith", "registerDeclaration", "replaceWithMultiple", "getDeclaration", "variableDeclaration", "variableDeclarator", "identifier", "getMemberExpression", "itemName", "memberExpression", "handleVariableDeclaration", "hub", "kind", "file", "declarations", "every", "declarator", "isIdentifier", "init", "assignmentExpression", "bindingIdentifiers", "getBindingIdentifiers", "assignments", "idName", "push", "expressionStatement", "sequenceExpression", "buildNestedAmbientModuleError", "buildError", "Error", "parentExport", "names", "Set", "realName", "assertIdentifier", "generateUid", "namespaceTopLevel", "isTSModuleBlock", "body", "exportNamedDeclaration", "isEmpty", "i", "length", "subNode", "transformed", "moduleName", "has", "add", "splice", "isTypeScript", "declaration", "nodes", "fallthroughValue", "objectExpression", "memberExpr", "template", "expression", "ast", "statement"], "sources": ["../src/namespace.ts"], "sourcesContent": ["import { template, types as t, type NodePath } from \"@babel/core\";\n\nimport { registerGlobalType } from \"./global-types.ts\";\n\nexport default function transpileNamespace(\n  path: NodePath<t.TSModuleDeclaration>,\n  allowNamespaces: boolean,\n) {\n  if (path.node.declare || path.node.id.type === \"StringLiteral\") {\n    path.remove();\n    return;\n  }\n\n  if (!allowNamespaces) {\n    throw path\n      .get(\"id\")\n      .buildCodeFrameError(\n        \"Namespace not marked type-only declare.\" +\n          \" Non-declarative namespaces are only supported experimentally in Babel.\" +\n          \" To enable and review caveats see:\" +\n          \" https://babeljs.io/docs/en/babel-plugin-transform-typescript\",\n      );\n  }\n\n  const name = path.node.id.name;\n  const value = handleNested(path, t.cloneNode(path.node, true));\n  if (value === null) {\n    // This means that `path` is a type-only namespace.\n    // We call `registerGlobalType` here to allow it to be stripped.\n    const program = path.findParent(p => p.isProgram());\n    registerGlobalType(program.scope, name);\n\n    path.remove();\n  } else if (path.scope.hasOwnBinding(name)) {\n    path.replaceWith(value);\n  } else {\n    path.scope.registerDeclaration(\n      path.replaceWithMultiple([getDeclaration(name), value])[0],\n    );\n  }\n}\n\nfunction getDeclaration(name: string) {\n  return t.variableDeclaration(\"let\", [\n    t.variableDeclarator(t.identifier(name)),\n  ]);\n}\n\nfunction getMemberExpression(name: string, itemName: string) {\n  return t.memberExpression(t.identifier(name), t.identifier(itemName));\n}\n\n/**\n * Convert export const foo = 1 to Namespace.foo = 1;\n *\n * @param {t.VariableDeclaration} node given variable declaration, e.g. `const foo = 1`\n * @param {string} name the generated unique namespace member name\n * @param {*} hub An instance implements HubInterface defined in `@babel/traverse` that can throw a code frame error\n */\nfunction handleVariableDeclaration(\n  node: t.VariableDeclaration,\n  name: string,\n  hub: any,\n): t.Statement[] {\n  if (node.kind !== \"const\") {\n    throw hub.file.buildCodeFrameError(\n      node,\n      \"Namespaces exporting non-const are not supported by Babel.\" +\n        \" Change to const or see:\" +\n        \" https://babeljs.io/docs/en/babel-plugin-transform-typescript\",\n    );\n  }\n  const { declarations } = node;\n  if (\n    declarations.every(\n      (declarator): declarator is t.VariableDeclarator & { id: t.Identifier } =>\n        t.isIdentifier(declarator.id),\n    )\n  ) {\n    // `export const a = 1` transforms to `const a = N.a = 1`, the output\n    // is smaller than `const a = 1; N.a = a`;\n    for (const declarator of declarations) {\n      declarator.init = t.assignmentExpression(\n        \"=\",\n        getMemberExpression(name, declarator.id.name),\n        declarator.init,\n      );\n    }\n    return [node];\n  }\n  // Now we have pattern in declarators\n  // `export const [a] = 1` transforms to `const [a] = 1; N.a = a`\n  const bindingIdentifiers = t.getBindingIdentifiers(node);\n  const assignments = [];\n  // getBindingIdentifiers returns an object without prototype.\n  // eslint-disable-next-line guard-for-in\n  for (const idName in bindingIdentifiers) {\n    assignments.push(\n      t.assignmentExpression(\n        \"=\",\n        getMemberExpression(name, idName),\n        t.cloneNode(bindingIdentifiers[idName]),\n      ),\n    );\n  }\n  return [node, t.expressionStatement(t.sequenceExpression(assignments))];\n}\n\nfunction buildNestedAmbientModuleError(path: NodePath, node: t.Node) {\n  return path.hub.buildError(\n    node,\n    \"Ambient modules cannot be nested in other modules or namespaces.\",\n    Error,\n  );\n}\n\nfunction handleNested(\n  path: NodePath,\n  node: t.TSModuleDeclaration,\n  parentExport?: t.Expression,\n): t.Statement | null {\n  const names = new Set();\n  const realName = node.id;\n  t.assertIdentifier(realName);\n\n  const name = path.scope.generateUid(realName.name);\n\n  const namespaceTopLevel: t.Statement[] = t.isTSModuleBlock(node.body)\n    ? node.body.body\n    : // We handle `namespace X.Y {}` as if it was\n      //   namespace X {\n      //     export namespace Y {}\n      //   }\n      [t.exportNamedDeclaration(node.body)];\n\n  let isEmpty = true;\n\n  for (let i = 0; i < namespaceTopLevel.length; i++) {\n    const subNode = namespaceTopLevel[i];\n\n    // The first switch is mainly to detect name usage. Only export\n    // declarations require further transformation.\n    switch (subNode.type) {\n      case \"TSModuleDeclaration\": {\n        if (!t.isIdentifier(subNode.id)) {\n          throw buildNestedAmbientModuleError(path, subNode);\n        }\n\n        const transformed = handleNested(path, subNode);\n        if (transformed !== null) {\n          isEmpty = false;\n          const moduleName = subNode.id.name;\n          if (names.has(moduleName)) {\n            namespaceTopLevel[i] = transformed;\n          } else {\n            names.add(moduleName);\n            namespaceTopLevel.splice(\n              i++,\n              1,\n              getDeclaration(moduleName),\n              transformed,\n            );\n          }\n        }\n        continue;\n      }\n      case \"TSEnumDeclaration\":\n      case \"FunctionDeclaration\":\n      case \"ClassDeclaration\":\n        isEmpty = false;\n        names.add(subNode.id.name);\n        continue;\n      case \"VariableDeclaration\": {\n        isEmpty = false;\n        // getBindingIdentifiers returns an object without prototype.\n        // eslint-disable-next-line guard-for-in\n        for (const name in t.getBindingIdentifiers(subNode)) {\n          names.add(name);\n        }\n        continue;\n      }\n      default:\n        isEmpty &&= t.isTypeScript(subNode);\n        // Neither named declaration nor export, continue to next item.\n        continue;\n      case \"ExportNamedDeclaration\":\n      // Export declarations get parsed using the next switch.\n    }\n\n    if (\"declare\" in subNode.declaration && subNode.declaration.declare) {\n      continue;\n    }\n\n    // Transform the export declarations that occur inside of a namespace.\n    switch (subNode.declaration.type) {\n      case \"TSEnumDeclaration\":\n      case \"FunctionDeclaration\":\n      case \"ClassDeclaration\": {\n        isEmpty = false;\n        const itemName = subNode.declaration.id.name;\n        names.add(itemName);\n        namespaceTopLevel.splice(\n          i++,\n          1,\n          subNode.declaration,\n          t.expressionStatement(\n            t.assignmentExpression(\n              \"=\",\n              getMemberExpression(name, itemName),\n              t.identifier(itemName),\n            ),\n          ),\n        );\n        break;\n      }\n      case \"VariableDeclaration\": {\n        isEmpty = false;\n        const nodes = handleVariableDeclaration(\n          subNode.declaration,\n          name,\n          path.hub,\n        );\n        namespaceTopLevel.splice(i, nodes.length, ...nodes);\n        i += nodes.length - 1;\n        break;\n      }\n      case \"TSModuleDeclaration\": {\n        if (!t.isIdentifier(subNode.declaration.id)) {\n          throw buildNestedAmbientModuleError(path, subNode.declaration);\n        }\n\n        const transformed = handleNested(\n          path,\n          subNode.declaration,\n          t.identifier(name),\n        );\n        if (transformed !== null) {\n          isEmpty = false;\n          const moduleName = subNode.declaration.id.name;\n          if (names.has(moduleName)) {\n            namespaceTopLevel[i] = transformed;\n          } else {\n            names.add(moduleName);\n            namespaceTopLevel.splice(\n              i++,\n              1,\n              getDeclaration(moduleName),\n              transformed,\n            );\n          }\n        } else {\n          namespaceTopLevel.splice(i, 1);\n          i--;\n        }\n      }\n    }\n  }\n\n  if (isEmpty) return null;\n\n  // {}\n  let fallthroughValue: t.Expression = t.objectExpression([]);\n\n  if (parentExport) {\n    const memberExpr = t.memberExpression(parentExport, realName);\n    fallthroughValue = template.expression.ast`\n      ${t.cloneNode(memberExpr)} ||\n        (${t.cloneNode(memberExpr)} = ${fallthroughValue})\n    `;\n  }\n\n  return template.statement.ast`\n    (function (${t.identifier(name)}) {\n      ${namespaceTopLevel}\n    })(${realName} || (${t.cloneNode(realName)} = ${fallthroughValue}));\n  `;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAD,OAAA;AAEe,SAASE,kBAAkBA,CACxCC,IAAqC,EACrCC,eAAwB,EACxB;EACA,IAAID,IAAI,CAACE,IAAI,CAACC,OAAO,IAAIH,IAAI,CAACE,IAAI,CAACE,EAAE,CAACC,IAAI,KAAK,eAAe,EAAE;IAC9DL,IAAI,CAACM,MAAM,CAAC,CAAC;IACb;EACF;EAEA,IAAI,CAACL,eAAe,EAAE;IACpB,MAAMD,IAAI,CACPO,GAAG,CAAC,IAAI,CAAC,CACTC,mBAAmB,CAClB,yCAAyC,GACvC,yEAAyE,GACzE,oCAAoC,GACpC,+DACJ,CAAC;EACL;EAEA,MAAMC,IAAI,GAAGT,IAAI,CAACE,IAAI,CAACE,EAAE,CAACK,IAAI;EAC9B,MAAMC,KAAK,GAAGC,YAAY,CAACX,IAAI,EAAEY,WAAC,CAACC,SAAS,CAACb,IAAI,CAACE,IAAI,EAAE,IAAI,CAAC,CAAC;EAC9D,IAAIQ,KAAK,KAAK,IAAI,EAAE;IAGlB,MAAMI,OAAO,GAAGd,IAAI,CAACe,UAAU,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;IACnD,IAAAC,+BAAkB,EAACJ,OAAO,CAACK,KAAK,EAAEV,IAAI,CAAC;IAEvCT,IAAI,CAACM,MAAM,CAAC,CAAC;EACf,CAAC,MAAM,IAAIN,IAAI,CAACmB,KAAK,CAACC,aAAa,CAACX,IAAI,CAAC,EAAE;IACzCT,IAAI,CAACqB,WAAW,CAACX,KAAK,CAAC;EACzB,CAAC,MAAM;IACLV,IAAI,CAACmB,KAAK,CAACG,mBAAmB,CAC5BtB,IAAI,CAACuB,mBAAmB,CAAC,CAACC,cAAc,CAACf,IAAI,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAC3D,CAAC;EACH;AACF;AAEA,SAASc,cAAcA,CAACf,IAAY,EAAE;EACpC,OAAOG,WAAC,CAACa,mBAAmB,CAAC,KAAK,EAAE,CAClCb,WAAC,CAACc,kBAAkB,CAACd,WAAC,CAACe,UAAU,CAAClB,IAAI,CAAC,CAAC,CACzC,CAAC;AACJ;AAEA,SAASmB,mBAAmBA,CAACnB,IAAY,EAAEoB,QAAgB,EAAE;EAC3D,OAAOjB,WAAC,CAACkB,gBAAgB,CAAClB,WAAC,CAACe,UAAU,CAAClB,IAAI,CAAC,EAAEG,WAAC,CAACe,UAAU,CAACE,QAAQ,CAAC,CAAC;AACvE;AASA,SAASE,yBAAyBA,CAChC7B,IAA2B,EAC3BO,IAAY,EACZuB,GAAQ,EACO;EACf,IAAI9B,IAAI,CAAC+B,IAAI,KAAK,OAAO,EAAE;IACzB,MAAMD,GAAG,CAACE,IAAI,CAAC1B,mBAAmB,CAChCN,IAAI,EACJ,4DAA4D,GAC1D,0BAA0B,GAC1B,+DACJ,CAAC;EACH;EACA,MAAM;IAAEiC;EAAa,CAAC,GAAGjC,IAAI;EAC7B,IACEiC,YAAY,CAACC,KAAK,CACfC,UAAU,IACTzB,WAAC,CAAC0B,YAAY,CAACD,UAAU,CAACjC,EAAE,CAChC,CAAC,EACD;IAGA,KAAK,MAAMiC,UAAU,IAAIF,YAAY,EAAE;MACrCE,UAAU,CAACE,IAAI,GAAG3B,WAAC,CAAC4B,oBAAoB,CACtC,GAAG,EACHZ,mBAAmB,CAACnB,IAAI,EAAE4B,UAAU,CAACjC,EAAE,CAACK,IAAI,CAAC,EAC7C4B,UAAU,CAACE,IACb,CAAC;IACH;IACA,OAAO,CAACrC,IAAI,CAAC;EACf;EAGA,MAAMuC,kBAAkB,GAAG7B,WAAC,CAAC8B,qBAAqB,CAACxC,IAAI,CAAC;EACxD,MAAMyC,WAAW,GAAG,EAAE;EAGtB,KAAK,MAAMC,MAAM,IAAIH,kBAAkB,EAAE;IACvCE,WAAW,CAACE,IAAI,CACdjC,WAAC,CAAC4B,oBAAoB,CACpB,GAAG,EACHZ,mBAAmB,CAACnB,IAAI,EAAEmC,MAAM,CAAC,EACjChC,WAAC,CAACC,SAAS,CAAC4B,kBAAkB,CAACG,MAAM,CAAC,CACxC,CACF,CAAC;EACH;EACA,OAAO,CAAC1C,IAAI,EAAEU,WAAC,CAACkC,mBAAmB,CAAClC,WAAC,CAACmC,kBAAkB,CAACJ,WAAW,CAAC,CAAC,CAAC;AACzE;AAEA,SAASK,6BAA6BA,CAAChD,IAAc,EAAEE,IAAY,EAAE;EACnE,OAAOF,IAAI,CAACgC,GAAG,CAACiB,UAAU,CACxB/C,IAAI,EACJ,kEAAkE,EAClEgD,KACF,CAAC;AACH;AAEA,SAASvC,YAAYA,CACnBX,IAAc,EACdE,IAA2B,EAC3BiD,YAA2B,EACP;EACpB,MAAMC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;EACvB,MAAMC,QAAQ,GAAGpD,IAAI,CAACE,EAAE;EACxBQ,WAAC,CAAC2C,gBAAgB,CAACD,QAAQ,CAAC;EAE5B,MAAM7C,IAAI,GAAGT,IAAI,CAACmB,KAAK,CAACqC,WAAW,CAACF,QAAQ,CAAC7C,IAAI,CAAC;EAElD,MAAMgD,iBAAgC,GAAG7C,WAAC,CAAC8C,eAAe,CAACxD,IAAI,CAACyD,IAAI,CAAC,GACjEzD,IAAI,CAACyD,IAAI,CAACA,IAAI,GAKd,CAAC/C,WAAC,CAACgD,sBAAsB,CAAC1D,IAAI,CAACyD,IAAI,CAAC,CAAC;EAEzC,IAAIE,OAAO,GAAG,IAAI;EAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,iBAAiB,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;IACjD,MAAME,OAAO,GAAGP,iBAAiB,CAACK,CAAC,CAAC;IAIpC,QAAQE,OAAO,CAAC3D,IAAI;MAClB,KAAK,qBAAqB;QAAE;UAC1B,IAAI,CAACO,WAAC,CAAC0B,YAAY,CAAC0B,OAAO,CAAC5D,EAAE,CAAC,EAAE;YAC/B,MAAM4C,6BAA6B,CAAChD,IAAI,EAAEgE,OAAO,CAAC;UACpD;UAEA,MAAMC,WAAW,GAAGtD,YAAY,CAACX,IAAI,EAAEgE,OAAO,CAAC;UAC/C,IAAIC,WAAW,KAAK,IAAI,EAAE;YACxBJ,OAAO,GAAG,KAAK;YACf,MAAMK,UAAU,GAAGF,OAAO,CAAC5D,EAAE,CAACK,IAAI;YAClC,IAAI2C,KAAK,CAACe,GAAG,CAACD,UAAU,CAAC,EAAE;cACzBT,iBAAiB,CAACK,CAAC,CAAC,GAAGG,WAAW;YACpC,CAAC,MAAM;cACLb,KAAK,CAACgB,GAAG,CAACF,UAAU,CAAC;cACrBT,iBAAiB,CAACY,MAAM,CACtBP,CAAC,EAAE,EACH,CAAC,EACDtC,cAAc,CAAC0C,UAAU,CAAC,EAC1BD,WACF,CAAC;YACH;UACF;UACA;QACF;MACA,KAAK,mBAAmB;MACxB,KAAK,qBAAqB;MAC1B,KAAK,kBAAkB;QACrBJ,OAAO,GAAG,KAAK;QACfT,KAAK,CAACgB,GAAG,CAACJ,OAAO,CAAC5D,EAAE,CAACK,IAAI,CAAC;QAC1B;MACF,KAAK,qBAAqB;QAAE;UAC1BoD,OAAO,GAAG,KAAK;UAGf,KAAK,MAAMpD,IAAI,IAAIG,WAAC,CAAC8B,qBAAqB,CAACsB,OAAO,CAAC,EAAE;YACnDZ,KAAK,CAACgB,GAAG,CAAC3D,IAAI,CAAC;UACjB;UACA;QACF;MACA;QACEoD,OAAO,KAAPA,OAAO,GAAKjD,WAAC,CAAC0D,YAAY,CAACN,OAAO,CAAC;QAEnC;MACF,KAAK,wBAAwB;IAE/B;IAEA,IAAI,SAAS,IAAIA,OAAO,CAACO,WAAW,IAAIP,OAAO,CAACO,WAAW,CAACpE,OAAO,EAAE;MACnE;IACF;IAGA,QAAQ6D,OAAO,CAACO,WAAW,CAAClE,IAAI;MAC9B,KAAK,mBAAmB;MACxB,KAAK,qBAAqB;MAC1B,KAAK,kBAAkB;QAAE;UACvBwD,OAAO,GAAG,KAAK;UACf,MAAMhC,QAAQ,GAAGmC,OAAO,CAACO,WAAW,CAACnE,EAAE,CAACK,IAAI;UAC5C2C,KAAK,CAACgB,GAAG,CAACvC,QAAQ,CAAC;UACnB4B,iBAAiB,CAACY,MAAM,CACtBP,CAAC,EAAE,EACH,CAAC,EACDE,OAAO,CAACO,WAAW,EACnB3D,WAAC,CAACkC,mBAAmB,CACnBlC,WAAC,CAAC4B,oBAAoB,CACpB,GAAG,EACHZ,mBAAmB,CAACnB,IAAI,EAAEoB,QAAQ,CAAC,EACnCjB,WAAC,CAACe,UAAU,CAACE,QAAQ,CACvB,CACF,CACF,CAAC;UACD;QACF;MACA,KAAK,qBAAqB;QAAE;UAC1BgC,OAAO,GAAG,KAAK;UACf,MAAMW,KAAK,GAAGzC,yBAAyB,CACrCiC,OAAO,CAACO,WAAW,EACnB9D,IAAI,EACJT,IAAI,CAACgC,GACP,CAAC;UACDyB,iBAAiB,CAACY,MAAM,CAACP,CAAC,EAAEU,KAAK,CAACT,MAAM,EAAE,GAAGS,KAAK,CAAC;UACnDV,CAAC,IAAIU,KAAK,CAACT,MAAM,GAAG,CAAC;UACrB;QACF;MACA,KAAK,qBAAqB;QAAE;UAC1B,IAAI,CAACnD,WAAC,CAAC0B,YAAY,CAAC0B,OAAO,CAACO,WAAW,CAACnE,EAAE,CAAC,EAAE;YAC3C,MAAM4C,6BAA6B,CAAChD,IAAI,EAAEgE,OAAO,CAACO,WAAW,CAAC;UAChE;UAEA,MAAMN,WAAW,GAAGtD,YAAY,CAC9BX,IAAI,EACJgE,OAAO,CAACO,WAAW,EACnB3D,WAAC,CAACe,UAAU,CAAClB,IAAI,CACnB,CAAC;UACD,IAAIwD,WAAW,KAAK,IAAI,EAAE;YACxBJ,OAAO,GAAG,KAAK;YACf,MAAMK,UAAU,GAAGF,OAAO,CAACO,WAAW,CAACnE,EAAE,CAACK,IAAI;YAC9C,IAAI2C,KAAK,CAACe,GAAG,CAACD,UAAU,CAAC,EAAE;cACzBT,iBAAiB,CAACK,CAAC,CAAC,GAAGG,WAAW;YACpC,CAAC,MAAM;cACLb,KAAK,CAACgB,GAAG,CAACF,UAAU,CAAC;cACrBT,iBAAiB,CAACY,MAAM,CACtBP,CAAC,EAAE,EACH,CAAC,EACDtC,cAAc,CAAC0C,UAAU,CAAC,EAC1BD,WACF,CAAC;YACH;UACF,CAAC,MAAM;YACLR,iBAAiB,CAACY,MAAM,CAACP,CAAC,EAAE,CAAC,CAAC;YAC9BA,CAAC,EAAE;UACL;QACF;IACF;EACF;EAEA,IAAID,OAAO,EAAE,OAAO,IAAI;EAGxB,IAAIY,gBAA8B,GAAG7D,WAAC,CAAC8D,gBAAgB,CAAC,EAAE,CAAC;EAE3D,IAAIvB,YAAY,EAAE;IAChB,MAAMwB,UAAU,GAAG/D,WAAC,CAACkB,gBAAgB,CAACqB,YAAY,EAAEG,QAAQ,CAAC;IAC7DmB,gBAAgB,GAAGG,cAAQ,CAACC,UAAU,CAACC,GAAG;AAC9C,QAAQlE,WAAC,CAACC,SAAS,CAAC8D,UAAU,CAAC;AAC/B,WAAW/D,WAAC,CAACC,SAAS,CAAC8D,UAAU,CAAC,MAAMF,gBAAgB;AACxD,KAAK;EACH;EAEA,OAAOG,cAAQ,CAACG,SAAS,CAACD,GAAG;AAC/B,iBAAiBlE,WAAC,CAACe,UAAU,CAAClB,IAAI,CAAC;AACnC,QAAQgD,iBAAiB;AACzB,SAASH,QAAQ,QAAQ1C,WAAC,CAACC,SAAS,CAACyC,QAAQ,CAAC,MAAMmB,gBAAgB;AACpE,GAAG;AACH", "ignoreList": []}