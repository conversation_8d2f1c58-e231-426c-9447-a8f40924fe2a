{"version": 3, "names": ["register", "module", "exports", "Object", "assign", "default", "revert", "__esModule"], "sources": ["../src/browser.js"], "sourcesContent": ["// TODO: Remove this file in Babel 8\n\n// required to safely use babel/register within a browserify codebase\n\nfunction register() {}\n\nmodule.exports = Object.assign(register, {\n  default: register,\n  register,\n  revert: function revert() {},\n  __esModule: true,\n});\n"], "mappings": "AAIA,SAASA,QAAQA,CAAA,EAAG,CAAC;AAErBC,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAACJ,QAAQ,EAAE;EACvCK,OAAO,EAAEL,QAAQ;EACjBA,QAAQ;EACRM,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG,CAAC,CAAC;EAC5BC,UAAU,EAAE;AACd,CAAC,CAAC", "ignoreList": []}