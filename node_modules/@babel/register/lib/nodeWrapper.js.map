{"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "globalModuleCache", "_cache", "internalModuleCache", "Object", "create", "node", "smsPath", "resolve", "register", "default", "module", "exports"], "sources": ["../src/nodeWrapper.js"], "sourcesContent": ["/**\n * This file wraps the implementation of register so all modules `require()`-ed\n * internally within register are stored in a separate module cache.\n * This prevents un-transformed modules being stored in global module cache,\n * and allows register to transform these modules if they are loaded externally.\n */\n\n// TODO: Remove this file in Babel 8\n\nconst Module = require(\"module\");\n\nconst globalModuleCache = Module._cache;\nconst internalModuleCache = Object.create(null);\n\nModule._cache = internalModuleCache;\nconst node = require(\"./node.js\");\n\n// NOTE: This Module._cache set is intercepted by the beforeEach hook in\n// packages/babel-register/test/index.js to install dependencies mocks.\nModule._cache = globalModuleCache;\n\n// Add source-map-support to global cache as it's stateful\nconst smsPath = require.resolve(\"source-map-support\");\nglobalModuleCache[smsPath] = internalModuleCache[smsPath];\n\nconst register = node.default;\nregister();\n\nmodule.exports = node;\n"], "mappings": "AASA,MAAMA,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAEhC,MAAMC,iBAAiB,GAAGF,MAAM,CAACG,MAAM;AACvC,MAAMC,mBAAmB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AAE/CN,MAAM,CAACG,MAAM,GAAGC,mBAAmB;AACnC,MAAMG,IAAI,GAAGN,OAAO,CAAC,WAAW,CAAC;AAIjCD,MAAM,CAACG,MAAM,GAAGD,iBAAiB;AAGjC,MAAMM,OAAO,GAAGP,OAAO,CAACQ,OAAO,CAAC,oBAAoB,CAAC;AACrDP,iBAAiB,CAACM,OAAO,CAAC,GAAGJ,mBAAmB,CAACI,OAAO,CAAC;AAEzD,MAAME,QAAQ,GAAGH,IAAI,CAACI,OAAO;AAC7BD,QAAQ,CAAC,CAAC;AAEVE,MAAM,CAACC,OAAO,GAAGN,IAAI", "ignoreList": []}