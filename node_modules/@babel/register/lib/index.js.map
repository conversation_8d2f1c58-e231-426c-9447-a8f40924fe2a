{"version": 3, "names": ["exports", "module", "args", "register", "__esModule", "node", "require", "default", "Object", "assign"], "sources": ["../src/index.js"], "sourcesContent": ["/**\n * This file wraps the compiled ES6 module implementation of register so\n * that it can be used both from a standard CommonJS environment, and also\n * from a compiled Babel import.\n */\n\nif (USE_ESM) {\n  module.exports = require(\"./experimental-worker.js\");\n} else if (process.env.BABEL_8_BREAKING) {\n  module.exports = require(\"./experimental-worker.js\");\n} else {\n  exports = module.exports = function (...args) {\n    return register(...args);\n  };\n  exports.__esModule = true;\n\n  const node = require(\"./nodeWrapper.js\");\n  const register = node.default;\n\n  Object.assign(exports, node);\n}\n"], "mappings": "AAUO;EACLA,OAAO,GAAGC,MAAM,CAACD,OAAO,GAAG,UAAU,GAAGE,IAAI,EAAE;IAC5C,OAAOC,QAAQ,CAAC,GAAGD,IAAI,CAAC;EAC1B,CAAC;EACDF,OAAO,CAACI,UAAU,GAAG,IAAI;EAEzB,MAAMC,IAAI,GAAGC,OAAO,CAAC,kBAAkB,CAAC;EACxC,MAAMH,QAAQ,GAAGE,IAAI,CAACE,OAAO;EAE7BC,MAAM,CAACC,MAAM,CAACT,OAAO,EAAEK,IAAI,CAAC;AAC9B", "ignoreList": []}