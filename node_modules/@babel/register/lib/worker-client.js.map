{"version": 3, "names": ["path", "require", "ACTIONS", "GET_DEFAULT_EXTENSIONS", "SET_OPTIONS", "TRANSFORM", "TRANSFORM_SYNC", "_send", "WeakMap", "_eCache", "Client", "constructor", "send", "_classPrivateFieldInitSpec", "_classPrivateFieldSet", "getDefaultExtensions", "_classPrivateFieldGet2", "_classPrivateFieldGet", "call", "undefined", "setOptions", "options", "transform", "code", "filename", "exports", "WorkerClient", "_worker", "_signal", "_WorkerClient", "action", "payload", "subChannel", "_classPrivateGetter", "_get_worker_threads", "MessageChannel", "postMessage", "signal", "port", "port1", "Atomics", "wait", "message", "receiveMessageOnPort", "port2", "error", "Object", "assign", "errorData", "result", "Worker", "resolve", "__dirname", "env", "_get_markInRegisterWorker", "process", "Int32Array", "SharedArrayBuffer", "unref", "_this", "_this2", "markInRegisterWorker", "_LocalClient", "_handleMessage", "LocalClient", "_assertClassBrand$_", "_assert<PERSON>lassBrand", "_", "isLocalClient"], "sources": ["../src/worker-client.js"], "sourcesContent": ["const path = require(\"path\");\n\nconst ACTIONS = {\n  GET_DEFAULT_EXTENSIONS: \"GET_DEFAULT_EXTENSIONS\",\n  SET_OPTIONS: \"SET_OPTIONS\",\n  TRANSFORM: \"TRANSFORM\",\n  TRANSFORM_SYNC: \"TRANSFORM_SYNC\",\n};\n\nclass Client {\n  #send;\n\n  constructor(send) {\n    this.#send = send;\n  }\n\n  #eCache;\n  /** @return {string[]} */\n  getDefaultExtensions() {\n    return (this.#eCache ??= this.#send(\n      ACTIONS.GET_DEFAULT_EXTENSIONS,\n      undefined,\n    ));\n  }\n\n  /**\n   * @param {object} options\n   * @return {void}\n   */\n  setOptions(options) {\n    return this.#send(ACTIONS.SET_OPTIONS, options);\n  }\n\n  /**\n   * @param {string} code\n   * @param {string} filename\n   * @return {{ code: string, map: object } | null}\n   */\n  transform(code, filename) {\n    return this.#send(ACTIONS.TRANSFORM, { code, filename });\n  }\n}\n\n// We need to run Babel in a worker because require hooks must\n// run synchronously, but many steps of Babel's config loading\n// (which is done for each file) can be asynchronous\nexports.WorkerClient = class WorkerClient extends Client {\n  // These two require() calls are in deferred so that they are not imported in\n  // older Node.js versions (which don't support workers).\n  // TODO: Hoist them in Babel 8.\n\n  /** @type {typeof import(\"worker_threads\")} */\n  static get #worker_threads() {\n    return require(\"worker_threads\");\n  }\n\n  static get #markInRegisterWorker() {\n    return require(\"./is-in-register-worker.js\").markInRegisterWorker;\n  }\n\n  #worker = new WorkerClient.#worker_threads.Worker(\n    path.resolve(__dirname, \"./worker/index.js\"),\n    { env: WorkerClient.#markInRegisterWorker(process.env) },\n  );\n\n  #signal = new Int32Array(new SharedArrayBuffer(4));\n\n  constructor() {\n    super((action, payload) => {\n      this.#signal[0] = 0;\n      const subChannel = new WorkerClient.#worker_threads.MessageChannel();\n\n      this.#worker.postMessage(\n        { signal: this.#signal, port: subChannel.port1, action, payload },\n        [subChannel.port1],\n      );\n\n      Atomics.wait(this.#signal, 0, 0);\n      const { message } = WorkerClient.#worker_threads.receiveMessageOnPort(\n        subChannel.port2,\n      );\n\n      if (message.error) throw Object.assign(message.error, message.errorData);\n      else return message.result;\n    });\n\n    // The worker will never exit by itself. Prevent it from keeping\n    // the main process alive.\n    this.#worker.unref();\n  }\n};\n\nif (!process.env.BABEL_8_BREAKING) {\n  exports.LocalClient = class LocalClient extends Client {\n    isLocalClient = true;\n\n    static #handleMessage;\n\n    constructor() {\n      LocalClient.#handleMessage ??= require(\"./worker/handle-message.js\");\n\n      super((action, payload) => {\n        return LocalClient.#handleMessage(\n          action === ACTIONS.TRANSFORM ? ACTIONS.TRANSFORM_SYNC : action,\n          payload,\n        );\n      });\n    }\n  };\n}\n"], "mappings": ";;;;;;;AAAA,MAAMA,IAAI,GAAGC,OAAO,CAAC,MAAM,CAAC;AAE5B,MAAMC,OAAO,GAAG;EACdC,sBAAsB,EAAE,wBAAwB;EAChDC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE,WAAW;EACtBC,cAAc,EAAE;AAClB,CAAC;AAAC,IAAAC,KAAA,OAAAC,OAAA;AAAA,IAAAC,OAAA,OAAAD,OAAA;AAEF,MAAME,MAAM,CAAC;EAGXC,WAAWA,CAACC,IAAI,EAAE;IAAAC,0BAAA,OAAAN,KAAA;IAAAM,0BAAA,OAAAJ,OAAA;IAChBK,qBAAA,CAAAP,KAAA,MAAI,EAASK,IAAI;EACnB;EAIAG,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACrB,QAAAA,sBAAA,GAAAC,qBAAA,CAAAR,OAAA,EAAQ,IAAI,aAAAO,sBAAA,GAAAF,qBAAA,CAAAL,OAAA,EAAJ,IAAI,EAAAQ,qBAAA,CAAAV,KAAA,EAAa,IAAI,EAAAW,IAAA,CAAJ,IAAI,EAC3BhB,OAAO,CAACC,sBAAsB,EAC9BgB,SAAS;EAEb;EAMAC,UAAUA,CAACC,OAAO,EAAE;IAClB,OAAAJ,qBAAA,CAAAV,KAAA,EAAO,IAAI,EAAAW,IAAA,CAAJ,IAAI,EAAOhB,OAAO,CAACE,WAAW,EAAEiB,OAAO;EAChD;EAOAC,SAASA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IACxB,OAAAP,qBAAA,CAAAV,KAAA,EAAO,IAAI,EAAAW,IAAA,CAAJ,IAAI,EAAOhB,OAAO,CAACG,SAAS,EAAE;MAAEkB,IAAI;MAAEC;IAAS,CAAC;EACzD;AACF;AAKAC,OAAO,CAACC,YAAY,IAAAC,OAAA,OAAAnB,OAAA,IAAAoB,OAAA,OAAApB,OAAA,IAAAqB,aAAA,GAAG,MAAMH,YAAY,SAAShB,MAAM,CAAC;EAqBvDC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAACmB,MAAM,EAAEC,OAAO,KAAK;MACzBd,qBAAA,CAAAW,OAAA,MAAI,EAAS,CAAC,CAAC,GAAG,CAAC;MACnB,MAAMI,UAAU,GAAG,KAAIC,mBAAA,CAAAJ,aAAA,EAAAH,YAAY,EAAAQ,mBAAA,EAAiBC,cAAc,EAAC,CAAC;MAEpElB,qBAAA,CAAAU,OAAA,MAAI,EAASS,WAAW,CACtB;QAAEC,MAAM,EAAApB,qBAAA,CAAAW,OAAA,EAAE,IAAI,CAAQ;QAAEU,IAAI,EAAEN,UAAU,CAACO,KAAK;QAAET,MAAM;QAAEC;MAAQ,CAAC,EACjE,CAACC,UAAU,CAACO,KAAK,CACnB,CAAC;MAEDC,OAAO,CAACC,IAAI,CAAAxB,qBAAA,CAAAW,OAAA,EAAC,IAAI,GAAU,CAAC,EAAE,CAAC,CAAC;MAChC,MAAM;QAAEc;MAAQ,CAAC,GAAGT,mBAAA,CAAAJ,aAAA,EAAAH,YAAY,EAAAQ,mBAAA,EAAiBS,oBAAoB,CACnEX,UAAU,CAACY,KACb,CAAC;MAED,IAAIF,OAAO,CAACG,KAAK,EAAE,MAAMC,MAAM,CAACC,MAAM,CAACL,OAAO,CAACG,KAAK,EAAEH,OAAO,CAACM,SAAS,CAAC,CAAC,KACpE,OAAON,OAAO,CAACO,MAAM;IAC5B,CAAC,CAAC;IAACpC,0BAAA,OAAAc,OAAA,EAxBK,KAAIM,mBAAA,CAAAJ,aAAA,EAAAH,YAAY,EAAAQ,mBAAA,EAAiBgB,MAAM,EAC/ClD,IAAI,CAACmD,OAAO,CAACC,SAAS,EAAE,mBAAmB,CAAC,EAC5C;MAAEC,GAAG,EAAApB,mBAAA,CAAAJ,aAAA,EAAEH,YAAY,EAAA4B,yBAAA,EAAApC,IAAA,CAAZQ,YAAY,EAAuB6B,OAAO,CAACF,GAAG;IAAE,CACzD,CAAC;IAAAxC,0BAAA,OAAAe,OAAA,EAES,IAAI4B,UAAU,CAAC,IAAIC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAuBhDxC,qBAAA,CAAAU,OAAA,MAAI,EAAS+B,KAAK,CAAC,CAAC;EACtB;AACF,CAAC;AAAC,SAAAxB,oBAAAyB,KAAA,EAtC6B;EAC3B,OAAO1D,OAAO,CAAC,gBAAgB,CAAC;AAClC;AAAC,SAAAqD,0BAAAM,MAAA,EAEkC;EACjC,OAAO3D,OAAO,CAAC,4BAA4B,CAAC,CAAC4D,oBAAoB;AACnE;AAkCiC;EAAA,IAAAC,YAAA,EAAAC,cAAA;EACjCtC,OAAO,CAACuC,WAAW,IAAAF,YAAA,GAAG,MAAME,WAAW,SAAStD,MAAM,CAAC;IAKrDC,WAAWA,CAAA,EAAG;MAAA,IAAAsD,mBAAA;MACZ,CAAAA,mBAAA,GAAAC,iBAAA,CAAAJ,YAAA,EAAAE,WAAW,EAAAD,cAAA,EAAAI,CAAA,YAAAF,mBAAA,GAAAF,cAAA,CAAAI,CAAA,GAAAD,iBAAA,CAAAJ,YAAA,EAAXE,WAAW,EAAoB/D,OAAO,CAAC,4BAA4B,CAAC;MAEpE,KAAK,CAAC,CAAC6B,MAAM,EAAEC,OAAO,KAAK;QACzB,OAAAmC,iBAAA,CAAAJ,YAAA,EAAOE,WAAW,EAAAD,cAAA,EAAAI,CAAA,CAAAjD,IAAA,CAAX8C,WAAW,EAChBlC,MAAM,KAAK5B,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,cAAc,GAAGwB,MAAM,EAC9DC,OAAO;MAEX,CAAC,CAAC;MAAC,KAZLqC,aAAa,GAAG,IAAI;IAapB;EACF,CAAC,EAAAL,cAAA;IAAAI,CAAA;EAAA,GAAAL,YAAA;AACH", "ignoreList": []}