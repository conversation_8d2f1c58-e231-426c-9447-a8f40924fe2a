{"version": 3, "names": ["envVarName", "envVarValue", "exports", "markInRegisterWorker", "env", "Object", "assign", "isInRegisterWorker", "process"], "sources": ["../src/is-in-register-worker.js"], "sourcesContent": ["\"use strict\";\n\n/**\n * Since workers inherit the exec options from the parent thread, we\n * must be careful to avoid infinite \"@babel/register\" setup loops.\n *\n * If @babel/register is imported using the -r/--require flag, the worker\n * will have the same flag and we must avoid registering the @babel/register\n * hook again.\n *\n * - markInRegisterWorker() can be used to mark a set of env vars (that will\n *   be forwarded to a worker) as being in the @babel/register worker.\n * - isInRegisterWorker will be true in @babel/register workers.\n */\n\nconst envVarName = \"___INTERNAL___IS_INSIDE_BABEL_REGISTER_WORKER___\";\nconst envVarValue = \"yes_I_am\";\n\nexports.markInRegisterWorker = env => ({ ...env, [envVarName]: envVarValue });\nexports.isInRegisterWorker = process.env[envVarName] === envVarValue;\n"], "mappings": "AAAA,YAAY;AAeZ,MAAMA,UAAU,GAAG,kDAAkD;AACrE,MAAMC,WAAW,GAAG,UAAU;AAE9BC,OAAO,CAACC,oBAAoB,GAAGC,GAAG,IAAAC,MAAA,CAAAC,MAAA,KAAUF,GAAG;EAAE,CAACJ,UAAU,GAAGC;AAAW,EAAG;AAC7EC,OAAO,CAACK,kBAAkB,GAAGC,OAAO,CAACJ,GAAG,CAACJ,UAAU,CAAC,KAAKC,WAAW", "ignoreList": []}