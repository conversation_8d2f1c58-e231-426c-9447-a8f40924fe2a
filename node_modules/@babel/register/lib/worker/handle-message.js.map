{"version": 3, "names": ["babel", "require", "setOptions", "transform", "transformSync", "module", "exports", "handleMessage", "action", "payload", "DEFAULT_EXTENSIONS", "code", "filename", "Error"], "sources": ["../../src/worker/handle-message.js"], "sourcesContent": ["const babel = require(\"./babel-core.js\");\nconst { setOptions, transform, transformSync } = require(\"./transform.js\");\n\nmodule.exports = function handleMessage(action, payload) {\n  switch (action) {\n    case \"GET_DEFAULT_EXTENSIONS\":\n      return babel.DEFAULT_EXTENSIONS;\n    case \"SET_OPTIONS\":\n      setOptions(payload);\n      return;\n    case \"TRANSFORM\":\n      return transform(payload.code, payload.filename);\n    case \"TRANSFORM_SYNC\":\n      if (!process.env.BABEL_8_BREAKING) {\n        return transformSync(payload.code, payload.filename);\n      }\n  }\n\n  throw new Error(`Unknown internal parser worker action: ${action}`);\n};\n"], "mappings": "AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AACxC,MAAM;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAc,CAAC,GAAGH,OAAO,CAAC,gBAAgB,CAAC;AAE1EI,MAAM,CAACC,OAAO,GAAG,SAASC,aAAaA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACvD,QAAQD,MAAM;IACZ,KAAK,wBAAwB;MAC3B,OAAOR,KAAK,CAACU,kBAAkB;IACjC,KAAK,aAAa;MAChBR,UAAU,CAACO,OAAO,CAAC;MACnB;IACF,KAAK,WAAW;MACd,OAAON,SAAS,CAACM,OAAO,CAACE,IAAI,EAAEF,OAAO,CAACG,QAAQ,CAAC;IAClD,KAAK,gBAAgB;MACgB;QACjC,OAAOR,aAAa,CAACK,OAAO,CAACE,IAAI,EAAEF,OAAO,CAACG,QAAQ,CAAC;MACtD;EACJ;EAEA,MAAM,IAAIC,KAAK,CAAE,0CAAyCL,MAAO,EAAC,CAAC;AACrE,CAAC", "ignoreList": []}