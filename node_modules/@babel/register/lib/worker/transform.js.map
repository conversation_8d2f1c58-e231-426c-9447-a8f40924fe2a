{"version": 3, "names": ["asyncGeneratorStep", "gen", "resolve", "reject", "_next", "_throw", "key", "arg", "info", "value", "error", "done", "Promise", "then", "_asyncToGenerator", "fn", "self", "args", "arguments", "apply", "err", "undefined", "cloneDeep", "require", "path", "fs", "babel", "registerCache", "nmRE", "escapeRegExp", "sep", "string", "replace", "cache", "transformOpts", "exports", "setOptions", "opts", "clear", "load", "get", "extensions", "Object", "assign", "caller", "name", "cwd", "ignore", "only", "cwdRE", "RegExp", "transform", "input", "filename", "loadOptionsAsync", "sourceRoot", "dirname", "cached", "store", "cacheLookup", "code", "map", "transformAsync", "sourceMaps", "ast", "transformSync", "OptionManager", "init", "id", "cache<PERSON>ey", "JSON", "stringify", "version", "env", "getEnv", "fileMtime", "statSync", "mtime", "set<PERSON>irty"], "sources": ["../../src/worker/transform.js"], "sourcesContent": ["\"use strict\";\n\nconst cloneDeep = require(\"clone-deep\");\nconst path = require(\"path\");\nconst fs = require(\"fs\");\n\nconst babel = require(\"./babel-core.js\");\nconst registerCache = require(\"../cache.js\");\n\nconst nmRE = escapeRegExp(path.sep + \"node_modules\" + path.sep);\n\nfunction escapeRegExp(string) {\n  return string.replace(/[|\\\\{}()[\\]^$+*?.]/g, \"\\\\$&\");\n}\n\nlet cache;\nlet transformOpts;\nexports.setOptions = function (opts) {\n  if (opts.cache === false && cache) {\n    registerCache.clear();\n    cache = null;\n  } else if (opts.cache !== false && !cache) {\n    registerCache.load();\n    cache = registerCache.get();\n  }\n\n  delete opts.cache;\n  delete opts.extensions;\n\n  transformOpts = {\n    ...opts,\n    caller: {\n      name: \"@babel/register\",\n      ...(opts.caller || {}),\n    },\n  };\n\n  let { cwd = \".\" } = transformOpts;\n\n  // Ensure that the working directory is resolved up front so that\n  // things don't break if it changes later.\n  cwd = transformOpts.cwd = path.resolve(cwd);\n\n  if (transformOpts.ignore === undefined && transformOpts.only === undefined) {\n    const cwdRE = escapeRegExp(cwd);\n\n    // Only compile things inside the current working directory.\n    transformOpts.only = [new RegExp(\"^\" + cwdRE, \"i\")];\n    // Ignore any node_modules inside the current working directory.\n    transformOpts.ignore = [\n      new RegExp(`^${cwdRE}(?:${path.sep}.*)?${nmRE}`, \"i\"),\n    ];\n  }\n};\n\nexports.transform = async function (input, filename) {\n  const opts = await babel.loadOptionsAsync({\n    // sourceRoot can be overwritten\n    sourceRoot: path.dirname(filename) + path.sep,\n    ...cloneDeep(transformOpts),\n    filename,\n  });\n\n  // Bail out ASAP if the file has been ignored.\n  if (opts === null) return null;\n\n  const { cached, store } = cacheLookup(opts, filename);\n  if (cached) return cached;\n\n  const { code, map } = await babel.transformAsync(input, {\n    ...opts,\n    sourceMaps: opts.sourceMaps === undefined ? \"both\" : opts.sourceMaps,\n    ast: false,\n  });\n\n  return store({ code, map });\n};\n\nif (!process.env.BABEL_8_BREAKING) {\n  exports.transformSync = function (input, filename) {\n    const opts = new babel.OptionManager().init({\n      // sourceRoot can be overwritten\n      sourceRoot: path.dirname(filename) + path.sep,\n      ...cloneDeep(transformOpts),\n      filename,\n    });\n\n    // Bail out ASAP if the file has been ignored.\n    if (opts === null) return null;\n\n    const { cached, store } = cacheLookup(opts, filename);\n    if (cached) return cached;\n\n    const { code, map } = babel.transformSync(input, {\n      ...opts,\n      sourceMaps: opts.sourceMaps === undefined ? \"both\" : opts.sourceMaps,\n      ast: false,\n    });\n\n    return store({ code, map });\n  };\n}\n\nconst id = value => value;\n\nfunction cacheLookup(opts, filename) {\n  if (!cache) return { cached: null, store: id };\n\n  let cacheKey = `${JSON.stringify(opts)}:${babel.version}`;\n\n  const env = babel.getEnv();\n  if (env) cacheKey += `:${env}`;\n\n  const cached = cache[cacheKey];\n  const fileMtime = +fs.statSync(filename).mtime;\n\n  if (cached && cached.mtime === fileMtime) {\n    return { cached: cached.value, store: id };\n  }\n\n  return {\n    cached: null,\n    store(value) {\n      cache[cacheKey] = { value, mtime: fileMtime };\n      registerCache.setDirty();\n      return value;\n    },\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,mBAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,GAAA,cAAAC,IAAA,GAAAP,GAAA,CAAAK,GAAA,EAAAC,GAAA,OAAAE,KAAA,GAAAD,IAAA,CAAAC,KAAA,WAAAC,KAAA,IAAAP,MAAA,CAAAO,KAAA,iBAAAF,IAAA,CAAAG,IAAA,IAAAT,OAAA,CAAAO,KAAA,YAAAG,OAAA,CAAAV,OAAA,CAAAO,KAAA,EAAAI,IAAA,CAAAT,KAAA,EAAAC,MAAA;AAAA,SAAAS,kBAAAC,EAAA,6BAAAC,IAAA,SAAAC,IAAA,GAAAC,SAAA,aAAAN,OAAA,WAAAV,OAAA,EAAAC,MAAA,QAAAF,GAAA,GAAAc,EAAA,CAAAI,KAAA,CAAAH,IAAA,EAAAC,IAAA,YAAAb,MAAAK,KAAA,IAAAT,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,UAAAI,KAAA,cAAAJ,OAAAe,GAAA,IAAApB,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,WAAAe,GAAA,KAAAhB,KAAA,CAAAiB,SAAA;AAEb,MAAMC,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC;AACvC,MAAMC,IAAI,GAAGD,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAME,EAAE,GAAGF,OAAO,CAAC,IAAI,CAAC;AAExB,MAAMG,KAAK,GAAGH,OAAO,CAAC,iBAAiB,CAAC;AACxC,MAAMI,aAAa,GAAGJ,OAAO,CAAC,aAAa,CAAC;AAE5C,MAAMK,IAAI,GAAGC,YAAY,CAACL,IAAI,CAACM,GAAG,GAAG,cAAc,GAAGN,IAAI,CAACM,GAAG,CAAC;AAE/D,SAASD,YAAYA,CAACE,MAAM,EAAE;EAC5B,OAAOA,MAAM,CAACC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;AACtD;AAEA,IAAIC,KAAK;AACT,IAAIC,aAAa;AACjBC,OAAO,CAACC,UAAU,GAAG,UAAUC,IAAI,EAAE;EACnC,IAAIA,IAAI,CAACJ,KAAK,KAAK,KAAK,IAAIA,KAAK,EAAE;IACjCN,aAAa,CAACW,KAAK,CAAC,CAAC;IACrBL,KAAK,GAAG,IAAI;EACd,CAAC,MAAM,IAAII,IAAI,CAACJ,KAAK,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;IACzCN,aAAa,CAACY,IAAI,CAAC,CAAC;IACpBN,KAAK,GAAGN,aAAa,CAACa,GAAG,CAAC,CAAC;EAC7B;EAEA,OAAOH,IAAI,CAACJ,KAAK;EACjB,OAAOI,IAAI,CAACI,UAAU;EAEtBP,aAAa,GAAAQ,MAAA,CAAAC,MAAA,KACRN,IAAI;IACPO,MAAM,EAAAF,MAAA,CAAAC,MAAA;MACJE,IAAI,EAAE;IAAiB,GACnBR,IAAI,CAACO,MAAM,IAAI,CAAC,CAAC;EACtB,EACF;EAED,IAAI;IAAEE,GAAG,GAAG;EAAI,CAAC,GAAGZ,aAAa;EAIjCY,GAAG,GAAGZ,aAAa,CAACY,GAAG,GAAGtB,IAAI,CAACtB,OAAO,CAAC4C,GAAG,CAAC;EAE3C,IAAIZ,aAAa,CAACa,MAAM,KAAK1B,SAAS,IAAIa,aAAa,CAACc,IAAI,KAAK3B,SAAS,EAAE;IAC1E,MAAM4B,KAAK,GAAGpB,YAAY,CAACiB,GAAG,CAAC;IAG/BZ,aAAa,CAACc,IAAI,GAAG,CAAC,IAAIE,MAAM,CAAC,GAAG,GAAGD,KAAK,EAAE,GAAG,CAAC,CAAC;IAEnDf,aAAa,CAACa,MAAM,GAAG,CACrB,IAAIG,MAAM,CAAE,IAAGD,KAAM,MAAKzB,IAAI,CAACM,GAAI,OAAMF,IAAK,EAAC,EAAE,GAAG,CAAC,CACtD;EACH;AACF,CAAC;AAEDO,OAAO,CAACgB,SAAS,GAAArC,iBAAA,CAAG,WAAgBsC,KAAK,EAAEC,QAAQ,EAAE;EACnD,MAAMhB,IAAI,SAASX,KAAK,CAAC4B,gBAAgB,CAAAZ,MAAA,CAAAC,MAAA;IAEvCY,UAAU,EAAE/B,IAAI,CAACgC,OAAO,CAACH,QAAQ,CAAC,GAAG7B,IAAI,CAACM;EAAG,GAC1CR,SAAS,CAACY,aAAa,CAAC;IAC3BmB;EAAQ,EACT,CAAC;EAGF,IAAIhB,IAAI,KAAK,IAAI,EAAE,OAAO,IAAI;EAE9B,MAAM;IAAEoB,MAAM;IAAEC;EAAM,CAAC,GAAGC,WAAW,CAACtB,IAAI,EAAEgB,QAAQ,CAAC;EACrD,IAAII,MAAM,EAAE,OAAOA,MAAM;EAEzB,MAAM;IAAEG,IAAI;IAAEC;EAAI,CAAC,SAASnC,KAAK,CAACoC,cAAc,CAACV,KAAK,EAAAV,MAAA,CAAAC,MAAA,KACjDN,IAAI;IACP0B,UAAU,EAAE1B,IAAI,CAAC0B,UAAU,KAAK1C,SAAS,GAAG,MAAM,GAAGgB,IAAI,CAAC0B,UAAU;IACpEC,GAAG,EAAE;EAAK,EACX,CAAC;EAEF,OAAON,KAAK,CAAC;IAAEE,IAAI;IAAEC;EAAI,CAAC,CAAC;AAC7B,CAAC;AAEkC;EACjC1B,OAAO,CAAC8B,aAAa,GAAG,UAAUb,KAAK,EAAEC,QAAQ,EAAE;IACjD,MAAMhB,IAAI,GAAG,IAAIX,KAAK,CAACwC,aAAa,CAAC,CAAC,CAACC,IAAI,CAAAzB,MAAA,CAAAC,MAAA;MAEzCY,UAAU,EAAE/B,IAAI,CAACgC,OAAO,CAACH,QAAQ,CAAC,GAAG7B,IAAI,CAACM;IAAG,GAC1CR,SAAS,CAACY,aAAa,CAAC;MAC3BmB;IAAQ,EACT,CAAC;IAGF,IAAIhB,IAAI,KAAK,IAAI,EAAE,OAAO,IAAI;IAE9B,MAAM;MAAEoB,MAAM;MAAEC;IAAM,CAAC,GAAGC,WAAW,CAACtB,IAAI,EAAEgB,QAAQ,CAAC;IACrD,IAAII,MAAM,EAAE,OAAOA,MAAM;IAEzB,MAAM;MAAEG,IAAI;MAAEC;IAAI,CAAC,GAAGnC,KAAK,CAACuC,aAAa,CAACb,KAAK,EAAAV,MAAA,CAAAC,MAAA,KAC1CN,IAAI;MACP0B,UAAU,EAAE1B,IAAI,CAAC0B,UAAU,KAAK1C,SAAS,GAAG,MAAM,GAAGgB,IAAI,CAAC0B,UAAU;MACpEC,GAAG,EAAE;IAAK,EACX,CAAC;IAEF,OAAON,KAAK,CAAC;MAAEE,IAAI;MAAEC;IAAI,CAAC,CAAC;EAC7B,CAAC;AACH;AAEA,MAAMO,EAAE,GAAG3D,KAAK,IAAIA,KAAK;AAEzB,SAASkD,WAAWA,CAACtB,IAAI,EAAEgB,QAAQ,EAAE;EACnC,IAAI,CAACpB,KAAK,EAAE,OAAO;IAAEwB,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAEU;EAAG,CAAC;EAE9C,IAAIC,QAAQ,GAAI,GAAEC,IAAI,CAACC,SAAS,CAAClC,IAAI,CAAE,IAAGX,KAAK,CAAC8C,OAAQ,EAAC;EAEzD,MAAMC,GAAG,GAAG/C,KAAK,CAACgD,MAAM,CAAC,CAAC;EAC1B,IAAID,GAAG,EAAEJ,QAAQ,IAAK,IAAGI,GAAI,EAAC;EAE9B,MAAMhB,MAAM,GAAGxB,KAAK,CAACoC,QAAQ,CAAC;EAC9B,MAAMM,SAAS,GAAG,CAAClD,EAAE,CAACmD,QAAQ,CAACvB,QAAQ,CAAC,CAACwB,KAAK;EAE9C,IAAIpB,MAAM,IAAIA,MAAM,CAACoB,KAAK,KAAKF,SAAS,EAAE;IACxC,OAAO;MAAElB,MAAM,EAAEA,MAAM,CAAChD,KAAK;MAAEiD,KAAK,EAAEU;IAAG,CAAC;EAC5C;EAEA,OAAO;IACLX,MAAM,EAAE,IAAI;IACZC,KAAKA,CAACjD,KAAK,EAAE;MACXwB,KAAK,CAACoC,QAAQ,CAAC,GAAG;QAAE5D,KAAK;QAAEoE,KAAK,EAAEF;MAAU,CAAC;MAC7ChD,aAAa,CAACmD,QAAQ,CAAC,CAAC;MACxB,OAAOrE,KAAK;IACd;EACF,CAAC;AACH", "ignoreList": []}