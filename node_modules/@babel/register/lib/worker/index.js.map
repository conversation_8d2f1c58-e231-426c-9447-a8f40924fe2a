{"version": 3, "names": ["babel", "require", "handleMessage", "parentPort", "addListener", "_asyncToGenerator", "signal", "port", "action", "payload", "response", "init", "result", "error", "errorData", "Object", "assign", "postMessage", "_unused", "Error", "close", "Atomics", "store", "notify"], "sources": ["../../src/worker/index.js"], "sourcesContent": ["const babel = require(\"./babel-core.js\");\nconst handleMessage = require(\"./handle-message.js\");\n\nconst { parentPort } = require(\"worker_threads\");\n\nparentPort.addListener(\"message\", async ({ signal, port, action, payload }) => {\n  let response;\n\n  try {\n    if (babel.init) await babel.init;\n\n    response = { result: await handleMessage(action, payload) };\n  } catch (error) {\n    response = { error, errorData: { ...error } };\n  }\n\n  try {\n    port.postMessage(response);\n  } catch {\n    port.postMessage({\n      error: new Error(\"Cannot serialize worker response\"),\n    });\n  } finally {\n    port.close();\n    Atomics.store(signal, 0, 1);\n    Atomics.notify(signal, 0);\n  }\n});\n"], "mappings": ";;AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AACxC,MAAMC,aAAa,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAEpD,MAAM;EAAEE;AAAW,CAAC,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AAEhDE,UAAU,CAACC,WAAW,CAAC,SAAS,EAAAC,iBAAA,CAAE,WAAO;EAAEC,MAAM;EAAEC,IAAI;EAAEC,MAAM;EAAEC;AAAQ,CAAC,EAAK;EAC7E,IAAIC,QAAQ;EAEZ,IAAI;IACF,IAAIV,KAAK,CAACW,IAAI,EAAE,MAAMX,KAAK,CAACW,IAAI;IAEhCD,QAAQ,GAAG;MAAEE,MAAM,QAAQV,aAAa,CAACM,MAAM,EAAEC,OAAO;IAAE,CAAC;EAC7D,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdH,QAAQ,GAAG;MAAEG,KAAK;MAAEC,SAAS,EAAAC,MAAA,CAAAC,MAAA,KAAOH,KAAK;IAAG,CAAC;EAC/C;EAEA,IAAI;IACFN,IAAI,CAACU,WAAW,CAACP,QAAQ,CAAC;EAC5B,CAAC,CAAC,OAAAQ,OAAA,EAAM;IACNX,IAAI,CAACU,WAAW,CAAC;MACfJ,KAAK,EAAE,IAAIM,KAAK,CAAC,kCAAkC;IACrD,CAAC,CAAC;EACJ,CAAC,SAAS;IACRZ,IAAI,CAACa,KAAK,CAAC,CAAC;IACZC,OAAO,CAACC,KAAK,CAAChB,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IAC3Be,OAAO,CAACE,MAAM,CAACjB,MAAM,EAAE,CAAC,CAAC;EAC3B;AACF,CAAC,EAAC", "ignoreList": []}