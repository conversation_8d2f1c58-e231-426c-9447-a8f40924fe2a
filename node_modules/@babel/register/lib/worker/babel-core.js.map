{"version": 3, "names": ["cache", "require", "initialize", "babel", "exports", "init", "version", "DEFAULT_EXTENSIONS", "loadOptionsAsync", "transformAsync", "getEnv", "OptionManager", "transformSync", "initializeCacheFilename"], "sources": ["../../src/worker/babel-core.js"], "sourcesContent": ["const cache = require(\"./cache.js\");\n\nfunction initialize(babel) {\n  exports.init = null;\n  exports.version = babel.version;\n  exports.DEFAULT_EXTENSIONS = babel.DEFAULT_EXTENSIONS;\n  exports.loadOptionsAsync = babel.loadOptionsAsync;\n  exports.transformAsync = babel.transformAsync;\n  exports.getEnv = babel.getEnv;\n\n  if (!process.env.BABEL_8_BREAKING) {\n    exports.OptionManager = babel.OptionManager;\n    exports.transformSync = babel.transformSync;\n  }\n\n  cache.initializeCacheFilename();\n}\n\nif (USE_ESM) {\n  // @ts-expect-error CJS-ESM interop.\n  exports.init = import(\"@babel/core\").then(initialize);\n} else {\n  initialize(require(\"@babel/core\"));\n}\n"], "mappings": "AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AAEnC,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzBC,OAAO,CAACC,IAAI,GAAG,IAAI;EACnBD,OAAO,CAACE,OAAO,GAAGH,KAAK,CAACG,OAAO;EAC/BF,OAAO,CAACG,kBAAkB,GAAGJ,KAAK,CAACI,kBAAkB;EACrDH,OAAO,CAACI,gBAAgB,GAAGL,KAAK,CAACK,gBAAgB;EACjDJ,OAAO,CAACK,cAAc,GAAGN,KAAK,CAACM,cAAc;EAC7CL,OAAO,CAACM,MAAM,GAAGP,KAAK,CAACO,MAAM;EAEM;IACjCN,OAAO,CAACO,aAAa,GAAGR,KAAK,CAACQ,aAAa;IAC3CP,OAAO,CAACQ,aAAa,GAAGT,KAAK,CAACS,aAAa;EAC7C;EAEAZ,KAAK,CAACa,uBAAuB,CAAC,CAAC;AACjC;AAKO;EACLX,UAAU,CAACD,OAAO,CAAC,aAAa,CAAC,CAAC;AACpC", "ignoreList": []}