{"version": 3, "names": ["_core", "require", "_helperReplaceSupers", "hasOwnDecorators", "node", "_node$decorators", "decorators", "length", "hasDecorators", "body", "some", "prop", "key", "value", "t", "objectProperty", "identifier", "method", "objectMethod", "blockStatement", "takeDecorators", "result", "arrayExpression", "map", "decorator", "expression", "undefined", "<PERSON><PERSON><PERSON>", "computed", "isIdentifier", "stringLiteral", "name", "String", "extractElementDescriptor", "file", "classRef", "superRef", "path", "isMethod", "isClassMethod", "isPrivate", "buildCodeFrameError", "type", "isFunction", "_path$ensureFunctionN", "ensureFunctionName", "NodePath", "prototype", "scope", "isTSDeclareMethod", "ReplaceSupers", "methodPath", "objectRef", "refToPreserve", "replace", "properties", "kind", "static", "booleanLiteral", "filter", "Boolean", "push", "toExpression", "isClassProperty", "template", "statements", "ast", "buildUndefinedNode", "remove", "objectExpression", "addDecorateHelper", "addHelper", "buildDecoratedClass", "ref", "elements", "initializeId", "generateUidIdentifier", "isDeclaration", "id", "isStrict", "isInStrictMode", "superClass", "cloneNode", "superId", "generateUidIdentifierBasedOnNode", "classDecorators", "definitions", "element", "abstract", "wrapperCall", "nullLiteral", "arguments", "directives", "directive", "directiveLiteral", "replacement", "classPathDesc", "statement", "instanceNodes", "wrapClass", "replaceWith", "get"], "sources": ["../src/decorators-2018-09.ts"], "sourcesContent": ["// TODO(Babel 8): Remove this file\n\nimport { types as t, template } from \"@babel/core\";\nimport type { File, NodePath } from \"@babel/core\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\n\ntype Decoratable = Extract<t.Node, { decorators?: t.Decorator[] | null }>;\n\nexport function hasOwnDecorators(node: t.Class | t.ClassBody[\"body\"][number]) {\n  // @ts-expect-error: 'decorators' not in TSIndexSignature\n  return !!node.decorators?.length;\n}\n\nexport function hasDecorators(node: t.Class) {\n  return hasOwnDecorators(node) || node.body.body.some(hasOwnDecorators);\n}\n\nfunction prop(key: string, value?: t.Expression) {\n  if (!value) return null;\n  return t.objectProperty(t.identifier(key), value);\n}\n\nfunction method(key: string, body: t.Statement[]) {\n  return t.objectMethod(\n    \"method\",\n    t.identifier(key),\n    [],\n    t.blockStatement(body),\n  );\n}\n\nfunction takeDecorators(node: Decoratable) {\n  let result: t.ArrayExpression | undefined;\n  if (node.decorators && node.decorators.length > 0) {\n    result = t.arrayExpression(\n      node.decorators.map(decorator => decorator.expression),\n    );\n  }\n  node.decorators = undefined;\n  return result;\n}\n\ntype AcceptedElement = Exclude<ClassElement, t.TSIndexSignature>;\ntype SupportedElement = Exclude<\n  AcceptedElement,\n  | t.ClassPrivateMethod\n  | t.ClassPrivateProperty\n  | t.ClassAccessorProperty\n  | t.StaticBlock\n>;\n\nfunction getKey(node: SupportedElement) {\n  if (node.computed) {\n    return node.key;\n  } else if (t.isIdentifier(node.key)) {\n    return t.stringLiteral(node.key.name);\n  } else {\n    return t.stringLiteral(\n      String(\n        // A non-identifier non-computed key\n        (node.key as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n          .value,\n      ),\n    );\n  }\n}\n\nfunction extractElementDescriptor(\n  file: File,\n  classRef: t.Identifier,\n  superRef: t.Identifier,\n  path: NodePath<AcceptedElement>,\n) {\n  const isMethod = path.isClassMethod();\n  if (path.isPrivate()) {\n    throw path.buildCodeFrameError(\n      `Private ${\n        isMethod ? \"methods\" : \"fields\"\n      } in decorated classes are not supported yet.`,\n    );\n  }\n  if (path.node.type === \"ClassAccessorProperty\") {\n    throw path.buildCodeFrameError(\n      `Accessor properties are not supported in 2018-09 decorator transform, please specify { \"version\": \"2021-12\" } instead.`,\n    );\n  }\n  if (path.node.type === \"StaticBlock\") {\n    throw path.buildCodeFrameError(\n      `Static blocks are not supported in 2018-09 decorator transform, please specify { \"version\": \"2021-12\" } instead.`,\n    );\n  }\n\n  if (path.isFunction()) {\n    if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n      // polyfill when being run by an older Babel version\n      path.ensureFunctionName ??=\n        // eslint-disable-next-line no-restricted-globals\n        require(\"@babel/traverse\").NodePath.prototype.ensureFunctionName;\n    }\n    // @ts-expect-error path is a ClassMethod, that technically\n    // is not supported as it does not have an .id property\n    // This plugin will however then transform the ClassMethod\n    // to a function expression, so it's fine.\n    path.ensureFunctionName(false);\n  }\n\n  const { node, scope } = path as NodePath<SupportedElement>;\n\n  if (!path.isTSDeclareMethod()) {\n    new ReplaceSupers({\n      methodPath: path as NodePath<\n        Exclude<SupportedElement, t.TSDeclareMethod>\n      >,\n      objectRef: classRef,\n      superRef,\n      file,\n      refToPreserve: classRef,\n    }).replace();\n  }\n\n  const properties: t.ObjectExpression[\"properties\"] = [\n    prop(\"kind\", t.stringLiteral(t.isClassMethod(node) ? node.kind : \"field\")),\n    prop(\"decorators\", takeDecorators(node as Decoratable)),\n    prop(\"static\", node.static && t.booleanLiteral(true)),\n    prop(\"key\", getKey(node)),\n  ].filter(Boolean);\n\n  if (t.isClassMethod(node)) {\n    properties.push(prop(\"value\", t.toExpression(node)));\n  } else if (t.isClassProperty(node) && node.value) {\n    properties.push(\n      method(\"value\", template.statements.ast`return ${node.value}`),\n    );\n  } else {\n    properties.push(prop(\"value\", scope.buildUndefinedNode()));\n  }\n\n  path.remove();\n\n  return t.objectExpression(properties);\n}\n\nfunction addDecorateHelper(file: File) {\n  return file.addHelper(\"decorate\");\n}\n\ntype ClassElement = t.Class[\"body\"][\"body\"][number];\ntype ClassElementPath = NodePath<ClassElement>;\n\nexport function buildDecoratedClass(\n  ref: t.Identifier,\n  path: NodePath<t.Class>,\n  elements: ClassElementPath[],\n  file: File,\n) {\n  const { node, scope } = path;\n  const initializeId = scope.generateUidIdentifier(\"initialize\");\n  const isDeclaration = node.id && path.isDeclaration();\n  const isStrict = path.isInStrictMode();\n  const { superClass } = node;\n\n  node.type = \"ClassDeclaration\";\n  if (!node.id) node.id = t.cloneNode(ref);\n\n  let superId: t.Identifier;\n  if (superClass) {\n    superId = scope.generateUidIdentifierBasedOnNode(node.superClass, \"super\");\n    node.superClass = superId;\n  }\n\n  const classDecorators = takeDecorators(node);\n  const definitions = t.arrayExpression(\n    elements\n      .filter(\n        element =>\n          // @ts-expect-error Ignore TypeScript's abstract methods (see #10514)\n          !element.node.abstract && element.node.type !== \"TSIndexSignature\",\n      )\n      .map(path =>\n        extractElementDescriptor(\n          file,\n          node.id,\n          superId,\n          // @ts-expect-error TS can not exclude TSIndexSignature\n          path,\n        ),\n      ),\n  );\n\n  const wrapperCall = template.expression.ast`\n    ${addDecorateHelper(file)}(\n      ${classDecorators || t.nullLiteral()},\n      function (${initializeId}, ${superClass ? t.cloneNode(superId) : null}) {\n        ${node}\n        return { F: ${t.cloneNode(node.id)}, d: ${definitions} };\n      },\n      ${superClass}\n    )\n  ` as t.CallExpression & { arguments: [unknown, t.FunctionExpression] };\n\n  if (!isStrict) {\n    wrapperCall.arguments[1].body.directives.push(\n      t.directive(t.directiveLiteral(\"use strict\")),\n    );\n  }\n\n  let replacement: t.Node = wrapperCall;\n  let classPathDesc = \"arguments.1.body.body.0\";\n  if (isDeclaration) {\n    replacement = template.statement.ast`let ${ref} = ${wrapperCall}`;\n    classPathDesc = \"declarations.0.init.\" + classPathDesc;\n  }\n\n  return {\n    instanceNodes: [\n      template.statement.ast`\n        ${t.cloneNode(initializeId)}(this)\n      ` as t.ExpressionStatement,\n    ],\n    wrapClass(path: NodePath<t.Class>) {\n      path.replaceWith(replacement);\n      return path.get(classPathDesc) as NodePath;\n    },\n  };\n}\n"], "mappings": ";;;;;;;;AAEA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,oBAAA,GAAAD,OAAA;AAIO,SAASE,gBAAgBA,CAACC,IAA2C,EAAE;EAAA,IAAAC,gBAAA;EAE5E,OAAO,CAAC,GAAAA,gBAAA,GAACD,IAAI,CAACE,UAAU,aAAfD,gBAAA,CAAiBE,MAAM;AAClC;AAEO,SAASC,aAAaA,CAACJ,IAAa,EAAE;EAC3C,OAAOD,gBAAgB,CAACC,IAAI,CAAC,IAAIA,IAAI,CAACK,IAAI,CAACA,IAAI,CAACC,IAAI,CAACP,gBAAgB,CAAC;AACxE;AAEA,SAASQ,IAAIA,CAACC,GAAW,EAAEC,KAAoB,EAAE;EAC/C,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;EACvB,OAAOC,WAAC,CAACC,cAAc,CAACD,WAAC,CAACE,UAAU,CAACJ,GAAG,CAAC,EAAEC,KAAK,CAAC;AACnD;AAEA,SAASI,MAAMA,CAACL,GAAW,EAAEH,IAAmB,EAAE;EAChD,OAAOK,WAAC,CAACI,YAAY,CACnB,QAAQ,EACRJ,WAAC,CAACE,UAAU,CAACJ,GAAG,CAAC,EACjB,EAAE,EACFE,WAAC,CAACK,cAAc,CAACV,IAAI,CACvB,CAAC;AACH;AAEA,SAASW,cAAcA,CAAChB,IAAiB,EAAE;EACzC,IAAIiB,MAAqC;EACzC,IAAIjB,IAAI,CAACE,UAAU,IAAIF,IAAI,CAACE,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;IACjDc,MAAM,GAAGP,WAAC,CAACQ,eAAe,CACxBlB,IAAI,CAACE,UAAU,CAACiB,GAAG,CAACC,SAAS,IAAIA,SAAS,CAACC,UAAU,CACvD,CAAC;EACH;EACArB,IAAI,CAACE,UAAU,GAAGoB,SAAS;EAC3B,OAAOL,MAAM;AACf;AAWA,SAASM,MAAMA,CAACvB,IAAsB,EAAE;EACtC,IAAIA,IAAI,CAACwB,QAAQ,EAAE;IACjB,OAAOxB,IAAI,CAACQ,GAAG;EACjB,CAAC,MAAM,IAAIE,WAAC,CAACe,YAAY,CAACzB,IAAI,CAACQ,GAAG,CAAC,EAAE;IACnC,OAAOE,WAAC,CAACgB,aAAa,CAAC1B,IAAI,CAACQ,GAAG,CAACmB,IAAI,CAAC;EACvC,CAAC,MAAM;IACL,OAAOjB,WAAC,CAACgB,aAAa,CACpBE,MAAM,CAEH5B,IAAI,CAACQ,GAAG,CACNC,KACL,CACF,CAAC;EACH;AACF;AAEA,SAASoB,wBAAwBA,CAC/BC,IAAU,EACVC,QAAsB,EACtBC,QAAsB,EACtBC,IAA+B,EAC/B;EACA,MAAMC,QAAQ,GAAGD,IAAI,CAACE,aAAa,CAAC,CAAC;EACrC,IAAIF,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE;IACpB,MAAMH,IAAI,CAACI,mBAAmB,CAC5B,WACEH,QAAQ,GAAG,SAAS,GAAG,QAAQ,8CAEnC,CAAC;EACH;EACA,IAAID,IAAI,CAACjC,IAAI,CAACsC,IAAI,KAAK,uBAAuB,EAAE;IAC9C,MAAML,IAAI,CAACI,mBAAmB,CAC5B,wHACF,CAAC;EACH;EACA,IAAIJ,IAAI,CAACjC,IAAI,CAACsC,IAAI,KAAK,aAAa,EAAE;IACpC,MAAML,IAAI,CAACI,mBAAmB,CAC5B,kHACF,CAAC;EACH;EAEA,IAAIJ,IAAI,CAACM,UAAU,CAAC,CAAC,EAAE;IAC4C;MAAA,IAAAC,qBAAA;MAE/D,CAAAA,qBAAA,GAAAP,IAAI,CAACQ,kBAAkB,YAAAD,qBAAA,GAAvBP,IAAI,CAACQ,kBAAkB,GAErB5C,OAAO,CAAC,iBAAiB,CAAC,CAAC6C,QAAQ,CAACC,SAAS,CAACF,kBAAkB;IACpE;IAKAR,IAAI,CAACQ,kBAAkB,CAAC,KAAK,CAAC;EAChC;EAEA,MAAM;IAAEzC,IAAI;IAAE4C;EAAM,CAAC,GAAGX,IAAkC;EAE1D,IAAI,CAACA,IAAI,CAACY,iBAAiB,CAAC,CAAC,EAAE;IAC7B,IAAIC,4BAAa,CAAC;MAChBC,UAAU,EAAEd,IAEX;MACDe,SAAS,EAAEjB,QAAQ;MACnBC,QAAQ;MACRF,IAAI;MACJmB,aAAa,EAAElB;IACjB,CAAC,CAAC,CAACmB,OAAO,CAAC,CAAC;EACd;EAEA,MAAMC,UAA4C,GAAG,CACnD5C,IAAI,CAAC,MAAM,EAAEG,WAAC,CAACgB,aAAa,CAAChB,WAAC,CAACyB,aAAa,CAACnC,IAAI,CAAC,GAAGA,IAAI,CAACoD,IAAI,GAAG,OAAO,CAAC,CAAC,EAC1E7C,IAAI,CAAC,YAAY,EAAES,cAAc,CAAChB,IAAmB,CAAC,CAAC,EACvDO,IAAI,CAAC,QAAQ,EAAEP,IAAI,CAACqD,MAAM,IAAI3C,WAAC,CAAC4C,cAAc,CAAC,IAAI,CAAC,CAAC,EACrD/C,IAAI,CAAC,KAAK,EAAEgB,MAAM,CAACvB,IAAI,CAAC,CAAC,CAC1B,CAACuD,MAAM,CAACC,OAAO,CAAC;EAEjB,IAAI9C,WAAC,CAACyB,aAAa,CAACnC,IAAI,CAAC,EAAE;IACzBmD,UAAU,CAACM,IAAI,CAAClD,IAAI,CAAC,OAAO,EAAEG,WAAC,CAACgD,YAAY,CAAC1D,IAAI,CAAC,CAAC,CAAC;EACtD,CAAC,MAAM,IAAIU,WAAC,CAACiD,eAAe,CAAC3D,IAAI,CAAC,IAAIA,IAAI,CAACS,KAAK,EAAE;IAChD0C,UAAU,CAACM,IAAI,CACb5C,MAAM,CAAC,OAAO,EAAE+C,cAAQ,CAACC,UAAU,CAACC,GAAG,UAAU9D,IAAI,CAACS,KAAK,EAAE,CAC/D,CAAC;EACH,CAAC,MAAM;IACL0C,UAAU,CAACM,IAAI,CAAClD,IAAI,CAAC,OAAO,EAAEqC,KAAK,CAACmB,kBAAkB,CAAC,CAAC,CAAC,CAAC;EAC5D;EAEA9B,IAAI,CAAC+B,MAAM,CAAC,CAAC;EAEb,OAAOtD,WAAC,CAACuD,gBAAgB,CAACd,UAAU,CAAC;AACvC;AAEA,SAASe,iBAAiBA,CAACpC,IAAU,EAAE;EACrC,OAAOA,IAAI,CAACqC,SAAS,CAAC,UAAU,CAAC;AACnC;AAKO,SAASC,mBAAmBA,CACjCC,GAAiB,EACjBpC,IAAuB,EACvBqC,QAA4B,EAC5BxC,IAAU,EACV;EACA,MAAM;IAAE9B,IAAI;IAAE4C;EAAM,CAAC,GAAGX,IAAI;EAC5B,MAAMsC,YAAY,GAAG3B,KAAK,CAAC4B,qBAAqB,CAAC,YAAY,CAAC;EAC9D,MAAMC,aAAa,GAAGzE,IAAI,CAAC0E,EAAE,IAAIzC,IAAI,CAACwC,aAAa,CAAC,CAAC;EACrD,MAAME,QAAQ,GAAG1C,IAAI,CAAC2C,cAAc,CAAC,CAAC;EACtC,MAAM;IAAEC;EAAW,CAAC,GAAG7E,IAAI;EAE3BA,IAAI,CAACsC,IAAI,GAAG,kBAAkB;EAC9B,IAAI,CAACtC,IAAI,CAAC0E,EAAE,EAAE1E,IAAI,CAAC0E,EAAE,GAAGhE,WAAC,CAACoE,SAAS,CAACT,GAAG,CAAC;EAExC,IAAIU,OAAqB;EACzB,IAAIF,UAAU,EAAE;IACdE,OAAO,GAAGnC,KAAK,CAACoC,gCAAgC,CAAChF,IAAI,CAAC6E,UAAU,EAAE,OAAO,CAAC;IAC1E7E,IAAI,CAAC6E,UAAU,GAAGE,OAAO;EAC3B;EAEA,MAAME,eAAe,GAAGjE,cAAc,CAAChB,IAAI,CAAC;EAC5C,MAAMkF,WAAW,GAAGxE,WAAC,CAACQ,eAAe,CACnCoD,QAAQ,CACLf,MAAM,CACL4B,OAAO,IAEL,CAACA,OAAO,CAACnF,IAAI,CAACoF,QAAQ,IAAID,OAAO,CAACnF,IAAI,CAACsC,IAAI,KAAK,kBACpD,CAAC,CACAnB,GAAG,CAACc,IAAI,IACPJ,wBAAwB,CACtBC,IAAI,EACJ9B,IAAI,CAAC0E,EAAE,EACPK,OAAO,EAEP9C,IACF,CACF,CACJ,CAAC;EAED,MAAMoD,WAAW,GAAGzB,cAAQ,CAACvC,UAAU,CAACyC,GAAG;AAC7C,MAAMI,iBAAiB,CAACpC,IAAI,CAAC;AAC7B,QAAQmD,eAAe,IAAIvE,WAAC,CAAC4E,WAAW,CAAC,CAAC;AAC1C,kBAAkBf,YAAY,KAAKM,UAAU,GAAGnE,WAAC,CAACoE,SAAS,CAACC,OAAO,CAAC,GAAG,IAAI;AAC3E,UAAU/E,IAAI;AACd,sBAAsBU,WAAC,CAACoE,SAAS,CAAC9E,IAAI,CAAC0E,EAAE,CAAC,QAAQQ,WAAW;AAC7D;AACA,QAAQL,UAAU;AAClB;AACA,GAAwE;EAEtE,IAAI,CAACF,QAAQ,EAAE;IACbU,WAAW,CAACE,SAAS,CAAC,CAAC,CAAC,CAAClF,IAAI,CAACmF,UAAU,CAAC/B,IAAI,CAC3C/C,WAAC,CAAC+E,SAAS,CAAC/E,WAAC,CAACgF,gBAAgB,CAAC,YAAY,CAAC,CAC9C,CAAC;EACH;EAEA,IAAIC,WAAmB,GAAGN,WAAW;EACrC,IAAIO,aAAa,GAAG,yBAAyB;EAC7C,IAAInB,aAAa,EAAE;IACjBkB,WAAW,GAAG/B,cAAQ,CAACiC,SAAS,CAAC/B,GAAG,OAAOO,GAAG,MAAMgB,WAAW,EAAE;IACjEO,aAAa,GAAG,sBAAsB,GAAGA,aAAa;EACxD;EAEA,OAAO;IACLE,aAAa,EAAE,CACblC,cAAQ,CAACiC,SAAS,CAAC/B,GAAG;AAC5B,UAAUpD,WAAC,CAACoE,SAAS,CAACP,YAAY,CAAC;AACnC,OAAO,CACF;IACDwB,SAASA,CAAC9D,IAAuB,EAAE;MACjCA,IAAI,CAAC+D,WAAW,CAACL,WAAW,CAAC;MAC7B,OAAO1D,IAAI,CAACgE,GAAG,CAACL,aAAa,CAAC;IAChC;EACF,CAAC;AACH", "ignoreList": []}