/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Emphasis, PhrasingContent} from 'mdast'
 */
/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Emphasis}
 *   mdast node.
 */
export function em(state: State, node: Readonly<Element>): Emphasis;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
import type { Emphasis } from 'mdast';
//# sourceMappingURL=em.d.ts.map