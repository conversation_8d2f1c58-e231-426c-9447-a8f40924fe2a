/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Image} from 'mdast'
 */
/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Image}
 *   mdast node.
 */
export function img(state: State, node: Readonly<Element>): Image;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
import type { Image } from 'mdast';
//# sourceMappingURL=img.d.ts.map