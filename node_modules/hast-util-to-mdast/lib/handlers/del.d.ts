/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Delete, PhrasingContent} from 'mdast'
 */
/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Delete}
 *   mdast node.
 */
export function del(state: State, node: Readonly<Element>): Delete;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
import type { Delete } from 'mdast';
//# sourceMappingURL=del.d.ts.map