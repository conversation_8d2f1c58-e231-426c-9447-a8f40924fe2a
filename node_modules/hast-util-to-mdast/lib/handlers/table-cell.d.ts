/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {PhrasingContent, TableCell} from 'mdast'
 */
/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {TableCell}
 *   mdast node.
 */
export function tableCell(state: State, node: Readonly<Element>): TableCell;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
import type { TableCell } from 'mdast';
//# sourceMappingURL=table-cell.d.ts.map