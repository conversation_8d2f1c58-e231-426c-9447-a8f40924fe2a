/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {Break} from 'mdast'
 */
/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Break}
 *   mdast node.
 */
export function br(state: State, node: Readonly<Element>): Break;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
import type { Break } from 'mdast';
//# sourceMappingURL=br.d.ts.map