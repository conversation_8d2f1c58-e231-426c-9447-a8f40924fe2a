/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {PhrasingContent, Strong} from 'mdast'
 */
/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Strong}
 *   mdast node.
 */
export function strong(state: State, node: Readonly<Element>): Strong;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
import type { Strong } from 'mdast';
//# sourceMappingURL=strong.d.ts.map