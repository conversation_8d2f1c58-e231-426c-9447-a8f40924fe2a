/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {Array<MdastRootContent>}
 *   mdast nodes.
 */
export function q(state: State, node: Readonly<Element>): Array<MdastRootContent>;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
import type { RootContent as MdastRootContent } from 'mdast';
//# sourceMappingURL=q.d.ts.map