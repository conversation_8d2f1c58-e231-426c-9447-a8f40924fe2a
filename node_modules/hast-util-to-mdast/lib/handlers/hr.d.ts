/**
 * @import {State} from 'hast-util-to-mdast'
 * @import {Element} from 'hast'
 * @import {ThematicBreak} from 'mdast'
 */
/**
 * @param {State} state
 *   State.
 * @param {Readonly<Element>} node
 *   hast element to transform.
 * @returns {ThematicBreak}
 *   mdast node.
 */
export function hr(state: State, node: Readonly<Element>): ThematicBreak;
import type { State } from 'hast-util-to-mdast';
import type { Element } from 'hast';
import type { ThematicBreak } from 'mdast';
//# sourceMappingURL=hr.d.ts.map