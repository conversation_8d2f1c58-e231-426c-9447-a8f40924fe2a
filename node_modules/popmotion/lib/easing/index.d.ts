import { Easing } from "./types";
export declare const linear: Easing;
export declare const easeIn: Easing;
export declare const easeOut: Easing;
export declare const easeInOut: Easing;
export declare const circIn: Easing;
export declare const circOut: Easing;
export declare const circInOut: Easing;
export declare const backIn: Easing;
export declare const backOut: Easing;
export declare const backInOut: Easing;
export declare const anticipate: Easing;
export declare const bounceOut: (p: number) => number;
export declare const bounceIn: Easing;
export declare const bounceInOut: (p: number) => number;
