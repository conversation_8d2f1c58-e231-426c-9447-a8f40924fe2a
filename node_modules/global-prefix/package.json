{"name": "global-prefix", "description": "Get the npm global path prefix.", "version": "0.1.5", "homepage": "https://github.com/jonschlinkert/global-prefix", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/global-prefix", "bugs": {"url": "https://github.com/jonschlinkert/global-prefix/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"homedir-polyfill": "^1.0.0", "ini": "^1.3.4", "is-windows": "^0.2.0", "which": "^1.2.12"}, "devDependencies": {"fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "keywords": ["global", "module", "modules", "npm", "path", "prefix", "resolve"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["global-modules", "global-paths"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}, "contributors": ["<PERSON> <<EMAIL>> (http://twitter.com/jonschlinkert)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/rmbaad)", "<PERSON><PERSON>hang <<EMAIL>> (https://packagist.org/packages/jason-chang)", "<PERSON><PERSON><PERSON> (https://www.ncode.nl)", "<PERSON> (https://github.com/mathiasvr)", "Charl<PERSON> Reagent (http://i.am.charlike.online)"]}