{"name": "compute-gcd", "version": "1.2.1", "description": "Computes the greatest common divisor (gcd).", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "p<PERSON><PERSON><PERSON>@outlook.com"}], "scripts": {"test": "./node_modules/.bin/mocha", "test-cov": "./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --dir ./reports/coverage -- -R spec", "coveralls": "./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --dir ./reports/coveralls/coverage --report lcovonly -- -R spec && cat ./reports/coveralls/coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js && rm -rf ./reports/coveralls"}, "main": "./lib", "repository": {"type": "git", "url": "git://github.com/compute-io/gcd.git"}, "keywords": ["compute.io", "compute", "computation", "mathematics", "math", "euclid", "stein", "binary gcd", "greatest common divisor", "greatest common factor", "highest common factor", "greatest common measure", "highest common divisor", "gcd", "gcf", "hcf", "gcm", "arithmetic", "array", "integer"], "bugs": {"url": "https://github.com/compute-io/gcd/issues"}, "dependencies": {"validate.io-array": "^1.0.3", "validate.io-function": "^1.0.2", "validate.io-integer-array": "^1.0.0"}, "devDependencies": {"chai": "2.x.x", "mocha": "2.x.x", "coveralls": "^2.11.1", "istanbul": "^0.3.0", "jshint": "2.x.x", "jshint-stylish": "^1.0.0"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}]}