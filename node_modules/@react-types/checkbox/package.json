{"name": "@react-types/checkbox", "version": "3.8.3", "description": "Spectrum UI components in React", "license": "Apache-2.0", "types": "src/index.d.ts", "repository": {"type": "git", "url": "https://github.com/adobe/react-spectrum"}, "dependencies": {"@react-types/shared": "^3.24.1"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "f0aa6aacee60af265dc8994b9274ccf072a850aa"}