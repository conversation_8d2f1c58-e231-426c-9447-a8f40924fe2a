#!/usr/bin/env node

var prettyjson = require('../lib/prettyjson');
var fs = require('fs');
var colors = require('colors/safe');
var argv = require('minimist')(process.argv.slice(2));

var options = {
  keysColor: argv.keys || process.env.PRETTYJSON_KEYS,
  dashColor: argv.dash || process.env.PRETTYJSON_DASH,
  defaultIndentation: argv.indent || process.env.PRETTYJSON_INDENT,
  stringColor: argv.string || process.env.PRETTYJSON_STRING,
  multilineStringColor: argv.multiline_string || process.env.PRETTYJSON_MULTILINE_STRING,
  numberColor: argv.number || process.env.PRETTYJSON_NUMBER,
  positiveNumberColor: argv.number || process.env.PRETTYJSON_NUMBER_POSITIVE,
  negativeNumberColor: argv.number || process.env.PRETTYJSON_NUMBER_NEGATIVE,
  noColor: argv['nocolor'] || process.env.PRETTYJSON_NOCOLOR,
  noAlign: argv['noalign'] || process.env.PRETTYJSON_NOALIGN,
  escape: argv['escape'] || process.env.PRETTYJSON_ESCAPE,
  inlineArrays: argv['inline-arrays'] || process.env.PRETTYJSON_INLINE_ARRAYS
};

var renderInputJson = function(input){
  console.log(prettyjson.renderString(input, options));
};

if (argv._.length) {
  // First parameter is the file to read and parse
  var filename = argv._[0];
  try {
    renderInputJson(fs.readFileSync(filename, 'utf8'));
  } catch (e) {
    console.error(colors.red('Error: ') + 'File \'' + filename + '\' does not exist');
    process.exit(1);
  }
} else {
  // Read input stream

  var streamData = '';

  process.stdin.resume();
  process.stdin.setEncoding('utf8');
  process.stdin.on('data', function (chunk) {
    if (chunk === '\n') {
      renderInputJson(streamData);
      streamData = '';
      return;
    }
    streamData += chunk;
  });
  process.stdin.on('end', function(){
    renderInputJson(streamData);
  });
}
