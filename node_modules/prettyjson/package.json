{"author": "<PERSON> <<EMAIL>> (https://github.com/rafeca)", "name": "<PERSON><PERSON><PERSON>", "description": "Package for formatting JSON data in a coloured YAML-style, perfect for CLI output", "version": "1.2.5", "homepage": "http://rafeca.com/prettyjson", "keywords": ["json", "cli", "formatting", "colors"], "repository": {"type": "git", "url": "https://github.com/rafeca/prettyjson.git"}, "bugs": {"url": "https://github.com/rafeca/prettyjson/issues"}, "main": "./lib/pretty<PERSON>son", "files": ["bin/pretty<PERSON>son", "lib/*.js"], "license": "MIT", "scripts": {"test": "npm run jshint && mocha --reporter spec", "testwin": "node ./node_modules/mocha/bin/mocha --reporter spec", "jshint": "jshint lib/*.js test/*.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec", "coveralls": "npm run coverage && cat ./coverage/lcov.info | coveralls && rm -rf ./coverage", "changelog": "git log $(git describe --tags --abbrev=0)..HEAD --pretty='* %s' --first-parent"}, "bin": {"prettyjson": "./bin/pretty<PERSON>son"}, "dependencies": {"colors": "1.4.0", "minimist": "^1.2.0"}, "devDependencies": {"coveralls": "^2.11.15", "istanbul": "^0.4.5", "jshint": "^2.9.4", "mocha": "^3.1.2", "mocha-lcov-reporter": "^1.2.0", "should": "^11.1.1"}}