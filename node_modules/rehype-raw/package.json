{"name": "rehype-raw", "version": "7.0.0", "description": "rehype plugin to reparse the tree (and raw nodes)", "license": "MIT", "keywords": ["html", "plugin", "raw", "rehype", "rehype-plugin", "unified"], "repository": "rehypejs/rehype-raw", "bugs": "https://github.com/rehypejs/rehype-raw/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/hast": "^3.0.0", "hast-util-raw": "^9.0.0", "vfile": "^6.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "c8": "^8.0.0", "prettier": "^3.0.0", "rehype-stringify": "^10.0.0", "remark-cli": "^11.0.0", "remark-parse": "^10.0.0", "remark-preset-wooorm": "^9.0.0", "remark-rehype": "^10.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "unified": "^11.0.0", "unist-util-visit": "^5.0.0", "xo": "^0.56.0"}, "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . --frail --output --quiet && prettier . --log-level warn --write && xo --fix", "prepack": "npm run build && npm run format", "test": "npm run build && npm run format && npm run test-coverage", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --check-coverage --reporter lcov npm run test-api"}, "prettier": {"bracketSpacing": false, "singleQuote": true, "semi": false, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"prettier": true}}