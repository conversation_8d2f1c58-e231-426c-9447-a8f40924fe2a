3.0.5:
  date: 2021-08-16
  fixed bugs:
    - Fixed a bug where `encoder.encodeHost` throws in the browser
  chores:
    - Setup browser tests

3.0.4:
  date: 2021-08-09
  fixed bugs:
    - Fixed a bug where leading slashes are trimmed incorrectly
  chores:
    - Removed punycode fallback for Electron v3
    - Updated dependencies

3.0.3:
  date: 2021-07-25
  fixed bugs:
    - >-
      GH-133 Fixed a bug where slashes in the `file` protocol are handled
      incorrectly

3.0.2:
  date: 2021-07-14
  fixed bugs:
    - >-
      GH-130 Fixed a bug where extra slashes and backslashes in the protocol are
      not handled correctly
  chores:
    - Added secure codecov publish script
    - Updated dependencies

3.0.1:
  date: 2021-01-02
  chores:
    - Updated dependencies

3.0.0:
  date: 2020-10-04
  new features:
    - GH-48 Added URL parser module
  breaking changes:
    - GH-49 Dropped support for Node < v10
  chores:
    - GH-50 Convert EncodeSet to ES6 class
    - GH-52 Automated releases and publish process
    - GH-51 Automated gh-pages docs deployment
    - >-
      GH-49 Updated .npmignore to prevent the addition of tests and config files
      in the published package
    - GH-49 Added system test
    - GH-49 Moved postman-collection to dev-dependencies
    - GH-49 Updated nyc configuration
    - GH-49 Updated ESLint rules
    - GH-49 Updated dependencies

2.1.3:
  date: 2020-07-29
  fixed bugs:
    - >-
      GH-41 Fixed a bug where square brackets are not removed from IPv6 hostname
      on `toNodeURL()`
  chores:
    - Updated dependencies

2.1.2:
  date: 2020-07-13
  chores:
    - Updated dependencies

2.1.1:
  date: 2020-03-15
  fixed bugs:
    - GH-20 Removed `&` and `=` from QUERY_ENCODE_SET

2.1.0:
  date: 2020-03-02
  new features:
    - GH-11 Added an option to disable encoding on `toNodeUrl()`
    - >-
      GH-13 Added `resolveNodeUrl()` method to resolve target URL relative to a
      base URL
  fixed bugs:
    - >-
      GH-12 Fixed a bug where Node.js native `url.domainToASCII` was not working
      as expected in Electron v3
    - GH-10 Handle query parameters with empty key or value
    - >-
      GH-9 Fixed a bug where query params without value were changed to params
      with empty value while encoding single param

2.0.0:
  date: 2020-02-06
  new features:
    - Implementation of the WHATWG URL Standard
    - Introduces new `/encoder` module with various encoding helpers
    - Method `toNodeUrl()` now follows the WHATWG URL standard for encoding
  breaking changes:
    - Renamed legacy `toNodeUrl()` method to `toLegacyNodeUrl()`
    - |
      Changed `encode()` function to encode SPACE, ("), (#), (&), ('), (<),
      (=), and (>). In previous versions, it was encoding SPACE, (!), ("), ((),
      ()), (<), (>), (%), ('), and (*).
    - Deprecated `toLegacyNodeUrl()` in favor of `toNodeUrl()`
    - Deprecated `encode()` in favor of `encodeQueryParam()`
    - |
      Dropped support for following methods:
        -> percentEncode()
        -> isPreEncoded()
        -> isPreEncodedCharacter()
        -> charactersToPercentEncode()
  chores:
    - Updated dependencies
    - Update license from MIT to Apache-2.0

1.0.3:
  date: 2019-10-16
  chores:
    - Updated dependencies

1.0.2:
  date: 2019-04-08
  new features:
    - >-
      Added `toNodeUrl()` function to convert a string URL into Node's URL type
      object

1.0.1:
  date: 2017-02-01
  chore:
    - Updated package.json

1.0.0:
  date: 2017-02-01
  initial release:
    - Initial Release
