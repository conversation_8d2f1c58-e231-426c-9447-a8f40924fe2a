{"name": "hast-util-is-body-ok-link", "version": "3.0.0", "description": "hast utility to check if a link element is “Body OK”", "license": "MIT", "keywords": ["body", "hast", "hast-util", "html", "link", "ok", "unist", "util", "utility"], "repository": "https://github.com/rehypejs/rehype-minify/tree/main/packages/hast-util-is-body-ok-link", "bugs": "https://github.com/rehypejs/rehype-minify/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["index.d.ts", "index.js", "lib/"], "dependencies": {"@types/hast": "^3.0.0"}, "scripts": {}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": false}