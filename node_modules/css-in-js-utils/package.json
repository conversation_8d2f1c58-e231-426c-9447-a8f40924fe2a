{"name": "css-in-js-utils", "version": "3.1.0", "description": "Useful utility functions for CSS in JS solutions", "main": "lib/index.js", "module": "es/index.js", "types": "es/index.d.ts", "jsnext:main": "es/index.js", "sideEffects": false, "files": ["lib/**", "es/**"], "keywords": ["css", "c<PERSON><PERSON>s", "utils", "small"], "repository": "https://github.com/robinweser/css-in-js-utils.git", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"build": "yarn run clean && yarn build:es && yarn build:lib", "build:lib": "cross-env BABEL_ENV=commonjs babel modules --extensions '.ts' --ignore 'modules/__tests__','**/*.d.ts' --out-dir lib", "build:es": "tsc && babel es --out-dir es", "clean": "rimraf es lib coverage", "check": "yarn lint && yarn test:coverage", "format": "prettier --write \"modules/**/*.{js,ts}\"", "lint": "eslint 'modules/**/*.{js,ts}'", "release": "git pull --rebase && yarn run check && yarn build && npm publish", "test": "cross-env BABEL_ENV=commonjs jest", "test:coverage": "cross-env BABEL_ENV=commonjs jest --coverage", "watch": "yarn test -- --watch"}, "jest": {"moduleDirectories": ["node_modules", "modules"]}, "dependencies": {"hyphenate-style-name": "^1.0.3"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/preset-typescript": "^7.9.0", "@types/jest": "^25.2.1", "@typescript-eslint/eslint-plugin": "^2.26.0", "@typescript-eslint/parser": "^2.26.0", "babel-jest": "^25.2.6", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-airbnb-base": "^14.1.0", "eslint-plugin-import": "^2.20.2", "jest": "^25.2.6", "prettier": "^1.7.4", "rimraf": "^2.6.1", "typescript": "^3.8.3"}}