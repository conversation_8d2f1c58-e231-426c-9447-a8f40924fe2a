{"name": "@codemirror/commands", "version": "6.8.0", "description": "Collection of editing commands for the CodeMirror code editor", "scripts": {"test": "cm-runtests", "prepare": "cm-buildhelper src/commands.ts"}, "keywords": ["editor", "code"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://marijnhaverbeke.nl"}, "type": "module", "main": "dist/index.cjs", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "types": "dist/index.d.ts", "module": "dist/index.js", "sideEffects": false, "license": "MIT", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.27.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "repository": {"type": "git", "url": "https://github.com/codemirror/commands.git"}}