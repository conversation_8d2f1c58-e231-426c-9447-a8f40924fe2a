## 6.1.2 (2024-12-10)

### Bug fixes

List `@lezer/lr` as a direct dependency for tools that require this.

Make block sequence items, rather than the whole sequence, fold targets.

## 6.1.1 (2024-04-12)

### Bug fixes

Make block mapping entries, rather than the entire mapping, foldable.

## 6.1.0 (2024-04-07)

### New features

`yamlFrontmatter` can now be used to wrap a language with a YAML frontmatter.

## 6.0.0 (2024-01-22)

### New Features

First versioned release.
