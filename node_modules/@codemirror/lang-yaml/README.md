<!-- NOTE: README.md is generated from src/README.md -->

# @codemirror/lang-yaml [![NPM version](https://img.shields.io/npm/v/@codemirror/lang-yaml.svg)](https://www.npmjs.org/package/@codemirror/lang-yaml)

[ [**WEBSITE**](https://codemirror.net/) | [**ISSUES**](https://github.com/codemirror/dev/issues) | [**FORUM**](https://discuss.codemirror.net/c/next/) | [**CHANGELOG**](https://github.com/codemirror/lang-yaml/blob/main/CHANGELOG.md) ]

This package implements YAML language support for the
[CodeMirror](https://codemirror.net/) code editor.

The [project page](https://codemirror.net/) has more information, a
number of [examples](https://codemirror.net/examples/) and the
[documentation](https://codemirror.net/docs/).

This code is released under an
[MIT license](https://github.com/codemirror/lang-yaml/tree/main/LICENSE).

We aim to be an inclusive, welcoming community. To make that explicit,
we have a [code of
conduct](http://contributor-covenant.org/version/1/1/0/) that applies
to communication around the project.

The initial implementation of this package was funded by [Braintrust Data](https://braintrustdata.com/).

## API Reference

<dl>
<dt id="user-content-yaml">
  <code><strong><a href="#user-content-yaml">yaml</a></strong>() → <a href="https://codemirror.net/docs/ref#language.LanguageSupport">LanguageSupport</a></code></dt>

<dd><p>Language support for YAML.</p>
</dd>
<dt id="user-content-yamllanguage">
  <code><strong><a href="#user-content-yamllanguage">yamlLanguage</a></strong>: <a href="https://codemirror.net/docs/ref#language.LRLanguage">LRLanguage</a></code></dt>

<dd><p>A language provider based on the <a href="https://github.com/lezer-parser/yaml">Lezer YAML
parser</a>, extended with
highlighting and indentation information.</p>
</dd>
<dt id="user-content-yamlfrontmatter">
  <code><strong><a href="#user-content-yamlfrontmatter">yamlFrontmatter</a></strong>(<a id="user-content-yamlfrontmatter^config" href="#user-content-yamlfrontmatter^config">config</a>: {content: <a href="https://codemirror.net/docs/ref#language.Language">Language</a> | <a href="https://codemirror.net/docs/ref#language.LanguageSupport">LanguageSupport</a>}) → <a href="https://codemirror.net/docs/ref#language.LanguageSupport">LanguageSupport</a></code></dt>

<dd><p>Returns language support for a document parsed as <code>config.content</code>
with an optional YAML &quot;frontmatter&quot; delimited by lines that
contain three dashes.</p>
</dd>
</dl>