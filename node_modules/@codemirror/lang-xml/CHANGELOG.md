## 6.1.0 (2024-03-06)

### New features

Add an `autoCloseTags` extension that closes tags on typing > or /. Enable it by default in the `xml()` language support.

## 6.0.2 (2023-01-12)

### Bug fixes

Use only the tag name for matching of opening and closing tags.

## 6.0.1 (2022-10-24)

### Bug fixes

Make sure the language object has a name.

## 6.0.0 (2022-06-08)

### Breaking changes

Update dependencies to 6.0.0

## 0.20.0 (2022-04-20)

### Breaking changes

Update dependencies to 0.20.0

## 0.19.2 (2021-09-23)

### New features

Use more specific highlighting tags for attribute names and values.

## 0.19.1 (2021-08-11)

### Bug fixes

Fix incorrect versions for @lezer dependencies.

## 0.19.0 (2021-08-11)

### Breaking changes

Update dependencies to 0.19.0

## 0.18.0 (2021-03-03)

### Breaking changes

Update dependencies to 0.18.

## 0.17.2 (2021-02-12)

### Bug fixes

Export configuration types and `completeFromSchema` function (which were previously accidentally unexported).

## 0.17.1 (2021-01-06)

### New features

The package now also exports a CommonJS module.

## 0.17.0 (2020-12-29)

### Breaking changes

First numbered release.

