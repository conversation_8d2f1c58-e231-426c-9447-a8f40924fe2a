# find-file-up [![NPM version](https://img.shields.io/npm/v/find-file-up.svg?style=flat)](https://www.npmjs.com/package/find-file-up) [![NPM downloads](https://img.shields.io/npm/dm/find-file-up.svg?style=flat)](https://npmjs.org/package/find-file-up) [![Build Status](https://img.shields.io/travis/jonschlinkert/find-file-up.svg?style=flat)](https://travis-ci.org/jonschlinkert/find-file-up)

Find a file, starting with the given cwd and recursively searching up one directory until it's found (or we run out of directories). Async and sync.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save find-file-up
```

## Usage

```js
var findFile = require('find-file-up');
```

**async**

```js
// find `foo.txt` starting at the given directory
findFile('foo.txt', '.', function(err, fp) {
  //=> /Users/<USER>/dev/find-file-up/fixtures/foo.txt
});

// search user home
findFile('foo.txt', '~/', function(err, fp) {
  //=> /Users/<USER>/foo.txt
});
```

**sync**

```js
var file = findFile.sync('foo.txt', 'a/b/c/');
```

## About

### Related projects

* [find-pkg](https://www.npmjs.com/package/find-pkg): Find the first directory with a package.json, recursing up, starting with the given directory. Similar… [more](https://github.com/jonschlinkert/find-pkg) | [homepage](https://github.com/jonschlinkert/find-pkg "Find the first directory with a package.json, recursing up, starting with the given directory. Similar to look-up but does not support globs and only searches for package.json. Async and sync.")
* [findup-sync](https://www.npmjs.com/package/findup-sync): Find the first file matching a given pattern in the current directory or the nearest… [more](https://github.com/cowboy/node-findup-sync) | [homepage](https://github.com/cowboy/node-findup-sync "Find the first file matching a given pattern in the current directory or the nearest ancestor directory.")
* [global-modules](https://www.npmjs.com/package/global-modules): The directory used by npm for globally installed npm modules. | [homepage](https://github.com/jonschlinkert/global-modules "The directory used by npm for globally installed npm modules.")
* [global-prefix](https://www.npmjs.com/package/global-prefix): Get the npm global path prefix. | [homepage](https://github.com/jonschlinkert/global-prefix "Get the npm global path prefix.")
* [load-module-pkg](https://www.npmjs.com/package/load-module-pkg): Load the package.json for any project currently installed in node_modules. | [homepage](https://github.com/jonschlinkert/load-module-pkg "Load the package.json for any project currently installed in node_modules.")
* [load-pkg](https://www.npmjs.com/package/load-pkg): Loads the package.json from the root of the user's current project. | [homepage](https://github.com/jonschlinkert/load-pkg "Loads the package.json from the root of the user's current project.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Building docs

_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_

To generate the readme and API documentation with [verb](https://github.com/verbose/verb):

```sh
$ npm install -g verb verb-generate-readme && verb
```

### Running tests

Install dev dependencies:

```sh
$ npm install -d && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

### License

Copyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT license](https://github.com/jonschlinkert/find-file-up/blob/master/LICENSE).

***

_This file was generated by [verb](https://github.com/verbose/verb), v0.9.0, on July 18, 2016._