{"name": "@swc/jest", "version": "0.2.36", "description": "swc integration for jest", "main": "index.js", "types": "index.d.ts", "homepage": "https://github.com/swc-project/pkgs/tree/main/packages/jest", "keywords": [], "author": "강동윤 <<EMAIL>>", "license": "MIT", "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": {"type": "git", "url": "https://github.com/swc-project/pkgs.git"}, "peerDependencies": {"@swc/core": "*"}, "dependencies": {"@jest/create-cache-key-function": "^29.7.0", "jsonc-parser": "^3.2.0", "@swc/counter": "^0.1.3"}, "devDependencies": {"@jest/transform": "^27.5.1", "@swc/core": "^1.3.107", "@types/node": "^16.11.12", "@typescript-eslint/eslint-plugin": "^5.6.0", "@typescript-eslint/parser": "^5.6.0", "eslint": "^8.4.1", "jest": "^27.4.4", "typescript": "^4.5.3"}, "engines": {"npm": ">= 7.0.0"}, "files": ["index.js", "index.d.ts"], "scripts": {"build": "tsc", "lint": "eslint . --ext .ts"}}