/**
 * @typedef {import('./lib/index.js').CreateContent} CreateContent
 * @typedef {import('./lib/index.js').CreateProperties} CreateProperties
 * @typedef {import('./lib/index.js').CreateRel} CreateRel
 * @typedef {import('./lib/index.js').CreateTarget} CreateTarget
 * @typedef {import('./lib/index.js').Options} Options
 * @typedef {import('./lib/index.js').Target} Target
 */

export {default} from './lib/index.js'
