{"name": "html-whitespace-sensitive-tag-names", "version": "3.0.0", "description": "List of whitespace sensitive HTML tag names", "license": "MIT", "keywords": ["html", "name", "sensitive", "space", "tag", "tagname", "white", "whitespace"], "repository": "https://github.com/rehypejs/rehype-minify/tree/main/packages/html-whitespace-sensitive-tag-names", "bugs": "https://github.com/rehypejs/rehype-minify/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["index.d.ts", "index.js", "lib/"], "scripts": {}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": false}