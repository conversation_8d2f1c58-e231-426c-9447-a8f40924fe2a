# Changelog

## v0.6.2 (2022-04-11)

#### :rocket: Enhancement
* [#292](https://github.com/cli-table/cli-table3/pull/292) Hyperlink support for cells ([@speedytwenty](https://github.com/speedytwenty))
* [#288](https://github.com/cli-table/cli-table3/pull/288) Add debugging capabilities ([@speedytwenty](https://github.com/speedytwenty))
* [#217](https://github.com/cli-table/cli-table3/pull/217) Add wrapOnWordBoundary option for consistently readable word wrapping ([@speedytwenty](https://github.com/speedytwenty))

#### :bug: Bug Fix
* [#290](https://github.com/cli-table/cli-table3/pull/290) Fix to erroneous column truncation when using colSpan ([@speedytwenty](https://github.com/speedytwenty))
* [#287](https://github.com/cli-table/cli-table3/pull/287) Correctly set cell padding for zero values ([@speedytwenty](https://github.com/speedytwenty))
* [#278](https://github.com/cli-table/cli-table3/pull/278) Refactored table layouts + Layout fix for #269 ([@speedytwenty](https://github.com/speedytwenty))

#### :memo: Documentation
* [#286](https://github.com/cli-table/cli-table3/pull/286) Regenerate docs ([@speedytwenty](https://github.com/speedytwenty))

#### :house: Internal
* [#276](https://github.com/cli-table/cli-table3/pull/276) Move CI to Github Actions ([@speedytwenty](https://github.com/speedytwenty))
* [#283](https://github.com/cli-table/cli-table3/pull/283) #196 - Fix to failing unit tests ([@speedytwenty](https://github.com/speedytwenty))

## v0.6.1 (2022-01-09)

* [#251](https://github.com/cli-table/cli-table3/pull/251) Pin `colors` to 1.4.0 ([@JJ](https://github.com/JJ))

## v0.6.0 (2020-03-30)

#### :boom: Breaking Change
* [#156](https://github.com/cli-table/cli-table3/pull/156) Drop support for Node 6 and 8 ([@Turbo87](https://github.com/Turbo87))

#### :bug: Bug Fix
* [#92](https://github.com/cli-table/cli-table3/pull/92) Emoji Length Calculation Fix ([@acupoftee](https://github.com/acupoftee))
* [#53](https://github.com/cli-table/cli-table3/pull/53) "Table" union type definition fix ([@macieklad](https://github.com/macieklad))

#### :memo: Documentation
* [#135](https://github.com/cli-table/cli-table3/pull/135) docs: use https ([@DanielRuf](https://github.com/DanielRuf))

#### :house: Internal
* [#132](https://github.com/cli-table/cli-table3/pull/132) Update lockfile ([@DanielRuf](https://github.com/DanielRuf))
* [#134](https://github.com/cli-table/cli-table3/pull/134) Fix ESLint errors ([@DanielRuf](https://github.com/DanielRuf))
* [#103](https://github.com/cli-table/cli-table3/pull/103) Fix Jest configuration ([@boneskull](https://github.com/boneskull))

#### Committers: 5
- Christopher Hiller ([@boneskull](https://github.com/boneskull))
- Daniel Ruf ([@DanielRuf](https://github.com/DanielRuf))
- Maciej Ładoś ([@macieklad](https://github.com/macieklad))
- Tee ([@acupoftee](https://github.com/acupoftee))
- Tobias Bieniek ([@Turbo87](https://github.com/Turbo87))


## v0.5.1 (2018-07-19)

#### :rocket: Enhancement
* [#21](https://github.com/cli-table/cli-table3/pull/21) Import type definition from `@types/cli-table2` ([@Turbo87](https://github.com/Turbo87))

#### Committers: 1
- Tobias Bieniek ([Turbo87](https://github.com/Turbo87))


## v0.5.0 (2018-06-11)

#### :boom: Breaking Change
* [#2](https://github.com/cli-table/cli-table3/pull/2) Update Node version requirements. ([@Turbo87](https://github.com/Turbo87))

#### :memo: Documentation
* [#11](https://github.com/cli-table/cli-table3/pull/11) Update Documentation. ([@Turbo87](https://github.com/Turbo87))

#### :house: Internal
* [#16](https://github.com/cli-table/cli-table3/pull/16) Replace `kind-of` dependency with `typeof` and `Array.isArray()`. ([@Turbo87](https://github.com/Turbo87))
* [#15](https://github.com/cli-table/cli-table3/pull/15) Remove Gulp. ([@Turbo87](https://github.com/Turbo87))
* [#13](https://github.com/cli-table/cli-table3/pull/13) Use ES6 class syntax and `let/const`. ([@Turbo87](https://github.com/Turbo87))
* [#12](https://github.com/cli-table/cli-table3/pull/12) Add ESLint and Prettier. ([@Turbo87](https://github.com/Turbo87))
* [#10](https://github.com/cli-table/cli-table3/pull/10) chore: use yarn cache. ([@DanielRuf](https://github.com/DanielRuf))
* [#9](https://github.com/cli-table/cli-table3/pull/9) Use Jest for testing. ([@Turbo87](https://github.com/Turbo87))
* [#3](https://github.com/cli-table/cli-table3/pull/3) Add `yarn.lock` file. ([@Turbo87](https://github.com/Turbo87))
* [#1](https://github.com/cli-table/cli-table3/pull/1) Skip broken test. ([@Turbo87](https://github.com/Turbo87))

#### Committers: 2
- Daniel Ruf ([DanielRuf](https://github.com/DanielRuf))
- Tobias Bieniek ([Turbo87](https://github.com/Turbo87))


## v0.4.0 (2018-06-10)

First official release as `cli-table3`. Changes compares to `cli-table2` v0.2.0:

#### :rocket: Enhancement
* [#27](https://github.com/jamestalmage/cli-table2/pull/27) Remove "lodash" dependency. ([@Turbo87](https://github.com/Turbo87))

#### :bug: Bug Fix
* [#29](https://github.com/jamestalmage/cli-table2/pull/29) Fix wordWrap with colSpan. ([@mmurphy](https://github.com/mmurphy))
* [#24](https://github.com/jamestalmage/cli-table2/pull/24) Fixing the runtime error when content is truncated. ([@sthadeshwar](https://github.com/sthadeshwar))

#### :memo: Documentation
* [#41](https://github.com/jamestalmage/cli-table2/pull/41) Create LICENSE. ([@GantMan](https://github.com/GantMan))

#### :house: Internal
* [#26](https://github.com/jamestalmage/cli-table2/pull/26) package.json: Whitelist JS files ([@Turbo87](https://github.com/Turbo87))

#### Committers: 4
- Gant Laborde ([GantMan](https://github.com/GantMan))
- Martin Murphy ([mmurphy](https://github.com/mmurphy))
- Satyajit Thadeshwar ([sthadeshwar](https://github.com/sthadeshwar))
- Tobias Bieniek ([Turbo87](https://github.com/Turbo87))
