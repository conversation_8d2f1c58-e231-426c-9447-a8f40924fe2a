/**
 * marked v7.0.4 - a markdown parser
 * Copyright (c) 2011-2023, <PERSON>. (MIT Licensed)
 * https://github.com/markedjs/marked
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).marked={})}(this,function(t){"use strict";function e(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!1,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!1,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}function n(e){t.defaults=e}t.defaults=e();const s=/[&<>"']/,r=new RegExp(s.source,"g"),i=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,l=new RegExp(i.source,"g"),a={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},o=e=>a[e];function h(e,t){if(t){if(s.test(e))return e.replace(r,o)}else if(i.test(e))return e.replace(l,o);return e}const c=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function m(e){return e.replace(c,(e,t)=>"colon"===(t=t.toLowerCase())?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):"")}const p=/(^|[^\[])\^/g;function u(n,e){n="string"==typeof n?n:n.source,e=e||"";const s={replace:(e,t)=>(t=(t="object"==typeof t&&"source"in t?t.source:t).replace(p,"$1"),n=n.replace(e,t),s),getRegex:()=>new RegExp(n,e)};return s}const g=/[^\w:]/g,d=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function k(e,t,n){if(e){let e;try{e=decodeURIComponent(m(n)).replace(g,"").toLowerCase()}catch(e){return null}if(0===e.indexOf("javascript:")||0===e.indexOf("vbscript:")||0===e.indexOf("data:"))return null}var s;t&&!d.test(n)&&(e=t,t=n,f[" "+e]||(x.test(e)?f[" "+e]=e+"/":f[" "+e]=y(e,"/",!0)),s=-1===(e=f[" "+e]).indexOf(":"),n="//"===t.substring(0,2)?s?t:e.replace(j,"$1")+t:"/"===t.charAt(0)?s?t:e.replace(D,"$1")+t:e+t);try{n=encodeURI(n).replace(/%25/g,"%")}catch(e){return null}return n}const f={},x=/^[^:]+:\/*[^/]*$/,j=/^([^:]+:)[\s\S]*$/,D=/^([^:]+:\/*[^/]*)[\s\S]*$/;var b={exec:()=>null};function w(e,t){var n=e.replace(/\|/g,(e,t,n)=>{let s=!1,r=t;for(;0<=--r&&"\\"===n[r];)s=!s;return s?"|":" |"}).split(/ \|/);let s=0;if(n[0].trim()||n.shift(),0<n.length&&!n[n.length-1].trim()&&n.pop(),t)if(n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;s<n.length;s++)n[s]=n[s].trim().replace(/\\\|/g,"|");return n}function y(e,t,n){var s=e.length;if(0===s)return"";let r=0;for(;r<s;){var i=e.charAt(s-r-1);if((i!==t||n)&&(i===t||!n))break;r++}return e.slice(0,s-r)}function _(e,t,n,s){var r=t.href,t=t.title?h(t.title):null,i=e[1].replace(/\\([\[\]])/g,"$1");return"!"!==e[0].charAt(0)?(s.state.inLink=!0,e={type:"link",raw:n,href:r,title:t,text:i,tokens:s.inlineTokens(i)},s.state.inLink=!1,e):{type:"image",raw:n,href:r,title:t,text:h(i)}}class ${options;rules;lexer;constructor(e){this.options=e||t.defaults}space(e){e=this.rules.block.newline.exec(e);if(e&&0<e[0].length)return{type:"space",raw:e[0]}}code(e){var t,e=this.rules.block.code.exec(e);if(e)return t=e[0].replace(/^ {1,4}/gm,""),{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?t:y(t,"\n")}}fences(e){var t,n,e=this.rules.block.fences.exec(e);if(e)return n=function(e,t){if(null===(e=e.match(/^(\s+)(?:```)/)))return t;const n=e[1];return t.split("\n").map(e=>{var t=e.match(/^\s+/);return null!==t&&([t]=t,t.length>=n.length)?e.slice(n.length):e}).join("\n")}(t=e[0],e[3]||""),{type:"code",raw:t,lang:e[2]&&e[2].trim().replace(this.rules.inline._escapes,"$1"),text:n}}heading(t){var n,t=this.rules.block.heading.exec(t);if(t){let e=t[2].trim();return/#$/.test(e)&&(n=y(e,"#"),!this.options.pedantic&&n&&!/ $/.test(n)||(e=n.trim())),{type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:this.lexer.inline(e)}}}hr(e){e=this.rules.block.hr.exec(e);if(e)return{type:"hr",raw:e[0]}}blockquote(e){var t,n,s,e=this.rules.block.blockquote.exec(e);if(e)return t=e[0].replace(/^ *>[ \t]?/gm,""),n=this.lexer.state.top,this.lexer.state.top=!0,s=this.lexer.blockTokens(t),this.lexer.state.top=n,{type:"blockquote",raw:e[0],tokens:s,text:t}}list(c){let p=this.rules.block.list.exec(c);if(p){let e=p[1].trim();var t,n=1<e.length,u={type:"list",raw:"",ordered:n,start:n?+e.slice(0,-1):"",loose:!1,items:[]},g=(e=n?"\\d{1,9}\\"+e.slice(-1):"\\"+e,this.options.pedantic&&(e=n?e:"[*+-]"),new RegExp(`^( {0,3}${e})((?:[	 ][^\\n]*)?(?:\\n|$))`));let a="",o="",h=!1;for(;c;){let e=!1;if(!(p=g.exec(c)))break;if(this.rules.block.hr.test(c))break;a=p[0],c=c.substring(a.length);let t=p[2].split("\n",1)[0].replace(/^\t+/,e=>" ".repeat(3*e.length)),n=c.split("\n",1)[0],s=0,r=(this.options.pedantic?(s=2,o=t.trimLeft()):(s=4<(s=p[2].search(/[^ ]/))?1:s,o=t.slice(s),s+=p[1].length),!1);if(!t&&/^ *$/.test(n)&&(a+=n+"\n",c=c.substring(n.length+1),e=!0),!e)for(var d=new RegExp(`^ {0,${Math.min(3,s-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),k=new RegExp(`^ {0,${Math.min(3,s-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),f=new RegExp(`^ {0,${Math.min(3,s-1)}}(?:\`\`\`|~~~)`),x=new RegExp(`^ {0,${Math.min(3,s-1)}}#`);c;){var m=c.split("\n",1)[0];if(n=m,this.options.pedantic&&(n=n.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),f.test(n))break;if(x.test(n))break;if(d.test(n))break;if(k.test(c))break;if(n.search(/[^ ]/)>=s||!n.trim())o+="\n"+n.slice(s);else{if(r)break;if(4<=t.search(/[^ ]/))break;if(f.test(t))break;if(x.test(t))break;if(k.test(t))break;o+="\n"+n}r||n.trim()||(r=!0),a+=m+"\n",c=c.substring(m.length+1),t=n.slice(s)}u.loose||(h?u.loose=!0:/\n *\n *$/.test(a)&&(h=!0));let i=null,l;this.options.gfm&&(i=/^\[[ xX]\] /.exec(o))&&(l="[ ] "!==i[0],o=o.replace(/^\[[ xX]\] +/,"")),u.items.push({type:"list_item",raw:a,task:!!i,checked:l,loose:!1,text:o,tokens:[]}),u.raw+=a}u.items[u.items.length-1].raw=a.trimRight(),u.items[u.items.length-1].text=o.trimRight(),u.raw=u.raw.trimRight();for(let e=0;e<u.items.length;e++)this.lexer.state.top=!1,u.items[e].tokens=this.lexer.blockTokens(u.items[e].text,[]),u.loose||(t=0<(t=u.items[e].tokens.filter(e=>"space"===e.type)).length&&t.some(e=>/\n.*\n/.test(e.raw)),u.loose=t);if(u.loose)for(let e=0;e<u.items.length;e++)u.items[e].loose=!0;return u}}html(e){var t,n,e=this.rules.block.html.exec(e);if(e)return t={type:"html",block:!0,raw:e[0],pre:!this.options.sanitizer&&("pre"===e[1]||"script"===e[1]||"style"===e[1]),text:e[0]},this.options.sanitize&&(e=this.options.sanitizer?this.options.sanitizer(e[0]):h(e[0]),(n=t).type="paragraph",n.text=e,n.tokens=this.lexer.inline(e)),t}def(e){var t,n,s,e=this.rules.block.def.exec(e);if(e)return t=e[1].toLowerCase().replace(/\s+/g," "),n=e[2]?e[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",s=e[3]&&e[3].substring(1,e[3].length-1).replace(this.rules.inline._escapes,"$1"),{type:"def",tag:t,raw:e[0],href:n,title:s}}table(e){e=this.rules.block.table.exec(e);if(e){var i={type:"table",raw:e[0],header:w(e[1]).map(e=>({text:e,tokens:[]})),align:e[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:e[3]&&e[3].trim()?e[3].replace(/\n[ \t]*$/,"").split("\n"):[]};if(i.header.length===i.align.length){let e=i.align.length,t,n,s,r;for(t=0;t<e;t++){var l=i.align[t];l&&(/^ *-+: *$/.test(l)?i.align[t]="right":/^ *:-+: *$/.test(l)?i.align[t]="center":/^ *:-+ *$/.test(l)?i.align[t]="left":i.align[t]=null)}for(e=i.rows.length,t=0;t<e;t++)i.rows[t]=w(i.rows[t],i.header.length).map(e=>({text:e,tokens:[]}));for(e=i.header.length,n=0;n<e;n++)i.header[n].tokens=this.lexer.inline(i.header[n].text);for(e=i.rows.length,n=0;n<e;n++)for(r=i.rows[n],s=0;s<r.length;s++)r[s].tokens=this.lexer.inline(r[s].text);return i}}}lheading(e){e=this.rules.block.lheading.exec(e);if(e)return{type:"heading",raw:e[0],depth:"="===e[2].charAt(0)?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(e){var t,e=this.rules.block.paragraph.exec(e);if(e)return t="\n"===e[1].charAt(e[1].length-1)?e[1].slice(0,-1):e[1],{type:"paragraph",raw:e[0],text:t,tokens:this.lexer.inline(t)}}text(e){e=this.rules.block.text.exec(e);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(e){e=this.rules.inline.escape.exec(e);if(e)return{type:"escape",raw:e[0],text:h(e[1])}}tag(e){e=this.rules.inline.tag.exec(e);if(e)return!this.lexer.state.inLink&&/^<a /i.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(e[0]):h(e[0]):e[0]}}link(n){n=this.rules.inline.link.exec(n);if(n){var s=n[2].trim();if(!this.options.pedantic&&/^</.test(s)){if(!/>$/.test(s))return;var r=y(s.slice(0,-1),"\\");if((s.length-r.length)%2==0)return}else{var i,r=function(n,s){if(-1!==n.indexOf(s[1])){let t=0;for(let e=0;e<n.length;e++)if("\\"===n[e])e++;else if(n[e]===s[0])t++;else if(n[e]===s[1]&&--t<0)return e}return-1}(n[2],"()");-1<r&&(i=(0===n[0].indexOf("!")?5:4)+n[1].length+r,n[2]=n[2].substring(0,r),n[0]=n[0].substring(0,i).trim(),n[3]="")}let e=n[2],t="";return this.options.pedantic?(r=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(e))&&(e=r[1],t=r[3]):t=n[3]?n[3].slice(1,-1):"",e=e.trim(),_(n,{href:(e=/^</.test(e)?this.options.pedantic&&!/>$/.test(s)?e.slice(1):e.slice(1,-1):e)&&e.replace(this.rules.inline._escapes,"$1"),title:t&&t.replace(this.rules.inline._escapes,"$1")},n[0],this.lexer)}}reflink(t,n){let s;if(s=(s=this.rules.inline.reflink.exec(t))||this.rules.inline.nolink.exec(t)){let e=(s[2]||s[1]).replace(/\s+/g," ");return(e=n[e.toLowerCase()])?_(s,e,s[0],this.lexer):{type:"text",raw:t=s[0].charAt(0),text:t}}}emStrong(r,i,e=""){let l=this.rules.inline.emStrong.lDelim.exec(r);if(l&&((!l[3]||!e.match(/[\p{L}\p{N}]/u))&&(!(l[1]||l[2]||"")||!e||this.rules.inline.punctuation.exec(e)))){var a=[...l[0]].length-1;let e,t,n=a,s=0;var o,h,c="*"===l[0][0]?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(c.lastIndex=0,i=i.slice(-1*r.length+a);null!=(l=c.exec(i));)if(e=l[1]||l[2]||l[3]||l[4]||l[5]||l[6])if(t=[...e].length,l[3]||l[4])n+=t;else if((l[5]||l[6])&&a%3&&!((a+t)%3))s+=t;else if(!(0<(n-=t)))return t=Math.min(t,t+n+s),o=[...r].slice(0,a+l.index+t+1).join(""),Math.min(a,t)%2?(h=o.slice(1,-1),{type:"em",raw:o,text:h,tokens:this.lexer.inlineTokens(h)}):(h=o.slice(2,-2),{type:"strong",raw:o,text:h,tokens:this.lexer.inlineTokens(h)})}}codespan(t){t=this.rules.inline.code.exec(t);if(t){let e=t[2].replace(/\n/g," ");var n=/[^ ]/.test(e),s=/^ /.test(e)&&/ $/.test(e);return e=h(e=n&&s?e.substring(1,e.length-1):e,!0),{type:"codespan",raw:t[0],text:e}}}br(e){e=this.rules.inline.br.exec(e);if(e)return{type:"br",raw:e[0]}}del(e){e=this.rules.inline.del.exec(e);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(n,s){n=this.rules.inline.autolink.exec(n);if(n){let e,t;return t="@"===n[2]?"mailto:"+(e=h(this.options.mangle?s(n[1]):n[1])):e=h(n[1]),{type:"link",raw:n[0],text:e,href:t,tokens:[{type:"text",raw:e,text:e}]}}}url(e,n){var s,r;if(s=this.rules.inline.url.exec(e)){let e,t;if("@"===s[2])e=h(this.options.mangle?n(s[0]):s[0]),t="mailto:"+e;else{for(;r=s[0],s[0]=this.rules.inline._backpedal.exec(s[0])[0],r!==s[0];);e=h(s[0]),t="www."===s[1]?"http://"+s[0]:s[0]}return{type:"link",raw:s[0],text:e,href:t,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(t,n){t=this.rules.inline.text.exec(t);if(t){let e;return e=this.lexer.state.inRawBlock?this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):h(t[0]):t[0]:h(this.options.smartypants?n(t[0]):t[0]),{type:"text",raw:t[0],text:e}}}}const z={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:b,lheading:/^((?:(?!^bull ).|\n(?!\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/,_label:/(?!\s*\])(?:\\.|[^\[\]\\])+/,_title:/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/},v=(z.def=u(z.def).replace("label",z._label).replace("title",z._title).getRegex(),z.bullet=/(?:[*+-]|\d{1,9}[.)])/,z.listItemStart=u(/^( *)(bull) */).replace("bull",z.bullet).getRegex(),z.list=u(z.list).replace(/bull/g,z.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+z.def.source+")").getRegex(),z._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",z._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,z.html=u(z.html,"i").replace("comment",z._comment).replace("tag",z._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),z.lheading=u(z.lheading).replace(/bull/g,z.bullet).getRegex(),z.paragraph=u(z._paragraph).replace("hr",z.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",z._tag).getRegex(),z.blockquote=u(z.blockquote).replace("paragraph",z.paragraph).getRegex(),z.normal={...z},z.gfm={...z.normal,table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"},z.gfm.table=u(z.gfm.table).replace("hr",z.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",z._tag).getRegex(),z.gfm.paragraph=u(z._paragraph).replace("hr",z.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",z.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",z._tag).getRegex(),z.pedantic={...z.normal,html:u("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",z._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:b,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:u(z.normal._paragraph).replace("hr",z.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",z.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()},{escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:b,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,rDelimAst:/^[^_*]*?__[^_*]*?\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\*)[punct](\*+)(?=[\s]|$)|[^punct\s](\*+)(?!\*)(?=[punct\s]|$)|(?!\*)[punct\s](\*+)(?=[^punct\s])|[\s](\*+)(?!\*)(?=[punct])|(?!\*)[punct](\*+)(?!\*)(?=[punct])|[^punct\s](\*+)(?=[^punct\s])/,rDelimUnd:/^[^_*]*?\*\*[^_*]*?_[^_*]*?(?=\*\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\s]|$)|[^punct\s](_+)(?!_)(?=[punct\s]|$)|(?!_)[punct\s](_+)(?=[^punct\s])|[\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:b,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^((?![*_])[\spunctuation])/});function O(e){return e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function S(t){let n="";for(let e=0;e<t.length;e++){var s=.5<Math.random()?"x"+t.charCodeAt(e).toString(16):t.charCodeAt(e).toString();n+="&#"+s+";"}return n}v._punctuation="\\p{P}$+<=>`^|~",v.punctuation=u(v.punctuation,"u").replace(/punctuation/g,v._punctuation).getRegex(),v.blockSkip=/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,v.anyPunctuation=/\\[punct]/g,v._escapes=/\\([punct])/g,v._comment=u(z._comment).replace("(?:--\x3e|$)","--\x3e").getRegex(),v.emStrong.lDelim=u(v.emStrong.lDelim,"u").replace(/punct/g,v._punctuation).getRegex(),v.emStrong.rDelimAst=u(v.emStrong.rDelimAst,"gu").replace(/punct/g,v._punctuation).getRegex(),v.emStrong.rDelimUnd=u(v.emStrong.rDelimUnd,"gu").replace(/punct/g,v._punctuation).getRegex(),v.anyPunctuation=u(v.anyPunctuation,"gu").replace(/punct/g,v._punctuation).getRegex(),v._escapes=u(v._escapes,"gu").replace(/punct/g,v._punctuation).getRegex(),v._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,v._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,v.autolink=u(v.autolink).replace("scheme",v._scheme).replace("email",v._email).getRegex(),v._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,v.tag=u(v.tag).replace("comment",v._comment).replace("attribute",v._attribute).getRegex(),v._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,v._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/,v._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,v.link=u(v.link).replace("label",v._label).replace("href",v._href).replace("title",v._title).getRegex(),v.reflink=u(v.reflink).replace("label",v._label).replace("ref",z._label).getRegex(),v.nolink=u(v.nolink).replace("ref",z._label).getRegex(),v.reflinkSearch=u(v.reflinkSearch,"g").replace("reflink",v.reflink).replace("nolink",v.nolink).getRegex(),v.normal={...v},v.pedantic={...v.normal,strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:u(/^!?\[(label)\]\((.*?)\)/).replace("label",v._label).getRegex(),reflink:u(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",v._label).getRegex()},v.gfm={...v.normal,escape:u(v.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},v.gfm.url=u(v.gfm.url,"i").replace("email",v.gfm._extended_email).getRegex(),v.breaks={...v.gfm,br:u(v.br).replace("{2,}","*").getRegex(),text:u(v.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()};class T{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||t.defaults,this.options.tokenizer=this.options.tokenizer||new $,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,(this.tokenizer.lexer=this).inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};e={block:z.normal,inline:v.normal};this.options.pedantic?(e.block=z.pedantic,e.inline=v.pedantic):this.options.gfm&&(e.block=z.gfm,this.options.breaks?e.inline=v.breaks:e.inline=v.gfm),this.tokenizer.rules=e}static get rules(){return{block:z,inline:v}}static lex(e,t){return new T(t).lex(e)}static lexInline(e,t){return new T(t).inlineTokens(e)}lex(e){var t;for(e=e.replace(/\r\n|\r/g,"\n"),this.blockTokens(e,this.tokens);t=this.inlineQueue.shift();)this.inlineTokens(t.src,t.tokens);return this.tokens}blockTokens(s,t=[]){s=this.options.pedantic?s.replace(/\t/g,"    ").replace(/^ +$/gm,""):s.replace(/^( *)(\t+)/gm,(e,t,n)=>t+"    ".repeat(n.length));let n,e,r,i;for(;s;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(e=>!!(n=e.call({lexer:this},s,t))&&(s=s.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.space(s))s=s.substring(n.raw.length),1===n.raw.length&&0<t.length?t[t.length-1].raw+="\n":t.push(n);else if(n=this.tokenizer.code(s))s=s.substring(n.raw.length),!(e=t[t.length-1])||"paragraph"!==e.type&&"text"!==e.type?t.push(n):(e.raw+="\n"+n.raw,e.text+="\n"+n.text,this.inlineQueue[this.inlineQueue.length-1].src=e.text);else if(n=this.tokenizer.fences(s))s=s.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(s))s=s.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(s))s=s.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(s))s=s.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(s))s=s.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(s))s=s.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(s))s=s.substring(n.raw.length),!(e=t[t.length-1])||"paragraph"!==e.type&&"text"!==e.type?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(e.raw+="\n"+n.raw,e.text+="\n"+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=e.text);else if(n=this.tokenizer.table(s))s=s.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(s))s=s.substring(n.raw.length),t.push(n);else{if(r=s,this.options.extensions&&this.options.extensions.startBlock){let t=1/0;const a=s.slice(1);let n;this.options.extensions.startBlock.forEach(e=>{"number"==typeof(n=e.call({lexer:this},a))&&0<=n&&(t=Math.min(t,n))}),t<1/0&&0<=t&&(r=s.substring(0,t+1))}if(this.state.top&&(n=this.tokenizer.paragraph(r)))e=t[t.length-1],i&&"paragraph"===e.type?(e.raw+="\n"+n.raw,e.text+="\n"+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=e.text):t.push(n),i=r.length!==s.length,s=s.substring(n.raw.length);else if(n=this.tokenizer.text(s))s=s.substring(n.raw.length),(e=t[t.length-1])&&"text"===e.type?(e.raw+="\n"+n.raw,e.text+="\n"+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=e.text):t.push(n);else if(s){var l="Infinite loop on byte: "+s.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(s,t=[]){let n,e,r,i=s,l,a,o;if(this.tokens.links){var h=Object.keys(this.tokens.links);if(0<h.length)for(;null!=(l=this.tokenizer.rules.inline.reflinkSearch.exec(i));)h.includes(l[0].slice(l[0].lastIndexOf("[")+1,-1))&&(i=i.slice(0,l.index)+"["+"a".repeat(l[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(l=this.tokenizer.rules.inline.blockSkip.exec(i));)i=i.slice(0,l.index)+"["+"a".repeat(l[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(l=this.tokenizer.rules.inline.anyPunctuation.exec(i));)i=i.slice(0,l.index)+"++"+i.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;s;)if(a||(o=""),a=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(e=>!!(n=e.call({lexer:this},s,t))&&(s=s.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.escape(s))s=s.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(s))s=s.substring(n.raw.length),(e=t[t.length-1])&&"text"===n.type&&"text"===e.type?(e.raw+=n.raw,e.text+=n.text):t.push(n);else if(n=this.tokenizer.link(s))s=s.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(s,this.tokens.links))s=s.substring(n.raw.length),(e=t[t.length-1])&&"text"===n.type&&"text"===e.type?(e.raw+=n.raw,e.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(s,i,o))s=s.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(s))s=s.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(s))s=s.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(s))s=s.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(s,S))s=s.substring(n.raw.length),t.push(n);else if(!this.state.inLink&&(n=this.tokenizer.url(s,S)))s=s.substring(n.raw.length),t.push(n);else{if(r=s,this.options.extensions&&this.options.extensions.startInline){let t=1/0;const p=s.slice(1);let n;this.options.extensions.startInline.forEach(e=>{"number"==typeof(n=e.call({lexer:this},p))&&0<=n&&(t=Math.min(t,n))}),t<1/0&&0<=t&&(r=s.substring(0,t+1))}if(n=this.tokenizer.inlineText(r,O))s=s.substring(n.raw.length),"_"!==n.raw.slice(-1)&&(o=n.raw.slice(-1)),a=!0,(e=t[t.length-1])&&"text"===e.type?(e.raw+=n.raw,e.text+=n.text):t.push(n);else if(s){var c="Infinite loop on byte: "+s.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}return t}}class R{options;constructor(e){this.options=e||t.defaults}code(e,t,n){var s,t=(t||"").match(/^\S*/)?.[0];return this.options.highlight&&null!=(s=this.options.highlight(e,t))&&s!==e&&(n=!0,e=s),e=e.replace(/\n$/,"")+"\n",t?'<pre><code class="'+this.options.langPrefix+h(t)+'">'+(n?e:h(e,!0))+"</code></pre>\n":"<pre><code>"+(n?e:h(e,!0))+"</code></pre>\n"}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,n,s){return this.options.headerIds?`<h${t} id="${this.options.headerPrefix+s.slug(n)}">${e}</h${t}>
`:`<h${t}>${e}</h${t}>
`}hr(){return this.options.xhtml?"<hr/>\n":"<hr>\n"}list(e,t,n){var s=t?"ol":"ul";return"<"+s+(t&&1!==n?' start="'+n+'"':"")+">\n"+e+"</"+s+">\n"}listitem(e,t,n){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}paragraph(e){return`<p>${e}</p>
`}table(e,t){return"<table>\n<thead>\n"+e+"</thead>\n"+(t=t&&`<tbody>${t}</tbody>`)+"</table>\n"}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){var n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return this.options.xhtml?"<br/>":"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){var s=k(this.options.sanitize,this.options.baseUrl,e);if(null===s)return n;let r='<a href="'+(e=s)+'"';return t&&(r+=' title="'+t+'"'),r+=">"+n+"</a>"}image(e,t,n){var s=k(this.options.sanitize,this.options.baseUrl,e);if(null===s)return n;let r=`<img src="${e=s}" alt="${n}"`;return t&&(r+=` title="${t}"`),r+=this.options.xhtml?"/>":">"}text(e){return e}}class A{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class I{seen;constructor(){this.seen={}}serialize(e){return e.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}getNextSafeSlug(e,t){let n=e,s=0;if(this.seen.hasOwnProperty(n))for(s=this.seen[e];s++,n=e+"-"+s,this.seen.hasOwnProperty(n););return t||(this.seen[e]=s,this.seen[n]=0),n}slug(e,t={}){e=this.serialize(e);return this.getNextSafeSlug(e,t.dryrun)}}class E{options;renderer;textRenderer;slugger;constructor(e){this.options=e||t.defaults,this.options.renderer=this.options.renderer||new R,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new A,this.slugger=new I}static parse(e,t){return new E(t).parse(e)}static parseInline(e,t){return new E(t).parseInline(e)}parse(s,r=!0){let i="";for(let n=0;n<s.length;n++){var l=s[n];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[l.type]){var e=l,t=this.options.extensions.renderers[e.type].call({parser:this},e);if(!1!==t||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(e.type)){i+=t||"";continue}}switch(l.type){case"space":continue;case"hr":i+=this.renderer.hr();continue;case"heading":var a=l;i+=this.renderer.heading(this.parseInline(a.tokens),a.depth,m(this.parseInline(a.tokens,this.textRenderer)),this.slugger);continue;case"code":i+=this.renderer.code(l.text,l.lang,!!l.escaped);continue;case"table":{var o=l,a="";let t="";for(let e=0;e<o.header.length;e++)t+=this.renderer.tablecell(this.parseInline(o.header[e].tokens),{header:!0,align:o.align[e]});a+=this.renderer.tablerow(t);let n="";for(let e=0;e<o.rows.length;e++){var h=o.rows[e];t="";for(let e=0;e<h.length;e++)t+=this.renderer.tablecell(this.parseInline(h[e].tokens),{header:!1,align:o.align[e]});n+=this.renderer.tablerow(t)}i+=this.renderer.table(a,n);continue}case"blockquote":var c=this.parse(l.tokens);i+=this.renderer.blockquote(c);continue;case"list":{var p=l,c=p.ordered,u=p.start,g=p.loose;let n="";for(let t=0;t<p.items.length;t++){var d,k=p.items[t],f=k.checked,x=k.task;let e="";k.task&&(d=this.renderer.checkbox(!!f),g?0<k.tokens.length&&"paragraph"===k.tokens[0].type?(k.tokens[0].text=d+" "+k.tokens[0].text,k.tokens[0].tokens&&0<k.tokens[0].tokens.length&&"text"===k.tokens[0].tokens[0].type&&(k.tokens[0].tokens[0].text=d+" "+k.tokens[0].tokens[0].text)):k.tokens.unshift({type:"text",text:d}):e+=d),e+=this.parse(k.tokens,g),n+=this.renderer.listitem(e,x,!!f)}i+=this.renderer.list(n,c,u);continue}case"html":i+=this.renderer.html(l.text,l.block);continue;case"paragraph":i+=this.renderer.paragraph(this.parseInline(l.tokens));continue;case"text":{let e=l,t=e.tokens?this.parseInline(e.tokens):e.text;for(;n+1<s.length&&"text"===s[n+1].type;)e=s[++n],t+="\n"+(e.tokens?this.parseInline(e.tokens):e.text);i+=r?this.renderer.paragraph(t):t;continue}default:u='Token with "'+l.type+'" type was not found.';if(this.options.silent)return console.error(u),"";throw new Error(u)}}return i}parseInline(t,n){n=n||this.renderer;let s="";for(let e=0;e<t.length;e++){var r=t[e];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type]){var i=this.options.extensions.renderers[r.type].call({parser:this},r);if(!1!==i||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(r.type)){s+=i||"";continue}}switch(r.type){case"escape":s+=n.text(r.text);break;case"html":s+=n.html(r.text);break;case"link":s+=n.link(r.href,r.title,this.parseInline(r.tokens,n));break;case"image":s+=n.image(r.href,r.title,r.text);break;case"strong":s+=n.strong(this.parseInline(r.tokens,n));break;case"em":s+=n.em(this.parseInline(r.tokens,n));break;case"codespan":s+=n.codespan(r.text);break;case"br":s+=n.br();break;case"del":s+=n.del(this.parseInline(r.tokens,n));break;case"text":s+=n.text(r.text);break;default:var l='Token with "'+r.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}return s}}class P{options;constructor(e){this.options=e||t.defaults}static passThroughHooks=new Set(["preprocess","postprocess"]);preprocess(e){return e}postprocess(e){return e}}class Z{defaults=e();options=this.setOptions;parse=this.#parseMarkdown(T.lex,E.parse);parseInline=this.#parseMarkdown(T.lexInline,E.parseInline);Parser=E;parser=E.parse;Renderer=R;TextRenderer=A;Lexer=T;lexer=T.lex;Tokenizer=$;Slugger=I;Hooks=P;constructor(...e){this.use(...e)}walkTokens(e,t){let n=[];for(const r of e)switch(n=n.concat(t.call(this,r)),r.type){case"table":var s=r;for(const i of s.header)n=n.concat(this.walkTokens(i.tokens,t));for(const l of s.rows)for(const a of l)n=n.concat(this.walkTokens(a.tokens,t));break;case"list":s=r;n=n.concat(this.walkTokens(s.items,t));break;default:{const o=r;this.defaults.extensions?.childTokens?.[o.type]?this.defaults.extensions.childTokens[o.type].forEach(e=>{n=n.concat(this.walkTokens(o[e],t))}):o.tokens&&(n=n.concat(this.walkTokens(o.tokens,t)))}}return n}use(...e){const b=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(e=>{var t={...e};if(t.async=this.defaults.async||t.async||!1,e.extensions&&(e.extensions.forEach(n=>{if(!n.name)throw new Error("extension name required");if("renderer"in n){const s=b.renderers[n.name];s?b.renderers[n.name]=function(...e){let t=n.renderer.apply(this,e);return t=!1===t?s.apply(this,e):t}:b.renderers[n.name]=n.renderer}if("tokenizer"in n){if(!n.level||"block"!==n.level&&"inline"!==n.level)throw new Error("extension level must be 'block' or 'inline'");var e=b[n.level];e?e.unshift(n.tokenizer):b[n.level]=[n.tokenizer],n.start&&("block"===n.level?b.startBlock?b.startBlock.push(n.start):b.startBlock=[n.start]:"inline"===n.level&&(b.startInline?b.startInline.push(n.start):b.startInline=[n.start]))}"childTokens"in n&&n.childTokens&&(b.childTokens[n.name]=n.childTokens)}),t.extensions=b),e.renderer){const i=this.defaults.renderer||new R(this.defaults);for(const l in e.renderer){const a=e.renderer[l];var n=l;const o=i[n];i[n]=(...e)=>{let t=a.apply(i,e);return(t=!1===t?o.apply(i,e):t)||""}}t.renderer=i}if(e.tokenizer){const h=this.defaults.tokenizer||new $(this.defaults);for(const c in e.tokenizer){const p=e.tokenizer[c];var s=c;const u=h[s];h[s]=(...e)=>{let t=p.apply(h,e);return t=!1===t?u.apply(h,e):t}}t.tokenizer=h}if(e.hooks){const g=this.defaults.hooks||new P;for(const d in e.hooks){const k=e.hooks[d];var r=d;const f=g[r];P.passThroughHooks.has(d)?g[r]=e=>{return this.defaults.async?Promise.resolve(k.call(g,e)).then(e=>f.call(g,e)):(e=k.call(g,e),f.call(g,e))}:g[r]=(...e)=>{let t=k.apply(g,e);return t=!1===t?f.apply(g,e):t}}t.hooks=g}if(e.walkTokens){const x=this.defaults.walkTokens,m=e.walkTokens;t.walkTokens=function(e){let t=[];return t.push(m.call(this,e)),t=x?t.concat(x.call(this,e)):t}}this.defaults={...this.defaults,...t}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}#parseMarkdown(c,p){return(t,e,r)=>{"function"==typeof e&&(r=e,e=null);var n,e={...e};const i={...this.defaults,...e},l=(!0===this.defaults.async&&!1===e.async&&(i.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),i.async=!0),this.#onError(!!i.silent,!!i.async,r));if(null==t)return l(new Error("marked(): input parameter is undefined or null"));if("string"!=typeof t)return l(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));if(e=i,n=r,e&&!e.silent&&(n&&console.warn("marked(): callback is deprecated since version 5.0.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/using_pro#async"),(e.sanitize||e.sanitizer)&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options"),!e.highlight&&"language-"===e.langPrefix||console.warn("marked(): highlight and langPrefix parameters are deprecated since version 5.0.0, should not be used and will be removed in the future. Instead use https://www.npmjs.com/package/marked-highlight."),e.mangle&&console.warn("marked(): mangle parameter is enabled by default, but is deprecated since version 5.0.0, and will be removed in the future. To clear this warning, install https://www.npmjs.com/package/marked-mangle, or disable by setting `{mangle: false}`."),e.baseUrl&&console.warn("marked(): baseUrl parameter is deprecated since version 5.0.0, should not be used and will be removed in the future. Instead use https://www.npmjs.com/package/marked-base-url."),e.smartypants&&console.warn("marked(): smartypants parameter is deprecated since version 5.0.0, should not be used and will be removed in the future. Instead use https://www.npmjs.com/package/marked-smartypants."),e.xhtml&&console.warn("marked(): xhtml parameter is deprecated since version 5.0.0, should not be used and will be removed in the future. Instead use https://www.npmjs.com/package/marked-xhtml."),e.headerIds||e.headerPrefix)&&console.warn("marked(): headerIds and headerPrefix parameters enabled by default, but are deprecated since version 5.0.0, and will be removed in the future. To clear this warning, install  https://www.npmjs.com/package/marked-gfm-heading-id, or disable by setting `{headerIds: false}`."),i.hooks&&(i.hooks.options=i),r){const a=r,o=i.highlight;let n;try{i.hooks&&(t=i.hooks.preprocess(t)),n=c(t,i)}catch(e){return l(e)}const h=t=>{let e;if(!t)try{i.walkTokens&&this.walkTokens(n,i.walkTokens),e=p(n,i),i.hooks&&(e=i.hooks.postprocess(e))}catch(e){t=e}return i.highlight=o,t?l(t):a(null,e)};if(!o||o.length<3)return h();if(delete i.highlight,!n.length)return h();let s=0;this.walkTokens(n,n=>{"code"===n.type&&(s++,setTimeout(()=>{o(n.text,n.lang,(e,t)=>{if(e)return h(e);null!=t&&t!==n.text&&(n.text=t,n.escaped=!0),0===--s&&h()})},0))}),void(0===s&&h())}else{if(i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(t):t).then(e=>c(e,i)).then(e=>i.walkTokens?Promise.all(this.walkTokens(e,i.walkTokens)).then(()=>e):e).then(e=>p(e,i)).then(e=>i.hooks?i.hooks.postprocess(e):e).catch(l);try{i.hooks&&(t=i.hooks.preprocess(t));var s=c(t,i);i.walkTokens&&this.walkTokens(s,i.walkTokens);let e=p(s,i);return e=i.hooks?i.hooks.postprocess(e):e}catch(e){return l(e)}}}}#onError(n,s,r){return e=>{var t;if(e.message+="\nPlease report this to https://github.com/markedjs/marked.",n)return t="<p>An error occurred:</p><pre>"+h(e.message+"",!0)+"</pre>",s?Promise.resolve(t):r?void r(null,t):t;if(s)return Promise.reject(e);if(!r)throw e;r(e)}}}const q=new Z;function L(e,t,n){return q.parse(e,t,n)}L.options=L.setOptions=function(e){return q.setOptions(e),n(L.defaults=q.defaults),L},L.getDefaults=e,L.defaults=t.defaults,L.use=function(...e){return q.use(...e),n(L.defaults=q.defaults),L},L.walkTokens=function(e,t){return q.walkTokens(e,t)},L.parseInline=q.parseInline,L.Parser=E,L.parser=E.parse,L.Renderer=R,L.TextRenderer=A,L.Lexer=T,L.lexer=T.lex,L.Tokenizer=$,L.Slugger=I,L.Hooks=P;var b=(L.parse=L).options,C=L.setOptions,B=L.use,Q=L.walkTokens,U=L.parseInline,M=L,H=E.parse,N=T.lex;t.Hooks=P,t.Lexer=T,t.Marked=Z,t.Parser=E,t.Renderer=R,t.Slugger=I,t.TextRenderer=A,t.Tokenizer=$,t.getDefaults=e,t.lexer=N,t.marked=L,t.options=b,t.parse=M,t.parseInline=U,t.parser=H,t.setOptions=C,t.use=B,t.walkTokens=Q});