import {
  init_editor_main
} from "./chunk-3XLLLGPC.js";
import "./chunk-HWTHU6AO.js";
import {
  CancellationTokenSource2 as CancellationTokenSource,
  Emitter2 as Emitter,
  KeyCode,
  KeyMod,
  MarkerSeverity2 as MarkerSeverity,
  MarkerTag,
  Position2 as Position,
  Range2 as Range,
  Selection2 as Selection,
  SelectionDirection,
  Token,
  Uri,
  editor,
  languages
} from "./chunk-FKTPZOQV.js";
import "./chunk-XPZLJQLW.js";
init_editor_main();
export {
  CancellationTokenSource,
  Emitter,
  KeyCode,
  KeyMod,
  MarkerSeverity,
  MarkerTag,
  Position,
  Range,
  Selection,
  SelectionDirection,
  Token,
  Uri,
  editor,
  languages
};
