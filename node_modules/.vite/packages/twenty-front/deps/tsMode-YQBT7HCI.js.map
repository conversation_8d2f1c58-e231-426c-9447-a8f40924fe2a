{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/language/typescript/tsMode.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/language/typescript/workerManager.ts\nvar WorkerManager = class {\n  constructor(_modeId, _defaults) {\n    this._modeId = _modeId;\n    this._defaults = _defaults;\n    this._worker = null;\n    this._client = null;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n    this._updateExtraLibsToken = 0;\n    this._extraLibsChangeListener = this._defaults.onDidExtraLibsChange(\n      () => this._updateExtraLibs()\n    );\n  }\n  dispose() {\n    this._configChangeListener.dispose();\n    this._extraLibsChangeListener.dispose();\n    this._stopWorker();\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  async _updateExtraLibs() {\n    if (!this._worker) {\n      return;\n    }\n    const myToken = ++this._updateExtraLibsToken;\n    const proxy = await this._worker.getProxy();\n    if (this._updateExtraLibsToken !== myToken) {\n      return;\n    }\n    proxy.updateExtraLibs(this._defaults.getExtraLibs());\n  }\n  _getClient() {\n    if (!this._client) {\n      this._client = (async () => {\n        this._worker = monaco_editor_core_exports.editor.createWebWorker({\n          // module that exports the create() method and returns a `TypeScriptWorker` instance\n          moduleId: \"vs/language/typescript/tsWorker\",\n          label: this._modeId,\n          keepIdleModels: true,\n          // passed in to the create() method\n          createData: {\n            compilerOptions: this._defaults.getCompilerOptions(),\n            extraLibs: this._defaults.getExtraLibs(),\n            customWorkerPath: this._defaults.workerOptions.customWorkerPath,\n            inlayHintsOptions: this._defaults.inlayHintsOptions\n          }\n        });\n        if (this._defaults.getEagerModelSync()) {\n          return await this._worker.withSyncedResources(\n            monaco_editor_core_exports.editor.getModels().filter((model) => model.getLanguageId() === this._modeId).map((model) => model.uri)\n          );\n        }\n        return await this._worker.getProxy();\n      })();\n    }\n    return this._client;\n  }\n  async getLanguageServiceWorker(...resources) {\n    const client = await this._getClient();\n    if (this._worker) {\n      await this._worker.withSyncedResources(resources);\n    }\n    return client;\n  }\n};\n\n// src/language/typescript/languageFeatures.ts\nimport {\n  typescriptDefaults\n} from \"./monaco.contribution.js\";\n\n// src/language/typescript/lib/lib.index.ts\nvar libFileSet = {};\nlibFileSet[\"lib.d.ts\"] = true;\nlibFileSet[\"lib.decorators.d.ts\"] = true;\nlibFileSet[\"lib.decorators.legacy.d.ts\"] = true;\nlibFileSet[\"lib.dom.asynciterable.d.ts\"] = true;\nlibFileSet[\"lib.dom.d.ts\"] = true;\nlibFileSet[\"lib.dom.iterable.d.ts\"] = true;\nlibFileSet[\"lib.es2015.collection.d.ts\"] = true;\nlibFileSet[\"lib.es2015.core.d.ts\"] = true;\nlibFileSet[\"lib.es2015.d.ts\"] = true;\nlibFileSet[\"lib.es2015.generator.d.ts\"] = true;\nlibFileSet[\"lib.es2015.iterable.d.ts\"] = true;\nlibFileSet[\"lib.es2015.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2015.proxy.d.ts\"] = true;\nlibFileSet[\"lib.es2015.reflect.d.ts\"] = true;\nlibFileSet[\"lib.es2015.symbol.d.ts\"] = true;\nlibFileSet[\"lib.es2015.symbol.wellknown.d.ts\"] = true;\nlibFileSet[\"lib.es2016.array.include.d.ts\"] = true;\nlibFileSet[\"lib.es2016.d.ts\"] = true;\nlibFileSet[\"lib.es2016.full.d.ts\"] = true;\nlibFileSet[\"lib.es2016.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2017.d.ts\"] = true;\nlibFileSet[\"lib.es2017.date.d.ts\"] = true;\nlibFileSet[\"lib.es2017.full.d.ts\"] = true;\nlibFileSet[\"lib.es2017.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2017.object.d.ts\"] = true;\nlibFileSet[\"lib.es2017.sharedmemory.d.ts\"] = true;\nlibFileSet[\"lib.es2017.string.d.ts\"] = true;\nlibFileSet[\"lib.es2017.typedarrays.d.ts\"] = true;\nlibFileSet[\"lib.es2018.asyncgenerator.d.ts\"] = true;\nlibFileSet[\"lib.es2018.asynciterable.d.ts\"] = true;\nlibFileSet[\"lib.es2018.d.ts\"] = true;\nlibFileSet[\"lib.es2018.full.d.ts\"] = true;\nlibFileSet[\"lib.es2018.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2018.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2018.regexp.d.ts\"] = true;\nlibFileSet[\"lib.es2019.array.d.ts\"] = true;\nlibFileSet[\"lib.es2019.d.ts\"] = true;\nlibFileSet[\"lib.es2019.full.d.ts\"] = true;\nlibFileSet[\"lib.es2019.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2019.object.d.ts\"] = true;\nlibFileSet[\"lib.es2019.string.d.ts\"] = true;\nlibFileSet[\"lib.es2019.symbol.d.ts\"] = true;\nlibFileSet[\"lib.es2020.bigint.d.ts\"] = true;\nlibFileSet[\"lib.es2020.d.ts\"] = true;\nlibFileSet[\"lib.es2020.date.d.ts\"] = true;\nlibFileSet[\"lib.es2020.full.d.ts\"] = true;\nlibFileSet[\"lib.es2020.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2020.number.d.ts\"] = true;\nlibFileSet[\"lib.es2020.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2020.sharedmemory.d.ts\"] = true;\nlibFileSet[\"lib.es2020.string.d.ts\"] = true;\nlibFileSet[\"lib.es2020.symbol.wellknown.d.ts\"] = true;\nlibFileSet[\"lib.es2021.d.ts\"] = true;\nlibFileSet[\"lib.es2021.full.d.ts\"] = true;\nlibFileSet[\"lib.es2021.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2021.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2021.string.d.ts\"] = true;\nlibFileSet[\"lib.es2021.weakref.d.ts\"] = true;\nlibFileSet[\"lib.es2022.array.d.ts\"] = true;\nlibFileSet[\"lib.es2022.d.ts\"] = true;\nlibFileSet[\"lib.es2022.error.d.ts\"] = true;\nlibFileSet[\"lib.es2022.full.d.ts\"] = true;\nlibFileSet[\"lib.es2022.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2022.object.d.ts\"] = true;\nlibFileSet[\"lib.es2022.regexp.d.ts\"] = true;\nlibFileSet[\"lib.es2022.sharedmemory.d.ts\"] = true;\nlibFileSet[\"lib.es2022.string.d.ts\"] = true;\nlibFileSet[\"lib.es2023.array.d.ts\"] = true;\nlibFileSet[\"lib.es2023.collection.d.ts\"] = true;\nlibFileSet[\"lib.es2023.d.ts\"] = true;\nlibFileSet[\"lib.es2023.full.d.ts\"] = true;\nlibFileSet[\"lib.es5.d.ts\"] = true;\nlibFileSet[\"lib.es6.d.ts\"] = true;\nlibFileSet[\"lib.esnext.collection.d.ts\"] = true;\nlibFileSet[\"lib.esnext.d.ts\"] = true;\nlibFileSet[\"lib.esnext.decorators.d.ts\"] = true;\nlibFileSet[\"lib.esnext.disposable.d.ts\"] = true;\nlibFileSet[\"lib.esnext.full.d.ts\"] = true;\nlibFileSet[\"lib.esnext.intl.d.ts\"] = true;\nlibFileSet[\"lib.esnext.object.d.ts\"] = true;\nlibFileSet[\"lib.esnext.promise.d.ts\"] = true;\nlibFileSet[\"lib.scripthost.d.ts\"] = true;\nlibFileSet[\"lib.webworker.asynciterable.d.ts\"] = true;\nlibFileSet[\"lib.webworker.d.ts\"] = true;\nlibFileSet[\"lib.webworker.importscripts.d.ts\"] = true;\nlibFileSet[\"lib.webworker.iterable.d.ts\"] = true;\n\n// src/language/typescript/languageFeatures.ts\nfunction flattenDiagnosticMessageText(diag, newLine, indent = 0) {\n  if (typeof diag === \"string\") {\n    return diag;\n  } else if (diag === void 0) {\n    return \"\";\n  }\n  let result = \"\";\n  if (indent) {\n    result += newLine;\n    for (let i = 0; i < indent; i++) {\n      result += \"  \";\n    }\n  }\n  result += diag.messageText;\n  indent++;\n  if (diag.next) {\n    for (const kid of diag.next) {\n      result += flattenDiagnosticMessageText(kid, newLine, indent);\n    }\n  }\n  return result;\n}\nfunction displayPartsToString(displayParts) {\n  if (displayParts) {\n    return displayParts.map((displayPart) => displayPart.text).join(\"\");\n  }\n  return \"\";\n}\nvar Adapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  // protected _positionToOffset(model: editor.ITextModel, position: monaco.IPosition): number {\n  // \treturn model.getOffsetAt(position);\n  // }\n  // protected _offsetToPosition(model: editor.ITextModel, offset: number): monaco.IPosition {\n  // \treturn model.getPositionAt(offset);\n  // }\n  _textSpanToRange(model, span) {\n    let p1 = model.getPositionAt(span.start);\n    let p2 = model.getPositionAt(span.start + span.length);\n    let { lineNumber: startLineNumber, column: startColumn } = p1;\n    let { lineNumber: endLineNumber, column: endColumn } = p2;\n    return { startLineNumber, startColumn, endLineNumber, endColumn };\n  }\n};\nvar LibFiles = class {\n  constructor(_worker) {\n    this._worker = _worker;\n    this._libFiles = {};\n    this._hasFetchedLibFiles = false;\n    this._fetchLibFilesPromise = null;\n  }\n  isLibFile(uri) {\n    if (!uri) {\n      return false;\n    }\n    if (uri.path.indexOf(\"/lib.\") === 0) {\n      return !!libFileSet[uri.path.slice(1)];\n    }\n    return false;\n  }\n  getOrCreateModel(fileName) {\n    const uri = monaco_editor_core_exports.Uri.parse(fileName);\n    const model = monaco_editor_core_exports.editor.getModel(uri);\n    if (model) {\n      return model;\n    }\n    if (this.isLibFile(uri) && this._hasFetchedLibFiles) {\n      return monaco_editor_core_exports.editor.createModel(this._libFiles[uri.path.slice(1)], \"typescript\", uri);\n    }\n    const matchedLibFile = typescriptDefaults.getExtraLibs()[fileName];\n    if (matchedLibFile) {\n      return monaco_editor_core_exports.editor.createModel(matchedLibFile.content, \"typescript\", uri);\n    }\n    return null;\n  }\n  _containsLibFile(uris) {\n    for (let uri of uris) {\n      if (this.isLibFile(uri)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  async fetchLibFilesIfNecessary(uris) {\n    if (!this._containsLibFile(uris)) {\n      return;\n    }\n    await this._fetchLibFiles();\n  }\n  _fetchLibFiles() {\n    if (!this._fetchLibFilesPromise) {\n      this._fetchLibFilesPromise = this._worker().then((w) => w.getLibFiles()).then((libFiles) => {\n        this._hasFetchedLibFiles = true;\n        this._libFiles = libFiles;\n      });\n    }\n    return this._fetchLibFilesPromise;\n  }\n};\nvar DiagnosticsAdapter = class extends Adapter {\n  constructor(_libFiles, _defaults, _selector, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n    this._defaults = _defaults;\n    this._selector = _selector;\n    this._disposables = [];\n    this._listener = /* @__PURE__ */ Object.create(null);\n    const onModelAdd = (model) => {\n      if (model.getLanguageId() !== _selector) {\n        return;\n      }\n      const maybeValidate = () => {\n        const { onlyVisible } = this._defaults.getDiagnosticsOptions();\n        if (onlyVisible) {\n          if (model.isAttachedToEditor()) {\n            this._doValidate(model);\n          }\n        } else {\n          this._doValidate(model);\n        }\n      };\n      let handle;\n      const changeSubscription = model.onDidChangeContent(() => {\n        clearTimeout(handle);\n        handle = window.setTimeout(maybeValidate, 500);\n      });\n      const visibleSubscription = model.onDidChangeAttached(() => {\n        const { onlyVisible } = this._defaults.getDiagnosticsOptions();\n        if (onlyVisible) {\n          if (model.isAttachedToEditor()) {\n            maybeValidate();\n          } else {\n            monaco_editor_core_exports.editor.setModelMarkers(model, this._selector, []);\n          }\n        }\n      });\n      this._listener[model.uri.toString()] = {\n        dispose() {\n          changeSubscription.dispose();\n          visibleSubscription.dispose();\n          clearTimeout(handle);\n        }\n      };\n      maybeValidate();\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._selector, []);\n      const key = model.uri.toString();\n      if (this._listener[key]) {\n        this._listener[key].dispose();\n        delete this._listener[key];\n      }\n    };\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidCreateModel((model) => onModelAdd(model))\n    );\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        onModelRemoved(event.model);\n        onModelAdd(event.model);\n      })\n    );\n    this._disposables.push({\n      dispose() {\n        for (const model of monaco_editor_core_exports.editor.getModels()) {\n          onModelRemoved(model);\n        }\n      }\n    });\n    const recomputeDiagostics = () => {\n      for (const model of monaco_editor_core_exports.editor.getModels()) {\n        onModelRemoved(model);\n        onModelAdd(model);\n      }\n    };\n    this._disposables.push(this._defaults.onDidChange(recomputeDiagostics));\n    this._disposables.push(this._defaults.onDidExtraLibsChange(recomputeDiagostics));\n    monaco_editor_core_exports.editor.getModels().forEach((model) => onModelAdd(model));\n  }\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables = [];\n  }\n  async _doValidate(model) {\n    const worker = await this._worker(model.uri);\n    if (model.isDisposed()) {\n      return;\n    }\n    const promises = [];\n    const { noSyntaxValidation, noSemanticValidation, noSuggestionDiagnostics } = this._defaults.getDiagnosticsOptions();\n    if (!noSyntaxValidation) {\n      promises.push(worker.getSyntacticDiagnostics(model.uri.toString()));\n    }\n    if (!noSemanticValidation) {\n      promises.push(worker.getSemanticDiagnostics(model.uri.toString()));\n    }\n    if (!noSuggestionDiagnostics) {\n      promises.push(worker.getSuggestionDiagnostics(model.uri.toString()));\n    }\n    const allDiagnostics = await Promise.all(promises);\n    if (!allDiagnostics || model.isDisposed()) {\n      return;\n    }\n    const diagnostics = allDiagnostics.reduce((p, c) => c.concat(p), []).filter(\n      (d) => (this._defaults.getDiagnosticsOptions().diagnosticCodesToIgnore || []).indexOf(d.code) === -1\n    );\n    const relatedUris = diagnostics.map((d) => d.relatedInformation || []).reduce((p, c) => c.concat(p), []).map(\n      (relatedInformation) => relatedInformation.file ? monaco_editor_core_exports.Uri.parse(relatedInformation.file.fileName) : null\n    );\n    await this._libFiles.fetchLibFilesIfNecessary(relatedUris);\n    if (model.isDisposed()) {\n      return;\n    }\n    monaco_editor_core_exports.editor.setModelMarkers(\n      model,\n      this._selector,\n      diagnostics.map((d) => this._convertDiagnostics(model, d))\n    );\n  }\n  _convertDiagnostics(model, diag) {\n    const diagStart = diag.start || 0;\n    const diagLength = diag.length || 1;\n    const { lineNumber: startLineNumber, column: startColumn } = model.getPositionAt(diagStart);\n    const { lineNumber: endLineNumber, column: endColumn } = model.getPositionAt(\n      diagStart + diagLength\n    );\n    const tags = [];\n    if (diag.reportsUnnecessary) {\n      tags.push(monaco_editor_core_exports.MarkerTag.Unnecessary);\n    }\n    if (diag.reportsDeprecated) {\n      tags.push(monaco_editor_core_exports.MarkerTag.Deprecated);\n    }\n    return {\n      severity: this._tsDiagnosticCategoryToMarkerSeverity(diag.category),\n      startLineNumber,\n      startColumn,\n      endLineNumber,\n      endColumn,\n      message: flattenDiagnosticMessageText(diag.messageText, \"\\n\"),\n      code: diag.code.toString(),\n      tags,\n      relatedInformation: this._convertRelatedInformation(model, diag.relatedInformation)\n    };\n  }\n  _convertRelatedInformation(model, relatedInformation) {\n    if (!relatedInformation) {\n      return [];\n    }\n    const result = [];\n    relatedInformation.forEach((info) => {\n      let relatedResource = model;\n      if (info.file) {\n        relatedResource = this._libFiles.getOrCreateModel(info.file.fileName);\n      }\n      if (!relatedResource) {\n        return;\n      }\n      const infoStart = info.start || 0;\n      const infoLength = info.length || 1;\n      const { lineNumber: startLineNumber, column: startColumn } = relatedResource.getPositionAt(infoStart);\n      const { lineNumber: endLineNumber, column: endColumn } = relatedResource.getPositionAt(\n        infoStart + infoLength\n      );\n      result.push({\n        resource: relatedResource.uri,\n        startLineNumber,\n        startColumn,\n        endLineNumber,\n        endColumn,\n        message: flattenDiagnosticMessageText(info.messageText, \"\\n\")\n      });\n    });\n    return result;\n  }\n  _tsDiagnosticCategoryToMarkerSeverity(category) {\n    switch (category) {\n      case 1 /* Error */:\n        return monaco_editor_core_exports.MarkerSeverity.Error;\n      case 3 /* Message */:\n        return monaco_editor_core_exports.MarkerSeverity.Info;\n      case 0 /* Warning */:\n        return monaco_editor_core_exports.MarkerSeverity.Warning;\n      case 2 /* Suggestion */:\n        return monaco_editor_core_exports.MarkerSeverity.Hint;\n    }\n    return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n};\nvar SuggestAdapter = class _SuggestAdapter extends Adapter {\n  get triggerCharacters() {\n    return [\".\"];\n  }\n  async provideCompletionItems(model, position, _context, token) {\n    const wordInfo = model.getWordUntilPosition(position);\n    const wordRange = new monaco_editor_core_exports.Range(\n      position.lineNumber,\n      wordInfo.startColumn,\n      position.lineNumber,\n      wordInfo.endColumn\n    );\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const info = await worker.getCompletionsAtPosition(resource.toString(), offset);\n    if (!info || model.isDisposed()) {\n      return;\n    }\n    const suggestions = info.entries.map((entry) => {\n      let range = wordRange;\n      if (entry.replacementSpan) {\n        const p1 = model.getPositionAt(entry.replacementSpan.start);\n        const p2 = model.getPositionAt(entry.replacementSpan.start + entry.replacementSpan.length);\n        range = new monaco_editor_core_exports.Range(p1.lineNumber, p1.column, p2.lineNumber, p2.column);\n      }\n      const tags = [];\n      if (entry.kindModifiers !== void 0 && entry.kindModifiers.indexOf(\"deprecated\") !== -1) {\n        tags.push(monaco_editor_core_exports.languages.CompletionItemTag.Deprecated);\n      }\n      return {\n        uri: resource,\n        position,\n        offset,\n        range,\n        label: entry.name,\n        insertText: entry.name,\n        sortText: entry.sortText,\n        kind: _SuggestAdapter.convertKind(entry.kind),\n        tags\n      };\n    });\n    return {\n      suggestions\n    };\n  }\n  async resolveCompletionItem(item, token) {\n    const myItem = item;\n    const resource = myItem.uri;\n    const position = myItem.position;\n    const offset = myItem.offset;\n    const worker = await this._worker(resource);\n    const details = await worker.getCompletionEntryDetails(\n      resource.toString(),\n      offset,\n      myItem.label\n    );\n    if (!details) {\n      return myItem;\n    }\n    return {\n      uri: resource,\n      position,\n      label: details.name,\n      kind: _SuggestAdapter.convertKind(details.kind),\n      detail: displayPartsToString(details.displayParts),\n      documentation: {\n        value: _SuggestAdapter.createDocumentationString(details)\n      }\n    };\n  }\n  static convertKind(kind) {\n    switch (kind) {\n      case Kind.primitiveType:\n      case Kind.keyword:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Keyword;\n      case Kind.variable:\n      case Kind.localVariable:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Variable;\n      case Kind.memberVariable:\n      case Kind.memberGetAccessor:\n      case Kind.memberSetAccessor:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Field;\n      case Kind.function:\n      case Kind.memberFunction:\n      case Kind.constructSignature:\n      case Kind.callSignature:\n      case Kind.indexSignature:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Function;\n      case Kind.enum:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Enum;\n      case Kind.module:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Module;\n      case Kind.class:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Class;\n      case Kind.interface:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Interface;\n      case Kind.warning:\n        return monaco_editor_core_exports.languages.CompletionItemKind.File;\n    }\n    return monaco_editor_core_exports.languages.CompletionItemKind.Property;\n  }\n  static createDocumentationString(details) {\n    let documentationString = displayPartsToString(details.documentation);\n    if (details.tags) {\n      for (const tag of details.tags) {\n        documentationString += `\n\n${tagToString(tag)}`;\n      }\n    }\n    return documentationString;\n  }\n};\nfunction tagToString(tag) {\n  let tagLabel = `*@${tag.name}*`;\n  if (tag.name === \"param\" && tag.text) {\n    const [paramName, ...rest] = tag.text;\n    tagLabel += `\\`${paramName.text}\\``;\n    if (rest.length > 0)\n      tagLabel += ` \\u2014 ${rest.map((r) => r.text).join(\" \")}`;\n  } else if (Array.isArray(tag.text)) {\n    tagLabel += ` \\u2014 ${tag.text.map((r) => r.text).join(\" \")}`;\n  } else if (tag.text) {\n    tagLabel += ` \\u2014 ${tag.text}`;\n  }\n  return tagLabel;\n}\nvar SignatureHelpAdapter = class _SignatureHelpAdapter extends Adapter {\n  constructor() {\n    super(...arguments);\n    this.signatureHelpTriggerCharacters = [\"(\", \",\"];\n  }\n  static _toSignatureHelpTriggerReason(context) {\n    switch (context.triggerKind) {\n      case monaco_editor_core_exports.languages.SignatureHelpTriggerKind.TriggerCharacter:\n        if (context.triggerCharacter) {\n          if (context.isRetrigger) {\n            return { kind: \"retrigger\", triggerCharacter: context.triggerCharacter };\n          } else {\n            return { kind: \"characterTyped\", triggerCharacter: context.triggerCharacter };\n          }\n        } else {\n          return { kind: \"invoked\" };\n        }\n      case monaco_editor_core_exports.languages.SignatureHelpTriggerKind.ContentChange:\n        return context.isRetrigger ? { kind: \"retrigger\" } : { kind: \"invoked\" };\n      case monaco_editor_core_exports.languages.SignatureHelpTriggerKind.Invoke:\n      default:\n        return { kind: \"invoked\" };\n    }\n  }\n  async provideSignatureHelp(model, position, token, context) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const info = await worker.getSignatureHelpItems(resource.toString(), offset, {\n      triggerReason: _SignatureHelpAdapter._toSignatureHelpTriggerReason(context)\n    });\n    if (!info || model.isDisposed()) {\n      return;\n    }\n    const ret = {\n      activeSignature: info.selectedItemIndex,\n      activeParameter: info.argumentIndex,\n      signatures: []\n    };\n    info.items.forEach((item) => {\n      const signature = {\n        label: \"\",\n        parameters: []\n      };\n      signature.documentation = {\n        value: displayPartsToString(item.documentation)\n      };\n      signature.label += displayPartsToString(item.prefixDisplayParts);\n      item.parameters.forEach((p, i, a) => {\n        const label = displayPartsToString(p.displayParts);\n        const parameter = {\n          label,\n          documentation: {\n            value: displayPartsToString(p.documentation)\n          }\n        };\n        signature.label += label;\n        signature.parameters.push(parameter);\n        if (i < a.length - 1) {\n          signature.label += displayPartsToString(item.separatorDisplayParts);\n        }\n      });\n      signature.label += displayPartsToString(item.suffixDisplayParts);\n      ret.signatures.push(signature);\n    });\n    return {\n      value: ret,\n      dispose() {\n      }\n    };\n  }\n};\nvar QuickInfoAdapter = class extends Adapter {\n  async provideHover(model, position, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const info = await worker.getQuickInfoAtPosition(resource.toString(), offset);\n    if (!info || model.isDisposed()) {\n      return;\n    }\n    const documentation = displayPartsToString(info.documentation);\n    const tags = info.tags ? info.tags.map((tag) => tagToString(tag)).join(\"  \\n\\n\") : \"\";\n    const contents = displayPartsToString(info.displayParts);\n    return {\n      range: this._textSpanToRange(model, info.textSpan),\n      contents: [\n        {\n          value: \"```typescript\\n\" + contents + \"\\n```\\n\"\n        },\n        {\n          value: documentation + (tags ? \"\\n\\n\" + tags : \"\")\n        }\n      ]\n    };\n  }\n};\nvar DocumentHighlightAdapter = class extends Adapter {\n  async provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const entries = await worker.getDocumentHighlights(resource.toString(), offset, [\n      resource.toString()\n    ]);\n    if (!entries || model.isDisposed()) {\n      return;\n    }\n    return entries.flatMap((entry) => {\n      return entry.highlightSpans.map((highlightSpans) => {\n        return {\n          range: this._textSpanToRange(model, highlightSpans.textSpan),\n          kind: highlightSpans.kind === \"writtenReference\" ? monaco_editor_core_exports.languages.DocumentHighlightKind.Write : monaco_editor_core_exports.languages.DocumentHighlightKind.Text\n        };\n      });\n    });\n  }\n};\nvar DefinitionAdapter = class extends Adapter {\n  constructor(_libFiles, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n  }\n  async provideDefinition(model, position, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const entries = await worker.getDefinitionAtPosition(resource.toString(), offset);\n    if (!entries || model.isDisposed()) {\n      return;\n    }\n    await this._libFiles.fetchLibFilesIfNecessary(\n      entries.map((entry) => monaco_editor_core_exports.Uri.parse(entry.fileName))\n    );\n    if (model.isDisposed()) {\n      return;\n    }\n    const result = [];\n    for (let entry of entries) {\n      const refModel = this._libFiles.getOrCreateModel(entry.fileName);\n      if (refModel) {\n        result.push({\n          uri: refModel.uri,\n          range: this._textSpanToRange(refModel, entry.textSpan)\n        });\n      }\n    }\n    return result;\n  }\n};\nvar ReferenceAdapter = class extends Adapter {\n  constructor(_libFiles, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n  }\n  async provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const entries = await worker.getReferencesAtPosition(resource.toString(), offset);\n    if (!entries || model.isDisposed()) {\n      return;\n    }\n    await this._libFiles.fetchLibFilesIfNecessary(\n      entries.map((entry) => monaco_editor_core_exports.Uri.parse(entry.fileName))\n    );\n    if (model.isDisposed()) {\n      return;\n    }\n    const result = [];\n    for (let entry of entries) {\n      const refModel = this._libFiles.getOrCreateModel(entry.fileName);\n      if (refModel) {\n        result.push({\n          uri: refModel.uri,\n          range: this._textSpanToRange(refModel, entry.textSpan)\n        });\n      }\n    }\n    return result;\n  }\n};\nvar OutlineAdapter = class extends Adapter {\n  async provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const root = await worker.getNavigationTree(resource.toString());\n    if (!root || model.isDisposed()) {\n      return;\n    }\n    const convert = (item, containerLabel) => {\n      const result2 = {\n        name: item.text,\n        detail: \"\",\n        kind: outlineTypeTable[item.kind] || monaco_editor_core_exports.languages.SymbolKind.Variable,\n        range: this._textSpanToRange(model, item.spans[0]),\n        selectionRange: this._textSpanToRange(model, item.spans[0]),\n        tags: [],\n        children: item.childItems?.map((child) => convert(child, item.text)),\n        containerName: containerLabel\n      };\n      return result2;\n    };\n    const result = root.childItems ? root.childItems.map((item) => convert(item)) : [];\n    return result;\n  }\n};\nvar Kind = class {\n  static {\n    this.unknown = \"\";\n  }\n  static {\n    this.keyword = \"keyword\";\n  }\n  static {\n    this.script = \"script\";\n  }\n  static {\n    this.module = \"module\";\n  }\n  static {\n    this.class = \"class\";\n  }\n  static {\n    this.interface = \"interface\";\n  }\n  static {\n    this.type = \"type\";\n  }\n  static {\n    this.enum = \"enum\";\n  }\n  static {\n    this.variable = \"var\";\n  }\n  static {\n    this.localVariable = \"local var\";\n  }\n  static {\n    this.function = \"function\";\n  }\n  static {\n    this.localFunction = \"local function\";\n  }\n  static {\n    this.memberFunction = \"method\";\n  }\n  static {\n    this.memberGetAccessor = \"getter\";\n  }\n  static {\n    this.memberSetAccessor = \"setter\";\n  }\n  static {\n    this.memberVariable = \"property\";\n  }\n  static {\n    this.constructorImplementation = \"constructor\";\n  }\n  static {\n    this.callSignature = \"call\";\n  }\n  static {\n    this.indexSignature = \"index\";\n  }\n  static {\n    this.constructSignature = \"construct\";\n  }\n  static {\n    this.parameter = \"parameter\";\n  }\n  static {\n    this.typeParameter = \"type parameter\";\n  }\n  static {\n    this.primitiveType = \"primitive type\";\n  }\n  static {\n    this.label = \"label\";\n  }\n  static {\n    this.alias = \"alias\";\n  }\n  static {\n    this.const = \"const\";\n  }\n  static {\n    this.let = \"let\";\n  }\n  static {\n    this.warning = \"warning\";\n  }\n};\nvar outlineTypeTable = /* @__PURE__ */ Object.create(null);\noutlineTypeTable[Kind.module] = monaco_editor_core_exports.languages.SymbolKind.Module;\noutlineTypeTable[Kind.class] = monaco_editor_core_exports.languages.SymbolKind.Class;\noutlineTypeTable[Kind.enum] = monaco_editor_core_exports.languages.SymbolKind.Enum;\noutlineTypeTable[Kind.interface] = monaco_editor_core_exports.languages.SymbolKind.Interface;\noutlineTypeTable[Kind.memberFunction] = monaco_editor_core_exports.languages.SymbolKind.Method;\noutlineTypeTable[Kind.memberVariable] = monaco_editor_core_exports.languages.SymbolKind.Property;\noutlineTypeTable[Kind.memberGetAccessor] = monaco_editor_core_exports.languages.SymbolKind.Property;\noutlineTypeTable[Kind.memberSetAccessor] = monaco_editor_core_exports.languages.SymbolKind.Property;\noutlineTypeTable[Kind.variable] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.const] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.localVariable] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.variable] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.function] = monaco_editor_core_exports.languages.SymbolKind.Function;\noutlineTypeTable[Kind.localFunction] = monaco_editor_core_exports.languages.SymbolKind.Function;\nvar FormatHelper = class extends Adapter {\n  static _convertOptions(options) {\n    return {\n      ConvertTabsToSpaces: options.insertSpaces,\n      TabSize: options.tabSize,\n      IndentSize: options.tabSize,\n      IndentStyle: 2 /* Smart */,\n      NewLineCharacter: \"\\n\",\n      InsertSpaceAfterCommaDelimiter: true,\n      InsertSpaceAfterSemicolonInForStatements: true,\n      InsertSpaceBeforeAndAfterBinaryOperators: true,\n      InsertSpaceAfterKeywordsInControlFlowStatements: true,\n      InsertSpaceAfterFunctionKeywordForAnonymousFunctions: true,\n      InsertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis: false,\n      InsertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets: false,\n      InsertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces: false,\n      PlaceOpenBraceOnNewLineForControlBlocks: false,\n      PlaceOpenBraceOnNewLineForFunctions: false\n    };\n  }\n  _convertTextChanges(model, change) {\n    return {\n      text: change.newText,\n      range: this._textSpanToRange(model, change.span)\n    };\n  }\n};\nvar FormatAdapter = class extends FormatHelper {\n  constructor() {\n    super(...arguments);\n    this.canFormatMultipleRanges = false;\n  }\n  async provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    const startOffset = model.getOffsetAt({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const endOffset = model.getOffsetAt({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const edits = await worker.getFormattingEditsForRange(\n      resource.toString(),\n      startOffset,\n      endOffset,\n      FormatHelper._convertOptions(options)\n    );\n    if (!edits || model.isDisposed()) {\n      return;\n    }\n    return edits.map((edit) => this._convertTextChanges(model, edit));\n  }\n};\nvar FormatOnTypeAdapter = class extends FormatHelper {\n  get autoFormatTriggerCharacters() {\n    return [\";\", \"}\", \"\\n\"];\n  }\n  async provideOnTypeFormattingEdits(model, position, ch, options, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const edits = await worker.getFormattingEditsAfterKeystroke(\n      resource.toString(),\n      offset,\n      ch,\n      FormatHelper._convertOptions(options)\n    );\n    if (!edits || model.isDisposed()) {\n      return;\n    }\n    return edits.map((edit) => this._convertTextChanges(model, edit));\n  }\n};\nvar CodeActionAdaptor = class extends FormatHelper {\n  async provideCodeActions(model, range, context, token) {\n    const resource = model.uri;\n    const start = model.getOffsetAt({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const end = model.getOffsetAt({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    const formatOptions = FormatHelper._convertOptions(model.getOptions());\n    const errorCodes = context.markers.filter((m) => m.code).map((m) => m.code).map(Number);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const codeFixes = await worker.getCodeFixesAtPosition(\n      resource.toString(),\n      start,\n      end,\n      errorCodes,\n      formatOptions\n    );\n    if (!codeFixes || model.isDisposed()) {\n      return { actions: [], dispose: () => {\n      } };\n    }\n    const actions = codeFixes.filter((fix) => {\n      return fix.changes.filter((change) => change.isNewFile).length === 0;\n    }).map((fix) => {\n      return this._tsCodeFixActionToMonacoCodeAction(model, context, fix);\n    });\n    return {\n      actions,\n      dispose: () => {\n      }\n    };\n  }\n  _tsCodeFixActionToMonacoCodeAction(model, context, codeFix) {\n    const edits = [];\n    for (const change of codeFix.changes) {\n      for (const textChange of change.textChanges) {\n        edits.push({\n          resource: model.uri,\n          versionId: void 0,\n          textEdit: {\n            range: this._textSpanToRange(model, textChange.span),\n            text: textChange.newText\n          }\n        });\n      }\n    }\n    const action = {\n      title: codeFix.description,\n      edit: { edits },\n      diagnostics: context.markers,\n      kind: \"quickfix\"\n    };\n    return action;\n  }\n};\nvar RenameAdapter = class extends Adapter {\n  constructor(_libFiles, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n  }\n  async provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    const fileName = resource.toString();\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const renameInfo = await worker.getRenameInfo(fileName, offset, {\n      allowRenameOfImportPath: false\n    });\n    if (renameInfo.canRename === false) {\n      return {\n        edits: [],\n        rejectReason: renameInfo.localizedErrorMessage\n      };\n    }\n    if (renameInfo.fileToRename !== void 0) {\n      throw new Error(\"Renaming files is not supported.\");\n    }\n    const renameLocations = await worker.findRenameLocations(\n      fileName,\n      offset,\n      /*strings*/\n      false,\n      /*comments*/\n      false,\n      /*prefixAndSuffix*/\n      false\n    );\n    if (!renameLocations || model.isDisposed()) {\n      return;\n    }\n    const edits = [];\n    for (const renameLocation of renameLocations) {\n      const model2 = this._libFiles.getOrCreateModel(renameLocation.fileName);\n      if (model2) {\n        edits.push({\n          resource: model2.uri,\n          versionId: void 0,\n          textEdit: {\n            range: this._textSpanToRange(model2, renameLocation.textSpan),\n            text: newName\n          }\n        });\n      } else {\n        throw new Error(`Unknown file ${renameLocation.fileName}.`);\n      }\n    }\n    return { edits };\n  }\n};\nvar InlayHintsAdapter = class extends Adapter {\n  async provideInlayHints(model, range, token) {\n    const resource = model.uri;\n    const fileName = resource.toString();\n    const start = model.getOffsetAt({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const end = model.getOffsetAt({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return null;\n    }\n    const tsHints = await worker.provideInlayHints(fileName, start, end);\n    const hints = tsHints.map((hint) => {\n      return {\n        ...hint,\n        label: hint.text,\n        position: model.getPositionAt(hint.position),\n        kind: this._convertHintKind(hint.kind)\n      };\n    });\n    return { hints, dispose: () => {\n    } };\n  }\n  _convertHintKind(kind) {\n    switch (kind) {\n      case \"Parameter\":\n        return monaco_editor_core_exports.languages.InlayHintKind.Parameter;\n      case \"Type\":\n        return monaco_editor_core_exports.languages.InlayHintKind.Type;\n      default:\n        return monaco_editor_core_exports.languages.InlayHintKind.Type;\n    }\n  }\n};\n\n// src/language/typescript/tsMode.ts\nvar javaScriptWorker;\nvar typeScriptWorker;\nfunction setupTypeScript(defaults) {\n  typeScriptWorker = setupMode(defaults, \"typescript\");\n}\nfunction setupJavaScript(defaults) {\n  javaScriptWorker = setupMode(defaults, \"javascript\");\n}\nfunction getJavaScriptWorker() {\n  return new Promise((resolve, reject) => {\n    if (!javaScriptWorker) {\n      return reject(\"JavaScript not registered!\");\n    }\n    resolve(javaScriptWorker);\n  });\n}\nfunction getTypeScriptWorker() {\n  return new Promise((resolve, reject) => {\n    if (!typeScriptWorker) {\n      return reject(\"TypeScript not registered!\");\n    }\n    resolve(typeScriptWorker);\n  });\n}\nfunction setupMode(defaults, modeId) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(modeId, defaults);\n  disposables.push(client);\n  const worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  const libFiles = new LibFiles(worker);\n  function registerProviders() {\n    const { modeConfiguration } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration.completionItems) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCompletionItemProvider(\n          modeId,\n          new SuggestAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.signatureHelp) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerSignatureHelpProvider(\n          modeId,\n          new SignatureHelpAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.hovers) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerHoverProvider(modeId, new QuickInfoAdapter(worker))\n      );\n    }\n    if (modeConfiguration.documentHighlights) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentHighlightProvider(\n          modeId,\n          new DocumentHighlightAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.definitions) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDefinitionProvider(\n          modeId,\n          new DefinitionAdapter(libFiles, worker)\n        )\n      );\n    }\n    if (modeConfiguration.references) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerReferenceProvider(\n          modeId,\n          new ReferenceAdapter(libFiles, worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentSymbols) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n          modeId,\n          new OutlineAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.rename) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerRenameProvider(\n          modeId,\n          new RenameAdapter(libFiles, worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentRangeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n          modeId,\n          new FormatAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.onTypeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerOnTypeFormattingEditProvider(\n          modeId,\n          new FormatOnTypeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.codeActions) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCodeActionProvider(modeId, new CodeActionAdaptor(worker))\n      );\n    }\n    if (modeConfiguration.inlayHints) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerInlayHintsProvider(modeId, new InlayHintsAdapter(worker))\n      );\n    }\n    if (modeConfiguration.diagnostics) {\n      providers.push(new DiagnosticsAdapter(libFiles, defaults, modeId, worker));\n    }\n  }\n  registerProviders();\n  disposables.push(asDisposable(providers));\n  return worker;\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\nexport {\n  Adapter,\n  CodeActionAdaptor,\n  DefinitionAdapter,\n  DiagnosticsAdapter,\n  DocumentHighlightAdapter,\n  FormatAdapter,\n  FormatHelper,\n  FormatOnTypeAdapter,\n  InlayHintsAdapter,\n  Kind,\n  LibFiles,\n  OutlineAdapter,\n  QuickInfoAdapter,\n  ReferenceAdapter,\n  RenameAdapter,\n  SignatureHelpAdapter,\n  SuggestAdapter,\n  WorkerManager,\n  flattenDiagnosticMessageText,\n  getJavaScriptWorker,\n  getTypeScriptWorker,\n  setupJavaScript,\n  setupTypeScript\n};\n"], "mappings": ";;;;;;;;;;;;;AAgMA,SAAS,6BAA6B,MAAM,SAAS,SAAS,GAAG;AAC/D,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT,WAAW,SAAS,QAAQ;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AACb,MAAI,QAAQ;AACV,cAAU;AACV,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,gBAAU;AAAA,IACZ;AAAA,EACF;AACA,YAAU,KAAK;AACf;AACA,MAAI,KAAK,MAAM;AACb,eAAW,OAAO,KAAK,MAAM;AAC3B,gBAAU,6BAA6B,KAAK,SAAS,MAAM;AAAA,IAC7D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,cAAc;AAC1C,MAAI,cAAc;AAChB,WAAO,aAAa,IAAI,CAAC,gBAAgB,YAAY,IAAI,EAAE,KAAK,EAAE;AAAA,EACpE;AACA,SAAO;AACT;AA8XA,SAAS,YAAY,KAAK;AACxB,MAAI,WAAW,KAAK,IAAI,IAAI;AAC5B,MAAI,IAAI,SAAS,WAAW,IAAI,MAAM;AACpC,UAAM,CAAC,WAAW,GAAG,IAAI,IAAI,IAAI;AACjC,gBAAY,KAAK,UAAU,IAAI;AAC/B,QAAI,KAAK,SAAS;AAChB,kBAAY,MAAW,KAAK,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC;AAAA,EAC5D,WAAW,MAAM,QAAQ,IAAI,IAAI,GAAG;AAClC,gBAAY,MAAW,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC;AAAA,EAC9D,WAAW,IAAI,MAAM;AACnB,gBAAY,MAAW,IAAI,IAAI;AAAA,EACjC;AACA,SAAO;AACT;AA0jBA,SAAS,gBAAgB,UAAU;AACjC,qBAAmB,UAAU,UAAU,YAAY;AACrD;AACA,SAAS,gBAAgB,UAAU;AACjC,qBAAmB,UAAU,UAAU,YAAY;AACrD;AACA,SAAS,sBAAsB;AAC7B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,CAAC,kBAAkB;AACrB,aAAO,OAAO,4BAA4B;AAAA,IAC5C;AACA,YAAQ,gBAAgB;AAAA,EAC1B,CAAC;AACH;AACA,SAAS,sBAAsB;AAC7B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,CAAC,kBAAkB;AACrB,aAAO,OAAO,4BAA4B;AAAA,IAC5C;AACA,YAAQ,gBAAgB;AAAA,EAC1B,CAAC;AACH;AACA,SAAS,UAAU,UAAU,QAAQ;AACnC,QAAM,cAAc,CAAC;AACrB,QAAM,YAAY,CAAC;AACnB,QAAM,SAAS,IAAI,cAAc,QAAQ,QAAQ;AACjD,cAAY,KAAK,MAAM;AACvB,QAAM,SAAS,IAAI,SAAS;AAC1B,WAAO,OAAO,yBAAyB,GAAG,IAAI;AAAA,EAChD;AACA,QAAM,WAAW,IAAI,SAAS,MAAM;AACpC,WAAS,oBAAoB;AAC3B,UAAM,EAAE,kBAAkB,IAAI;AAC9B,eAAW,SAAS;AACpB,QAAI,kBAAkB,iBAAiB;AACrC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,eAAe,MAAM;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,QAAI,kBAAkB,eAAe;AACnC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,qBAAqB,MAAM;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AACA,QAAI,kBAAkB,QAAQ;AAC5B,gBAAU;AAAA,QACR,2BAA2B,UAAU,sBAAsB,QAAQ,IAAI,iBAAiB,MAAM,CAAC;AAAA,MACjG;AAAA,IACF;AACA,QAAI,kBAAkB,oBAAoB;AACxC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,yBAAyB,MAAM;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AACA,QAAI,kBAAkB,aAAa;AACjC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,kBAAkB,UAAU,MAAM;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AACA,QAAI,kBAAkB,YAAY;AAChC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,iBAAiB,UAAU,MAAM;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AACA,QAAI,kBAAkB,iBAAiB;AACrC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,eAAe,MAAM;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,QAAI,kBAAkB,QAAQ;AAC5B,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,cAAc,UAAU,MAAM;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AACA,QAAI,kBAAkB,8BAA8B;AAClD,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,cAAc,MAAM;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,QAAI,kBAAkB,uBAAuB;AAC3C,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,oBAAoB,MAAM;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,QAAI,kBAAkB,aAAa;AACjC,gBAAU;AAAA,QACR,2BAA2B,UAAU,2BAA2B,QAAQ,IAAI,kBAAkB,MAAM,CAAC;AAAA,MACvG;AAAA,IACF;AACA,QAAI,kBAAkB,YAAY;AAChC,gBAAU;AAAA,QACR,2BAA2B,UAAU,2BAA2B,QAAQ,IAAI,kBAAkB,MAAM,CAAC;AAAA,MACvG;AAAA,IACF;AACA,QAAI,kBAAkB,aAAa;AACjC,gBAAU,KAAK,IAAI,mBAAmB,UAAU,UAAU,QAAQ,MAAM,CAAC;AAAA,IAC3E;AAAA,EACF;AACA,oBAAkB;AAClB,cAAY,KAAK,aAAa,SAAS,CAAC;AACxC,SAAO;AACT;AACA,SAAS,aAAa,aAAa;AACjC,SAAO,EAAE,SAAS,MAAM,WAAW,WAAW,EAAE;AAClD;AACA,SAAS,WAAW,aAAa;AAC/B,SAAO,YAAY,QAAQ;AACzB,gBAAY,IAAI,EAAE,QAAQ;AAAA,EAC5B;AACF;AAxyCA,IAOI,WACA,kBACA,mBACA,cACA,aAQA,YAGA,4BAKA,eA4EA,YAqHA,SAkBA,UAuDA,oBA+LA,gBAmIA,sBA2EA,kBA4BA,0BAwBA,mBAmCA,kBAmCA,gBA5yBJ,IAw0BI,MAsFA,kBAeA,cA2BA,eA+BA,qBAuBA,mBA8DA,eAyDA,mBAyCA,kBACA;AA/pCJ;AAAA;AAwBA;AA0EA;AA3FA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,eAAW,4BAA4B,kBAAuB;AAI9D,IAAI,gBAAgB,MAAM;AAAA,MACxB,YAAY,SAAS,WAAW;AAC9B,aAAK,UAAU;AACf,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,aAAK,UAAU;AACf,aAAK,wBAAwB,KAAK,UAAU,YAAY,MAAM,KAAK,YAAY,CAAC;AAChF,aAAK,wBAAwB;AAC7B,aAAK,2BAA2B,KAAK,UAAU;AAAA,UAC7C,MAAM,KAAK,iBAAiB;AAAA,QAC9B;AAAA,MACF;AAAA,MACA,UAAU;AACR,aAAK,sBAAsB,QAAQ;AACnC,aAAK,yBAAyB,QAAQ;AACtC,aAAK,YAAY;AAAA,MACnB;AAAA,MACA,cAAc;AACZ,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,QAAQ;AACrB,eAAK,UAAU;AAAA,QACjB;AACA,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,MAAM,mBAAmB;AACvB,YAAI,CAAC,KAAK,SAAS;AACjB;AAAA,QACF;AACA,cAAM,UAAU,EAAE,KAAK;AACvB,cAAM,QAAQ,MAAM,KAAK,QAAQ,SAAS;AAC1C,YAAI,KAAK,0BAA0B,SAAS;AAC1C;AAAA,QACF;AACA,cAAM,gBAAgB,KAAK,UAAU,aAAa,CAAC;AAAA,MACrD;AAAA,MACA,aAAa;AACX,YAAI,CAAC,KAAK,SAAS;AACjB,eAAK,WAAW,YAAY;AAC1B,iBAAK,UAAU,2BAA2B,OAAO,gBAAgB;AAAA;AAAA,cAE/D,UAAU;AAAA,cACV,OAAO,KAAK;AAAA,cACZ,gBAAgB;AAAA;AAAA,cAEhB,YAAY;AAAA,gBACV,iBAAiB,KAAK,UAAU,mBAAmB;AAAA,gBACnD,WAAW,KAAK,UAAU,aAAa;AAAA,gBACvC,kBAAkB,KAAK,UAAU,cAAc;AAAA,gBAC/C,mBAAmB,KAAK,UAAU;AAAA,cACpC;AAAA,YACF,CAAC;AACD,gBAAI,KAAK,UAAU,kBAAkB,GAAG;AACtC,qBAAO,MAAM,KAAK,QAAQ;AAAA,gBACxB,2BAA2B,OAAO,UAAU,EAAE,OAAO,CAAC,UAAU,MAAM,cAAc,MAAM,KAAK,OAAO,EAAE,IAAI,CAAC,UAAU,MAAM,GAAG;AAAA,cAClI;AAAA,YACF;AACA,mBAAO,MAAM,KAAK,QAAQ,SAAS;AAAA,UACrC,GAAG;AAAA,QACL;AACA,eAAO,KAAK;AAAA,MACd;AAAA,MACA,MAAM,4BAA4B,WAAW;AAC3C,cAAM,SAAS,MAAM,KAAK,WAAW;AACrC,YAAI,KAAK,SAAS;AAChB,gBAAM,KAAK,QAAQ,oBAAoB,SAAS;AAAA,QAClD;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAQA,IAAI,aAAa,CAAC;AAClB,eAAW,UAAU,IAAI;AACzB,eAAW,qBAAqB,IAAI;AACpC,eAAW,4BAA4B,IAAI;AAC3C,eAAW,4BAA4B,IAAI;AAC3C,eAAW,cAAc,IAAI;AAC7B,eAAW,uBAAuB,IAAI;AACtC,eAAW,4BAA4B,IAAI;AAC3C,eAAW,sBAAsB,IAAI;AACrC,eAAW,iBAAiB,IAAI;AAChC,eAAW,2BAA2B,IAAI;AAC1C,eAAW,0BAA0B,IAAI;AACzC,eAAW,yBAAyB,IAAI;AACxC,eAAW,uBAAuB,IAAI;AACtC,eAAW,yBAAyB,IAAI;AACxC,eAAW,wBAAwB,IAAI;AACvC,eAAW,kCAAkC,IAAI;AACjD,eAAW,+BAA+B,IAAI;AAC9C,eAAW,iBAAiB,IAAI;AAChC,eAAW,sBAAsB,IAAI;AACrC,eAAW,sBAAsB,IAAI;AACrC,eAAW,iBAAiB,IAAI;AAChC,eAAW,sBAAsB,IAAI;AACrC,eAAW,sBAAsB,IAAI;AACrC,eAAW,sBAAsB,IAAI;AACrC,eAAW,wBAAwB,IAAI;AACvC,eAAW,8BAA8B,IAAI;AAC7C,eAAW,wBAAwB,IAAI;AACvC,eAAW,6BAA6B,IAAI;AAC5C,eAAW,gCAAgC,IAAI;AAC/C,eAAW,+BAA+B,IAAI;AAC9C,eAAW,iBAAiB,IAAI;AAChC,eAAW,sBAAsB,IAAI;AACrC,eAAW,sBAAsB,IAAI;AACrC,eAAW,yBAAyB,IAAI;AACxC,eAAW,wBAAwB,IAAI;AACvC,eAAW,uBAAuB,IAAI;AACtC,eAAW,iBAAiB,IAAI;AAChC,eAAW,sBAAsB,IAAI;AACrC,eAAW,sBAAsB,IAAI;AACrC,eAAW,wBAAwB,IAAI;AACvC,eAAW,wBAAwB,IAAI;AACvC,eAAW,wBAAwB,IAAI;AACvC,eAAW,wBAAwB,IAAI;AACvC,eAAW,iBAAiB,IAAI;AAChC,eAAW,sBAAsB,IAAI;AACrC,eAAW,sBAAsB,IAAI;AACrC,eAAW,sBAAsB,IAAI;AACrC,eAAW,wBAAwB,IAAI;AACvC,eAAW,yBAAyB,IAAI;AACxC,eAAW,8BAA8B,IAAI;AAC7C,eAAW,wBAAwB,IAAI;AACvC,eAAW,kCAAkC,IAAI;AACjD,eAAW,iBAAiB,IAAI;AAChC,eAAW,sBAAsB,IAAI;AACrC,eAAW,sBAAsB,IAAI;AACrC,eAAW,yBAAyB,IAAI;AACxC,eAAW,wBAAwB,IAAI;AACvC,eAAW,yBAAyB,IAAI;AACxC,eAAW,uBAAuB,IAAI;AACtC,eAAW,iBAAiB,IAAI;AAChC,eAAW,uBAAuB,IAAI;AACtC,eAAW,sBAAsB,IAAI;AACrC,eAAW,sBAAsB,IAAI;AACrC,eAAW,wBAAwB,IAAI;AACvC,eAAW,wBAAwB,IAAI;AACvC,eAAW,8BAA8B,IAAI;AAC7C,eAAW,wBAAwB,IAAI;AACvC,eAAW,uBAAuB,IAAI;AACtC,eAAW,4BAA4B,IAAI;AAC3C,eAAW,iBAAiB,IAAI;AAChC,eAAW,sBAAsB,IAAI;AACrC,eAAW,cAAc,IAAI;AAC7B,eAAW,cAAc,IAAI;AAC7B,eAAW,4BAA4B,IAAI;AAC3C,eAAW,iBAAiB,IAAI;AAChC,eAAW,4BAA4B,IAAI;AAC3C,eAAW,4BAA4B,IAAI;AAC3C,eAAW,sBAAsB,IAAI;AACrC,eAAW,sBAAsB,IAAI;AACrC,eAAW,wBAAwB,IAAI;AACvC,eAAW,yBAAyB,IAAI;AACxC,eAAW,qBAAqB,IAAI;AACpC,eAAW,kCAAkC,IAAI;AACjD,eAAW,oBAAoB,IAAI;AACnC,eAAW,kCAAkC,IAAI;AACjD,eAAW,6BAA6B,IAAI;AA+B5C,IAAI,UAAU,MAAM;AAAA,MAClB,YAAY,SAAS;AACnB,aAAK,UAAU;AAAA,MACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,iBAAiB,OAAO,MAAM;AAC5B,YAAI,KAAK,MAAM,cAAc,KAAK,KAAK;AACvC,YAAI,KAAK,MAAM,cAAc,KAAK,QAAQ,KAAK,MAAM;AACrD,YAAI,EAAE,YAAY,iBAAiB,QAAQ,YAAY,IAAI;AAC3D,YAAI,EAAE,YAAY,eAAe,QAAQ,UAAU,IAAI;AACvD,eAAO,EAAE,iBAAiB,aAAa,eAAe,UAAU;AAAA,MAClE;AAAA,IACF;AACA,IAAI,WAAW,MAAM;AAAA,MACnB,YAAY,SAAS;AACnB,aAAK,UAAU;AACf,aAAK,YAAY,CAAC;AAClB,aAAK,sBAAsB;AAC3B,aAAK,wBAAwB;AAAA,MAC/B;AAAA,MACA,UAAU,KAAK;AACb,YAAI,CAAC,KAAK;AACR,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,KAAK,QAAQ,OAAO,MAAM,GAAG;AACnC,iBAAO,CAAC,CAAC,WAAW,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,QACvC;AACA,eAAO;AAAA,MACT;AAAA,MACA,iBAAiB,UAAU;AACzB,cAAM,MAAM,2BAA2B,IAAI,MAAM,QAAQ;AACzD,cAAM,QAAQ,2BAA2B,OAAO,SAAS,GAAG;AAC5D,YAAI,OAAO;AACT,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,UAAU,GAAG,KAAK,KAAK,qBAAqB;AACnD,iBAAO,2BAA2B,OAAO,YAAY,KAAK,UAAU,IAAI,KAAK,MAAM,CAAC,CAAC,GAAG,cAAc,GAAG;AAAA,QAC3G;AACA,cAAM,iBAAiB,mBAAmB,aAAa,EAAE,QAAQ;AACjE,YAAI,gBAAgB;AAClB,iBAAO,2BAA2B,OAAO,YAAY,eAAe,SAAS,cAAc,GAAG;AAAA,QAChG;AACA,eAAO;AAAA,MACT;AAAA,MACA,iBAAiB,MAAM;AACrB,iBAAS,OAAO,MAAM;AACpB,cAAI,KAAK,UAAU,GAAG,GAAG;AACvB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MACA,MAAM,yBAAyB,MAAM;AACnC,YAAI,CAAC,KAAK,iBAAiB,IAAI,GAAG;AAChC;AAAA,QACF;AACA,cAAM,KAAK,eAAe;AAAA,MAC5B;AAAA,MACA,iBAAiB;AACf,YAAI,CAAC,KAAK,uBAAuB;AAC/B,eAAK,wBAAwB,KAAK,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,YAAY,CAAC,EAAE,KAAK,CAAC,aAAa;AAC1F,iBAAK,sBAAsB;AAC3B,iBAAK,YAAY;AAAA,UACnB,CAAC;AAAA,QACH;AACA,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AACA,IAAI,qBAAqB,cAAc,QAAQ;AAAA,MAC7C,YAAY,WAAW,WAAW,WAAW,QAAQ;AACnD,cAAM,MAAM;AACZ,aAAK,YAAY;AACjB,aAAK,YAAY;AACjB,aAAK,YAAY;AACjB,aAAK,eAAe,CAAC;AACrB,aAAK,YAA4B,uBAAO,OAAO,IAAI;AACnD,cAAM,aAAa,CAAC,UAAU;AAC5B,cAAI,MAAM,cAAc,MAAM,WAAW;AACvC;AAAA,UACF;AACA,gBAAM,gBAAgB,MAAM;AAC1B,kBAAM,EAAE,YAAY,IAAI,KAAK,UAAU,sBAAsB;AAC7D,gBAAI,aAAa;AACf,kBAAI,MAAM,mBAAmB,GAAG;AAC9B,qBAAK,YAAY,KAAK;AAAA,cACxB;AAAA,YACF,OAAO;AACL,mBAAK,YAAY,KAAK;AAAA,YACxB;AAAA,UACF;AACA,cAAI;AACJ,gBAAM,qBAAqB,MAAM,mBAAmB,MAAM;AACxD,yBAAa,MAAM;AACnB,qBAAS,OAAO,WAAW,eAAe,GAAG;AAAA,UAC/C,CAAC;AACD,gBAAM,sBAAsB,MAAM,oBAAoB,MAAM;AAC1D,kBAAM,EAAE,YAAY,IAAI,KAAK,UAAU,sBAAsB;AAC7D,gBAAI,aAAa;AACf,kBAAI,MAAM,mBAAmB,GAAG;AAC9B,8BAAc;AAAA,cAChB,OAAO;AACL,2CAA2B,OAAO,gBAAgB,OAAO,KAAK,WAAW,CAAC,CAAC;AAAA,cAC7E;AAAA,YACF;AAAA,UACF,CAAC;AACD,eAAK,UAAU,MAAM,IAAI,SAAS,CAAC,IAAI;AAAA,YACrC,UAAU;AACR,iCAAmB,QAAQ;AAC3B,kCAAoB,QAAQ;AAC5B,2BAAa,MAAM;AAAA,YACrB;AAAA,UACF;AACA,wBAAc;AAAA,QAChB;AACA,cAAM,iBAAiB,CAAC,UAAU;AAChC,qCAA2B,OAAO,gBAAgB,OAAO,KAAK,WAAW,CAAC,CAAC;AAC3E,gBAAM,MAAM,MAAM,IAAI,SAAS;AAC/B,cAAI,KAAK,UAAU,GAAG,GAAG;AACvB,iBAAK,UAAU,GAAG,EAAE,QAAQ;AAC5B,mBAAO,KAAK,UAAU,GAAG;AAAA,UAC3B;AAAA,QACF;AACA,aAAK,aAAa;AAAA,UAChB,2BAA2B,OAAO,iBAAiB,CAAC,UAAU,WAAW,KAAK,CAAC;AAAA,QACjF;AACA,aAAK,aAAa,KAAK,2BAA2B,OAAO,mBAAmB,cAAc,CAAC;AAC3F,aAAK,aAAa;AAAA,UAChB,2BAA2B,OAAO,yBAAyB,CAAC,UAAU;AACpE,2BAAe,MAAM,KAAK;AAC1B,uBAAW,MAAM,KAAK;AAAA,UACxB,CAAC;AAAA,QACH;AACA,aAAK,aAAa,KAAK;AAAA,UACrB,UAAU;AACR,uBAAW,SAAS,2BAA2B,OAAO,UAAU,GAAG;AACjE,6BAAe,KAAK;AAAA,YACtB;AAAA,UACF;AAAA,QACF,CAAC;AACD,cAAM,sBAAsB,MAAM;AAChC,qBAAW,SAAS,2BAA2B,OAAO,UAAU,GAAG;AACjE,2BAAe,KAAK;AACpB,uBAAW,KAAK;AAAA,UAClB;AAAA,QACF;AACA,aAAK,aAAa,KAAK,KAAK,UAAU,YAAY,mBAAmB,CAAC;AACtE,aAAK,aAAa,KAAK,KAAK,UAAU,qBAAqB,mBAAmB,CAAC;AAC/E,mCAA2B,OAAO,UAAU,EAAE,QAAQ,CAAC,UAAU,WAAW,KAAK,CAAC;AAAA,MACpF;AAAA,MACA,UAAU;AACR,aAAK,aAAa,QAAQ,CAAC,MAAM,KAAK,EAAE,QAAQ,CAAC;AACjD,aAAK,eAAe,CAAC;AAAA,MACvB;AAAA,MACA,MAAM,YAAY,OAAO;AACvB,cAAM,SAAS,MAAM,KAAK,QAAQ,MAAM,GAAG;AAC3C,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,WAAW,CAAC;AAClB,cAAM,EAAE,oBAAoB,sBAAsB,wBAAwB,IAAI,KAAK,UAAU,sBAAsB;AACnH,YAAI,CAAC,oBAAoB;AACvB,mBAAS,KAAK,OAAO,wBAAwB,MAAM,IAAI,SAAS,CAAC,CAAC;AAAA,QACpE;AACA,YAAI,CAAC,sBAAsB;AACzB,mBAAS,KAAK,OAAO,uBAAuB,MAAM,IAAI,SAAS,CAAC,CAAC;AAAA,QACnE;AACA,YAAI,CAAC,yBAAyB;AAC5B,mBAAS,KAAK,OAAO,yBAAyB,MAAM,IAAI,SAAS,CAAC,CAAC;AAAA,QACrE;AACA,cAAM,iBAAiB,MAAM,QAAQ,IAAI,QAAQ;AACjD,YAAI,CAAC,kBAAkB,MAAM,WAAW,GAAG;AACzC;AAAA,QACF;AACA,cAAM,cAAc,eAAe,OAAO,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;AAAA,UACnE,CAAC,OAAO,KAAK,UAAU,sBAAsB,EAAE,2BAA2B,CAAC,GAAG,QAAQ,EAAE,IAAI,MAAM;AAAA,QACpG;AACA,cAAM,cAAc,YAAY,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;AAAA,UACvG,CAAC,uBAAuB,mBAAmB,OAAO,2BAA2B,IAAI,MAAM,mBAAmB,KAAK,QAAQ,IAAI;AAAA,QAC7H;AACA,cAAM,KAAK,UAAU,yBAAyB,WAAW;AACzD,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,mCAA2B,OAAO;AAAA,UAChC;AAAA,UACA,KAAK;AAAA,UACL,YAAY,IAAI,CAAC,MAAM,KAAK,oBAAoB,OAAO,CAAC,CAAC;AAAA,QAC3D;AAAA,MACF;AAAA,MACA,oBAAoB,OAAO,MAAM;AAC/B,cAAM,YAAY,KAAK,SAAS;AAChC,cAAM,aAAa,KAAK,UAAU;AAClC,cAAM,EAAE,YAAY,iBAAiB,QAAQ,YAAY,IAAI,MAAM,cAAc,SAAS;AAC1F,cAAM,EAAE,YAAY,eAAe,QAAQ,UAAU,IAAI,MAAM;AAAA,UAC7D,YAAY;AAAA,QACd;AACA,cAAM,OAAO,CAAC;AACd,YAAI,KAAK,oBAAoB;AAC3B,eAAK,KAAK,2BAA2B,UAAU,WAAW;AAAA,QAC5D;AACA,YAAI,KAAK,mBAAmB;AAC1B,eAAK,KAAK,2BAA2B,UAAU,UAAU;AAAA,QAC3D;AACA,eAAO;AAAA,UACL,UAAU,KAAK,sCAAsC,KAAK,QAAQ;AAAA,UAClE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,6BAA6B,KAAK,aAAa,IAAI;AAAA,UAC5D,MAAM,KAAK,KAAK,SAAS;AAAA,UACzB;AAAA,UACA,oBAAoB,KAAK,2BAA2B,OAAO,KAAK,kBAAkB;AAAA,QACpF;AAAA,MACF;AAAA,MACA,2BAA2B,OAAO,oBAAoB;AACpD,YAAI,CAAC,oBAAoB;AACvB,iBAAO,CAAC;AAAA,QACV;AACA,cAAM,SAAS,CAAC;AAChB,2BAAmB,QAAQ,CAAC,SAAS;AACnC,cAAI,kBAAkB;AACtB,cAAI,KAAK,MAAM;AACb,8BAAkB,KAAK,UAAU,iBAAiB,KAAK,KAAK,QAAQ;AAAA,UACtE;AACA,cAAI,CAAC,iBAAiB;AACpB;AAAA,UACF;AACA,gBAAM,YAAY,KAAK,SAAS;AAChC,gBAAM,aAAa,KAAK,UAAU;AAClC,gBAAM,EAAE,YAAY,iBAAiB,QAAQ,YAAY,IAAI,gBAAgB,cAAc,SAAS;AACpG,gBAAM,EAAE,YAAY,eAAe,QAAQ,UAAU,IAAI,gBAAgB;AAAA,YACvE,YAAY;AAAA,UACd;AACA,iBAAO,KAAK;AAAA,YACV,UAAU,gBAAgB;AAAA,YAC1B;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAAS,6BAA6B,KAAK,aAAa,IAAI;AAAA,UAC9D,CAAC;AAAA,QACH,CAAC;AACD,eAAO;AAAA,MACT;AAAA,MACA,sCAAsC,UAAU;AAC9C,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO,2BAA2B,eAAe;AAAA,UACnD,KAAK;AACH,mBAAO,2BAA2B,eAAe;AAAA,UACnD,KAAK;AACH,mBAAO,2BAA2B,eAAe;AAAA,UACnD,KAAK;AACH,mBAAO,2BAA2B,eAAe;AAAA,QACrD;AACA,eAAO,2BAA2B,eAAe;AAAA,MACnD;AAAA,IACF;AACA,IAAI,iBAAiB,MAAM,wBAAwB,QAAQ;AAAA,MACzD,IAAI,oBAAoB;AACtB,eAAO,CAAC,GAAG;AAAA,MACb;AAAA,MACA,MAAM,uBAAuB,OAAO,UAAU,UAAU,OAAO;AAC7D,cAAM,WAAW,MAAM,qBAAqB,QAAQ;AACpD,cAAM,YAAY,IAAI,2BAA2B;AAAA,UAC/C,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,QACX;AACA,cAAM,WAAW,MAAM;AACvB,cAAM,SAAS,MAAM,YAAY,QAAQ;AACzC,cAAM,SAAS,MAAM,KAAK,QAAQ,QAAQ;AAC1C,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,OAAO,MAAM,OAAO,yBAAyB,SAAS,SAAS,GAAG,MAAM;AAC9E,YAAI,CAAC,QAAQ,MAAM,WAAW,GAAG;AAC/B;AAAA,QACF;AACA,cAAM,cAAc,KAAK,QAAQ,IAAI,CAAC,UAAU;AAC9C,cAAI,QAAQ;AACZ,cAAI,MAAM,iBAAiB;AACzB,kBAAM,KAAK,MAAM,cAAc,MAAM,gBAAgB,KAAK;AAC1D,kBAAM,KAAK,MAAM,cAAc,MAAM,gBAAgB,QAAQ,MAAM,gBAAgB,MAAM;AACzF,oBAAQ,IAAI,2BAA2B,MAAM,GAAG,YAAY,GAAG,QAAQ,GAAG,YAAY,GAAG,MAAM;AAAA,UACjG;AACA,gBAAM,OAAO,CAAC;AACd,cAAI,MAAM,kBAAkB,UAAU,MAAM,cAAc,QAAQ,YAAY,MAAM,IAAI;AACtF,iBAAK,KAAK,2BAA2B,UAAU,kBAAkB,UAAU;AAAA,UAC7E;AACA,iBAAO;AAAA,YACL,KAAK;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA,OAAO,MAAM;AAAA,YACb,YAAY,MAAM;AAAA,YAClB,UAAU,MAAM;AAAA,YAChB,MAAM,gBAAgB,YAAY,MAAM,IAAI;AAAA,YAC5C;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAO;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM,sBAAsB,MAAM,OAAO;AACvC,cAAM,SAAS;AACf,cAAM,WAAW,OAAO;AACxB,cAAM,WAAW,OAAO;AACxB,cAAM,SAAS,OAAO;AACtB,cAAM,SAAS,MAAM,KAAK,QAAQ,QAAQ;AAC1C,cAAM,UAAU,MAAM,OAAO;AAAA,UAC3B,SAAS,SAAS;AAAA,UAClB;AAAA,UACA,OAAO;AAAA,QACT;AACA,YAAI,CAAC,SAAS;AACZ,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA,OAAO,QAAQ;AAAA,UACf,MAAM,gBAAgB,YAAY,QAAQ,IAAI;AAAA,UAC9C,QAAQ,qBAAqB,QAAQ,YAAY;AAAA,UACjD,eAAe;AAAA,YACb,OAAO,gBAAgB,0BAA0B,OAAO;AAAA,UAC1D;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAO,YAAY,MAAM;AACvB,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AACR,mBAAO,2BAA2B,UAAU,mBAAmB;AAAA,UACjE,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AACR,mBAAO,2BAA2B,UAAU,mBAAmB;AAAA,UACjE,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AACR,mBAAO,2BAA2B,UAAU,mBAAmB;AAAA,UACjE,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AACR,mBAAO,2BAA2B,UAAU,mBAAmB;AAAA,UACjE,KAAK,KAAK;AACR,mBAAO,2BAA2B,UAAU,mBAAmB;AAAA,UACjE,KAAK,KAAK;AACR,mBAAO,2BAA2B,UAAU,mBAAmB;AAAA,UACjE,KAAK,KAAK;AACR,mBAAO,2BAA2B,UAAU,mBAAmB;AAAA,UACjE,KAAK,KAAK;AACR,mBAAO,2BAA2B,UAAU,mBAAmB;AAAA,UACjE,KAAK,KAAK;AACR,mBAAO,2BAA2B,UAAU,mBAAmB;AAAA,QACnE;AACA,eAAO,2BAA2B,UAAU,mBAAmB;AAAA,MACjE;AAAA,MACA,OAAO,0BAA0B,SAAS;AACxC,YAAI,sBAAsB,qBAAqB,QAAQ,aAAa;AACpE,YAAI,QAAQ,MAAM;AAChB,qBAAW,OAAO,QAAQ,MAAM;AAC9B,mCAAuB;AAAA;AAAA,EAE7B,YAAY,GAAG,CAAC;AAAA,UACZ;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAeA,IAAI,uBAAuB,MAAM,8BAA8B,QAAQ;AAAA,MACrE,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,aAAK,iCAAiC,CAAC,KAAK,GAAG;AAAA,MACjD;AAAA,MACA,OAAO,8BAA8B,SAAS;AAC5C,gBAAQ,QAAQ,aAAa;AAAA,UAC3B,KAAK,2BAA2B,UAAU,yBAAyB;AACjE,gBAAI,QAAQ,kBAAkB;AAC5B,kBAAI,QAAQ,aAAa;AACvB,uBAAO,EAAE,MAAM,aAAa,kBAAkB,QAAQ,iBAAiB;AAAA,cACzE,OAAO;AACL,uBAAO,EAAE,MAAM,kBAAkB,kBAAkB,QAAQ,iBAAiB;AAAA,cAC9E;AAAA,YACF,OAAO;AACL,qBAAO,EAAE,MAAM,UAAU;AAAA,YAC3B;AAAA,UACF,KAAK,2BAA2B,UAAU,yBAAyB;AACjE,mBAAO,QAAQ,cAAc,EAAE,MAAM,YAAY,IAAI,EAAE,MAAM,UAAU;AAAA,UACzE,KAAK,2BAA2B,UAAU,yBAAyB;AAAA,UACnE;AACE,mBAAO,EAAE,MAAM,UAAU;AAAA,QAC7B;AAAA,MACF;AAAA,MACA,MAAM,qBAAqB,OAAO,UAAU,OAAO,SAAS;AAC1D,cAAM,WAAW,MAAM;AACvB,cAAM,SAAS,MAAM,YAAY,QAAQ;AACzC,cAAM,SAAS,MAAM,KAAK,QAAQ,QAAQ;AAC1C,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,OAAO,MAAM,OAAO,sBAAsB,SAAS,SAAS,GAAG,QAAQ;AAAA,UAC3E,eAAe,sBAAsB,8BAA8B,OAAO;AAAA,QAC5E,CAAC;AACD,YAAI,CAAC,QAAQ,MAAM,WAAW,GAAG;AAC/B;AAAA,QACF;AACA,cAAM,MAAM;AAAA,UACV,iBAAiB,KAAK;AAAA,UACtB,iBAAiB,KAAK;AAAA,UACtB,YAAY,CAAC;AAAA,QACf;AACA,aAAK,MAAM,QAAQ,CAAC,SAAS;AAC3B,gBAAM,YAAY;AAAA,YAChB,OAAO;AAAA,YACP,YAAY,CAAC;AAAA,UACf;AACA,oBAAU,gBAAgB;AAAA,YACxB,OAAO,qBAAqB,KAAK,aAAa;AAAA,UAChD;AACA,oBAAU,SAAS,qBAAqB,KAAK,kBAAkB;AAC/D,eAAK,WAAW,QAAQ,CAAC,GAAG,GAAG,MAAM;AACnC,kBAAM,QAAQ,qBAAqB,EAAE,YAAY;AACjD,kBAAM,YAAY;AAAA,cAChB;AAAA,cACA,eAAe;AAAA,gBACb,OAAO,qBAAqB,EAAE,aAAa;AAAA,cAC7C;AAAA,YACF;AACA,sBAAU,SAAS;AACnB,sBAAU,WAAW,KAAK,SAAS;AACnC,gBAAI,IAAI,EAAE,SAAS,GAAG;AACpB,wBAAU,SAAS,qBAAqB,KAAK,qBAAqB;AAAA,YACpE;AAAA,UACF,CAAC;AACD,oBAAU,SAAS,qBAAqB,KAAK,kBAAkB;AAC/D,cAAI,WAAW,KAAK,SAAS;AAAA,QAC/B,CAAC;AACD,eAAO;AAAA,UACL,OAAO;AAAA,UACP,UAAU;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAI,mBAAmB,cAAc,QAAQ;AAAA,MAC3C,MAAM,aAAa,OAAO,UAAU,OAAO;AACzC,cAAM,WAAW,MAAM;AACvB,cAAM,SAAS,MAAM,YAAY,QAAQ;AACzC,cAAM,SAAS,MAAM,KAAK,QAAQ,QAAQ;AAC1C,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,OAAO,MAAM,OAAO,uBAAuB,SAAS,SAAS,GAAG,MAAM;AAC5E,YAAI,CAAC,QAAQ,MAAM,WAAW,GAAG;AAC/B;AAAA,QACF;AACA,cAAM,gBAAgB,qBAAqB,KAAK,aAAa;AAC7D,cAAM,OAAO,KAAK,OAAO,KAAK,KAAK,IAAI,CAAC,QAAQ,YAAY,GAAG,CAAC,EAAE,KAAK,QAAQ,IAAI;AACnF,cAAM,WAAW,qBAAqB,KAAK,YAAY;AACvD,eAAO;AAAA,UACL,OAAO,KAAK,iBAAiB,OAAO,KAAK,QAAQ;AAAA,UACjD,UAAU;AAAA,YACR;AAAA,cACE,OAAO,oBAAoB,WAAW;AAAA,YACxC;AAAA,YACA;AAAA,cACE,OAAO,iBAAiB,OAAO,SAAS,OAAO;AAAA,YACjD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAI,2BAA2B,cAAc,QAAQ;AAAA,MACnD,MAAM,0BAA0B,OAAO,UAAU,OAAO;AACtD,cAAM,WAAW,MAAM;AACvB,cAAM,SAAS,MAAM,YAAY,QAAQ;AACzC,cAAM,SAAS,MAAM,KAAK,QAAQ,QAAQ;AAC1C,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,UAAU,MAAM,OAAO,sBAAsB,SAAS,SAAS,GAAG,QAAQ;AAAA,UAC9E,SAAS,SAAS;AAAA,QACpB,CAAC;AACD,YAAI,CAAC,WAAW,MAAM,WAAW,GAAG;AAClC;AAAA,QACF;AACA,eAAO,QAAQ,QAAQ,CAAC,UAAU;AAChC,iBAAO,MAAM,eAAe,IAAI,CAAC,mBAAmB;AAClD,mBAAO;AAAA,cACL,OAAO,KAAK,iBAAiB,OAAO,eAAe,QAAQ;AAAA,cAC3D,MAAM,eAAe,SAAS,qBAAqB,2BAA2B,UAAU,sBAAsB,QAAQ,2BAA2B,UAAU,sBAAsB;AAAA,YACnL;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAI,oBAAoB,cAAc,QAAQ;AAAA,MAC5C,YAAY,WAAW,QAAQ;AAC7B,cAAM,MAAM;AACZ,aAAK,YAAY;AAAA,MACnB;AAAA,MACA,MAAM,kBAAkB,OAAO,UAAU,OAAO;AAC9C,cAAM,WAAW,MAAM;AACvB,cAAM,SAAS,MAAM,YAAY,QAAQ;AACzC,cAAM,SAAS,MAAM,KAAK,QAAQ,QAAQ;AAC1C,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,UAAU,MAAM,OAAO,wBAAwB,SAAS,SAAS,GAAG,MAAM;AAChF,YAAI,CAAC,WAAW,MAAM,WAAW,GAAG;AAClC;AAAA,QACF;AACA,cAAM,KAAK,UAAU;AAAA,UACnB,QAAQ,IAAI,CAAC,UAAU,2BAA2B,IAAI,MAAM,MAAM,QAAQ,CAAC;AAAA,QAC7E;AACA,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,SAAS,CAAC;AAChB,iBAAS,SAAS,SAAS;AACzB,gBAAM,WAAW,KAAK,UAAU,iBAAiB,MAAM,QAAQ;AAC/D,cAAI,UAAU;AACZ,mBAAO,KAAK;AAAA,cACV,KAAK,SAAS;AAAA,cACd,OAAO,KAAK,iBAAiB,UAAU,MAAM,QAAQ;AAAA,YACvD,CAAC;AAAA,UACH;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAI,mBAAmB,cAAc,QAAQ;AAAA,MAC3C,YAAY,WAAW,QAAQ;AAC7B,cAAM,MAAM;AACZ,aAAK,YAAY;AAAA,MACnB;AAAA,MACA,MAAM,kBAAkB,OAAO,UAAU,SAAS,OAAO;AACvD,cAAM,WAAW,MAAM;AACvB,cAAM,SAAS,MAAM,YAAY,QAAQ;AACzC,cAAM,SAAS,MAAM,KAAK,QAAQ,QAAQ;AAC1C,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,UAAU,MAAM,OAAO,wBAAwB,SAAS,SAAS,GAAG,MAAM;AAChF,YAAI,CAAC,WAAW,MAAM,WAAW,GAAG;AAClC;AAAA,QACF;AACA,cAAM,KAAK,UAAU;AAAA,UACnB,QAAQ,IAAI,CAAC,UAAU,2BAA2B,IAAI,MAAM,MAAM,QAAQ,CAAC;AAAA,QAC7E;AACA,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,SAAS,CAAC;AAChB,iBAAS,SAAS,SAAS;AACzB,gBAAM,WAAW,KAAK,UAAU,iBAAiB,MAAM,QAAQ;AAC/D,cAAI,UAAU;AACZ,mBAAO,KAAK;AAAA,cACV,KAAK,SAAS;AAAA,cACd,OAAO,KAAK,iBAAiB,UAAU,MAAM,QAAQ;AAAA,YACvD,CAAC;AAAA,UACH;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAI,iBAAiB,cAAc,QAAQ;AAAA,MACzC,MAAM,uBAAuB,OAAO,OAAO;AACzC,cAAM,WAAW,MAAM;AACvB,cAAM,SAAS,MAAM,KAAK,QAAQ,QAAQ;AAC1C,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,OAAO,MAAM,OAAO,kBAAkB,SAAS,SAAS,CAAC;AAC/D,YAAI,CAAC,QAAQ,MAAM,WAAW,GAAG;AAC/B;AAAA,QACF;AACA,cAAM,UAAU,CAAC,MAAM,mBAAmB;AAvzB9C,cAAAA;AAwzBM,gBAAM,UAAU;AAAA,YACd,MAAM,KAAK;AAAA,YACX,QAAQ;AAAA,YACR,MAAM,iBAAiB,KAAK,IAAI,KAAK,2BAA2B,UAAU,WAAW;AAAA,YACrF,OAAO,KAAK,iBAAiB,OAAO,KAAK,MAAM,CAAC,CAAC;AAAA,YACjD,gBAAgB,KAAK,iBAAiB,OAAO,KAAK,MAAM,CAAC,CAAC;AAAA,YAC1D,MAAM,CAAC;AAAA,YACP,WAAUA,MAAA,KAAK,eAAL,gBAAAA,IAAiB,IAAI,CAAC,UAAU,QAAQ,OAAO,KAAK,IAAI;AAAA,YAClE,eAAe;AAAA,UACjB;AACA,iBAAO;AAAA,QACT;AACA,cAAM,SAAS,KAAK,aAAa,KAAK,WAAW,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,IAAI,CAAC;AACjF,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAI,QAAO,WAAM;AAAA,IAqFjB,GAnFI,GAAK,UAAU,IAGf,GAAK,UAAU,WAGf,GAAK,SAAS,UAGd,GAAK,SAAS,UAGd,GAAK,QAAQ,SAGb,GAAK,YAAY,aAGjB,GAAK,OAAO,QAGZ,GAAK,OAAO,QAGZ,GAAK,WAAW,OAGhB,GAAK,gBAAgB,aAGrB,GAAK,WAAW,YAGhB,GAAK,gBAAgB,kBAGrB,GAAK,iBAAiB,UAGtB,GAAK,oBAAoB,UAGzB,GAAK,oBAAoB,UAGzB,GAAK,iBAAiB,YAGtB,GAAK,4BAA4B,eAGjC,GAAK,gBAAgB,QAGrB,GAAK,iBAAiB,SAGtB,GAAK,qBAAqB,aAG1B,GAAK,YAAY,aAGjB,GAAK,gBAAgB,kBAGrB,GAAK,gBAAgB,kBAGrB,GAAK,QAAQ,SAGb,GAAK,QAAQ,SAGb,GAAK,QAAQ,SAGb,GAAK,MAAM,OAGX,GAAK,UAAU,WAnFR;AAsFX,IAAI,mBAAmC,uBAAO,OAAO,IAAI;AACzD,qBAAiB,KAAK,MAAM,IAAI,2BAA2B,UAAU,WAAW;AAChF,qBAAiB,KAAK,KAAK,IAAI,2BAA2B,UAAU,WAAW;AAC/E,qBAAiB,KAAK,IAAI,IAAI,2BAA2B,UAAU,WAAW;AAC9E,qBAAiB,KAAK,SAAS,IAAI,2BAA2B,UAAU,WAAW;AACnF,qBAAiB,KAAK,cAAc,IAAI,2BAA2B,UAAU,WAAW;AACxF,qBAAiB,KAAK,cAAc,IAAI,2BAA2B,UAAU,WAAW;AACxF,qBAAiB,KAAK,iBAAiB,IAAI,2BAA2B,UAAU,WAAW;AAC3F,qBAAiB,KAAK,iBAAiB,IAAI,2BAA2B,UAAU,WAAW;AAC3F,qBAAiB,KAAK,QAAQ,IAAI,2BAA2B,UAAU,WAAW;AAClF,qBAAiB,KAAK,KAAK,IAAI,2BAA2B,UAAU,WAAW;AAC/E,qBAAiB,KAAK,aAAa,IAAI,2BAA2B,UAAU,WAAW;AACvF,qBAAiB,KAAK,QAAQ,IAAI,2BAA2B,UAAU,WAAW;AAClF,qBAAiB,KAAK,QAAQ,IAAI,2BAA2B,UAAU,WAAW;AAClF,qBAAiB,KAAK,aAAa,IAAI,2BAA2B,UAAU,WAAW;AACvF,IAAI,eAAe,cAAc,QAAQ;AAAA,MACvC,OAAO,gBAAgB,SAAS;AAC9B,eAAO;AAAA,UACL,qBAAqB,QAAQ;AAAA,UAC7B,SAAS,QAAQ;AAAA,UACjB,YAAY,QAAQ;AAAA,UACpB,aAAa;AAAA,UACb,kBAAkB;AAAA,UAClB,gCAAgC;AAAA,UAChC,0CAA0C;AAAA,UAC1C,0CAA0C;AAAA,UAC1C,iDAAiD;AAAA,UACjD,sDAAsD;AAAA,UACtD,4DAA4D;AAAA,UAC5D,yDAAyD;AAAA,UACzD,6DAA6D;AAAA,UAC7D,yCAAyC;AAAA,UACzC,qCAAqC;AAAA,QACvC;AAAA,MACF;AAAA,MACA,oBAAoB,OAAO,QAAQ;AACjC,eAAO;AAAA,UACL,MAAM,OAAO;AAAA,UACb,OAAO,KAAK,iBAAiB,OAAO,OAAO,IAAI;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AACA,IAAI,gBAAgB,cAAc,aAAa;AAAA,MAC7C,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,aAAK,0BAA0B;AAAA,MACjC;AAAA,MACA,MAAM,oCAAoC,OAAO,OAAO,SAAS,OAAO;AACtE,cAAM,WAAW,MAAM;AACvB,cAAM,cAAc,MAAM,YAAY;AAAA,UACpC,YAAY,MAAM;AAAA,UAClB,QAAQ,MAAM;AAAA,QAChB,CAAC;AACD,cAAM,YAAY,MAAM,YAAY;AAAA,UAClC,YAAY,MAAM;AAAA,UAClB,QAAQ,MAAM;AAAA,QAChB,CAAC;AACD,cAAM,SAAS,MAAM,KAAK,QAAQ,QAAQ;AAC1C,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,QAAQ,MAAM,OAAO;AAAA,UACzB,SAAS,SAAS;AAAA,UAClB;AAAA,UACA;AAAA,UACA,aAAa,gBAAgB,OAAO;AAAA,QACtC;AACA,YAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC;AAAA,QACF;AACA,eAAO,MAAM,IAAI,CAAC,SAAS,KAAK,oBAAoB,OAAO,IAAI,CAAC;AAAA,MAClE;AAAA,IACF;AACA,IAAI,sBAAsB,cAAc,aAAa;AAAA,MACnD,IAAI,8BAA8B;AAChC,eAAO,CAAC,KAAK,KAAK,IAAI;AAAA,MACxB;AAAA,MACA,MAAM,6BAA6B,OAAO,UAAU,IAAI,SAAS,OAAO;AACtE,cAAM,WAAW,MAAM;AACvB,cAAM,SAAS,MAAM,YAAY,QAAQ;AACzC,cAAM,SAAS,MAAM,KAAK,QAAQ,QAAQ;AAC1C,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,QAAQ,MAAM,OAAO;AAAA,UACzB,SAAS,SAAS;AAAA,UAClB;AAAA,UACA;AAAA,UACA,aAAa,gBAAgB,OAAO;AAAA,QACtC;AACA,YAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC;AAAA,QACF;AACA,eAAO,MAAM,IAAI,CAAC,SAAS,KAAK,oBAAoB,OAAO,IAAI,CAAC;AAAA,MAClE;AAAA,IACF;AACA,IAAI,oBAAoB,cAAc,aAAa;AAAA,MACjD,MAAM,mBAAmB,OAAO,OAAO,SAAS,OAAO;AACrD,cAAM,WAAW,MAAM;AACvB,cAAM,QAAQ,MAAM,YAAY;AAAA,UAC9B,YAAY,MAAM;AAAA,UAClB,QAAQ,MAAM;AAAA,QAChB,CAAC;AACD,cAAM,MAAM,MAAM,YAAY;AAAA,UAC5B,YAAY,MAAM;AAAA,UAClB,QAAQ,MAAM;AAAA,QAChB,CAAC;AACD,cAAM,gBAAgB,aAAa,gBAAgB,MAAM,WAAW,CAAC;AACrE,cAAM,aAAa,QAAQ,QAAQ,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,MAAM;AACtF,cAAM,SAAS,MAAM,KAAK,QAAQ,QAAQ;AAC1C,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,YAAY,MAAM,OAAO;AAAA,UAC7B,SAAS,SAAS;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,YAAI,CAAC,aAAa,MAAM,WAAW,GAAG;AACpC,iBAAO,EAAE,SAAS,CAAC,GAAG,SAAS,MAAM;AAAA,UACrC,EAAE;AAAA,QACJ;AACA,cAAM,UAAU,UAAU,OAAO,CAAC,QAAQ;AACxC,iBAAO,IAAI,QAAQ,OAAO,CAAC,WAAW,OAAO,SAAS,EAAE,WAAW;AAAA,QACrE,CAAC,EAAE,IAAI,CAAC,QAAQ;AACd,iBAAO,KAAK,mCAAmC,OAAO,SAAS,GAAG;AAAA,QACpE,CAAC;AACD,eAAO;AAAA,UACL;AAAA,UACA,SAAS,MAAM;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,MACA,mCAAmC,OAAO,SAAS,SAAS;AAC1D,cAAM,QAAQ,CAAC;AACf,mBAAW,UAAU,QAAQ,SAAS;AACpC,qBAAW,cAAc,OAAO,aAAa;AAC3C,kBAAM,KAAK;AAAA,cACT,UAAU,MAAM;AAAA,cAChB,WAAW;AAAA,cACX,UAAU;AAAA,gBACR,OAAO,KAAK,iBAAiB,OAAO,WAAW,IAAI;AAAA,gBACnD,MAAM,WAAW;AAAA,cACnB;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AACA,cAAM,SAAS;AAAA,UACb,OAAO,QAAQ;AAAA,UACf,MAAM,EAAE,MAAM;AAAA,UACd,aAAa,QAAQ;AAAA,UACrB,MAAM;AAAA,QACR;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAI,gBAAgB,cAAc,QAAQ;AAAA,MACxC,YAAY,WAAW,QAAQ;AAC7B,cAAM,MAAM;AACZ,aAAK,YAAY;AAAA,MACnB;AAAA,MACA,MAAM,mBAAmB,OAAO,UAAU,SAAS,OAAO;AACxD,cAAM,WAAW,MAAM;AACvB,cAAM,WAAW,SAAS,SAAS;AACnC,cAAM,SAAS,MAAM,YAAY,QAAQ;AACzC,cAAM,SAAS,MAAM,KAAK,QAAQ,QAAQ;AAC1C,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,aAAa,MAAM,OAAO,cAAc,UAAU,QAAQ;AAAA,UAC9D,yBAAyB;AAAA,QAC3B,CAAC;AACD,YAAI,WAAW,cAAc,OAAO;AAClC,iBAAO;AAAA,YACL,OAAO,CAAC;AAAA,YACR,cAAc,WAAW;AAAA,UAC3B;AAAA,QACF;AACA,YAAI,WAAW,iBAAiB,QAAQ;AACtC,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QACpD;AACA,cAAM,kBAAkB,MAAM,OAAO;AAAA,UACnC;AAAA,UACA;AAAA;AAAA,UAEA;AAAA;AAAA,UAEA;AAAA;AAAA,UAEA;AAAA,QACF;AACA,YAAI,CAAC,mBAAmB,MAAM,WAAW,GAAG;AAC1C;AAAA,QACF;AACA,cAAM,QAAQ,CAAC;AACf,mBAAW,kBAAkB,iBAAiB;AAC5C,gBAAM,SAAS,KAAK,UAAU,iBAAiB,eAAe,QAAQ;AACtE,cAAI,QAAQ;AACV,kBAAM,KAAK;AAAA,cACT,UAAU,OAAO;AAAA,cACjB,WAAW;AAAA,cACX,UAAU;AAAA,gBACR,OAAO,KAAK,iBAAiB,QAAQ,eAAe,QAAQ;AAAA,gBAC5D,MAAM;AAAA,cACR;AAAA,YACF,CAAC;AAAA,UACH,OAAO;AACL,kBAAM,IAAI,MAAM,gBAAgB,eAAe,QAAQ,GAAG;AAAA,UAC5D;AAAA,QACF;AACA,eAAO,EAAE,MAAM;AAAA,MACjB;AAAA,IACF;AACA,IAAI,oBAAoB,cAAc,QAAQ;AAAA,MAC5C,MAAM,kBAAkB,OAAO,OAAO,OAAO;AAC3C,cAAM,WAAW,MAAM;AACvB,cAAM,WAAW,SAAS,SAAS;AACnC,cAAM,QAAQ,MAAM,YAAY;AAAA,UAC9B,YAAY,MAAM;AAAA,UAClB,QAAQ,MAAM;AAAA,QAChB,CAAC;AACD,cAAM,MAAM,MAAM,YAAY;AAAA,UAC5B,YAAY,MAAM;AAAA,UAClB,QAAQ,MAAM;AAAA,QAChB,CAAC;AACD,cAAM,SAAS,MAAM,KAAK,QAAQ,QAAQ;AAC1C,YAAI,MAAM,WAAW,GAAG;AACtB,iBAAO;AAAA,QACT;AACA,cAAM,UAAU,MAAM,OAAO,kBAAkB,UAAU,OAAO,GAAG;AACnE,cAAM,QAAQ,QAAQ,IAAI,CAAC,SAAS;AAClC,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,OAAO,KAAK;AAAA,YACZ,UAAU,MAAM,cAAc,KAAK,QAAQ;AAAA,YAC3C,MAAM,KAAK,iBAAiB,KAAK,IAAI;AAAA,UACvC;AAAA,QACF,CAAC;AACD,eAAO,EAAE,OAAO,SAAS,MAAM;AAAA,QAC/B,EAAE;AAAA,MACJ;AAAA,MACA,iBAAiB,MAAM;AACrB,gBAAQ,MAAM;AAAA,UACZ,KAAK;AACH,mBAAO,2BAA2B,UAAU,cAAc;AAAA,UAC5D,KAAK;AACH,mBAAO,2BAA2B,UAAU,cAAc;AAAA,UAC5D;AACE,mBAAO,2BAA2B,UAAU,cAAc;AAAA,QAC9D;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": ["_a"]}