{"version": 3, "sources": ["../../../../@scalar/api-client/dist/components/ScalarAsciiArt.vue2.js", "../../../../@scalar/api-client/dist/components/ScalarAsciiArt.vue.js"], "sourcesContent": ["import { defineComponent as h, computed as f, openBlock as i, createElementBlock as l, normalizeClass as y, Fragment as v, renderList as x, normalizeStyle as m, createElementVNode as $, toDisplayString as k } from \"vue\";\nconst p = 500, o = 100, B = /* @__PURE__ */ h({\n  __name: \"ScalarAsciiArt\",\n  props: {\n    art: {},\n    animate: { type: Boolean }\n  },\n  setup(u) {\n    const d = u, n = f(() => d.art.split(`\n`)), g = (a, s) => {\n      var e, t, r, c;\n      return {\n        animationDuration: `${a * o}ms, ${p}ms`,\n        animationTimingFunction: `steps(${a}), step-end`,\n        animationDelay: `${s * o}ms, 0ms`,\n        animationIterationCount: `1, ${((((e = n.value) == null ? void 0 : e.length) ?? 0) + (((c = (r = n.value) == null ? void 0 : r[((t = n.value) == null ? void 0 : t.length) - 1]) == null ? void 0 : c.length) ?? 0) + 5) * o / p}`\n      };\n    };\n    return (a, s) => (i(), l(\"div\", {\n      class: y([\"ascii-art font-code flex flex-col items-start text-[6px] leading-[7px]\", { \"ascii-art-animate\": a.animate }])\n    }, [\n      (i(!0), l(v, null, x(n.value, (e, t) => (i(), l(\"span\", {\n        key: t,\n        class: \"inline-block\",\n        style: m({ width: `calc(${e.length + 1}ch)` })\n      }, [\n        $(\"span\", {\n          class: \"inline-block whitespace-pre overflow-hidden\",\n          style: m(g(e.length, t))\n        }, k(e), 5)\n      ], 4))), 128))\n    ], 2));\n  }\n});\nexport {\n  B as default\n};\n", "import a from \"./ScalarAsciiArt.vue2.js\";\n/* empty css                    */\nimport o from \"../_virtual/_plugin-vue_export-helper.js\";\nconst i = /* @__PURE__ */ o(a, [[\"__scopeId\", \"data-v-ea0a9794\"]]);\nexport {\n  i as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AACA,IAAM,IAAI;AAAV,IAAe,IAAI;AAAnB,IAAwB,IAAoB,gBAAE;AAAA,EAC5C,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,KAAK,CAAC;AAAA,IACN,SAAS,EAAE,MAAM,QAAQ;AAAA,EAC3B;AAAA,EACA,MAAM,GAAG;AACP,UAAM,IAAI,GAAG,IAAI,SAAE,MAAM,EAAE,IAAI,MAAM;AAAA,CACxC,CAAC,GAAG,IAAI,CAAC,GAAGA,OAAM;AACb,UAAI,GAAG,GAAG,GAAG;AACb,aAAO;AAAA,QACL,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC;AAAA,QACnC,yBAAyB,SAAS,CAAC;AAAA,QACnC,gBAAgB,GAAGA,KAAI,CAAC;AAAA,QACxB,yBAAyB,UAAU,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,WAAW,QAAQ,KAAK,IAAI,EAAE,UAAU,OAAO,SAAS,IAAI,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,UAAU,CAAC,MAAM,OAAO,SAAS,EAAE,WAAW,KAAK,KAAK,IAAI,CAAC;AAAA,MAClO;AAAA,IACF;AACA,WAAO,CAAC,GAAGA,QAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC9B,OAAO,eAAE,CAAC,0EAA0E,EAAE,qBAAqB,EAAE,QAAQ,CAAC,CAAC;AAAA,IACzH,GAAG;AAAA,OACA,UAAE,IAAE,GAAG,mBAAE,UAAG,MAAM,WAAE,EAAE,OAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,QAAQ;AAAA,QACtD,KAAK;AAAA,QACL,OAAO;AAAA,QACP,OAAO,eAAE,EAAE,OAAO,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC;AAAA,MAC/C,GAAG;AAAA,QACD,gBAAE,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,OAAO,eAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;AAAA,QACzB,GAAG,gBAAE,CAAC,GAAG,CAAC;AAAA,MACZ,GAAG,CAAC,EAAE,GAAG,GAAG;AAAA,IACd,GAAG,CAAC;AAAA,EACN;AACF,CAAC;;;AC9BD,IAAM,IAAoB,EAAE,GAAG,CAAC,CAAC,aAAa,iBAAiB,CAAC,CAAC;", "names": ["s"]}