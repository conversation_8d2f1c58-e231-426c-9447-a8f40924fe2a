{"version": 3, "sources": ["../../../../@blocknote/core/node_modules/remark-rehype/lib/index.js"], "sourcesContent": ["/**\n * @import {Root as HastRoot} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {Options as ToHastOptions} from 'mdast-util-to-hast'\n * @import {Processor} from 'unified'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef {Omit<ToHastOptions, 'file'>} Options\n *\n * @callback TransformBridge\n *   Bridge-mode.\n *\n *   Runs the destination with the new hast tree.\n *   Discards result.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {Promise<undefined>}\n *   Nothing.\n *\n * @callback TransformMutate\n *  Mutate-mode.\n *\n *  Further transformers run on the hast tree.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {HastRoot}\n *   Tree (hast).\n */\n\nimport {toHast} from 'mdast-util-to-hast'\n\n/**\n * Turn markdown into HTML.\n *\n * ##### Notes\n *\n * ###### Signature\n *\n * * if a processor is given,\n *   runs the (rehype) plugins used on it with a hast tree,\n *   then discards the result (*bridge mode*)\n * * otherwise,\n *   returns a hast tree,\n *   the plugins used after `remarkRehype` are rehype plugins (*mutate mode*)\n *\n * > 👉 **Note**:\n * > It’s highly unlikely that you want to pass a `processor`.\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most plugins ignore `raw` nodes but two notable ones don’t:\n *\n * * `rehype-stringify` also has an option `allowDangerousHtml` which will\n *   output the raw HTML.\n *   This is typically discouraged as noted by the option name but is useful if\n *   you completely trust authors\n * * `rehype-raw` can handle the raw embedded HTML strings by parsing them\n *   into standard hast nodes (`element`, `text`, etc);\n *   this is a heavy task as it needs a full HTML parser,\n *   but it is the only way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark,\n * which we follow by default.\n * They are supported by GitHub,\n * so footnotes can be enabled in markdown with `remark-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes,\n * which is hidden for sighted users but shown to assistive technology.\n * When your page is not in English,\n * you must define translated values.\n *\n * Back references use ARIA attributes,\n * but the section label itself uses a heading that is hidden with an\n * `sr-only` class.\n * To show it to sighted users,\n * define different attributes in `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem,\n * as it links footnote calls to footnote definitions on the page through `id`\n * attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * *Example: headings (DOM clobbering)* in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * * when the node has a `value`\n *   (and doesn’t have `data.hName`, `data.hProperties`, or `data.hChildren`,\n *   see later),\n *   create a hast `text` node\n * * otherwise,\n *   create a `<div>` element (which could be changed with `data.hName`),\n *   with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @overload\n * @param {Processor} processor\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge}\n *\n * @overload\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformMutate}\n *\n * @overload\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge | TransformMutate}\n *\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n *   Processor or configuration (optional).\n * @param {Readonly<Options> | null | undefined} [options]\n *   When a processor was given,\n *   configuration (optional).\n * @returns {TransformBridge | TransformMutate}\n *   Transform.\n */\nexport default function remarkRehype(destination, options) {\n  if (destination && 'run' in destination) {\n    /**\n     * @type {TransformBridge}\n     */\n    return async function (tree, file) {\n      // Cast because root in -> root out.\n      const hastTree = /** @type {HastRoot} */ (\n        toHast(tree, {file, ...options})\n      )\n      await destination.run(hastTree, file)\n    }\n  }\n\n  /**\n   * @type {TransformMutate}\n   */\n  return function (tree, file) {\n    // Cast because root in -> root out.\n    // To do: in the future, disallow ` || options` fallback.\n    // With `unified-engine`, `destination` can be `undefined` but\n    // `options` will be the file set.\n    // We should not pass that as `options`.\n    return /** @type {HastRoot} */ (\n      toHast(tree, {file, ...(destination || options)})\n    )\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;AAmJe,SAAR,aAA8B,aAAa,SAAS;AACzD,MAAI,eAAe,SAAS,aAAa;AAIvC,WAAO,eAAgB,MAAM,MAAM;AAEjC,YAAM;AAAA;AAAA,QACJ,OAAO,MAAM,EAAC,MAAM,GAAG,QAAO,CAAC;AAAA;AAEjC,YAAM,YAAY,IAAI,UAAU,IAAI;AAAA,IACtC;AAAA,EACF;AAKA,SAAO,SAAU,MAAM,MAAM;AAM3B;AAAA;AAAA,MACE,OAAO,MAAM,EAAC,MAAM,GAAI,eAAe,QAAQ,CAAC;AAAA;AAAA,EAEpD;AACF;", "names": []}