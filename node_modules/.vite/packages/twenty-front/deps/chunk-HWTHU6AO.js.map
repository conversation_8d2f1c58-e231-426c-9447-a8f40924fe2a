{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/language/typescript/monaco.contribution.js"], "sourcesContent": ["import '../../editor/editor.api.js';\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/language/typescript/lib/typescriptServicesMetadata.ts\nvar typescriptVersion = \"5.4.5\";\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/language/typescript/monaco.contribution.ts\nvar ModuleKind = /* @__PURE__ */ ((ModuleKind2) => {\n  ModuleKind2[ModuleKind2[\"None\"] = 0] = \"None\";\n  ModuleKind2[ModuleKind2[\"CommonJS\"] = 1] = \"CommonJS\";\n  ModuleKind2[ModuleKind2[\"AMD\"] = 2] = \"AMD\";\n  ModuleKind2[ModuleKind2[\"UMD\"] = 3] = \"UMD\";\n  ModuleKind2[ModuleKind2[\"System\"] = 4] = \"System\";\n  ModuleKind2[ModuleKind2[\"ES2015\"] = 5] = \"ES2015\";\n  ModuleKind2[ModuleKind2[\"ESNext\"] = 99] = \"ESNext\";\n  return ModuleKind2;\n})(ModuleKind || {});\nvar JsxEmit = /* @__PURE__ */ ((JsxEmit2) => {\n  JsxEmit2[JsxEmit2[\"None\"] = 0] = \"None\";\n  JsxEmit2[JsxEmit2[\"Preserve\"] = 1] = \"Preserve\";\n  JsxEmit2[JsxEmit2[\"React\"] = 2] = \"React\";\n  JsxEmit2[JsxEmit2[\"ReactNative\"] = 3] = \"ReactNative\";\n  JsxEmit2[JsxEmit2[\"ReactJSX\"] = 4] = \"ReactJSX\";\n  JsxEmit2[JsxEmit2[\"ReactJSXDev\"] = 5] = \"ReactJSXDev\";\n  return JsxEmit2;\n})(JsxEmit || {});\nvar NewLineKind = /* @__PURE__ */ ((NewLineKind2) => {\n  NewLineKind2[NewLineKind2[\"CarriageReturnLineFeed\"] = 0] = \"CarriageReturnLineFeed\";\n  NewLineKind2[NewLineKind2[\"LineFeed\"] = 1] = \"LineFeed\";\n  return NewLineKind2;\n})(NewLineKind || {});\nvar ScriptTarget = /* @__PURE__ */ ((ScriptTarget2) => {\n  ScriptTarget2[ScriptTarget2[\"ES3\"] = 0] = \"ES3\";\n  ScriptTarget2[ScriptTarget2[\"ES5\"] = 1] = \"ES5\";\n  ScriptTarget2[ScriptTarget2[\"ES2015\"] = 2] = \"ES2015\";\n  ScriptTarget2[ScriptTarget2[\"ES2016\"] = 3] = \"ES2016\";\n  ScriptTarget2[ScriptTarget2[\"ES2017\"] = 4] = \"ES2017\";\n  ScriptTarget2[ScriptTarget2[\"ES2018\"] = 5] = \"ES2018\";\n  ScriptTarget2[ScriptTarget2[\"ES2019\"] = 6] = \"ES2019\";\n  ScriptTarget2[ScriptTarget2[\"ES2020\"] = 7] = \"ES2020\";\n  ScriptTarget2[ScriptTarget2[\"ESNext\"] = 99] = \"ESNext\";\n  ScriptTarget2[ScriptTarget2[\"JSON\"] = 100] = \"JSON\";\n  ScriptTarget2[ScriptTarget2[\"Latest\"] = 99 /* ESNext */] = \"Latest\";\n  return ScriptTarget2;\n})(ScriptTarget || {});\nvar ModuleResolutionKind = /* @__PURE__ */ ((ModuleResolutionKind2) => {\n  ModuleResolutionKind2[ModuleResolutionKind2[\"Classic\"] = 1] = \"Classic\";\n  ModuleResolutionKind2[ModuleResolutionKind2[\"NodeJs\"] = 2] = \"NodeJs\";\n  return ModuleResolutionKind2;\n})(ModuleResolutionKind || {});\nvar LanguageServiceDefaultsImpl = class {\n  constructor(compilerOptions, diagnosticsOptions, workerOptions, inlayHintsOptions, modeConfiguration) {\n    this._onDidChange = new monaco_editor_core_exports.Emitter();\n    this._onDidExtraLibsChange = new monaco_editor_core_exports.Emitter();\n    this._extraLibs = /* @__PURE__ */ Object.create(null);\n    this._removedExtraLibs = /* @__PURE__ */ Object.create(null);\n    this._eagerModelSync = false;\n    this.setCompilerOptions(compilerOptions);\n    this.setDiagnosticsOptions(diagnosticsOptions);\n    this.setWorkerOptions(workerOptions);\n    this.setInlayHintsOptions(inlayHintsOptions);\n    this.setModeConfiguration(modeConfiguration);\n    this._onDidExtraLibsChangeTimeout = -1;\n  }\n  get onDidChange() {\n    return this._onDidChange.event;\n  }\n  get onDidExtraLibsChange() {\n    return this._onDidExtraLibsChange.event;\n  }\n  get modeConfiguration() {\n    return this._modeConfiguration;\n  }\n  get workerOptions() {\n    return this._workerOptions;\n  }\n  get inlayHintsOptions() {\n    return this._inlayHintsOptions;\n  }\n  getExtraLibs() {\n    return this._extraLibs;\n  }\n  addExtraLib(content, _filePath) {\n    let filePath;\n    if (typeof _filePath === \"undefined\") {\n      filePath = `ts:extralib-${Math.random().toString(36).substring(2, 15)}`;\n    } else {\n      filePath = _filePath;\n    }\n    if (this._extraLibs[filePath] && this._extraLibs[filePath].content === content) {\n      return {\n        dispose: () => {\n        }\n      };\n    }\n    let myVersion = 1;\n    if (this._removedExtraLibs[filePath]) {\n      myVersion = this._removedExtraLibs[filePath] + 1;\n    }\n    if (this._extraLibs[filePath]) {\n      myVersion = this._extraLibs[filePath].version + 1;\n    }\n    this._extraLibs[filePath] = {\n      content,\n      version: myVersion\n    };\n    this._fireOnDidExtraLibsChangeSoon();\n    return {\n      dispose: () => {\n        let extraLib = this._extraLibs[filePath];\n        if (!extraLib) {\n          return;\n        }\n        if (extraLib.version !== myVersion) {\n          return;\n        }\n        delete this._extraLibs[filePath];\n        this._removedExtraLibs[filePath] = myVersion;\n        this._fireOnDidExtraLibsChangeSoon();\n      }\n    };\n  }\n  setExtraLibs(libs) {\n    for (const filePath in this._extraLibs) {\n      this._removedExtraLibs[filePath] = this._extraLibs[filePath].version;\n    }\n    this._extraLibs = /* @__PURE__ */ Object.create(null);\n    if (libs && libs.length > 0) {\n      for (const lib of libs) {\n        const filePath = lib.filePath || `ts:extralib-${Math.random().toString(36).substring(2, 15)}`;\n        const content = lib.content;\n        let myVersion = 1;\n        if (this._removedExtraLibs[filePath]) {\n          myVersion = this._removedExtraLibs[filePath] + 1;\n        }\n        this._extraLibs[filePath] = {\n          content,\n          version: myVersion\n        };\n      }\n    }\n    this._fireOnDidExtraLibsChangeSoon();\n  }\n  _fireOnDidExtraLibsChangeSoon() {\n    if (this._onDidExtraLibsChangeTimeout !== -1) {\n      return;\n    }\n    this._onDidExtraLibsChangeTimeout = window.setTimeout(() => {\n      this._onDidExtraLibsChangeTimeout = -1;\n      this._onDidExtraLibsChange.fire(void 0);\n    }, 0);\n  }\n  getCompilerOptions() {\n    return this._compilerOptions;\n  }\n  setCompilerOptions(options) {\n    this._compilerOptions = options || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(void 0);\n  }\n  getDiagnosticsOptions() {\n    return this._diagnosticsOptions;\n  }\n  setDiagnosticsOptions(options) {\n    this._diagnosticsOptions = options || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(void 0);\n  }\n  setWorkerOptions(options) {\n    this._workerOptions = options || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(void 0);\n  }\n  setInlayHintsOptions(options) {\n    this._inlayHintsOptions = options || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(void 0);\n  }\n  setMaximumWorkerIdleTime(value) {\n  }\n  setEagerModelSync(value) {\n    this._eagerModelSync = value;\n  }\n  getEagerModelSync() {\n    return this._eagerModelSync;\n  }\n  setModeConfiguration(modeConfiguration) {\n    this._modeConfiguration = modeConfiguration || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(void 0);\n  }\n};\nvar typescriptVersion2 = typescriptVersion;\nvar modeConfigurationDefault = {\n  completionItems: true,\n  hovers: true,\n  documentSymbols: true,\n  definitions: true,\n  references: true,\n  documentHighlights: true,\n  rename: true,\n  diagnostics: true,\n  documentRangeFormattingEdits: true,\n  signatureHelp: true,\n  onTypeFormattingEdits: true,\n  codeActions: true,\n  inlayHints: true\n};\nvar typescriptDefaults = new LanguageServiceDefaultsImpl(\n  { allowNonTsExtensions: true, target: 99 /* Latest */ },\n  { noSemanticValidation: false, noSyntaxValidation: false, onlyVisible: false },\n  {},\n  {},\n  modeConfigurationDefault\n);\nvar javascriptDefaults = new LanguageServiceDefaultsImpl(\n  { allowNonTsExtensions: true, allowJs: true, target: 99 /* Latest */ },\n  { noSemanticValidation: true, noSyntaxValidation: false, onlyVisible: false },\n  {},\n  {},\n  modeConfigurationDefault\n);\nvar getTypeScriptWorker = () => {\n  return getMode().then((mode) => mode.getTypeScriptWorker());\n};\nvar getJavaScriptWorker = () => {\n  return getMode().then((mode) => mode.getJavaScriptWorker());\n};\nmonaco_editor_core_exports.languages.typescript = {\n  ModuleKind,\n  JsxEmit,\n  NewLineKind,\n  ScriptTarget,\n  ModuleResolutionKind,\n  typescriptVersion: typescriptVersion2,\n  typescriptDefaults,\n  javascriptDefaults,\n  getTypeScriptWorker,\n  getJavaScriptWorker\n};\nfunction getMode() {\n  if (false) {\n    return new Promise((resolve, reject) => {\n      __require([\"vs/language/typescript/tsMode\"], resolve, reject);\n    });\n  } else {\n    return import(\"./tsMode.js\");\n  }\n}\nmonaco_editor_core_exports.languages.onLanguage(\"typescript\", () => {\n  return getMode().then((mode) => mode.setupTypeScript(typescriptDefaults));\n});\nmonaco_editor_core_exports.languages.onLanguage(\"javascript\", () => {\n  return getMode().then((mode) => mode.setupJavaScript(javascriptDefaults));\n});\nexport {\n  JsxEmit,\n  ModuleKind,\n  ModuleResolutionKind,\n  NewLineKind,\n  ScriptTarget,\n  getJavaScriptWorker,\n  getTypeScriptWorker,\n  javascriptDefaults,\n  typescriptDefaults,\n  typescriptVersion2 as typescriptVersion\n};\n"], "mappings": ";;;;;;;;;AAmQA,SAAS,UAAU;AACjB,MAAI,OAAO;AACT,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,gBAAU,CAAC,+BAA+B,GAAG,SAAS,MAAM;AAAA,IAC9D,CAAC;AAAA,EACH,OAAO;AACL,WAAO,OAAO,sBAAa;AAAA,EAC7B;AACF;AA3QA,IAQI,WACA,kBACA,mBACA,cACA,aAQA,YAGA,mBAGA,4BAKA,YAUA,SASA,aAKA,cAcA,sBAKA,6BAyIA,oBACA,0BAeA,oBAOA,oBAOA,qBAGA;AApPJ;AAAA;AAAA;AA4BA;AApBA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,oBAAoB;AAGxB,IAAI,6BAA6B,CAAC;AAClC,eAAW,4BAA4B,kBAAuB;AAI9D,IAAI,cAA8B,CAAC,gBAAgB;AACjD,kBAAY,YAAY,MAAM,IAAI,CAAC,IAAI;AACvC,kBAAY,YAAY,UAAU,IAAI,CAAC,IAAI;AAC3C,kBAAY,YAAY,KAAK,IAAI,CAAC,IAAI;AACtC,kBAAY,YAAY,KAAK,IAAI,CAAC,IAAI;AACtC,kBAAY,YAAY,QAAQ,IAAI,CAAC,IAAI;AACzC,kBAAY,YAAY,QAAQ,IAAI,CAAC,IAAI;AACzC,kBAAY,YAAY,QAAQ,IAAI,EAAE,IAAI;AAC1C,aAAO;AAAA,IACT,GAAG,cAAc,CAAC,CAAC;AACnB,IAAI,WAA2B,CAAC,aAAa;AAC3C,eAAS,SAAS,MAAM,IAAI,CAAC,IAAI;AACjC,eAAS,SAAS,UAAU,IAAI,CAAC,IAAI;AACrC,eAAS,SAAS,OAAO,IAAI,CAAC,IAAI;AAClC,eAAS,SAAS,aAAa,IAAI,CAAC,IAAI;AACxC,eAAS,SAAS,UAAU,IAAI,CAAC,IAAI;AACrC,eAAS,SAAS,aAAa,IAAI,CAAC,IAAI;AACxC,aAAO;AAAA,IACT,GAAG,WAAW,CAAC,CAAC;AAChB,IAAI,eAA+B,CAAC,iBAAiB;AACnD,mBAAa,aAAa,wBAAwB,IAAI,CAAC,IAAI;AAC3D,mBAAa,aAAa,UAAU,IAAI,CAAC,IAAI;AAC7C,aAAO;AAAA,IACT,GAAG,eAAe,CAAC,CAAC;AACpB,IAAI,gBAAgC,CAAC,kBAAkB;AACrD,oBAAc,cAAc,KAAK,IAAI,CAAC,IAAI;AAC1C,oBAAc,cAAc,KAAK,IAAI,CAAC,IAAI;AAC1C,oBAAc,cAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,oBAAc,cAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,oBAAc,cAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,oBAAc,cAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,oBAAc,cAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,oBAAc,cAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,oBAAc,cAAc,QAAQ,IAAI,EAAE,IAAI;AAC9C,oBAAc,cAAc,MAAM,IAAI,GAAG,IAAI;AAC7C;AAAA,QAAc,cAAc,QAAQ,IAAI;AAAA;AAAA,MAAe,IAAI;AAC3D,aAAO;AAAA,IACT,GAAG,gBAAgB,CAAC,CAAC;AACrB,IAAI,wBAAwC,CAAC,0BAA0B;AACrE,4BAAsB,sBAAsB,SAAS,IAAI,CAAC,IAAI;AAC9D,4BAAsB,sBAAsB,QAAQ,IAAI,CAAC,IAAI;AAC7D,aAAO;AAAA,IACT,GAAG,wBAAwB,CAAC,CAAC;AAC7B,IAAI,8BAA8B,MAAM;AAAA,MACtC,YAAY,iBAAiB,oBAAoB,eAAe,mBAAmB,mBAAmB;AACpG,aAAK,eAAe,IAAI,2BAA2B,QAAQ;AAC3D,aAAK,wBAAwB,IAAI,2BAA2B,QAAQ;AACpE,aAAK,aAA6B,uBAAO,OAAO,IAAI;AACpD,aAAK,oBAAoC,uBAAO,OAAO,IAAI;AAC3D,aAAK,kBAAkB;AACvB,aAAK,mBAAmB,eAAe;AACvC,aAAK,sBAAsB,kBAAkB;AAC7C,aAAK,iBAAiB,aAAa;AACnC,aAAK,qBAAqB,iBAAiB;AAC3C,aAAK,qBAAqB,iBAAiB;AAC3C,aAAK,+BAA+B;AAAA,MACtC;AAAA,MACA,IAAI,cAAc;AAChB,eAAO,KAAK,aAAa;AAAA,MAC3B;AAAA,MACA,IAAI,uBAAuB;AACzB,eAAO,KAAK,sBAAsB;AAAA,MACpC;AAAA,MACA,IAAI,oBAAoB;AACtB,eAAO,KAAK;AAAA,MACd;AAAA,MACA,IAAI,gBAAgB;AAClB,eAAO,KAAK;AAAA,MACd;AAAA,MACA,IAAI,oBAAoB;AACtB,eAAO,KAAK;AAAA,MACd;AAAA,MACA,eAAe;AACb,eAAO,KAAK;AAAA,MACd;AAAA,MACA,YAAY,SAAS,WAAW;AAC9B,YAAI;AACJ,YAAI,OAAO,cAAc,aAAa;AACpC,qBAAW,eAAe,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE,CAAC;AAAA,QACvE,OAAO;AACL,qBAAW;AAAA,QACb;AACA,YAAI,KAAK,WAAW,QAAQ,KAAK,KAAK,WAAW,QAAQ,EAAE,YAAY,SAAS;AAC9E,iBAAO;AAAA,YACL,SAAS,MAAM;AAAA,YACf;AAAA,UACF;AAAA,QACF;AACA,YAAI,YAAY;AAChB,YAAI,KAAK,kBAAkB,QAAQ,GAAG;AACpC,sBAAY,KAAK,kBAAkB,QAAQ,IAAI;AAAA,QACjD;AACA,YAAI,KAAK,WAAW,QAAQ,GAAG;AAC7B,sBAAY,KAAK,WAAW,QAAQ,EAAE,UAAU;AAAA,QAClD;AACA,aAAK,WAAW,QAAQ,IAAI;AAAA,UAC1B;AAAA,UACA,SAAS;AAAA,QACX;AACA,aAAK,8BAA8B;AACnC,eAAO;AAAA,UACL,SAAS,MAAM;AACb,gBAAI,WAAW,KAAK,WAAW,QAAQ;AACvC,gBAAI,CAAC,UAAU;AACb;AAAA,YACF;AACA,gBAAI,SAAS,YAAY,WAAW;AAClC;AAAA,YACF;AACA,mBAAO,KAAK,WAAW,QAAQ;AAC/B,iBAAK,kBAAkB,QAAQ,IAAI;AACnC,iBAAK,8BAA8B;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AAAA,MACA,aAAa,MAAM;AACjB,mBAAW,YAAY,KAAK,YAAY;AACtC,eAAK,kBAAkB,QAAQ,IAAI,KAAK,WAAW,QAAQ,EAAE;AAAA,QAC/D;AACA,aAAK,aAA6B,uBAAO,OAAO,IAAI;AACpD,YAAI,QAAQ,KAAK,SAAS,GAAG;AAC3B,qBAAW,OAAO,MAAM;AACtB,kBAAM,WAAW,IAAI,YAAY,eAAe,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE,CAAC;AAC3F,kBAAM,UAAU,IAAI;AACpB,gBAAI,YAAY;AAChB,gBAAI,KAAK,kBAAkB,QAAQ,GAAG;AACpC,0BAAY,KAAK,kBAAkB,QAAQ,IAAI;AAAA,YACjD;AACA,iBAAK,WAAW,QAAQ,IAAI;AAAA,cAC1B;AAAA,cACA,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AACA,aAAK,8BAA8B;AAAA,MACrC;AAAA,MACA,gCAAgC;AAC9B,YAAI,KAAK,iCAAiC,IAAI;AAC5C;AAAA,QACF;AACA,aAAK,+BAA+B,OAAO,WAAW,MAAM;AAC1D,eAAK,+BAA+B;AACpC,eAAK,sBAAsB,KAAK,MAAM;AAAA,QACxC,GAAG,CAAC;AAAA,MACN;AAAA,MACA,qBAAqB;AACnB,eAAO,KAAK;AAAA,MACd;AAAA,MACA,mBAAmB,SAAS;AAC1B,aAAK,mBAAmB,WAA2B,uBAAO,OAAO,IAAI;AACrE,aAAK,aAAa,KAAK,MAAM;AAAA,MAC/B;AAAA,MACA,wBAAwB;AACtB,eAAO,KAAK;AAAA,MACd;AAAA,MACA,sBAAsB,SAAS;AAC7B,aAAK,sBAAsB,WAA2B,uBAAO,OAAO,IAAI;AACxE,aAAK,aAAa,KAAK,MAAM;AAAA,MAC/B;AAAA,MACA,iBAAiB,SAAS;AACxB,aAAK,iBAAiB,WAA2B,uBAAO,OAAO,IAAI;AACnE,aAAK,aAAa,KAAK,MAAM;AAAA,MAC/B;AAAA,MACA,qBAAqB,SAAS;AAC5B,aAAK,qBAAqB,WAA2B,uBAAO,OAAO,IAAI;AACvE,aAAK,aAAa,KAAK,MAAM;AAAA,MAC/B;AAAA,MACA,yBAAyB,OAAO;AAAA,MAChC;AAAA,MACA,kBAAkB,OAAO;AACvB,aAAK,kBAAkB;AAAA,MACzB;AAAA,MACA,oBAAoB;AAClB,eAAO,KAAK;AAAA,MACd;AAAA,MACA,qBAAqB,mBAAmB;AACtC,aAAK,qBAAqB,qBAAqC,uBAAO,OAAO,IAAI;AACjF,aAAK,aAAa,KAAK,MAAM;AAAA,MAC/B;AAAA,IACF;AACA,IAAI,qBAAqB;AACzB,IAAI,2BAA2B;AAAA,MAC7B,iBAAiB;AAAA,MACjB,QAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,oBAAoB;AAAA,MACpB,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,8BAA8B;AAAA,MAC9B,eAAe;AAAA,MACf,uBAAuB;AAAA,MACvB,aAAa;AAAA,MACb,YAAY;AAAA,IACd;AACA,IAAI,qBAAqB,IAAI;AAAA,MAC3B;AAAA,QAAE,sBAAsB;AAAA,QAAM,QAAQ;AAAA;AAAA,MAAgB;AAAA,MACtD,EAAE,sBAAsB,OAAO,oBAAoB,OAAO,aAAa,MAAM;AAAA,MAC7E,CAAC;AAAA,MACD,CAAC;AAAA,MACD;AAAA,IACF;AACA,IAAI,qBAAqB,IAAI;AAAA,MAC3B;AAAA,QAAE,sBAAsB;AAAA,QAAM,SAAS;AAAA,QAAM,QAAQ;AAAA;AAAA,MAAgB;AAAA,MACrE,EAAE,sBAAsB,MAAM,oBAAoB,OAAO,aAAa,MAAM;AAAA,MAC5E,CAAC;AAAA,MACD,CAAC;AAAA,MACD;AAAA,IACF;AACA,IAAI,sBAAsB,MAAM;AAC9B,aAAO,QAAQ,EAAE,KAAK,CAAC,SAAS,KAAK,oBAAoB,CAAC;AAAA,IAC5D;AACA,IAAI,sBAAsB,MAAM;AAC9B,aAAO,QAAQ,EAAE,KAAK,CAAC,SAAS,KAAK,oBAAoB,CAAC;AAAA,IAC5D;AACA,+BAA2B,UAAU,aAAa;AAAA,MAChD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAUA,+BAA2B,UAAU,WAAW,cAAc,MAAM;AAClE,aAAO,QAAQ,EAAE,KAAK,CAAC,SAAS,KAAK,gBAAgB,kBAAkB,CAAC;AAAA,IAC1E,CAAC;AACD,+BAA2B,UAAU,WAAW,cAAc,MAAM;AAClE,aAAO,QAAQ,EAAE,KAAK,CAAC,SAAS,KAAK,gBAAgB,kBAAkB,CAAC;AAAA,IAC1E,CAAC;AAAA;AAAA;", "names": []}