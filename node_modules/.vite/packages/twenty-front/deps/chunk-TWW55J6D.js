import {
  toMarkdown
} from "./chunk-J2QCQ7VY.js";

// node_modules/remark-stringify/lib/index.js
function remarkStringify(options) {
  const self = this;
  self.compiler = compiler;
  function compiler(tree) {
    return toMarkdown(tree, {
      ...self.data("settings"),
      ...options,
      // Note: this option is not in the readme.
      // The goal is for it to be set by plugins on `data` instead of being
      // passed by users.
      extensions: self.data("toMarkdownExtensions") || []
    });
  }
}

export {
  remarkStringify
};
//# sourceMappingURL=chunk-TWW55J6D.js.map
