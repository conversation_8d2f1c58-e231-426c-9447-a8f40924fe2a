{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/python/python.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/python/python.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"'''\", \"'''\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(\n        \"^\\\\s*(?:def|class|for|if|elif|else|while|try|with|finally|except|async|match|case).*?:\\\\s*$\"\n      ),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ],\n  folding: {\n    offSide: true,\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".python\",\n  keywords: [\n    // This section is the result of running\n    // `import keyword; for k in sorted(keyword.kwlist + keyword.softkwlist): print(\"  '\" + k + \"',\")`\n    // in a Python REPL,\n    // though note that the output from Python 3 is not a strict superset of the\n    // output from Python 2.\n    \"False\",\n    // promoted to keyword.kwlist in Python 3\n    \"None\",\n    // promoted to keyword.kwlist in Python 3\n    \"True\",\n    // promoted to keyword.kwlist in Python 3\n    \"_\",\n    // new in Python 3.10\n    \"and\",\n    \"as\",\n    \"assert\",\n    \"async\",\n    // new in Python 3\n    \"await\",\n    // new in Python 3\n    \"break\",\n    \"case\",\n    // new in Python 3.10\n    \"class\",\n    \"continue\",\n    \"def\",\n    \"del\",\n    \"elif\",\n    \"else\",\n    \"except\",\n    \"exec\",\n    // Python 2, but not 3.\n    \"finally\",\n    \"for\",\n    \"from\",\n    \"global\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"is\",\n    \"lambda\",\n    \"match\",\n    // new in Python 3.10\n    \"nonlocal\",\n    // new in Python 3\n    \"not\",\n    \"or\",\n    \"pass\",\n    \"print\",\n    // Python 2, but not 3.\n    \"raise\",\n    \"return\",\n    \"try\",\n    \"type\",\n    // new in Python 3.12\n    \"while\",\n    \"with\",\n    \"yield\",\n    \"int\",\n    \"float\",\n    \"long\",\n    \"complex\",\n    \"hex\",\n    \"abs\",\n    \"all\",\n    \"any\",\n    \"apply\",\n    \"basestring\",\n    \"bin\",\n    \"bool\",\n    \"buffer\",\n    \"bytearray\",\n    \"callable\",\n    \"chr\",\n    \"classmethod\",\n    \"cmp\",\n    \"coerce\",\n    \"compile\",\n    \"complex\",\n    \"delattr\",\n    \"dict\",\n    \"dir\",\n    \"divmod\",\n    \"enumerate\",\n    \"eval\",\n    \"execfile\",\n    \"file\",\n    \"filter\",\n    \"format\",\n    \"frozenset\",\n    \"getattr\",\n    \"globals\",\n    \"hasattr\",\n    \"hash\",\n    \"help\",\n    \"id\",\n    \"input\",\n    \"intern\",\n    \"isinstance\",\n    \"issubclass\",\n    \"iter\",\n    \"len\",\n    \"locals\",\n    \"list\",\n    \"map\",\n    \"max\",\n    \"memoryview\",\n    \"min\",\n    \"next\",\n    \"object\",\n    \"oct\",\n    \"open\",\n    \"ord\",\n    \"pow\",\n    \"print\",\n    \"property\",\n    \"reversed\",\n    \"range\",\n    \"raw_input\",\n    \"reduce\",\n    \"reload\",\n    \"repr\",\n    \"reversed\",\n    \"round\",\n    \"self\",\n    \"set\",\n    \"setattr\",\n    \"slice\",\n    \"sorted\",\n    \"staticmethod\",\n    \"str\",\n    \"sum\",\n    \"super\",\n    \"tuple\",\n    \"type\",\n    \"unichr\",\n    \"unicode\",\n    \"vars\",\n    \"xrange\",\n    \"zip\",\n    \"__dict__\",\n    \"__methods__\",\n    \"__members__\",\n    \"__class__\",\n    \"__bases__\",\n    \"__name__\",\n    \"__mro__\",\n    \"__subclasses__\",\n    \"__init__\",\n    \"__import__\"\n  ],\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[,:;]/, \"delimiter\"],\n      [/[{}\\[\\]()]/, \"@brackets\"],\n      [/@[a-zA-Z_]\\w*/, \"tag\"],\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    // Deal with white space, including single and multi-line comments\n    whitespace: [\n      [/\\s+/, \"white\"],\n      [/(^#.*$)/, \"comment\"],\n      [/'''/, \"string\", \"@endDocString\"],\n      [/\"\"\"/, \"string\", \"@endDblDocString\"]\n    ],\n    endDocString: [\n      [/[^']+/, \"string\"],\n      [/\\\\'/, \"string\"],\n      [/'''/, \"string\", \"@popall\"],\n      [/'/, \"string\"]\n    ],\n    endDblDocString: [\n      [/[^\"]+/, \"string\"],\n      [/\\\\\"/, \"string\"],\n      [/\"\"\"/, \"string\", \"@popall\"],\n      [/\"/, \"string\"]\n    ],\n    // Recognize hex, negatives, decimals, imaginaries, longs, and scientific notation\n    numbers: [\n      [/-?0x([abcdef]|[ABCDEF]|\\d)+[lL]?/, \"number.hex\"],\n      [/-?(\\d*\\.)?\\d+([eE][+\\-]?\\d+)?[jJ]?[lL]?/, \"number\"]\n    ],\n    // Recognize strings, including those broken across lines with \\ (but not without)\n    strings: [\n      [/'$/, \"string.escape\", \"@popall\"],\n      [/f'{1,3}/, \"string.escape\", \"@fStringBody\"],\n      [/'/, \"string.escape\", \"@stringBody\"],\n      [/\"$/, \"string.escape\", \"@popall\"],\n      [/f\"{1,3}/, \"string.escape\", \"@fDblStringBody\"],\n      [/\"/, \"string.escape\", \"@dblStringBody\"]\n    ],\n    fStringBody: [\n      [/[^\\\\'\\{\\}]+$/, \"string\", \"@popall\"],\n      [/[^\\\\'\\{\\}]+/, \"string\"],\n      [/\\{[^\\}':!=]+/, \"identifier\", \"@fStringDetail\"],\n      [/\\\\./, \"string\"],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    stringBody: [\n      [/[^\\\\']+$/, \"string\", \"@popall\"],\n      [/[^\\\\']+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    fDblStringBody: [\n      [/[^\\\\\"\\{\\}]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"\\{\\}]+/, \"string\"],\n      [/\\{[^\\}':!=]+/, \"identifier\", \"@fStringDetail\"],\n      [/\\\\./, \"string\"],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    dblStringBody: [\n      [/[^\\\\\"]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    fStringDetail: [\n      [/[:][^}]+/, \"string\"],\n      [/[!][ars]/, \"string\"],\n      // only !a, !r, !s are supported by f-strings: https://docs.python.org/3/tutorial/inputoutput.html#formatted-string-literals\n      [/=/, \"string\"],\n      [/\\}/, \"identifier\", \"@pop\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;;;;AAAA,IAOI,WACA,kBACA,mBACA,cACA,aAQA,YAGA,4BAKA,MAwCA;AAnEJ;AAAA;AAwBA;AAjBA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,eAAW,4BAA4B,kBAAuB;AAI9D,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,aAAa;AAAA,QACb,cAAc,CAAC,OAAO,KAAK;AAAA,MAC7B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,QAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,MACxD;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,cAAc;AAAA,QACZ;AAAA,UACE,YAAY,IAAI;AAAA,YACd;AAAA,UACF;AAAA,UACA,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,QACnF;AAAA,MACF;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,QACT,SAAS;AAAA,UACP,OAAO,IAAI,OAAO,iBAAiB;AAAA,UACnC,KAAK,IAAI,OAAO,oBAAoB;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMR;AAAA;AAAA,QAEA;AAAA;AAAA,QAEA;AAAA;AAAA,QAEA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,QAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,oBAAoB;AAAA,QACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,MAC1D;AAAA,MACA,WAAW;AAAA,QACT,MAAM;AAAA,UACJ,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,WAAW;AAAA,UACtB,EAAE,SAAS,WAAW;AAAA,UACtB,CAAC,SAAS,WAAW;AAAA,UACrB,CAAC,cAAc,WAAW;AAAA,UAC1B,CAAC,iBAAiB,KAAK;AAAA,UACvB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,aAAa;AAAA,gBACb,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAEA,YAAY;AAAA,UACV,CAAC,OAAO,OAAO;AAAA,UACf,CAAC,WAAW,SAAS;AAAA,UACrB,CAAC,OAAO,UAAU,eAAe;AAAA,UACjC,CAAC,OAAO,UAAU,kBAAkB;AAAA,QACtC;AAAA,QACA,cAAc;AAAA,UACZ,CAAC,SAAS,QAAQ;AAAA,UAClB,CAAC,OAAO,QAAQ;AAAA,UAChB,CAAC,OAAO,UAAU,SAAS;AAAA,UAC3B,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,iBAAiB;AAAA,UACf,CAAC,SAAS,QAAQ;AAAA,UAClB,CAAC,OAAO,QAAQ;AAAA,UAChB,CAAC,OAAO,UAAU,SAAS;AAAA,UAC3B,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA;AAAA,QAEA,SAAS;AAAA,UACP,CAAC,oCAAoC,YAAY;AAAA,UACjD,CAAC,2CAA2C,QAAQ;AAAA,QACtD;AAAA;AAAA,QAEA,SAAS;AAAA,UACP,CAAC,MAAM,iBAAiB,SAAS;AAAA,UACjC,CAAC,WAAW,iBAAiB,cAAc;AAAA,UAC3C,CAAC,KAAK,iBAAiB,aAAa;AAAA,UACpC,CAAC,MAAM,iBAAiB,SAAS;AAAA,UACjC,CAAC,WAAW,iBAAiB,iBAAiB;AAAA,UAC9C,CAAC,KAAK,iBAAiB,gBAAgB;AAAA,QACzC;AAAA,QACA,aAAa;AAAA,UACX,CAAC,gBAAgB,UAAU,SAAS;AAAA,UACpC,CAAC,eAAe,QAAQ;AAAA,UACxB,CAAC,gBAAgB,cAAc,gBAAgB;AAAA,UAC/C,CAAC,OAAO,QAAQ;AAAA,UAChB,CAAC,KAAK,iBAAiB,SAAS;AAAA,UAChC,CAAC,OAAO,QAAQ;AAAA,QAClB;AAAA,QACA,YAAY;AAAA,UACV,CAAC,YAAY,UAAU,SAAS;AAAA,UAChC,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,OAAO,QAAQ;AAAA,UAChB,CAAC,KAAK,iBAAiB,SAAS;AAAA,UAChC,CAAC,OAAO,QAAQ;AAAA,QAClB;AAAA,QACA,gBAAgB;AAAA,UACd,CAAC,gBAAgB,UAAU,SAAS;AAAA,UACpC,CAAC,eAAe,QAAQ;AAAA,UACxB,CAAC,gBAAgB,cAAc,gBAAgB;AAAA,UAC/C,CAAC,OAAO,QAAQ;AAAA,UAChB,CAAC,KAAK,iBAAiB,SAAS;AAAA,UAChC,CAAC,OAAO,QAAQ;AAAA,QAClB;AAAA,QACA,eAAe;AAAA,UACb,CAAC,YAAY,UAAU,SAAS;AAAA,UAChC,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,OAAO,QAAQ;AAAA,UAChB,CAAC,KAAK,iBAAiB,SAAS;AAAA,UAChC,CAAC,OAAO,QAAQ;AAAA,QAClB;AAAA,QACA,eAAe;AAAA,UACb,CAAC,YAAY,QAAQ;AAAA,UACrB,CAAC,YAAY,QAAQ;AAAA;AAAA,UAErB,CAAC,KAAK,QAAQ;AAAA,UACd,CAAC,MAAM,cAAc,MAAM;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}