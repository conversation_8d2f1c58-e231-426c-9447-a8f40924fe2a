{"version": 3, "sources": ["../../../../@scalar/api-client/dist/libs/hot-keys.js"], "sourcesContent": ["import { isMacOS as a } from \"@scalar/use-tooltip\";\nconst f = [\n  \"Escape\",\n  \"ArrowDown\",\n  \"ArrowUp\",\n  \"Enter\",\n  \"F1\",\n  \"F2\",\n  \"F3\",\n  \"F4\",\n  \"F5\",\n  \"F6\",\n  \"F7\",\n  \"F8\",\n  \"F9\",\n  \"F10\",\n  \"F11\",\n  \"F12\"\n], d = {\n  Escape: { event: \"closeModal\" },\n  Enter: { event: \"executeRequest\", modifiers: [\"default\"] },\n  b: { event: \"toggleSidebar\", modifiers: [\"default\"] },\n  k: { event: \"openCommandPalette\", modifiers: [\"default\"] },\n  l: { event: \"focusAddressBar\", modifiers: [\"default\"] }\n}, c = (e) => {\n  if (!(e.target instanceof HTMLElement)) return !1;\n  const t = e.target;\n  return t.tagName === \"INPUT\" ? !f.includes(e.key) : !!(t.tagName === \"TEXTAREA\" || t.getAttribute(\"contenteditable\") || t.contentEditable === \"true\");\n}, l = {\n  Alt: \"altKey\",\n  Control: \"ctrlKey\",\n  Shift: \"shiftKey\",\n  Meta: \"metaKey\"\n}, u = (e) => e.map((t) => t === \"default\" ? a() ? \"metaKey\" : \"ctrlKey\" : l[t]), E = (e, t, { hotKeys: o = d, modifiers: n = [\"default\"] } = {}) => {\n  const i = e.key === \" \" ? \"Space\" : e.key, r = o[i];\n  r && (i === \"Escape\" ? t.emit({ [r.event]: e }) : u(r.modifiers || n).every((s) => e[s] === !0) ? t.emit({ [r.event]: e }) : !c(e) && r.modifiers === void 0 && t.emit({ [r.event]: e }));\n};\nexport {\n  d as DEFAULT_HOTKEYS,\n  u as getModifiers,\n  E as handleHotKeyDown,\n  c as isInput\n};\n"], "mappings": ";;;;;AACA,IAAM,IAAI;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAjBA,IAiBG,IAAI;AAAA,EACL,QAAQ,EAAE,OAAO,aAAa;AAAA,EAC9B,OAAO,EAAE,OAAO,kBAAkB,WAAW,CAAC,SAAS,EAAE;AAAA,EACzD,GAAG,EAAE,OAAO,iBAAiB,WAAW,CAAC,SAAS,EAAE;AAAA,EACpD,GAAG,EAAE,OAAO,sBAAsB,WAAW,CAAC,SAAS,EAAE;AAAA,EACzD,GAAG,EAAE,OAAO,mBAAmB,WAAW,CAAC,SAAS,EAAE;AACxD;AAvBA,IAuBG,IAAI,CAAC,MAAM;AACZ,MAAI,EAAE,EAAE,kBAAkB,aAAc,QAAO;AAC/C,QAAM,IAAI,EAAE;AACZ,SAAO,EAAE,YAAY,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,YAAY,cAAc,EAAE,aAAa,iBAAiB,KAAK,EAAE,oBAAoB;AAChJ;AA3BA,IA2BG,IAAI;AAAA,EACL,KAAK;AAAA,EACL,SAAS;AAAA,EACT,OAAO;AAAA,EACP,MAAM;AACR;AAhCA,IAgCG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,MAAM,YAAY,QAAE,IAAI,YAAY,YAAY,EAAE,CAAC,CAAC;AAhC/E,IAgCkF,IAAI,CAAC,GAAG,GAAG,EAAE,SAAS,IAAI,GAAG,WAAW,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM;AACnJ,QAAM,IAAI,EAAE,QAAQ,MAAM,UAAU,EAAE,KAAK,IAAI,EAAE,CAAC;AAClD,QAAM,MAAM,WAAW,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,aAAa,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM,IAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,cAAc,UAAU,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AACzL;", "names": []}