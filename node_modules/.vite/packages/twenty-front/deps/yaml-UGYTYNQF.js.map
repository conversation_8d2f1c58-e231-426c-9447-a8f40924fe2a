{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/yaml/yaml.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/yaml/yaml.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    offSide: true\n  },\n  onEnterRules: [\n    {\n      beforeText: /:\\s*$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.Indent\n      }\n    }\n  ]\n};\nvar language = {\n  tokenPostfix: \".yaml\",\n  brackets: [\n    { token: \"delimiter.bracket\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: [\"true\", \"True\", \"TRUE\", \"false\", \"False\", \"FALSE\", \"null\", \"Null\", \"Null\", \"~\"],\n  numberInteger: /(?:0|[+-]?[0-9]+)/,\n  numberFloat: /(?:0|[+-]?[0-9]+)(?:\\.[0-9]+)?(?:e[-+][1-9][0-9]*)?/,\n  numberOctal: /0o[0-7]+/,\n  numberHex: /0x[0-9a-fA-F]+/,\n  numberInfinity: /[+-]?\\.(?:inf|Inf|INF)/,\n  numberNaN: /\\.(?:nan|Nan|NAN)/,\n  numberDate: /\\d{4}-\\d\\d-\\d\\d([Tt ]\\d\\d:\\d\\d:\\d\\d(\\.\\d+)?(( ?[+-]\\d\\d?(:\\d\\d)?)|Z)?)?/,\n  escapes: /\\\\(?:[btnfr\\\\\"']|[0-7][0-7]?|[0-3][0-7]{2})/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      // Directive\n      [/%[^ ]+.*$/, \"meta.directive\"],\n      // Document Markers\n      [/---/, \"operators.directivesEnd\"],\n      [/\\.{3}/, \"operators.documentEnd\"],\n      // Block Structure Indicators\n      [/[-?:](?= )/, \"operators\"],\n      { include: \"@anchor\" },\n      { include: \"@tagHandle\" },\n      { include: \"@flowCollections\" },\n      { include: \"@blockStyle\" },\n      // Numbers\n      [/@numberInteger(?![ \\t]*\\S+)/, \"number\"],\n      [/@numberFloat(?![ \\t]*\\S+)/, \"number.float\"],\n      [/@numberOctal(?![ \\t]*\\S+)/, \"number.octal\"],\n      [/@numberHex(?![ \\t]*\\S+)/, \"number.hex\"],\n      [/@numberInfinity(?![ \\t]*\\S+)/, \"number.infinity\"],\n      [/@numberNaN(?![ \\t]*\\S+)/, \"number.nan\"],\n      [/@numberDate(?![ \\t]*\\S+)/, \"number.date\"],\n      // Key:Value pair\n      [/(\".*?\"|'.*?'|[^#'\"]*?)([ \\t]*)(:)( |$)/, [\"type\", \"white\", \"operators\", \"white\"]],\n      { include: \"@flowScalars\" },\n      // String nodes\n      [\n        /.+?(?=(\\s+#|$))/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // Flow Collection: Flow Mapping\n    object: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      // Flow Mapping termination\n      [/\\}/, \"@brackets\", \"@pop\"],\n      // Flow Mapping delimiter\n      [/,/, \"delimiter.comma\"],\n      // Flow Mapping Key:Value delimiter\n      [/:(?= )/, \"operators\"],\n      // Flow Mapping Key:Value key\n      [/(?:\".*?\"|'.*?'|[^,\\{\\[]+?)(?=: )/, \"type\"],\n      // Start Flow Style\n      { include: \"@flowCollections\" },\n      { include: \"@flowScalars\" },\n      // Scalar Data types\n      { include: \"@tagHandle\" },\n      { include: \"@anchor\" },\n      { include: \"@flowNumber\" },\n      // Other value (keyword or string)\n      [\n        /[^\\},]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // Flow Collection: Flow Sequence\n    array: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      // Flow Sequence termination\n      [/\\]/, \"@brackets\", \"@pop\"],\n      // Flow Sequence delimiter\n      [/,/, \"delimiter.comma\"],\n      // Start Flow Style\n      { include: \"@flowCollections\" },\n      { include: \"@flowScalars\" },\n      // Scalar Data types\n      { include: \"@tagHandle\" },\n      { include: \"@anchor\" },\n      { include: \"@flowNumber\" },\n      // Other value (keyword or string)\n      [\n        /[^\\],]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // First line of a Block Style\n    multiString: [[/^( +).+$/, \"string\", \"@multiStringContinued.$1\"]],\n    // Further lines of a Block Style\n    //   Workaround for indentation detection\n    multiStringContinued: [\n      [\n        /^( *).+$/,\n        {\n          cases: {\n            \"$1==$S2\": \"string\",\n            \"@default\": { token: \"@rematch\", next: \"@popall\" }\n          }\n        }\n      ]\n    ],\n    whitespace: [[/[ \\t\\r\\n]+/, \"white\"]],\n    // Only line comments\n    comment: [[/#.*$/, \"comment\"]],\n    // Start Flow Collections\n    flowCollections: [\n      [/\\[/, \"@brackets\", \"@array\"],\n      [/\\{/, \"@brackets\", \"@object\"]\n    ],\n    // Start Flow Scalars (quoted strings)\n    flowScalars: [\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/'[^']*'/, \"string\"],\n      [/\"/, \"string\", \"@doubleQuotedString\"]\n    ],\n    doubleQuotedString: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    // Start Block Scalar\n    blockStyle: [[/[>|][0-9]*[+-]?$/, \"operators\", \"@multiString\"]],\n    // Numbers in Flow Collections (terminate with ,]})\n    flowNumber: [\n      [/@numberInteger(?=[ \\t]*[,\\]\\}])/, \"number\"],\n      [/@numberFloat(?=[ \\t]*[,\\]\\}])/, \"number.float\"],\n      [/@numberOctal(?=[ \\t]*[,\\]\\}])/, \"number.octal\"],\n      [/@numberHex(?=[ \\t]*[,\\]\\}])/, \"number.hex\"],\n      [/@numberInfinity(?=[ \\t]*[,\\]\\}])/, \"number.infinity\"],\n      [/@numberNaN(?=[ \\t]*[,\\]\\}])/, \"number.nan\"],\n      [/@numberDate(?=[ \\t]*[,\\]\\}])/, \"number.date\"]\n    ],\n    tagHandle: [[/\\![^ ]*/, \"tag\"]],\n    anchor: [[/[&*][^ ]+/, \"namespace\"]]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;;;;AAAA,IAOI,WACA,kBACA,mBACA,cACA,aAQA,YAGA,4BAKA,MAmCA;AA9DJ;AAAA;AAwBA;AAjBA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,eAAW,4BAA4B,kBAAuB;AAI9D,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,MACX;AAAA,MACA,cAAc;AAAA,QACZ;AAAA,UACE,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,cAAc,2BAA2B,UAAU,aAAa;AAAA,UAClE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,UAAU;AAAA,QACR,EAAE,OAAO,qBAAqB,MAAM,KAAK,OAAO,IAAI;AAAA,QACpD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,MACrD;AAAA,MACA,UAAU,CAAC,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,QAAQ,QAAQ,QAAQ,GAAG;AAAA,MACzF,eAAe;AAAA,MACf,aAAa;AAAA,MACb,aAAa;AAAA,MACb,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,WAAW;AAAA,QACT,MAAM;AAAA,UACJ,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,WAAW;AAAA;AAAA,UAEtB,CAAC,aAAa,gBAAgB;AAAA;AAAA,UAE9B,CAAC,OAAO,yBAAyB;AAAA,UACjC,CAAC,SAAS,uBAAuB;AAAA;AAAA,UAEjC,CAAC,cAAc,WAAW;AAAA,UAC1B,EAAE,SAAS,UAAU;AAAA,UACrB,EAAE,SAAS,aAAa;AAAA,UACxB,EAAE,SAAS,mBAAmB;AAAA,UAC9B,EAAE,SAAS,cAAc;AAAA;AAAA,UAEzB,CAAC,+BAA+B,QAAQ;AAAA,UACxC,CAAC,6BAA6B,cAAc;AAAA,UAC5C,CAAC,6BAA6B,cAAc;AAAA,UAC5C,CAAC,2BAA2B,YAAY;AAAA,UACxC,CAAC,gCAAgC,iBAAiB;AAAA,UAClD,CAAC,2BAA2B,YAAY;AAAA,UACxC,CAAC,4BAA4B,aAAa;AAAA;AAAA,UAE1C,CAAC,0CAA0C,CAAC,QAAQ,SAAS,aAAa,OAAO,CAAC;AAAA,UAClF,EAAE,SAAS,eAAe;AAAA;AAAA,UAE1B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,aAAa;AAAA,gBACb,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAEA,QAAQ;AAAA,UACN,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,WAAW;AAAA;AAAA,UAEtB,CAAC,MAAM,aAAa,MAAM;AAAA;AAAA,UAE1B,CAAC,KAAK,iBAAiB;AAAA;AAAA,UAEvB,CAAC,UAAU,WAAW;AAAA;AAAA,UAEtB,CAAC,oCAAoC,MAAM;AAAA;AAAA,UAE3C,EAAE,SAAS,mBAAmB;AAAA,UAC9B,EAAE,SAAS,eAAe;AAAA;AAAA,UAE1B,EAAE,SAAS,aAAa;AAAA,UACxB,EAAE,SAAS,UAAU;AAAA,UACrB,EAAE,SAAS,cAAc;AAAA;AAAA,UAEzB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,aAAa;AAAA,gBACb,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAEA,OAAO;AAAA,UACL,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,WAAW;AAAA;AAAA,UAEtB,CAAC,MAAM,aAAa,MAAM;AAAA;AAAA,UAE1B,CAAC,KAAK,iBAAiB;AAAA;AAAA,UAEvB,EAAE,SAAS,mBAAmB;AAAA,UAC9B,EAAE,SAAS,eAAe;AAAA;AAAA,UAE1B,EAAE,SAAS,aAAa;AAAA,UACxB,EAAE,SAAS,UAAU;AAAA,UACrB,EAAE,SAAS,cAAc;AAAA;AAAA,UAEzB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,aAAa;AAAA,gBACb,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAEA,aAAa,CAAC,CAAC,YAAY,UAAU,0BAA0B,CAAC;AAAA;AAAA;AAAA,QAGhE,sBAAsB;AAAA,UACpB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW;AAAA,gBACX,YAAY,EAAE,OAAO,YAAY,MAAM,UAAU;AAAA,cACnD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,YAAY,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA;AAAA,QAEpC,SAAS,CAAC,CAAC,QAAQ,SAAS,CAAC;AAAA;AAAA,QAE7B,iBAAiB;AAAA,UACf,CAAC,MAAM,aAAa,QAAQ;AAAA,UAC5B,CAAC,MAAM,aAAa,SAAS;AAAA,QAC/B;AAAA;AAAA,QAEA,aAAa;AAAA,UACX,CAAC,mBAAmB,gBAAgB;AAAA,UACpC,CAAC,mBAAmB,gBAAgB;AAAA,UACpC,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,KAAK,UAAU,qBAAqB;AAAA,QACvC;AAAA,QACA,oBAAoB;AAAA,UAClB,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,QACxB;AAAA;AAAA,QAEA,YAAY,CAAC,CAAC,oBAAoB,aAAa,cAAc,CAAC;AAAA;AAAA,QAE9D,YAAY;AAAA,UACV,CAAC,mCAAmC,QAAQ;AAAA,UAC5C,CAAC,iCAAiC,cAAc;AAAA,UAChD,CAAC,iCAAiC,cAAc;AAAA,UAChD,CAAC,+BAA+B,YAAY;AAAA,UAC5C,CAAC,oCAAoC,iBAAiB;AAAA,UACtD,CAAC,+BAA+B,YAAY;AAAA,UAC5C,CAAC,gCAAgC,aAAa;AAAA,QAChD;AAAA,QACA,WAAW,CAAC,CAAC,WAAW,KAAK,CAAC;AAAA,QAC9B,QAAQ,CAAC,CAAC,aAAa,WAAW,CAAC;AAAA,MACrC;AAAA,IACF;AAAA;AAAA;", "names": []}