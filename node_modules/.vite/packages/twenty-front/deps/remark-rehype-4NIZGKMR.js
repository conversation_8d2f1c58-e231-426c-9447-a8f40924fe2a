import {
  defaultFootnoteBackContent,
  defaultFootnoteBack<PERSON>abel,
  handlers,
  toHast
} from "./chunk-MPYJ4POF.js";
import "./chunk-FPMN7SAE.js";
import "./chunk-XTJKMDAQ.js";
import "./chunk-TFQJNSQ7.js";
import "./chunk-OBJQZ5YF.js";
import "./chunk-C4W2J7YQ.js";
import "./chunk-XPZLJQLW.js";

// node_modules/@blocknote/core/node_modules/remark-rehype/lib/index.js
function remarkRehype(destination, options) {
  if (destination && "run" in destination) {
    return async function(tree, file) {
      const hastTree = (
        /** @type {HastRoot} */
        toHast(tree, { file, ...options })
      );
      await destination.run(hastTree, file);
    };
  }
  return function(tree, file) {
    return (
      /** @type {HastRoot} */
      toHast(tree, { file, ...destination || options })
    );
  };
}
export {
  remarkRehype as default,
  defaultFootnoteBackContent,
  defaultFootnoteBackLabel,
  handlers as defaultHandlers
};
//# sourceMappingURL=remark-rehype-4NIZGKMR.js.map
