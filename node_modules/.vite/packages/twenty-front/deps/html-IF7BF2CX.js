import {
  editor_api_exports,
  init_editor_api
} from "./chunk-FKTPZOQV.js";
import {
  __esm
} from "./chunk-XPZLJQLW.js";

// node_modules/monaco-editor/esm/vs/basic-languages/html/html.js
var __defProp, __getOwnPropDesc, __getOwnPropNames, __hasOwnProp, __copyProps, __reExport, monaco_editor_core_exports, EMPTY_ELEMENTS, conf, language;
var init_html = __esm({
  "node_modules/monaco-editor/esm/vs/basic-languages/html/html.js"() {
    init_editor_api();
    __defProp = Object.defineProperty;
    __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    __getOwnPropNames = Object.getOwnPropertyNames;
    __hasOwnProp = Object.prototype.hasOwnProperty;
    __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
    monaco_editor_core_exports = {};
    __reExport(monaco_editor_core_exports, editor_api_exports);
    EMPTY_ELEMENTS = [
      "area",
      "base",
      "br",
      "col",
      "embed",
      "hr",
      "img",
      "input",
      "keygen",
      "link",
      "menuitem",
      "meta",
      "param",
      "source",
      "track",
      "wbr"
    ];
    conf = {
      wordPattern: /(-?\d*\.\d\w*)|([^\`\~\!\@\$\^\&\*\(\)\=\+\[\{\]\}\\\|\;\:\'\"\,\.\<\>\/\s]+)/g,
      comments: {
        blockComment: ["<!--", "-->"]
      },
      brackets: [
        ["<!--", "-->"],
        ["<", ">"],
        ["{", "}"],
        ["(", ")"]
      ],
      autoClosingPairs: [
        { open: "{", close: "}" },
        { open: "[", close: "]" },
        { open: "(", close: ")" },
        { open: '"', close: '"' },
        { open: "'", close: "'" }
      ],
      surroundingPairs: [
        { open: '"', close: '"' },
        { open: "'", close: "'" },
        { open: "{", close: "}" },
        { open: "[", close: "]" },
        { open: "(", close: ")" },
        { open: "<", close: ">" }
      ],
      onEnterRules: [
        {
          beforeText: new RegExp(
            `<(?!(?:${EMPTY_ELEMENTS.join("|")}))([_:\\w][_:\\w-.\\d]*)([^/>]*(?!/)>)[^<]*$`,
            "i"
          ),
          afterText: /^<\/([_:\w][_:\w-.\d]*)\s*>$/i,
          action: {
            indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent
          }
        },
        {
          beforeText: new RegExp(
            `<(?!(?:${EMPTY_ELEMENTS.join("|")}))(\\w[\\w\\d]*)([^/>]*(?!/)>)[^<]*$`,
            "i"
          ),
          action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }
        }
      ],
      folding: {
        markers: {
          start: new RegExp("^\\s*<!--\\s*#region\\b.*-->"),
          end: new RegExp("^\\s*<!--\\s*#endregion\\b.*-->")
        }
      }
    };
    language = {
      defaultToken: "",
      tokenPostfix: ".html",
      ignoreCase: true,
      // The main tokenizer for our languages
      tokenizer: {
        root: [
          [/<!DOCTYPE/, "metatag", "@doctype"],
          [/<!--/, "comment", "@comment"],
          [/(<)((?:[\w\-]+:)?[\w\-]+)(\s*)(\/>)/, ["delimiter", "tag", "", "delimiter"]],
          [/(<)(script)/, ["delimiter", { token: "tag", next: "@script" }]],
          [/(<)(style)/, ["delimiter", { token: "tag", next: "@style" }]],
          [/(<)((?:[\w\-]+:)?[\w\-]+)/, ["delimiter", { token: "tag", next: "@otherTag" }]],
          [/(<\/)((?:[\w\-]+:)?[\w\-]+)/, ["delimiter", { token: "tag", next: "@otherTag" }]],
          [/</, "delimiter"],
          [/[^<]+/]
          // text
        ],
        doctype: [
          [/[^>]+/, "metatag.content"],
          [/>/, "metatag", "@pop"]
        ],
        comment: [
          [/-->/, "comment", "@pop"],
          [/[^-]+/, "comment.content"],
          [/./, "comment.content"]
        ],
        otherTag: [
          [/\/?>/, "delimiter", "@pop"],
          [/"([^"]*)"/, "attribute.value"],
          [/'([^']*)'/, "attribute.value"],
          [/[\w\-]+/, "attribute.name"],
          [/=/, "delimiter"],
          [/[ \t\r\n]+/]
          // whitespace
        ],
        // -- BEGIN <script> tags handling
        // After <script
        script: [
          [/type/, "attribute.name", "@scriptAfterType"],
          [/"([^"]*)"/, "attribute.value"],
          [/'([^']*)'/, "attribute.value"],
          [/[\w\-]+/, "attribute.name"],
          [/=/, "delimiter"],
          [
            />/,
            {
              token: "delimiter",
              next: "@scriptEmbedded",
              nextEmbedded: "text/javascript"
            }
          ],
          [/[ \t\r\n]+/],
          // whitespace
          [/(<\/)(script\s*)(>)/, ["delimiter", "tag", { token: "delimiter", next: "@pop" }]]
        ],
        // After <script ... type
        scriptAfterType: [
          [/=/, "delimiter", "@scriptAfterTypeEquals"],
          [
            />/,
            {
              token: "delimiter",
              next: "@scriptEmbedded",
              nextEmbedded: "text/javascript"
            }
          ],
          // cover invalid e.g. <script type>
          [/[ \t\r\n]+/],
          // whitespace
          [/<\/script\s*>/, { token: "@rematch", next: "@pop" }]
        ],
        // After <script ... type =
        scriptAfterTypeEquals: [
          [
            /"module"/,
            {
              token: "attribute.value",
              switchTo: "@scriptWithCustomType.text/javascript"
            }
          ],
          [
            /'module'/,
            {
              token: "attribute.value",
              switchTo: "@scriptWithCustomType.text/javascript"
            }
          ],
          [
            /"([^"]*)"/,
            {
              token: "attribute.value",
              switchTo: "@scriptWithCustomType.$1"
            }
          ],
          [
            /'([^']*)'/,
            {
              token: "attribute.value",
              switchTo: "@scriptWithCustomType.$1"
            }
          ],
          [
            />/,
            {
              token: "delimiter",
              next: "@scriptEmbedded",
              nextEmbedded: "text/javascript"
            }
          ],
          // cover invalid e.g. <script type=>
          [/[ \t\r\n]+/],
          // whitespace
          [/<\/script\s*>/, { token: "@rematch", next: "@pop" }]
        ],
        // After <script ... type = $S2
        scriptWithCustomType: [
          [
            />/,
            {
              token: "delimiter",
              next: "@scriptEmbedded.$S2",
              nextEmbedded: "$S2"
            }
          ],
          [/"([^"]*)"/, "attribute.value"],
          [/'([^']*)'/, "attribute.value"],
          [/[\w\-]+/, "attribute.name"],
          [/=/, "delimiter"],
          [/[ \t\r\n]+/],
          // whitespace
          [/<\/script\s*>/, { token: "@rematch", next: "@pop" }]
        ],
        scriptEmbedded: [
          [/<\/script/, { token: "@rematch", next: "@pop", nextEmbedded: "@pop" }],
          [/[^<]+/, ""]
        ],
        // -- END <script> tags handling
        // -- BEGIN <style> tags handling
        // After <style
        style: [
          [/type/, "attribute.name", "@styleAfterType"],
          [/"([^"]*)"/, "attribute.value"],
          [/'([^']*)'/, "attribute.value"],
          [/[\w\-]+/, "attribute.name"],
          [/=/, "delimiter"],
          [
            />/,
            {
              token: "delimiter",
              next: "@styleEmbedded",
              nextEmbedded: "text/css"
            }
          ],
          [/[ \t\r\n]+/],
          // whitespace
          [/(<\/)(style\s*)(>)/, ["delimiter", "tag", { token: "delimiter", next: "@pop" }]]
        ],
        // After <style ... type
        styleAfterType: [
          [/=/, "delimiter", "@styleAfterTypeEquals"],
          [
            />/,
            {
              token: "delimiter",
              next: "@styleEmbedded",
              nextEmbedded: "text/css"
            }
          ],
          // cover invalid e.g. <style type>
          [/[ \t\r\n]+/],
          // whitespace
          [/<\/style\s*>/, { token: "@rematch", next: "@pop" }]
        ],
        // After <style ... type =
        styleAfterTypeEquals: [
          [
            /"([^"]*)"/,
            {
              token: "attribute.value",
              switchTo: "@styleWithCustomType.$1"
            }
          ],
          [
            /'([^']*)'/,
            {
              token: "attribute.value",
              switchTo: "@styleWithCustomType.$1"
            }
          ],
          [
            />/,
            {
              token: "delimiter",
              next: "@styleEmbedded",
              nextEmbedded: "text/css"
            }
          ],
          // cover invalid e.g. <style type=>
          [/[ \t\r\n]+/],
          // whitespace
          [/<\/style\s*>/, { token: "@rematch", next: "@pop" }]
        ],
        // After <style ... type = $S2
        styleWithCustomType: [
          [
            />/,
            {
              token: "delimiter",
              next: "@styleEmbedded.$S2",
              nextEmbedded: "$S2"
            }
          ],
          [/"([^"]*)"/, "attribute.value"],
          [/'([^']*)'/, "attribute.value"],
          [/[\w\-]+/, "attribute.name"],
          [/=/, "delimiter"],
          [/[ \t\r\n]+/],
          // whitespace
          [/<\/style\s*>/, { token: "@rematch", next: "@pop" }]
        ],
        styleEmbedded: [
          [/<\/style/, { token: "@rematch", next: "@pop", nextEmbedded: "@pop" }],
          [/[^<]+/, ""]
        ]
        // -- END <style> tags handling
      }
    };
  }
});
init_html();
export {
  conf,
  language
};
/*! Bundled license information:

monaco-editor/esm/vs/basic-languages/html/html.js:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=html-IF7BF2CX.js.map
