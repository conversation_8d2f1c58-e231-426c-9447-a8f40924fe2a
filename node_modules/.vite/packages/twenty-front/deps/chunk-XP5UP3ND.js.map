{"version": 3, "sources": ["../../../../@scalar/api-client/dist/components/IconSelector.vue.js"], "sourcesContent": ["import { defineComponent as g, computed as V, openBlock as s, createBlock as n, unref as e, withCtx as o, createVNode as a, createElementVNode as u, renderSlot as d, createTextVNode as p, createElementBlock as k, Fragment as y, renderList as _, toDisplayString as w } from \"vue\";\nimport { RadioGroup as S, RadioGroupLabel as m, RadioGroupOption as C } from \"@headlessui/vue\";\nimport { ScalarPopover as I } from \"@scalar/components\";\nimport { libraryIcons as h, LibraryIcon as B } from \"@scalar/icons\";\nconst G = { class: \"flex text-sm\" }, L = { class: \"bg-b-1 custom-scroll grid w-dvw max-w-[420px] auto-rows-[32px] grid-cols-[repeat(auto-fill,minmax(32px,1fr))] content-between justify-between rounded border p-1\" }, $ = /* @__PURE__ */ g({\n  __name: \"IconSelector\",\n  props: {\n    modelValue: {},\n    placement: {}\n  },\n  emits: [\"update:modelValue\"],\n  setup(i, { emit: f }) {\n    const b = i, x = f, c = V({\n      get: () => b.modelValue,\n      set: (l) => x(\"update:modelValue\", l)\n    });\n    return (l, r) => (s(), n(e(I), {\n      class: \"bg-b-2 rounded\",\n      focus: \"\",\n      placement: l.placement ?? \"bottom\"\n    }, {\n      popover: o(({ close: v }) => [\n        a(e(S), {\n          modelValue: c.value,\n          \"onUpdate:modelValue\": r[0] || (r[0] = (t) => c.value = t),\n          class: \"flex flex-col\"\n        }, {\n          default: o(() => [\n            u(\"div\", G, [\n              a(e(m), { class: \"text-c-2 px-1 py-1\" }, {\n                default: o(() => [\n                  d(l.$slots, \"title\", {}, () => [\n                    r[1] || (r[1] = p(\"Select an icon\"))\n                  ])\n                ]),\n                _: 3\n              })\n            ]),\n            u(\"ul\", L, [\n              (s(!0), k(y, null, _(e(h), (t) => (s(), n(e(C), {\n                key: t.src,\n                as: \"li\",\n                class: \"text-c-3 hover:text-c-2 hover:bg-b-2 ui-checked:bg-b-3 ui-active:bg-b-2 flex cursor-pointer items-center justify-center rounded p-2\",\n                value: t.src,\n                onClick: v\n              }, {\n                default: o(() => [\n                  a(e(m), { class: \"sr-only\" }, {\n                    default: o(() => [\n                      p(w(t.src.replaceAll(\"-\", \" \")) + \" Icon \", 1)\n                    ]),\n                    _: 2\n                  }, 1024),\n                  a(e(B), {\n                    class: \"stroke-[1.5]\",\n                    src: t.src\n                  }, null, 8, [\"src\"])\n                ]),\n                _: 2\n              }, 1032, [\"value\", \"onClick\"]))), 128))\n            ])\n          ]),\n          _: 2\n        }, 1032, [\"modelValue\"])\n      ]),\n      default: o(() => [\n        d(l.$slots, \"default\")\n      ]),\n      _: 3\n    }, 8, [\"placement\"]));\n  }\n});\nexport {\n  $ as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,IAAI,EAAE,OAAO,eAAe;AAAlC,IAAqC,IAAI,EAAE,OAAO,mKAAmK;AAArN,IAAwN,IAAoB,gBAAE;AAAA,EAC5O,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,YAAY,CAAC;AAAA,IACb,WAAW,CAAC;AAAA,EACd;AAAA,EACA,OAAO,CAAC,mBAAmB;AAAA,EAC3B,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAMA,KAAI,GAAG,IAAI,GAAG,IAAI,SAAE;AAAA,MACxB,KAAK,MAAMA,GAAE;AAAA,MACb,KAAK,CAAC,MAAM,EAAE,qBAAqB,CAAC;AAAA,IACtC,CAAC;AACD,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,YAAE,MAAE,CAAC,GAAG;AAAA,MAC7B,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW,EAAE,aAAa;AAAA,IAC5B,GAAG;AAAA,MACD,SAAS,QAAE,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,QAC3B,YAAE,MAAE,EAAC,GAAG;AAAA,UACN,YAAY,EAAE;AAAA,UACd,uBAAuB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;AAAA,UACxD,OAAO;AAAA,QACT,GAAG;AAAA,UACD,SAAS,QAAE,MAAM;AAAA,YACf,gBAAE,OAAO,GAAG;AAAA,cACV,YAAE,MAAE,EAAC,GAAG,EAAE,OAAO,qBAAqB,GAAG;AAAA,gBACvC,SAAS,QAAE,MAAM;AAAA,kBACf,WAAE,EAAE,QAAQ,SAAS,CAAC,GAAG,MAAM;AAAA,oBAC7B,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,gBAAgB;AAAA,kBACpC,CAAC;AAAA,gBACH,CAAC;AAAA,gBACD,GAAG;AAAA,cACL,CAAC;AAAA,YACH,CAAC;AAAA,YACD,gBAAE,MAAM,GAAG;AAAA,eACR,UAAE,IAAE,GAAG,mBAAE,UAAG,MAAM,WAAE,MAAE,EAAC,GAAG,CAAC,OAAO,UAAE,GAAG,YAAE,MAAE,EAAC,GAAG;AAAA,gBAC9C,KAAK,EAAE;AAAA,gBACP,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO,EAAE;AAAA,gBACT,SAAS;AAAA,cACX,GAAG;AAAA,gBACD,SAAS,QAAE,MAAM;AAAA,kBACf,YAAE,MAAE,EAAC,GAAG,EAAE,OAAO,UAAU,GAAG;AAAA,oBAC5B,SAAS,QAAE,MAAM;AAAA,sBACf,gBAAE,gBAAE,EAAE,IAAI,WAAW,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC;AAAA,oBAC/C,CAAC;AAAA,oBACD,GAAG;AAAA,kBACL,GAAG,IAAI;AAAA,kBACP,YAAE,MAAE,CAAC,GAAG;AAAA,oBACN,OAAO;AAAA,oBACP,KAAK,EAAE;AAAA,kBACT,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC;AAAA,gBACrB,CAAC;AAAA,gBACD,GAAG;AAAA,cACL,GAAG,MAAM,CAAC,SAAS,SAAS,CAAC,EAAE,GAAG,GAAG;AAAA,YACvC,CAAC;AAAA,UACH,CAAC;AAAA,UACD,GAAG;AAAA,QACL,GAAG,MAAM,CAAC,YAAY,CAAC;AAAA,MACzB,CAAC;AAAA,MACD,SAAS,QAAE,MAAM;AAAA,QACf,WAAE,EAAE,QAAQ,SAAS;AAAA,MACvB,CAAC;AAAA,MACD,GAAG;AAAA,IACL,GAAG,GAAG,CAAC,WAAW,CAAC;AAAA,EACrB;AACF,CAAC;", "names": ["b"]}