{"name": "json-stringify-deterministic", "description": "deterministic version of JSON.stringify() so you can get a consistent hash from stringified results.", "homepage": "https://github.com/Kikobeats/json-stringify-deterministic", "version": "1.0.12", "types": "./lib/index.d.ts", "main": "lib", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>", "url": "https://github.com/Kikobeats"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/kikobeats/json-stringify-deterministic.git"}, "bugs": {"url": "https://github.com/Kikobeats/json-stringify-deterministic/issues"}, "keywords": ["deterministic", "hash", "json", "sort", "stable", "stringify"], "devDependencies": {"@commitlint/cli": "latest", "@commitlint/config-conventional": "latest", "@ksmithut/prettier-standard": "latest", "c8": "latest", "ci-publish": "latest", "conventional-github-releaser": "latest", "finepack": "latest", "git-authors-cli": "latest", "mocha": "latest", "nano-staged": "latest", "npm-check-updates": "latest", "should": "latest", "simple-git-hooks": "latest", "standard": "latest", "standard-markdown": "latest", "standard-version": "latest"}, "engines": {"node": ">= 4"}, "files": ["index.js", "lib"], "scripts": {"clean": "rm -rf node_modules", "contributors": "(npx git-authors-cli && npx finepack && git add package.json && git commit -m 'build: contributors' --no-verify) || true", "coveralls": "nyc report --reporter=text-lcov | coveralls", "lint": "standard && standard-markdown", "postrelease": "npm run release:tags && npm run release:github && (ci-publish || npm publish --access=public)", "prerelease": "npm run update:check && npm run contributors", "pretest": "npm run lint", "release": "standard-version -a", "release:github": "conventional-github-releaser -p angular", "release:tags": "git push --follow-tags origin HEAD:master", "test": "c8 mocha --require should", "update": "ncu -u", "update:check": "ncu -- --error-level 2"}, "license": "MIT", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "nano-staged": {"*.js": ["prettier-standard"], "*.md": ["standard-markdown"], "package.json": ["finepack"]}, "simple-git-hooks": {"commit-msg": "npx commitlint --edit", "pre-commit": "npx nano-staged"}, "standard": {"globals": ["describe", "it"]}}