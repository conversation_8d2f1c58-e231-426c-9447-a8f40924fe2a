{"name": "is-finalizationregistry", "version": "1.0.2", "description": "Is this value a JS FinalizationRegistry? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "scripts": {"prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-finalizationregistry.git"}, "keywords": ["weakref", "finalization", "finalizationregistry", "finalization registry"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-finalizationregistry/issues"}, "homepage": "https://github.com/inspect-js/is-finalizationregistry#readme", "devDependencies": {"@ljharb/eslint-config": "^18.0.0", "aud": "^1.1.5", "auto-changelog": "^2.3.0", "eslint": "^7.32.0", "for-each": "^0.3.3", "nyc": "^10.3.2", "object-inspect": "^1.11.0", "safe-publish-latest": "^1.1.4", "tape": "^5.3.1"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "dependencies": {"call-bind": "^1.0.2"}}