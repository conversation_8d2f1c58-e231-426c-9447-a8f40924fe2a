{"name": "just-clone", "version": "6.2.0", "description": "deep copies objects and arrays", "type": "module", "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.mjs"}, "./package.json": "./package.json"}, "main": "index.cjs", "types": "index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rollup -c"}, "repository": "https://github.com/angus-c/just", "keywords": ["object", "clone", "copy", "deep-copy", "extend", "no-dependencies", "just"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/angus-c/just/issues"}}