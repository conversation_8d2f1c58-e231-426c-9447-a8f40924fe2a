# just-clone

## 6.2.0

### Minor Changes

- Rename node module .js -> .cjs

## 6.1.1

### Patch Changes

- fix: reorder exports to set default last #488

## 6.1.0

### Minor Changes

- package.json updates to fix #467 and #483

## 6.0.1

### Patch Changes

- Keep ESMs in sync with commonJS modules

## 6.0.0

### Major Changes

- 7dc1f05a: Supports Deep Cloning of Sets and Maps (Major bump due to reduced legacy ES support)

## 5.1.0

### Minor Changes

- Support cloning Maps and Sets
