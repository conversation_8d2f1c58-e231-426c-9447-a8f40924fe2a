{"name": "unist-util-remove-position", "version": "4.0.2", "description": "unist utility to remove positions from a tree", "license": "MIT", "keywords": ["unist", "unist-util", "util", "utility", "remove", "position", "location", "clean", "force"], "repository": "syntax-tree/unist-util-remove-position", "bugs": "https://github.com/syntax-tree/unist-util-remove-position/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/unist": "^2.0.0", "unist-util-visit": "^4.0.0"}, "devDependencies": {"@types/mdast": "^3.0.0", "@types/node": "^18.0.0", "c8": "^7.0.0", "mdast-util-from-markdown": "^1.0.0", "prettier": "^2.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "tsd": "^0.25.0", "type-coverage": "^2.0.0", "typescript": "^4.0.0", "unist-builder": "^3.0.0", "xo": "^0.53.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && tsd && type-coverage", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --check-coverage --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true}, "remarkConfig": {"plugins": ["preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true}}