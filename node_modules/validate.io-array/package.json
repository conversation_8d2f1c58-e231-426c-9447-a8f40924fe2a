{"name": "validate.io-array", "version": "1.0.6", "description": "Validates if a value is an array.", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "./node_modules/.bin/mocha", "test-cov": "./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --dir ./reports/coverage -- -R spec", "coveralls": "./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --dir ./reports/coveralls/coverage --report lcovonly -- -R spec && cat ./reports/coveralls/coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js && rm -rf ./reports/coveralls"}, "main": "./lib", "repository": {"type": "git", "url": "git://github.com/validate-io/array.git"}, "keywords": ["validate.io", "validate", "validation", "validator", "valid", "array", "is", "isarray", "type", "check"], "bugs": {"url": "https://github.com/validate-io/array/issues"}, "dependencies": {}, "devDependencies": {"chai": "2.x.x", "coveralls": "^2.11.1", "istanbul": "^0.3.0", "jshint": "2.x.x", "jshint-stylish": "^1.0.0", "mocha": "2.x.x", "proxyquire": "^1.4.0"}, "license": "MIT"}